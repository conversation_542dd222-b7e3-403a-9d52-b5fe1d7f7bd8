{"permissions": {"allow": ["<PERSON><PERSON>(mvn clean:*)", "<PERSON><PERSON>(mvn:*)", "Bash(timeout 60 mvn spring-boot:run -DskipTests)", "<PERSON><PERSON>(ulimit:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(pkill:*)", "Bash(find:*)", "<PERSON><PERSON>(diff:*)", "Bash(rm:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./start-minimal.sh:*)", "Bash(./start-dev.sh:*)"], "deny": []}}