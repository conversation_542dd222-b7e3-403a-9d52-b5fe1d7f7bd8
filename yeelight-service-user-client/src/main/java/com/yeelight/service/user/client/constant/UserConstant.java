package com.yeelight.service.user.client.constant;

import java.io.Serializable;

/**
 * @program: yeelight-service-user
 * @description: 用户常量类
 * @author: <PERSON>
 * @create: 2019-09-16 14:32
 **/
public class UserConstant implements Serializable {
    /**
     * token有效期，默认12小时
     */
    public static final  int ACCESS_TOKEN_VALIDITY_SECONDS = 60 * 60 * 12;
    /**
     * token有效期，默认30天
     */
    public static final int REFRESH_TOKEN_VALIDITY_SECONDS = 60 * 60 * 24 * 30;

    public static final String USER_SOCIAL_WEIXIN_PROVIDER_NAME = "wechat";
    public static final String USER_SOCIAL_XIAOMI_PROVIDER_NAME = "xiaomi";

    public static final String XIAOMI_OAUTH_REDIRECT_URI = "XIAOMI_OAUTH_REDIRECT_URI";

    /**
     * 小米 refresh token 过期时间（秒）
     */
    public static final Long XIAOMI_REFRESH_TOKEN_EXPIRE_TIME = 7776000L;

    /**
     * 小米 access token 过期时间（秒）
     */
    public static final Long XIAOMI_ACCESS_TOKEN_EXPIRE_TIME = 259200L;

    /**
     * 小米 token 刷新 提前时间（秒）
     */
    public static final Long XIAOMI_TOKEN_ADVANCE_TIME = 6000L;

    public static final String YEELIGHT_USER_EVENT_CONSUMER_GROUP = "YEELIGHT_USER_EVENT_CONSUMER_GROUP";

    public static final String USER_VENDOR_KEY = "userVendor";

    public static final String ORIGINAL_DOMAIN_KEY = "originalDomain";
    public static final String ORIGINAL_PROTOCOL_KEY = "originalProtocol";
    public static final String ORIGINAL_PORT_KEY = "originalPort";
    public static final String ORIGINAL_PATH_KEY = "originalPath";

    /**
     * 浏览器信息
     */
    public static final String USER_LOGIN_ENVIRONMENT_UA = "USER_LOGIN_ENVIRONMENT_UA";

    /**
     * 服务器IP
     */
    public static final String USER_LOGIN_ENVIRONMENT_SERVER_IP = "USER_LOGIN_ENVIRONMENT_SERVER_IP";
    /**
     * 服务器名称
     */
    public static final String USER_LOGIN_ENVIRONMENT_SERVER_NAME = "USER_LOGIN_ENVIRONMENT_SERVER_NAME";
    /**
     * 客户端IP
     */
    public static final String USER_LOGIN_ENVIRONMENT_IP = "USER_LOGIN_ENVIRONMENT_IP";

    public static final String USER_LOGIN_ENVIRONMENT_LOCALE = "lang_session";

    public static final String BLACK_USER_MONITORING = "black:user:monitoring:";

    public static final String COMMA_DELIMITERS = ",";
}
