package com.yeelight.service.user.client.domain;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.util.Assert;

/**
 * @program: yeelight-oauth-api
 * @description: 自定义授权类，用于封装用户权限信息，实现GrantedAuthority接口
 * @author: <PERSON>
 * @create: 2019-07-18 09:59
 **/
public class CustomGrantedAuthority implements GrantedAuthority {
    private String role;

    public CustomGrantedAuthority() {

    }
    public CustomGrantedAuthority(String role) {
        Assert.hasText(role, "A granted authority textual representation is required");
        this.role = role;
    }

    @Override
    public String getAuthority() {
        return role;
    }
}
