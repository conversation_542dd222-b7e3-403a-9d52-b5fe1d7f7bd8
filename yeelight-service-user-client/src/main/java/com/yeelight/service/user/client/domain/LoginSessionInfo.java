package com.yeelight.service.user.client.domain;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 登录会话信息
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class LoginSessionInfo extends LoginUserInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * session id
     */
    private String sessionId;
}
