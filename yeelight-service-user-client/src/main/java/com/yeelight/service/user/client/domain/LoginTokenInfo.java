package com.yeelight.service.user.client.domain;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * token登录信息
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class LoginTokenInfo extends LoginUserInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * accessToken
     */
    private String accessToken;
}
