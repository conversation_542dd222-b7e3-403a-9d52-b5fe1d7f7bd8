package com.yeelight.service.user.client.domain;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.enums.UserLoginType;
import com.yeelight.service.user.client.token.JustAuthAuthenticationToken;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;
import org.springframework.security.core.Authentication;

import java.io.Serializable;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

/**
 * 登录用户信息
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class LoginUserInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String username;

    /**
     * 所属供应商
     */
    private UserVendorEnum vendor;

    /**
     * 是否过期
     */
    private boolean expired;

    /**
     * 过期时长
     */
    private Duration maxInactiveInterval;

    /**
     * 最后访问时间
     */
    private Instant lastAccessedTime;

    /**
     * 创建时间
     */
    private Instant creationTime;

    /**
     * 过期时间
     */
    private Instant expirationTime;

    /**
     * 认证信息
     */
    private OAuth2AuthenticationDto authentication;

    /**
     * 登录的用户
     */
    private YeelightUser user;

    /**
     * 浏览器ua
     */
    private String userAgent;

    /**
     * 浏览器类型
     */
    private String browserName;

    /**
     * 浏览器版本
     */
    private String browserVersion;

    /**
     * 浏览器引擎
     */
    private String browserEngine;

    /**
     * 浏览器引擎版本
     */
    private String browserEngineVersion;

    /**
     * 平台类型
     */
    private String platform;

    /**
     * 系统类型
     */
    private String osName;

    /**
     * 系统版本
     */
    private String osVersion;

    /**
     * 是否为移动平台
     */
    private boolean isMobile;

    /**
     * 是否为Iphone或者iPod设备
     */
    public boolean isIPhoneOrIPod;

    /**
     * 是否为Iphone或者iPod设备
     */
    public boolean isIPad;

    /**
     * 是否为IOS平台，包括IPhone、IPod、IPad
     */
    public boolean isIos;

    /**
     * 是否为Android平台，包括Android和Google TV
     */
    public boolean isAndroid;

    /**
     * 地区语言
     */
    private Locale locale;

    /**
     * 客户端ip
     */
    private String ip;

    /**
     * 服务器ip
     */
    private String serviceIp;

    /**
     * 服务器名
     */
    private String serviceName;

    /**
     * 登录类型
     */
    private String loginType;

    /**
     * 三方登录类型
     */
    private String justAuthSource;

    public void attachAuthentication(Authentication authentication) {
        if (Objects.nonNull(authentication)  && this.authentication.isAuthenticated()) {
            this.username = this.authentication.getName();
            // 获取Authentication的类型
            String authenticationType = authentication.getClass().getSimpleName();
            // 根据authenticationType进行不同的处理
            UserLoginType userLoginType = UserLoginType.getTypeByCode(authenticationType);
            this.loginType = userLoginType.name();
            attachJustAuthSource(authentication, userLoginType);
        }
    }

    private void attachJustAuthSource(Authentication authentication, UserLoginType userLoginType) {
        if (UserLoginType.JustAuth.equals(userLoginType)) {
            JustAuthAuthenticationToken justAuthAuthenticationToken = (JustAuthAuthenticationToken) authentication;
            if (Objects.nonNull(justAuthAuthenticationToken) && Objects.nonNull(justAuthAuthenticationToken.getDetails()) && justAuthAuthenticationToken.getDetails() instanceof AuthUser) {
                AuthUser authUser = (AuthUser) authentication.getDetails();
                this.justAuthSource = Objects.nonNull(authUser) ? (Objects.nonNull(this.vendor) ? UserVendorHolder.removeVendor(this.vendor.getCode(), authUser.getSource()): authUser.getSource()) : null;
            }
        }
    }

    /**
     * 获取OAuth2Request - 新版方法，返回YeelightOAuth2Request
     * @return YeelightOAuth2Request对象
     */
    public YeelightOAuth2Request getYeelightOAuth2Request() {
        if (Objects.nonNull(this.authentication) && Objects.nonNull(this.authentication.getStoredRequest())) {
            return this.authentication.getStoredRequest();
        }
        return null;
    }

    public String getOAuth2ClientId() {
        YeelightOAuth2Request oAuth2Request = getYeelightOAuth2Request();
        if (oAuth2Request != null) {
            return oAuth2Request.getClientId();
        }
        return null;
    }

    /**
     *
     * Oauth支持的5类 grant_type
     * authorization_code — 授权码模式(即先登录获取code,再获取token)
     * password — 密码模式(将用户名,密码传过去,直接获取token)
     * client_credentials — 客户端模式(无用户,用户向客户端注册,然后客户端以自己的名义向’服务端’获取资源)
     * implicit — 简化模式(在redirect_uri 的Hash传递token; Auth客户端运行在浏览器中,如JS,Flash)
     * refresh_token — 刷新access_token
     */
    public String getOAuth2GrantType() {
        YeelightOAuth2Request oAuth2Request = getYeelightOAuth2Request();
        if (oAuth2Request != null) {
            // YeelightOAuth2Request没有getGrantType方法，从requestParameters中获取
            Map<String, String> params = oAuth2Request.getRequestParameters();
            return params != null ? params.get("grant_type") : null;
        }
        return null;
    }

    public String getOAuth2LoginType() {
        String grantType = getOAuth2GrantType();
        if (UserLoginType.OAuth2.name().equals(this.loginType) && Objects.nonNull(grantType) && "password".equals(grantType)) {
            Map<String, String> requestParameters = getOAuth2RequestParameters();
            if (Objects.nonNull(requestParameters)) {
                return parseOauth2LoginType(requestParameters);
            }
        }
        return null;
    }

    private static String parseOauth2LoginType(Map<String, String> requestParameters) {
        String accountTypeKey = "accountType";
        String captchaKey = "captcha";
        String usernameKey = "username";
        String wxInfoKey = "wxInfo";
        String providerNameKey = "providerName";
        String appNameKey = "appName";
        String loginTokenKey = "loginToken";
        String externalIdTokenKey = "externalIdToken";
        String idKey = "ID";
        String phoneKey = "PHONE_NUMBER";
        String emailKey = "EMAIL";
        
        if (requestParameters.containsKey(accountTypeKey) && requestParameters.containsKey(captchaKey)  && requestParameters.containsKey(usernameKey) ) {
            return parseGeneralLoginType(requestParameters, emailKey, accountTypeKey, phoneKey, usernameKey, idKey);
        } else if (requestParameters.containsKey(wxInfoKey)) {
            // 手机号+微信信息
            return "PhoneAndWechatInfo";
        } else if (requestParameters.containsKey(providerNameKey)) {
            // 三方信息
            return "SocialInfo";
        } else if (requestParameters.containsKey(captchaKey)  && requestParameters.containsKey(usernameKey)) {
            // 手机号+验证码
            return "PhoneAndCode";
        } else if (requestParameters.containsKey(appNameKey) && requestParameters.containsKey(loginTokenKey)) {
            // 手机号一键登录
            return "PhoneOneStep";
        } else if (requestParameters.containsKey(externalIdTokenKey)) {
            // 外部系统一键登录
            return "ExternalId";
        }
        return "password";
    }

    private static String parseGeneralLoginType(Map<String, String> requestParameters, String emailKey, String accountTypeKey, String phoneKey, String usernameKey, String idKey) {
        if (emailKey.equals(requestParameters.get(accountTypeKey))) {
            // 邮箱+验证码
            return "EmailAndCode";
        } else if(phoneKey.equals(requestParameters.get(accountTypeKey))) {
            // 手机号+验证码
            return "PhoneAndCode";
        } else if(usernameKey.toUpperCase(Locale.ROOT).equals(requestParameters.get(accountTypeKey))) {
            // 用户名+验证码
            return "UsernameAndCode";
        } else if(idKey.equals(requestParameters.get(accountTypeKey))) {
            // ID+验证码
            return "IdAndCode";
        }
        // 账号+验证码
        return "AccountAndCode";
    }

    public String getOAuth2RedirectUri() {
        YeelightOAuth2Request oAuth2Request = getYeelightOAuth2Request();
        if (oAuth2Request != null) {
            return oAuth2Request.getRedirectUri();
        }
        return null;
    }

    public Map<String, String> getOAuth2RequestParameters() {
        YeelightOAuth2Request oAuth2Request = getYeelightOAuth2Request();
        if (oAuth2Request != null) {
            return oAuth2Request.getRequestParameters();
        }
        return null;
    }

    public Map<String, Serializable> getOAuth2Extensions() {
        YeelightOAuth2Request oAuth2Request = getYeelightOAuth2Request();
        if (oAuth2Request != null) {
            return oAuth2Request.getExtensions();
        }
        return null;
    }

    public Collection<String> getOAuth2Scope() {
        YeelightOAuth2Request oAuth2Request = getYeelightOAuth2Request();
        if (oAuth2Request != null) {
            return oAuth2Request.getScope();
        }
        return null;
    }
}
