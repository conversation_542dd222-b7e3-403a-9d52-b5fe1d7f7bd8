package com.yeelight.service.user.client.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 代理信息
 *
 * <AUTHOR>
 * @date 2020-02-19 13:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProxyInfo implements Serializable {
    /**
     * 被代理的token
     */
    private String token;
    /**
     * 使用代理的应用服务列表
     */
    private List<String> applyApplicationList;
    /**
     * 参数
     */
    private Map<String, Object> params;
}
