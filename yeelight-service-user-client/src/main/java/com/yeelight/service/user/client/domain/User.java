package com.yeelight.service.user.client.domain;

import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 用户信息
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class User extends YeelightUser implements UserDetails, CredentialsContainer {

    private String totpSecret;

    @Override
    public void eraseCredentials() {
        this.setPassword(null);
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        List<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new CustomGrantedAuthority("ROLE_USER"));
        return authorities;
    }

}
