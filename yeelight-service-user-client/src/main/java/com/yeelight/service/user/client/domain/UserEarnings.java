package com.yeelight.service.user.client.domain;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户收益表
 * <AUTHOR>
 */
@Table(name = "yeelight_user_earnings")
@Data
public class UserEarnings implements Serializable {
    /**
     * ID
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    /**
     * 收入来源
     */
    @Column(name = "earning_from")
    private String earningFrom;

    /**
     * 来源ID
     */
    @Column(name = "from_id")
    private Long fromId;

    /**
     * 是否入账 0：未入账 1：已入账
     */
    @Column(name = "is_credit")
    private Integer isCredit;

    /**
     * 收入类型
     */
    @Column(name = "earning_type")
    private String earningType;

    /**
     * 收入流水号
     */
    @Column(name = "earning_no")
    private String earningNo;


    /**
     * 收入金额
     */
    @Column(name = "earning_money")
    private BigDecimal earningMoney;

    /**
     * 当前账号余额
     */
    @Column(name = "account_money")
    private BigDecimal accountMoney;


    /**
     * 收入时间
     */
    @Column(name = "earning_time")
    private Integer earningTime;

    /**
     * 是否作废 0：未作废 1：已作废
     */
    @Column(name = "is_invalid")
    private Integer isInvalid;

    /**
     * 作废时间
     */
    @Column(name = "invalid_time")
    private Integer invalidTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    private Integer auditTime;

    /**
     * 审核人uid
     */
    @Column(name = "audit_uid")
    private Long auditUid;

    /**
     * 审核人
     */
    @Column(name = "audit_name")
    private String auditName;
}