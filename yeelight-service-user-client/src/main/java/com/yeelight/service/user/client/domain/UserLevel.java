package com.yeelight.service.user.client.domain;

import com.yeelight.service.user.client.enums.UserEarningFrom;
import com.yeelight.service.user.client.enums.UserEarningTypes;
import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户等级表
 * <AUTHOR>
 */
@Table(name = "yeelight_user_level")
@Data
public class UserLevel  implements Serializable {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 等级类型
     */
    @Column(name = "level_type")
    private String levelType;

    /**
     * 等级名称
     */
    @Column(name = "level_name")
    private String levelName;

    /**
     * 所需成长值
     */
    @Column(name = "required_growth")
    private Integer requiredGrowth;

    /**
     * 分成规则
     */
    @Column(name = "commission_rules")
    private String commissionRules;

    /**
     * 成长规则
     */
    @Column(name = "growth_rules")
    private String growthRules;

    /**
     * 等级描述
     */
    @Column(name = "level_desc")
    private String levelDesc;

    /**
     * 创建人id
     */
    @Column(name = "create_uid")
    private Long createUid;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Integer createTime;

    /**
     * 修改人id
     */
    @Column(name = "update_uid")
    private Long updateUid;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Integer updateTime;

    @Data
    public static class CommissionRule implements Serializable {
        /**
         * 分成比
         */
        private BigDecimal commissionRate;

        /**
         * 分成类型
         */
        private UserEarningTypes commissionType;

        /**
         * 分成来源
         */
        private UserEarningFrom commissionFrom;
    }

    @Data
    public static class GrowthRule implements Serializable {
        /**
         * 每成长值对应金额
         */
        private BigDecimal moneyRequiredPerGrowth;

        /**
         * 收入类型
         */
        private UserEarningTypes earningType;

        /**
         * 收入来源
         */
        private UserEarningFrom earningFrom;
    }
}