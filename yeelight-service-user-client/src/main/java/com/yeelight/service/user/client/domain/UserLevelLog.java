package com.yeelight.service.user.client.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 用户等级日志表
 * <AUTHOR>
 */
@Table(name = "yeelight_user_level_log")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLevelLog implements Serializable {
    /**
     * ID
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    /**
     * 用户名称
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 升级前成长值
     */
    @Column(name = "before_growth")
    private Integer beforeGrowth;

    /**
     * 升级后成长值
     */
    @Column(name = "after_growth")
    private Integer afterGrowth;

    /**
     * 升级前等级
     */
    @Column(name = "before_level")
    private Long beforeLevel;

    /**
     * 升级后等级
     */
    @Column(name = "after_level")
    private Long afterLevel;

    /**
     * 等级类型
     */
    @Column(name = "level_type")
    private String levelType;

    /**
     * 升级备注
     */
    private String description;

    /**
     * 创建人id
     */
    @Column(name = "create_uid")
    private Long createUid;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Integer createTime;

    /**
     * 修改人id
     */
    @Column(name = "update_uid")
    private Long updateUid;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Integer updateTime;
}