package com.yeelight.service.user.client.domain;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 用户等级关系表
 * <AUTHOR>
 */
@Table(name = "yeelight_user_level_rel")
@Data
public class UserLevelRel implements Serializable {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    /**
     * 等级ID
     */
    @Column(name = "level_id")
    private Long levelId;

    /**
     * 等级类型
     */
    @Column(name = "level_type")
    private String levelType;

    /**
     * 等级名称
     */
    @Column(name = "level_name")
    private String levelName;

    /**
     * 等级名称
     */
    @Column(name = "growth")
    private Integer growth;

    @Transient
    private Integer count;
}