package com.yeelight.service.user.client.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户监控属性
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserMonitoringProperty implements Serializable {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 每分钟限制次数
     */
    private Long limitPerMinute;

    /**
     * 最后一次设置时间
     */
    private Long lastSetTime;

    /**
     * 是否移除
     */
    private Boolean remove;
}
