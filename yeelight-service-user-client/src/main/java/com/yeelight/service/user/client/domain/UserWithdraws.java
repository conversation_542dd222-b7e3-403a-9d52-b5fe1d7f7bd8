package com.yeelight.service.user.client.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户提现表
 * <AUTHOR>
 */
@Table(name = "yeelight_user_withdraws")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserWithdraws implements Serializable {
    /**
     * ID
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    /**
     * 提现类型
     */
    @Column(name = "withdraw_type")
    private String withdrawType;

    /**
     * 提现流水号
     */
    @Column(name = "withdraw_no")
    private String withdrawNo;


    /**
     * 申请提现金额
     */
    @Column(name = "apply_money")
    private BigDecimal applyMoney;

    /**
     * 当前账号余额
     */
    @Column(name = "account_money")
    private BigDecimal accountMoney;

    /**
     * 提现状态 0- 待审核 1- 提现成功 -1- 提现失败
     */
    private Integer status;

    /**
     * 提现申请时间
     */
    @Column(name = "apply_time")
    private Integer applyTime;

    /**
     * 持卡人姓名
     */
    @Column(name = "bank_user_name")
    private String bankUserName;

    /**
     * 开户行所在地
     */
    @Column(name = "bank_location")
    private String bankLocation;

    /**
     * 银行名称
     */
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 银行卡号
     */
    @Column(name = "bank_card")
    private String bankCard;

    /**
     * 审核时间
     */
    @Column(name = "audit_time")
    private Integer auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人uid
     */
    @Column(name = "audit_uid")
    private Long auditUid;

    /**
     * 审核人
     */
    @Column(name = "audit_name")
    private String auditName;
}