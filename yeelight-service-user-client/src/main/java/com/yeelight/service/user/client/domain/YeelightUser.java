package com.yeelight.service.user.client.domain;

import com.yeelight.service.user.client.enums.UserStatus;
import lombok.Data;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;

/**
 * @program: yeelight-service-oauth
 * @description: 用户信息实体类
 * @author: Sheldon
 * @create: 2019-06-10 14:10
 **/
@Data
public class YeelightUser implements UserDetails, CredentialsContainer {
    private static final long serialVersionUID = -9630570298872783L;
    private Long id;
    private String username;
    private String password;
    private String status;
    private String region;
    private String phoneNumber;
    private String email;
    private String name;
    private Collection<GrantedAuthority> grantedAuthorities;
    private Collection<String> resources = new ArrayList<>();

    @Override
    public void eraseCredentials() {
        this.password = null;
    }

    /**
     * 返回用户权限集合
     * @return Collection
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        ArrayList<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new CustomGrantedAuthority("ROLE_USER"));
        return authorities;
    }

    /**
     * 返回用户权限集合
     * @return String
     */
    @Override
    public String getPassword() {
        return this.password;
    }

    /**
     * 返回用户的用户名
     * @return String
     */
    @Override
    public String getUsername() {
        return this.username;
    }

    /**
     * 账户是否未过期（true 未过期, false 过期）
     * @return boolean
     */
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 账户是否未锁定（true 未锁定, false 锁定）
     * 用户账户可能会被封锁，达到一定要求可恢复
     * @return boolean
     */
    @Override
    public boolean isAccountNonLocked() {
        return UserStatus.ENABLED.getCode().equals(status);
    }

    /**
     * 密码是否未过期（true 未过期, false 过期）
     * 一些安全级别高的系统，可能要求 30 天更换一次密码，如果密码过期，需要提醒用户更换密码
     * @return boolean
     */
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 账户是否可用（true 可用, false 不可用）
     * 系统一般不会真正的删除用户信息，而是假删除，通过一个状态码标志用户是否被删除，这样做的好处是可以保留用户信息，以便日后分析
     * @return boolean
     */
    @Override
    public boolean isEnabled() {
        return UserStatus.ENABLED.getCode().equals(status);
    }

    /**
     * 用户每登录一个新 Session 会话都会创建一个对应的 SessionInformation 对象，该对象是 SessionId 和用户信息的封装，相关信息会缓存在 principals 和 sessionIds 这两个 Map 集合中。需要注意的是 principals 集合采用的是以用户信息（UserDetails）为 key 的设计，在 HashMap 中以对象为 key 必须重写 hashCode 和 equals 方法
     * 所以重写了equals, hashCode这两个方法
     * @param obj 用户信息
     * @return boolean
     */
    @Override
    public boolean equals(Object obj) {
        return obj instanceof YeelightUser && this.username.equals(((YeelightUser) obj).getUsername())  && this.id.equals(((YeelightUser) obj).getId());
    }

    @Override
    public int hashCode() {
        return this.id.hashCode();
    }
}
