package com.yeelight.service.user.client.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户账户表
 * <AUTHOR>
 */
@JsonInclude()
@Table(name = "yeelight_users_account")
@Data
public class YeelightUserAccount implements Serializable {

    /**
     * ID
     */
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    @Column(name = "estimate_withdrawable_amount")
    private BigDecimal estimateWithdrawableAmount;

    @Column(name = "actual_withdrawable_amount")
    private BigDecimal actualWithdrawableAmount;

    @Column(name = "accumulative_withdrawn_amount")
    private BigDecimal accumulativeWithdrawnAmount;

}
