package com.yeelight.service.user.client.domain;

import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 用户银行卡表
 * <AUTHOR>
 */
@Table(name = "yeelight_users_bank")
@Data
public class YeelightUserBank implements Serializable {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Long id;

    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "card_no")
    private String cardNo;

    private String location;

    @Column(name = "is_default")
    private Integer isDefault;

    private String type;

    @Column(name = "create_time")
    private Integer createTime;

    @Column(name = "update_time")
    private Integer updateTime;

    public static YeelightUserBank newDesignerInstance(Long uid) {
        YeelightUserBank userBank = new YeelightUserBank();
        userBank.setYeelightUserId(uid);
        userBank.setIsDefault(1);
        userBank.setType(UserLevelTypes.DESIGNER.getCode());
        userBank.setCreateTime(DateUtils.getCurrentSecond());
        userBank.setUpdateTime(DateUtils.getCurrentSecond());
        return userBank;
    }

}
