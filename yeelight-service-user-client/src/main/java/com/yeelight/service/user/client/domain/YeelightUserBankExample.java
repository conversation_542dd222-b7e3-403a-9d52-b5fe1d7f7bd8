package com.yeelight.service.user.client.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户银行卡表查询条件
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class YeelightUserBankExample implements Serializable {

    private Long id;

    private Long yeelightUserId;

    private String userName;

    private String bankName;

    private String cardNo;

    private Integer isDefault;

    private String type;

    private List<Long> ids;

}
