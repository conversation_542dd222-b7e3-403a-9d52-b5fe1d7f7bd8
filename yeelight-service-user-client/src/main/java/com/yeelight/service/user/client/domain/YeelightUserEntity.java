package com.yeelight.service.user.client.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户实体类
 * <AUTHOR>
 */
@Data
public class YeelightUserEntity implements Serializable {

    private Long id;

    private String realName;

    private String phoneNumber;

    private String avatar;

    private String birthday;

    private String idCard;

    private String provinceId;

    private String provinceName;

    private String cityId;

    private String cityName;

    private String regionId;

    private String regionName;

    private String address;

    private String idPicObverse;

    private String idPicReverse;

    private String certificationData;

}
