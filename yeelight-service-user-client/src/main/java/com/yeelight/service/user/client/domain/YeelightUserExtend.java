package com.yeelight.service.user.client.domain;

import lombok.Data;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 用户扩展信息表
 * <AUTHOR>
 */
@Data
@Table(name = "yeelight_users_extend")
public class YeelightUserExtend implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "yeelight_user_id")
    private Long yeelightUserId;

    @Column(name = "real_name")
    private String realName;

    @Column(name = "username_modify_num")
    private Integer usernameModifyNum;

    private String birthday;

    @Column(name = "province_id")
    private String provinceId;

    @Column(name = "province_name")
    private String provinceName;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "city_name")
    private String cityName;

    @Column(name = "region_id")
    private String regionId;

    @Column(name = "region_name")
    private String regionName;

    private String address;

    @Column(name = "id_card")
    private String idCard;

    @Column(name = "id_pic_obverse")
    private String idPicObverse;

    @Column(name = "id_pic_reverse")
    private String idPicReverse;

    @Column(name = "certification_data")
    private String certificationData;

    @Column(name = "totp_secret")
    private String totpSecret;

}
