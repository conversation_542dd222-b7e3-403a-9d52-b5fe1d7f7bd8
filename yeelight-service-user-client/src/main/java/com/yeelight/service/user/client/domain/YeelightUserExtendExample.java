package com.yeelight.service.user.client.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户扩展表查询条件
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class YeelightUserExtendExample implements Serializable {

    private Long id;

    private Long yeelightUserId;

    private Long notEqualUnionId;

    private String realName;

    private String birthday;

    private String provinceId;

    private String provinceName;

    private String cityId;

    private String cityName;

    private String regionId;

    private String regionName;

    private String idCard;

    private String totpSecret;

}
