package com.yeelight.service.user.client.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 小米用户Dto
 * <AUTHOR>
 */
@Data
@Deprecated
public class MiUserDto implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 小米ID
     */
    private Long mid;

    /**
     * Access Token
     */
    private String accessToken;

    /**
     * Refresh Token
     */
    private String refreshToken;

    /**
     * 过期时间
     */
    private Integer expiresIn;

    /**
     * 小米昵称
     */
    private String nickname;

    /**
     * 小米头像	
     */
    private String avatarUrl;

    /**
     * 添加时间
     */
    private Integer createdTime;

    /**
     * 更新时间	
     */
    private Integer updatedTime;

    /**
     * 记录count计算结果
     */
    private Integer count;
}