package com.yeelight.service.user.client.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 监控用户Dto
 * <AUTHOR>
 * @program: yeelight-service-user
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MonitoringYeelightUserDto extends YeelightUserDto implements Serializable {
    /**
     * 每分钟限制次数
     */
    private Long limitPerMinute;

    /**
     * 最后一次设置时间
     */
    private Long lastSetTime;
}
