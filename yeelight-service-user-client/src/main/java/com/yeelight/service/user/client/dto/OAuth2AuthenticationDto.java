package com.yeelight.service.user.client.dto;

import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;
import lombok.Data;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collection;

/**
 * @program: yeelight-service-user
 * @description: OAuth2认证Dto - 已适配使用自建OAuth2对象，保持向后兼容性
 * @author: Sheldon
 * @create: 2019-07-17 17:32
 **/
@Data
public class OAuth2AuthenticationDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 使用自建的OAuth2Request替代旧版依赖
     * 保持字段名不变，确保序列化兼容性
     */
    private YeelightOAuth2Request storedRequest;

    private Authentication userAuthentication;

    private String name;

    private Object credentials;

    private Object principal;

    private boolean clientOnly;

    private boolean authenticated;

    private Collection<GrantedAuthority> authorities;

    private Object details;

    /**
     * 默认构造函数
     */
    public OAuth2AuthenticationDto() {
    }
}
