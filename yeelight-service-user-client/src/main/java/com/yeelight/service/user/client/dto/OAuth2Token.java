package com.yeelight.service.user.client.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * @program: yeelight-service-user
 * @description: OAuth2Token DTO
 * @author: yeelight
 * @create: 2022-09-08 17:43
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OAuth2Token implements Serializable {
    @J<PERSON><PERSON>ield(name = "access_token")
    @JsonProperty(value = "access_token")
    private String accessToken;
    @JsonProperty(value = "token_type")
    @JSONField(name = "token_type")
    private String tokenType;
    @JsonProperty(value = "refresh_token")
    @JSONField(name = "refresh_token")
    private String refreshToken;
    @JsonProperty(value = "expires_in")
    @JSONField(name = "expires_in")
    private Integer expiresIn;
    private String scope;
    private Long id;
    private String region;
    private String device;
    @JsonProperty(value = "client_id")
    @JSONField(name = "client_id")
    private String clientId;
    private String username;
}
