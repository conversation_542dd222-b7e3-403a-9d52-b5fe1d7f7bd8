package com.yeelight.service.user.client.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: yeelight-service-user
 * @description: 可信合作伙伴注册用户Dto
 * @author: <PERSON>
 * @create: 2024-12-10 17:23
 **/
@Data
public class PartnerRegisterUserDto implements Serializable {
    /**
     * 用户名, 与请求参数一一对应
     * 请求的参数是手机号列表或者邮箱列表
     */
    private String username;

    /**
     * 添加成功的易来用户 ID
     */
    private Long yeelightId;

    /**
     * 添加成功的添加成功的易来用户 ID 签名
     */
    private String yeelightIdSign;
}
