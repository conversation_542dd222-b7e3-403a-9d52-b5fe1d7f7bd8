package com.yeelight.service.user.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.Optional;

/**
 * 社交用户信息
 * <AUTHOR>
 */
@Data
public class SocialUserDto implements Serializable {
    /**
     * ID
     */
    private Long id;

    private Long yeelightId;

    /**
     * 第三方系统的唯一ID
     */
    private String uuid;

    /**
     * 第三方系统的唯一ID GITHUB、GITEE、QQ
     */
    private String source;

    /**
     * 用户的授权令牌
     */
    private String accessToken;

    /**
     * 第三方用户的授权令牌的有效期
     */
    private Integer expireIn;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 第三方用户的 open id
     */
    private String openId;

    /**
     * 第三方用户的 ID
     */
    private String uid;

    /**
     * 个别平台的授权信息
     */
    private String accessCode;

    /**
     * 第三方用户的 union id
     */
    private String unionId;

    /**
     * 第三方用户授予的权限
     */
    private String scope;

    /**
     * 个别平台的授权信息
     */
    private String tokenType;

    /**
     * id token
     */
    private String idToken;

    /**
     * 小米平台用户的附带属性
     */
    private String macAlgorithm;

    /**
     * 小米平台用户的附带属性
     */
    private String macKey;

    /**
     * 用户的授权code
     */
    private String code;

    /**
     * Twitter平台用户的附带属性
     */
    private String oauthToken;

    /**
     * Twitter平台用户的附带属性
     */
    private String oauthTokenSecret;

    /**
     * 微信会话秘钥
     */
    private String sessionKey;

    private Integer createdTime;

    private Integer updatedTime;

    private Integer lastTokenTime;

    public boolean isAccessTokenExpired() {
        return ((int) Instant.now().getEpochSecond() - Optional.ofNullable(this.getExpireIn()).orElse(0)) >= (Optional.ofNullable(this.getLastTokenTime()).orElse(0) - 60);
    }
}
