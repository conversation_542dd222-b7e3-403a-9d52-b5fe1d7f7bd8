package com.yeelight.service.user.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: yeelight-service-user
 * @description: 用户等级和规则数据传输对象
 * @author: <PERSON>
 * @create: 2020-03-19 16:09
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLevelAndRulesData implements Serializable  {
    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String userMobile;


    /**
     * 用户当前成长值
     */
    private Integer userCurrentGrowth;

    /**
     * 用户当前等级ID
     */
    private Long userCurrentLevelId;

    /**
     * 返佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 参与返佣的金额
     */
    private BigDecimal commissionMoney;

    /**
     * 预计收入金额
     */
    private BigDecimal expectedMoney;

    /**
     * 可得成长值
     */
    private Integer growth;
}
