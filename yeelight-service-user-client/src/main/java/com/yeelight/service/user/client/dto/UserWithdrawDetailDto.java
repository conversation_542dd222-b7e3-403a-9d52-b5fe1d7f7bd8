package com.yeelight.service.user.client.dto;

import com.yeelight.service.user.client.domain.UserLevelRel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户提现详情数据传输对象
 * <AUTHOR>
 */
@Data
public class UserWithdrawDetailDto implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 提现类型
     */
    private String withdrawType;

    /**
     * 提现流水号
     */
    private String withdrawNo;


    /**
     * 申请提现金额
     */
    private BigDecimal applyMoney;

    /**
     * 当前账号余额
     */
    private BigDecimal accountMoney;

    /**
     * 提现状态 0- 待审核 1- 提现成功 -1- 提现失败
     */
    private Integer status;

    /**
     * 提现申请时间
     */
    private Integer applyTime;

    /**
     * 持卡人姓名
     */
    private String bankUserName;

    /**
     * 开户行所在地
     */
    private String bankLocation;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 审核时间
     */
    private Integer auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人uid
     */
    private Long auditUid;

    /**
     * 审核人
     */
    private String auditName;


    private String realName;

    private String phoneNumber;

    private String provinceId;

    private String provinceName;

    private String cityId;

    private String cityName;

    private String regionId;

    private String regionName;

    private String address;

    private UserLevelRel userLevelRel;

}
