package com.yeelight.service.user.client.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 微信用户信息
 * <AUTHOR>
 */
@Data
@Deprecated
public class WxUserDto implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 用户性别
     */
    private String gender;

    /**
     * 语言
     */
    private String language;

    /**
     * 用户所在国家
     */
    private String country;

    /**
     * 用户所在省份
     */
    private String province;

    /**
     * 用户所在城市
     */
    private String city;

    /**
     * 用户头像
     */
    private String avatarUrl;

    /**
     * openId
     */
    private String openId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * 微信会话秘钥
     */
    private String sessionKey;

    /**
     * 区号
     */
    private String countryCode;

    /**
     * 用户绑定的手机号（国外手机号会有区号）
     */
    private String phoneNumber;

    /**
     * 没有区号的手机号
     */
    private String purePhoneNumber;

    /**
     * 小米ID
     */
    private Long mid;

    /**
     * 添加时间(授权时间)
     */
    private Integer createdTime;

    /**
     * 更新时间	
     */
    private Integer updatedTime;

    /**
     * 绑定时间
     */
    private Integer bindTime;

    /**
     * 解绑时间
     */
    private Integer unbindTime;

    /**
     * 绑定状态[0:未绑定,1:绑定,] 
     */
    private String status;

    /**
     * 记录count计算结果
     */
    private Integer count;
}