package com.yeelight.service.user.client.dto;

import com.yeelight.service.framework.enums.UserVendorEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Arrays;

/**
 * @program: yeelight-service-user
 * @description: 用户信息传输对象
 * @author: Sheldon
 * @create: 2019-06-20 17:15
 **/
@Data
public class YeelightUserDto implements Serializable {
    /**
     * 用户ID
     */
    private Long id;

    /**
     * 登录账号
     */
    private String username;

    /**
     * 名称
     */
    private String name;

    /**
     * 密码
     */
    private String password;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 状态:  1-可用，0-禁用，-1-锁定
     */
    private String status;

    private String avatar;

    /**
     * 用户地域
     */
    private String region;

    /**
     * 获取当前对象所属的供应商编码。
     * 该方法通过遍历UserVendorEnum枚举，找到第一个其userId范围包含当前对象ID的枚举项，若无匹配项则返回默认供应商编码。
     *
     * @return 返回供应商的编码字符串。
     */
    public String getVendor() {
        // 根据当前对象的ID，在UserVendorEnum枚举中查找匹配的供应商，若无匹配则返回默认供应商
        UserVendorEnum userVendorEnum = Arrays.stream(UserVendorEnum.values()).filter(vendor -> vendor.userIdRange().contains(this.getId())).findFirst().orElse(UserVendorEnum.DEFAULT);
        return userVendorEnum.getCode();
    }

    /**
     * 获取当前对象所在区域的代码。
     * 此方法根据当前对象的ID，在UserVendorEnum中找到匹配的用户供应商枚举项，
     * 若找到则返回该枚举项所代表的区域代码，否则返回默认区域代码。
     *
     * @return 返回一个表示区域代码的字符串。
     */
    public String getRegion() {
        // 根据当前对象ID，筛选出匹配的用户供应商枚举项，若无匹配项则默认为UserVendorEnum.DEFAULT
        UserVendorEnum userVendorEnum = Arrays.stream(UserVendorEnum.values()).filter(vendor -> vendor.userIdRange().contains(this.getId())).findFirst().orElse(UserVendorEnum.DEFAULT);
        // 返回选中的用户供应商枚举项所代表的区域代码
        return userVendorEnum.getRegion().getCode();
    }
}
