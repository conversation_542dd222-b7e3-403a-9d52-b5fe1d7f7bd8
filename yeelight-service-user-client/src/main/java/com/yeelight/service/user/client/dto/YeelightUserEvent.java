package com.yeelight.service.user.client.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.*;

import java.io.Serializable;

/**
 * @program: yeelight-service-user
 * @description: 用户事件数据传输对象
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-18 17:25
 **/
@Data
@NoArgsConstructor
@Builder
public class YeelightUserEvent<T> implements Serializable {
    public static final String TOPIC_NAME = "yeelight.user.event";

    public static final String CONSUMER_GROUP_PREFIX = "YEELIGHT_USER_EVENT_CONSUMER_GROUP_";

    private EventTypeEnum eventType;

    private T payload;

    public YeelightUserEvent(EventTypeEnum eventType, T payload) {
        this.eventType = eventType;
        this.payload = payload;
    }

    public static <T> YeelightUserEvent<T> parse(String json, Class<T> clazz) {
        return JSON.parseObject(json, new TypeReference<YeelightUserEvent<T>>(clazz) {});
    }

    @Getter
    public enum EventTypeEnum implements Serializable {
        /**
         * 用户事件类型
         */
        CREATED,
        UPDATED,
        REMOVED,
        UPDATED_PASSWORD,
        UPDATED_USERNAME,
        UPDATED_PHONE_NUMBER,
        UPDATED_EMAIL,
        UPDATED_NAME,
        UPDATED_AVATAR,
        DISABLED,
        ENABLED,
        LOCKED,
        UNLOCKED
    }
}
