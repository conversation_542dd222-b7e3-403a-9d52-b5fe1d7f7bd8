package com.yeelight.service.user.client.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: yeelight-service-user
 * @description: 用户事件传输对象
 * @author: <PERSON>
 * @create: 2019-06-20 17:15
 **/
@Data
public class YeelightUserEventDto implements Serializable {
    /**
     * 用户ID
     */
    private Long id;

    /**
     * 登录账号
     */
    private String username;

    /**
     * 名称
     */
    private String name;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 状态:  1-可用，0-禁用，-1-锁定
     */
    private String status;

    private String avatar;

    /**
     * 用户地域
     */
    private String region;

    /**
     * 用户所示服务商
     */
    private String vendor;
}
