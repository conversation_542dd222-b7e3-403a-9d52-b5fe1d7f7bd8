package com.yeelight.service.user.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Yeelight用户扩展数据传输对象
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class YeelightUserExtendDto implements Serializable {

    private Long id;

    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 用户名字修改次数
     */
    private Integer usernameModifyNum;


    private String totpSecret;

}
