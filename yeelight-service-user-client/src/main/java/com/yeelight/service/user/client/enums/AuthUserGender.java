package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户性别
 * <AUTHOR>
 */
@Getter
public enum AuthUserGender implements BaseEnum<String> {
    // 1:男 0:女 -1:未知
    MALE("1", "男"),
    FEMALE("0", "女"),
    UNKNOWN("-1", "未知");

    private final String code;
    private final String desc;

    AuthUserGender(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
