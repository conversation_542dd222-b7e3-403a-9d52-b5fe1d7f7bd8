package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * @program: yeelight-service-c
 * @description: 小米账号绑定状态
 * @author: <PERSON>
 * @create: 2019-03-25 17:33
 **/
@Getter
@Deprecated
public enum BindStatus  implements BaseEnum<String> {
    /**
     * 已绑定
     */
    BIND("1", "已绑定"),
    /**
     * 未绑定
     */
    UNBIND("0", "未绑定");

    private final String code;
    private final String description;

    BindStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static BindStatus getStatusByCode(String code) {
        for (BindStatus bindStatus : BindStatus.values()) {
            if (bindStatus.getCode().equals(code)) {
                return bindStatus;
            }
        }
        return null;
    }
}
