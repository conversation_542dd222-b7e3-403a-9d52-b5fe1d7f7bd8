package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 用户删除状态枚举类
 */
@Getter
public enum UserDeleted  implements BaseEnum<String> {
    /**
     * 已删除
     */
    DELETED("1", "已删除"),
    /**
     * 未删除
     */
    UNDELETED("0", "未删除");

    private final String code;
    private final String description;

    UserDeleted(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserDeleted getStatusByCode(String code) {
        for (UserDeleted userDeleted : UserDeleted.values()) {
            if (userDeleted.getCode().equals(code)) {
                return userDeleted;
            }
        }
        return null;
    }
}
