package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户收入来源
 * <AUTHOR>
 */

@Getter
public enum UserEarningFrom  implements BaseEnum<String> {
    /**
     * 默认收入来源
     */
    SYSTEM("system", "系统"),
    /**
     * 设计师返佣
     */
    ORDER("order", "订单");

    private final String code;
    private final String description;

    UserEarningFrom(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserEarningFrom getTypesByCode(String code) {
        for (UserEarningFrom earningFrom : UserEarningFrom.values()) {
            if (earningFrom.getCode().equals(code)) {
                return earningFrom;
            }
        }
        return null;
    }
}
