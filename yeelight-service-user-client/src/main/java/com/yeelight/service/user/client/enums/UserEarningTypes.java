package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户收入类型
 * <AUTHOR>
 */
@Getter
public enum UserEarningTypes  implements BaseEnum<String> {
    /**
     * 默认收入
     */
    DEFAULT("default", "默认收入"),
    /**
     * 设计师返佣
     */
    DESIGNER_COMMISSION("designer_commission", "设计师返佣");

    private final String code;
    private final String description;

    UserEarningTypes(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserEarningTypes getTypesByCode(String code) {
        for (UserEarningTypes earningType : UserEarningTypes.values()) {
            if (earningType.getCode().equals(code)) {
                return earningType;
            }
        }
        return null;
    }
}
