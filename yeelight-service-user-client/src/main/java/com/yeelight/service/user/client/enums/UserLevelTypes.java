package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户等级类型
 * <AUTHOR>
 */
@Getter
public enum UserLevelTypes  implements BaseEnum<String> {
    /**
     * Yeelight等级
     */
    YEELIGHT("yeelight", "等级"),
    /**
     * 设计师等级
     */
    DESIGNER("designer", "设计师等级");

    private final String code;
    private final String description;

    UserLevelTypes(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserLevelTypes getTypesByCode(String code) {
        for (UserLevelTypes levelType : UserLevelTypes.values()) {
            if (levelType.getCode().equals(code)) {
                return levelType;
            }
        }
        return null;
    }
}
