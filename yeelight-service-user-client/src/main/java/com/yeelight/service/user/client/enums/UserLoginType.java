package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * The login type is determined based on the authentication in this object.
 * If the authentication is not null and is authenticated, it returns the type of the authentication.
 * If the authentication type is not recognized, it returns the simple name of the authentication class.
 * <p>
 * The possible login types are:
 * - "用户名密码登录" for UsernamePasswordAuthenticationToken
 * - "邮箱验证码登录" for EmailCodeAuthenticationToken
 * - "Google令牌登录" for GoogleCodeAuthenticationToken
 * - "手机验证码登录" for SmsAuthenticationToken
 * - "三方登录" for JustAuthAuthenticationToken
 * - "OAuth登录" for OAuth2Authentication
 * - "RememberMe登录" for RememberMeAuthenticationToken
 * - "PreAuthenticated登录" for PreAuthenticatedAuthenticationToken
 * - "匿名登录" for AnonymousAuthenticationToken
 * <AUTHOR>
 */

@Getter
public enum UserLoginType implements BaseEnum<String> {
    /**
     * 用户登录类型
     */
    UsernamePassword("UsernamePasswordAuthenticationToken", "用户名密码登录"),
    EmailCode("EmailCodeAuthenticationToken", "邮箱验证码登录"),
    GoogleCode("GoogleCodeAuthenticationToken", "Google令牌登录"),
    Sms("SmsAuthenticationToken", "手机验证码登录"),
    JustAuth("JustAuthAuthenticationToken", "三方登录"),
    OAuth2("OAuth2Authentication", "OAuth登录"),
    RememberMe("RememberMeAuthenticationToken", "RememberMe登录"),
    PreAuthenticated("PreAuthenticatedAuthenticationToken", "PreAuthenticated登录"),
    Anonymous("AnonymousAuthenticationToken", "匿名登录"),
    Other("Other", "其他登录")
    ;

    private final String code;
    private final String description;

    UserLoginType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserLoginType getTypeByCode(String code) {
        for (UserLoginType userLoginType : UserLoginType.values()) {
            if (userLoginType.getCode().equals(code)) {
                return userLoginType;
            }
        }
        return Other;
    }
}
