package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户社交来源
 * <AUTHOR>
 */
@Getter
public enum UserSocialSource implements BaseEnum<String> {
    /**
     * 用户社交来源
     */
    GOOGLE("GOOGLE", "Google"),
    AMAZON("AMAZON", "Amazon"),
    APPLE("APPLE", "Apple"),
    XIAOMI("XIAOM<PERSON>", "小米"),
    WECHAT_MP("WECHAT_MP", "微信"),
    WECHAT_MINI_SHOP("WECHAT_MINI_SHOP", "微信商城小程序"),
    WECHAT_MINI_LINK("WECHAT_MINI_LINK", "微信控制小程序"),
    CHINA_TELECOM("CHINA_TELECOM", "中国电信"),
    ;

    private final String code;
    private final String description;

    UserSocialSource(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserSocialSource getSourceByCode(String code) {
        for (UserSocialSource source : UserSocialSource.values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }
}
