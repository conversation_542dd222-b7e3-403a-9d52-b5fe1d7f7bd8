package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 用户状态枚举类
 * @date: 2020/5/28 10:52
 */
@Getter
public enum UserStatus  implements BaseEnum<String> {
    /**
     * 用户状态
     */
    ENABLED("1", "正常"),
    DISABLED("0", "禁用"),
    LOCKED("-1", "锁定");

    private final String code;
    private final String description;

    UserStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserStatus getStatusByCode(String code) {
        for (UserStatus userStatus : UserStatus.values()) {
            if (userStatus.getCode().equals(code)) {
                return userStatus;
            }
        }
        return null;
    }
}
