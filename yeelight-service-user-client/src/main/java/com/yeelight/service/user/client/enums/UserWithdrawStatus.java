package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户提现状态
 * <AUTHOR>
 */
@Getter
public enum UserWithdrawStatus  implements BaseEnum<Integer> {
    /**
     * 待审核
     */
    待审核(0, "待审核"),
    /**
     * 审核通过
     */
    审核通过(1, "审核通过"),
    /**
     * 拒绝提现
     */
    拒绝提现(-1, "拒绝提现");

    private final Integer code;
    private final String description;

    UserWithdrawStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserWithdrawStatus getStatusByCode(Integer code) {
        for (UserWithdrawStatus withdrawStatus : UserWithdrawStatus.values()) {
            if (withdrawStatus.getCode().equals(code)) {
                return withdrawStatus;
            }
        }
        return null;
    }
}
