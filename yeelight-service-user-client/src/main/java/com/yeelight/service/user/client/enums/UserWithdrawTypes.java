package com.yeelight.service.user.client.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * 用户提现类型
 * <AUTHOR>
 */
@Getter
public enum UserWithdrawTypes  implements BaseEnum<String> {
    /**
     * 默认提现
     */
    DEFAULT("default", "默认提现"),
    /**
     * 设计师提现
     */
    DESIGNER("designer", "设计师提现");

    private final String code;
    private final String description;

    UserWithdrawTypes(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static UserWithdrawTypes getTypesByCode(String code) {
        for (UserWithdrawTypes withdrawType : UserWithdrawTypes.values()) {
            if (withdrawType.getCode().equals(code)) {
                return withdrawType;
            }
        }
        return null;
    }
}
