package com.yeelight.service.user.client.filter;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonObject;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.utils.AuthUtils;
import com.yeelight.service.user.client.utils.YeelightUserContextUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.io.IOException;
import java.util.Map;

/**
 * @program: yeelight-c-api
 * @description: OauthFilter 过滤器
 * @author: Sheldon
 * @create: 2019-07-18 11:23
 **/
public class OauthFilter implements Filter {
    private String applicationName;

    public static final String ID_KEY = "id";
    public static final String NAME_KEY = "name";
    public static final String PHONE_NUMBER_KEY = "phoneNumber";
    public static final String STATUS_KEY = "status";
    public static final String REGION_KEY = "region";
    public static final String EMAIL_KEY = "email";
    public static final String USERNAME_KEY = "username";

    @Value("${spring.application.name}")
    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    @Override
    public void init(FilterConfig filterConfig) {

    }

    /**
     * 对请求进行过滤，实现OAuth2认证的处理逻辑。
     *
     * @param request  ServletRequest，表示客户端的请求。
     * @param response ServletResponse，表示对客户端的响应。
     * @param chain    FilterChain，提供了一个方法，以将请求传递给过滤链中的下一个过滤器。
     * @throws IOException 如果发生输入/输出错误。
     * @throws ServletException 如果过滤过程中触发了Servlet相关的异常。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse resp = (HttpServletResponse) response;

            // 从请求头中获取OAuth2认证信息
            String authenticationStr = req.getHeader("OAuth2AuthenticationDto");
            String clientId = req.getHeader("clientId");
            YeelightUserContextUtils.setClientId(clientId);
            // 如果认证信息不为空，则进行认证处理
            if (StringUtils.isNotBlank(authenticationStr)) {
                // 使用Gson对认证信息进行解析
                Gson gson = new GsonBuilder()
                        // 注册自定义的GrantedAuthority和Authentication反序列化器
                        .registerTypeAdapter(GrantedAuthority.class, (JsonDeserializer<GrantedAuthority>) (json, typeOfT, context) -> {
                            JsonObject jsonObject = json.getAsJsonObject();
                            return context.<CustomGrantedAuthority>deserialize(jsonObject, CustomGrantedAuthority.class);
                        }).registerTypeAdapter(Authentication.class, (JsonDeserializer<Authentication>) (json, typeOfT, context) -> {
                            JsonObject jsonObject = json.getAsJsonObject();
                            return context.<YeelightOAuth2Authentication>deserialize(jsonObject, YeelightOAuth2Authentication.class);
                        }).
                                create();

                // 解析认证信息
                OAuth2AuthenticationDto authenticationDto = gson.fromJson(authenticationStr, OAuth2AuthenticationDto.class);
                // 检查资源id是否合法
                if (!authenticationDto.getStoredRequest().getResourceIds().isEmpty() && !authenticationDto.getStoredRequest().getResourceIds().contains(applicationName)) {
                    // 如果资源id不合法，则进行认证失败处理
                    AuthUtils.authFail(resp);
                    return;
                }
                //clientId校验
                if (StringUtils.isNotEmpty(clientId) && !clientId.equals(authenticationDto.getStoredRequest().getClientId())) {
                    AuthUtils.authFail(resp);
                    return;
                }
                // TODO: 验证scope和authorities

                // 处理用户信息，并设置到上下文中
                Object principal = authenticationDto.getPrincipal();
                if (principal instanceof Map) {
                    // 如果用户信息是Map类型，则进行解析
                    Map principalMap = (Map) principal;
                    YeelightUser user = new YeelightUser();
                    // 从用户信息中提取用户属性，并设置到用户对象中
                    user.setId(((Double) principalMap.get(ID_KEY)).longValue());
                    user.setName((String) principalMap.get(NAME_KEY));
                    user.setPhoneNumber((String) principalMap.get(PHONE_NUMBER_KEY));
                    user.setStatus((String) principalMap.get(STATUS_KEY));
                    user.setRegion((String) principalMap.get(REGION_KEY));
                    user.setEmail((String) principalMap.get(EMAIL_KEY));
                    user.setUsername((String) principalMap.get(USERNAME_KEY));
                    // 设置用户到上下文中
                    YeelightUserContextUtils.setYeelightUser(user);
                } else {
                    // 如果没有有效的用户信息，则清除上下文中的用户信息
                    YeelightUserContextUtils.setYeelightUser(null);
                }


            }
            // 继续传递请求到下一个过滤器，或至目标资源
            chain.doFilter(request, response);
        } finally {
            // 过滤器销毁时的处理逻辑
            destroy();
        }
    }

    @Override
    public void destroy() {
        YeelightUserContextUtils.setYeelightUser(null);
        YeelightUserContextUtils.setClientId(null);
    }
}
