package com.yeelight.service.user.client.filter;

import com.yeelight.service.user.client.domain.ProxyInfo;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.service.TokenProxyService;
import com.yeelight.service.user.client.utils.AuthUtils;
import com.yeelight.service.user.client.utils.YeelightUserContextUtils;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * oauth代理拦截器
 *
 * @author: 齐观毅
 * @create: 2019-07-18 11:23
 **/
public class OauthProxyFilter implements Filter {
    private String applicationName;

    public static final String ID_KEY = "id";
    public static final String NAME_KEY = "name";
    public static final String PHONE_NUMBER_KEY = "phoneNumber";
    public static final String STATUS_KEY = "status";
    public static final String REGION_KEY = "region";
    public static final String EMAIL_KEY = "email";
    public static final String USERNAME_KEY = "username";

    @Setter
    private TokenProxyService proxyService;

    @Value("${spring.application.name}")
    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }


    @Override
    public void init(FilterConfig filterConfig) {

    }

    /**
     * 对请求进行过滤，实现身份验证和授权流程。
     * 如果请求头中"yp"字段指示为代理请求，则验证用户代理权限，通过则进行身份信息的更新。
     *
     * @param request  Servlet请求对象，包含客户端发起的请求信息。
     * @param response Servlet响应对象，用于向客户端发送响应。
     * @param chain    过滤器链，用于将请求传递给下一个过滤器或最终的Servlet。
     * @throws IOException 如果发生输入/输出错误。
     * @throws ServletException 如果过滤过程中触发了Servlet相关的异常。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            // 将ServletRequest和ServletResponse转换为HttpServletRequest和HttpServletResponse
            HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse resp = (HttpServletResponse) response;

            // 从请求头中获取"yp"字段，判断是否为代理请求
            String yp = req.getHeader("yp");
            boolean proxy = "true".equalsIgnoreCase(yp);
            if (!proxy) {
                // 如果不是代理请求，则直接通过
                chain.doFilter(request, response);
                return;
            }

            // 验证用户代理权限
            YeelightUser authYeelightUser = YeelightUserContextUtils.getYeelightUserOrNull();
            if (authYeelightUser == null) {
                AuthUtils.authFail(resp);
                return;
            }

            // 获取代理信息
            ProxyInfo proxyInfo = proxyService.getProxyInfo(authYeelightUser.getId());
            if (proxyInfo == null) {
                AuthUtils.authFail(resp);
                return;
            }

            // 检查用户是否有权限访问当前应用
            if (!proxyInfo.getApplyApplicationList().contains(applicationName)) {
                chain.doFilter(request, response);
                return;
            }

            // 获取用户的OAuth2认证信息
            OAuth2AuthenticationDto authentication = proxyService.getAuthentication(proxyInfo.getToken());
            if (authentication == null) {
                AuthUtils.authFail(resp);
                return;
            }

            // 设置代理用户信息
            YeelightUserContextUtils.setProxyYeelightUser(authYeelightUser);

            // 处理和设置用户身份信息
            Object principal = authentication.getPrincipal();
            if (principal instanceof Map) {
                // 如果身份信息是Map格式，从中提取用户信息并设置
                Map principalMap = (Map) principal;
                YeelightUser user = new YeelightUser();
                user.setId(((Double) principalMap.get(ID_KEY)).longValue());
                user.setName((String) principalMap.get(NAME_KEY));
                user.setPhoneNumber((String) principalMap.get(PHONE_NUMBER_KEY));
                user.setStatus((String) principalMap.get(STATUS_KEY));
                user.setRegion((String) principalMap.get(REGION_KEY));
                user.setEmail((String) principalMap.get(EMAIL_KEY));
                user.setUsername((String) principalMap.get(USERNAME_KEY));
                YeelightUserContextUtils.setYeelightUser(user);
            } else if (principal instanceof YeelightUser) {
                // 如果身份信息已是YeelightUser实例，直接设置
                YeelightUserContextUtils.setYeelightUser((YeelightUser) principal);
            } else {
                // 如果身份信息格式不符合预期，清除用户信息
                YeelightUserContextUtils.setYeelightUser(null);
            }

            // 继续过滤链
            chain.doFilter(request, response);
        } finally {
            // 过滤器销毁时的资源释放或清理工作
            destroy();
        }
    }

    /**
     * 销毁当前用户会话。
     * 该方法重写了destroy方法，主要进行用户会话的销毁操作，包括设置代理用户和清除当前用户。
     * 此操作不会返回任何值。
     */
    @Override
    public void destroy() {
        // 设置当前用户为代理用户，代理用户用于后续会话恢复或其他需要用户身份的操作
        YeelightUserContextUtils.setYeelightUser(YeelightUserContextUtils.getProxyYeelightUserOrNull());
        // 清除代理用户，以确保会话安全性和单例用户的唯一性
        YeelightUserContextUtils.setProxyYeelightUser(null);
    }
}
