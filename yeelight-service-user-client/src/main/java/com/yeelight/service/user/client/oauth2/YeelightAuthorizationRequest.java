package com.yeelight.service.user.client.oauth2;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * Yeelight自建AuthorizationRequest
 * 完全兼容org.springframework.security.oauth2.provider.AuthorizationRequest
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YeelightAuthorizationRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求参数
     */
    @Builder.Default
    private Map<String, String> requestParameters = new HashMap<>();

    /**
     * 授权范围
     */
    @Builder.Default
    private Set<String> scope = new HashSet<>();

    /**
     * 授权类型集合
     */
    @Builder.Default
    private Set<String> authorities = new HashSet<>();

    /**
     * 是否已批准
     */
    @Builder.Default
    private boolean approved = false;

    /**
     * 资源ID集合
     */
    @Builder.Default
    private Set<String> resourceIds = new HashSet<>();

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 响应类型集合
     */
    @Builder.Default
    private Set<String> responseTypes = new HashSet<>();

    /**
     * 状态参数
     */
    private String state;

    /**
     * 扩展信息
     */
    @Builder.Default
    private Map<String, Serializable> extensions = new HashMap<>();

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * 获取单个响应类型
     * 兼容旧版AuthorizationRequest.getResponseType()方法
     */
    public String getResponseType() {
        return responseTypes.isEmpty() ? null : responseTypes.iterator().next();
    }

    /**
     * 设置单个响应类型
     * 兼容旧版AuthorizationRequest.setResponseType()方法
     */
    public void setResponseType(String responseType) {
        this.responseTypes = responseType != null ? 
            Set.of(responseType) : new HashSet<>();
    }

    /**
     * 获取单个授权范围字符串
     * 兼容旧版AuthorizationRequest.getScope()方法
     */
    public String getScopeString() {
        return scope.isEmpty() ? null : String.join(" ", scope);
    }

    /**
     * 设置授权范围字符串
     * 兼容旧版AuthorizationRequest.setScope()方法
     */
    public void setScopeString(String scopeString) {
        if (scopeString != null && !scopeString.trim().isEmpty()) {
            this.scope = new HashSet<>(Arrays.asList(scopeString.trim().split("\\s+")));
        } else {
            this.scope = new HashSet<>();
        }
    }

    /**
     * 创建OAuth2Request对象
     * 兼容旧版AuthorizationRequest.createOAuth2Request()方法
     */
    public YeelightOAuth2Request createOAuth2Request() {
        return YeelightOAuth2Request.builder()
                .clientId(this.clientId)
                .requestParameters(new HashMap<>(this.requestParameters))
                .scope(new HashSet<>(this.scope))
                .authorities(new HashSet<>(this.authorities))
                .approved(this.approved)
                .resourceIds(new HashSet<>(this.resourceIds))
                .redirectUri(this.redirectUri)
                .responseTypes(new HashSet<>(this.responseTypes))
                .extensions(new HashMap<>(this.extensions))
                .grantType(this.grantType)
                .build();
    }

    /**
     * 从请求参数创建AuthorizationRequest
     * 兼容旧版AuthorizationRequest构造方法
     */
    public static YeelightAuthorizationRequest fromParameters(Map<String, String> parameters) {
        YeelightAuthorizationRequestBuilder builder = YeelightAuthorizationRequest.builder()
                .requestParameters(new HashMap<>(parameters))
                .clientId(parameters.get("client_id"))
                .redirectUri(parameters.get("redirect_uri"))
                .state(parameters.get("state"))
                .grantType(parameters.get("grant_type"));

        // 处理响应类型
        String responseType = parameters.get("response_type");
        if (responseType != null) {
            builder.responseTypes(Set.of(responseType));
        }

        // 处理授权范围
        String scope = parameters.get("scope");
        if (scope != null && !scope.trim().isEmpty()) {
            builder.scope(new HashSet<>(Arrays.asList(scope.trim().split("\\s+"))));
        }

        return builder.build();
    }

    /**
     * 检查是否为隐式授权类型
     */
    public boolean isImplicit() {
        return responseTypes.contains("token");
    }

    /**
     * 检查是否为授权码模式
     */
    public boolean isAuthorizationCode() {
        return responseTypes.contains("code");
    }

    /**
     * 添加扩展信息
     */
    public void addExtension(String key, Serializable value) {
        if (this.extensions == null) {
            this.extensions = new HashMap<>(0);
        }
        this.extensions.put(key, value);
    }

    /**
     * 获取扩展信息
     */
    public Serializable getExtension(String key) {
        return this.extensions != null ? this.extensions.get(key) : null;
    }

    /**
     * 检查是否包含指定的授权范围
     */
    public boolean hasScope(String scope) {
        return this.scope != null && this.scope.contains(scope);
    }

    /**
     * 检查是否包含指定的资源ID
     */
    public boolean hasResourceId(String resourceId) {
        return this.resourceIds != null && this.resourceIds.contains(resourceId);
    }

    /**
     * 合并另一个AuthorizationRequest的信息
     */
    public void merge(YeelightAuthorizationRequest other) {
        if (other == null) {
            return;
        }

        if (other.scope != null) {
            if (this.scope == null) {
                this.scope = new HashSet<>();
            }
            this.scope.addAll(other.scope);
        }

        if (other.authorities != null) {
            if (this.authorities == null) {
                this.authorities = new HashSet<>();
            }
            this.authorities.addAll(other.authorities);
        }

        if (other.resourceIds != null) {
            if (this.resourceIds == null) {
                this.resourceIds = new HashSet<>();
            }
            this.resourceIds.addAll(other.resourceIds);
        }

        if (other.responseTypes != null) {
            if (this.responseTypes == null) {
                this.responseTypes = new HashSet<>();
            }
            this.responseTypes.addAll(other.responseTypes);
        }

        if (other.extensions != null) {
            if (this.extensions == null) {
                this.extensions = new HashMap<>();
            }
            this.extensions.putAll(other.extensions);
        }

        if (other.requestParameters != null) {
            if (this.requestParameters == null) {
                this.requestParameters = new HashMap<>();
            }
            this.requestParameters.putAll(other.requestParameters);
        }
    }

    /**
     * 复制当前对象
     */
    public YeelightAuthorizationRequest copy() {
        return YeelightAuthorizationRequest.builder()
                .clientId(this.clientId)
                .requestParameters(this.requestParameters != null ? new HashMap<>(this.requestParameters) : new HashMap<>(0))
                .scope(this.scope != null ? new HashSet<>(this.scope) : new HashSet<>())
                .authorities(this.authorities != null ? new HashSet<>(this.authorities) : new HashSet<>())
                .approved(this.approved)
                .resourceIds(this.resourceIds != null ? new HashSet<>(this.resourceIds) : new HashSet<>())
                .redirectUri(this.redirectUri)
                .responseTypes(this.responseTypes != null ? new HashSet<>(this.responseTypes) : new HashSet<>())
                .state(this.state)
                .extensions(this.extensions != null ? new HashMap<>(this.extensions) : new HashMap<>(0))
                .grantType(this.grantType)
                .build();
    }
}
