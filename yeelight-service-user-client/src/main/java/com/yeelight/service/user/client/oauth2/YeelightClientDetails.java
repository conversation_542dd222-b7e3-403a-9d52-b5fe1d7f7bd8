package com.yeelight.service.user.client.oauth2;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Yeelight自建ClientDetails接口
 * 完全兼容org.springframework.security.oauth2.provider.ClientDetails
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
public interface YeelightClientDetails {

    /**
     * 获取客户端ID
     * @return 客户端ID
     */
    String getClientId();

    /**
     * 获取资源ID集合
     * @return 资源ID集合
     */
    Set<String> getResourceIds();

    /**
     * 是否需要客户端密钥
     * @return true如果需要密钥
     */
    boolean isSecretRequired();

    /**
     * 获取客户端密钥
     * @return 客户端密钥
     */
    String getClientSecret();

    /**
     * 是否有范围限制
     * @return true如果有范围限制
     */
    boolean isScoped();

    /**
     * 获取授权范围
     * @return 授权范围集合
     */
    Set<String> getScope();

    /**
     * 获取授权类型集合
     * @return 授权类型集合
     */
    Set<String> getAuthorizedGrantTypes();

    /**
     * 获取注册的重定向URI集合
     * @return 重定向URI集合
     */
    Set<String> getRegisteredRedirectUri();

    /**
     * 获取权限集合
     * @return 权限集合
     */
    Collection<GrantedAuthority> getAuthorities();

    /**
     * 获取访问令牌有效期（秒）
     * @return 访问令牌有效期
     */
    Integer getAccessTokenValiditySeconds();

    /**
     * 获取刷新令牌有效期（秒）
     * @return 刷新令牌有效期
     */
    Integer getRefreshTokenValiditySeconds();

    /**
     * 是否自动批准指定范围
     * @param scope 范围
     * @return true如果自动批准
     */
    boolean isAutoApprove(String scope);

    /**
     * 获取附加信息
     * @return 附加信息映射
     */
    Map<String, Object> getAdditionalInformation();
}
