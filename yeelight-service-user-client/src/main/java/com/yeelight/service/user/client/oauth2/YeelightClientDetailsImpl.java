package com.yeelight.service.user.client.oauth2;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.security.core.GrantedAuthority;

import java.util.*;

/**
 * Yeelight自建ClientDetails实现类
 * 完全兼容org.springframework.security.oauth2.provider.ClientDetails
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 *
 * <AUTHOR>
 * @since 2025-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YeelightClientDetailsImpl implements YeelightClientDetails {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 资源ID集合
     */
    @Builder.Default
    private Set<String> resourceIds = new HashSet<>();

    /**
     * 客户端密钥
     */
    private String clientSecret;

    /**
     * 授权范围
     */
    @Builder.Default
    private Set<String> scope = new HashSet<>();

    /**
     * 授权类型集合
     */
    @Builder.Default
    private Set<String> authorizedGrantTypes = new HashSet<>();

    /**
     * 重定向URI集合
     */
    @Builder.Default
    private Set<String> registeredRedirectUri = new HashSet<>();

    /**
     * 权限集合
     */
    @Builder.Default
    private Collection<GrantedAuthority> authorities = new ArrayList<>();

    /**
     * 访问令牌有效期（秒）
     */
    private Integer accessTokenValiditySeconds;

    /**
     * 刷新令牌有效期（秒）
     */
    private Integer refreshTokenValiditySeconds;

    /**
     * 附加信息
     */
    @Builder.Default
    private Map<String, Object> additionalInformation = new HashMap<>();

    /**
     * 是否自动批准
     */
    private boolean autoApprove;



    /**
     * 是否需要客户端密钥
     */
    @Override
    public boolean isSecretRequired() {
        return clientSecret != null && !clientSecret.isEmpty();
    }

    /**
     * 是否有范围限制
     */
    @Override
    public boolean isScoped() {
        return scope != null && !scope.isEmpty();
    }

    /**
     * 是否自动批准指定范围
     */
    @Override
    public boolean isAutoApprove(String scope) {
        return autoApprove;
    }
}
