package com.yeelight.service.user.client.oauth2;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * Yeelight自建OAuth2AccessToken
 * 完全兼容org.springframework.security.oauth2.common.OAuth2AccessToken
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Data
public class YeelightOAuth2AccessToken implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 访问令牌类型：Bearer
     */
    public static final String BEARER_TYPE = "Bearer";

    /**
     * OAuth2访问令牌类型：oauth2_access_token
     */
    public static final String OAUTH2_TYPE = "oauth2_access_token";

    /**
     * The access token issued by the authorization server. This value is REQUIRED.
     */
    public static String ACCESS_TOKEN = "access_token";

    /**
     * The type of the token issued as described in <a
     * href="http://tools.ietf.org/html/draft-ietf-oauth-v2-22#section-7.1">Section 7.1</a>. Value is case insensitive.
     * This value is REQUIRED.
     */
    public static String TOKEN_TYPE = "token_type";

    /**
     * The lifetime in seconds of the access token. For example, the value "3600" denotes that the access token will
     * expire in one hour from the time the response was generated. This value is OPTIONAL.
     */
    public static String EXPIRES_IN = "expires_in";

    /**
     * The refresh token which can be used to obtain new access tokens using the same authorization grant as described
     * in <a href="http://tools.ietf.org/html/draft-ietf-oauth-v2-22#section-6">Section 6</a>. This value is OPTIONAL.
     */
    public static String REFRESH_TOKEN = "refresh_token";

    /**
     * The scope of the access token as described by <a
     * href="http://tools.ietf.org/html/draft-ietf-oauth-v2-22#section-3.3">Section 3.3</a>
     */
    public static String SCOPE = "scope";

    /**
     * 访问令牌值
     */
    private String value;

    /**
     * 令牌类型（通常是"Bearer"）
     */
    private String tokenType = BEARER_TYPE;

    /**
     * 刷新令牌
     */
    private YeelightOAuth2RefreshToken refreshToken;

    /**
     * 过期时间
     */
    private Date expiration;

    /**
     * 授权范围
     */
    private Set<String> scope;

    /**
     * 附加信息
     */
    private Map<String, Object> additionalInformation;

    /**
     * 默认构造函数
     */
    public YeelightOAuth2AccessToken() {
    }

    /**
     * 构造函数
     * @param value 令牌值
     */
    public YeelightOAuth2AccessToken(String value) {
        this.value = value;
    }

    /**
     * 获取过期时间（秒）
     * @return 过期时间秒数，如果没有过期时间返回null
     */
    public Integer getExpiresIn() {
        if (expiration != null) {
            long now = System.currentTimeMillis();
            long expirationTime = expiration.getTime();
            if (expirationTime > now) {
                return (int) ((expirationTime - now) / 1000);
            } else {
                // 已过期
                return 0;
            }
        }
        return null;
    }

    /**
     * 检查令牌是否已过期
     * @return true如果已过期
     */
    public boolean isExpired() {
        if (expiration != null) {
            return expiration.before(new Date());
        }
        return false;
    }

    /**
     * 从旧版OAuth2AccessToken创建（兼容性构造函数）
     */
    @Deprecated
    public static YeelightOAuth2AccessToken fromLegacyOAuth2AccessToken(Object legacyToken) {
        if (legacyToken == null) {
            return null;
        }
        
        try {
            YeelightOAuth2AccessToken token = new YeelightOAuth2AccessToken();
            
            // 使用反射获取字段值
            token.value = getFieldValue(legacyToken, "value", String.class);
            token.tokenType = getFieldValue(legacyToken, "tokenType", String.class);
            token.expiration = getFieldValue(legacyToken, "expiration", Date.class);
            token.scope = getFieldValue(legacyToken, "scope", Set.class);
            token.additionalInformation = getFieldValue(legacyToken, "additionalInformation", Map.class);
            
            // 处理refreshToken
            Object legacyRefreshToken = getFieldValue(legacyToken, "refreshToken", Object.class);
            if (legacyRefreshToken != null) {
                token.refreshToken = YeelightOAuth2RefreshToken.fromLegacyOAuth2RefreshToken(legacyRefreshToken);
            }
            
            return token;
        } catch (Exception e) {
            return new YeelightOAuth2AccessToken();
        }
    }

    /**
     * 反射工具方法
     */
    private static <T> T getFieldValue(Object obj, String fieldName, Class<T> type) {
        try {
            Class<?> clazz = obj.getClass();
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return type.cast(value);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建器模式
     */
    public static class Builder {
        private String value;
        private String tokenType = BEARER_TYPE;
        private YeelightOAuth2RefreshToken refreshToken;
        private Date expiration;
        private Set<String> scope;
        private Map<String, Object> additionalInformation;

        public Builder value(String value) {
            this.value = value;
            return this;
        }

        public Builder tokenType(String tokenType) {
            this.tokenType = tokenType;
            return this;
        }

        public Builder refreshToken(YeelightOAuth2RefreshToken refreshToken) {
            this.refreshToken = refreshToken;
            return this;
        }

        public Builder expiration(Date expiration) {
            this.expiration = expiration;
            return this;
        }

        public Builder scope(Set<String> scope) {
            this.scope = scope;
            return this;
        }

        public Builder additionalInformation(Map<String, Object> additionalInformation) {
            this.additionalInformation = additionalInformation;
            return this;
        }

        public YeelightOAuth2AccessToken build() {
            YeelightOAuth2AccessToken token = new YeelightOAuth2AccessToken();
            token.value = this.value;
            token.tokenType = this.tokenType;
            token.refreshToken = this.refreshToken;
            token.expiration = this.expiration;
            token.scope = this.scope;
            token.additionalInformation = this.additionalInformation;
            return token;
        }
    }
}
