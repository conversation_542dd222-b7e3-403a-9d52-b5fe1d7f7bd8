package com.yeelight.service.user.client.oauth2;

import lombok.Data;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collection;

/**
 * Yeelight自建OAuth2Authentication
 * 完全兼容org.springframework.security.oauth2.provider.OAuth2Authentication
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Data
public class YeelightOAuth2Authentication implements Authentication, Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * OAuth2请求信息
     */
    private YeelightOAuth2Request oAuth2Request;

    /**
     * 用户认证信息
     */
    private Authentication userAuthentication;

    /**
     * 是否已认证
     */
    private boolean authenticated = true;

    /**
     * 认证对象详情
     */
    private Object details;

    /**
     * 默认构造函数
     */
    public YeelightOAuth2Authentication() {
    }

    /**
     * 构造函数
     * @param oAuth2Request OAuth2请求信息
     * @param userAuthentication 用户认证信息
     */
    public YeelightOAuth2Authentication(YeelightOAuth2Request oAuth2Request, Authentication userAuthentication) {
        this.oAuth2Request = oAuth2Request;
        this.userAuthentication = userAuthentication;
    }

    /**
     * 是否为客户端模式（没有用户认证）
     * @return true如果是客户端模式
     */
    public boolean isClientOnly() {
        return userAuthentication == null;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (userAuthentication != null && userAuthentication.getAuthorities() != null) {
            return userAuthentication.getAuthorities();
        }
        return null;
    }

    @Override
    public Object getCredentials() {
        return "";
    }

    @Override
    public Object getDetails() {
        return userAuthentication != null ? userAuthentication.getDetails() : null;
    }

    @Override
    public Object getPrincipal() {
        return userAuthentication != null ? userAuthentication.getPrincipal() : oAuth2Request.getClientId();
    }

    @Override
    public boolean isAuthenticated() {
        return authenticated;
    }

    @Override
    public void setAuthenticated(boolean authenticated) throws IllegalArgumentException {
        this.authenticated = authenticated;
    }

    @Override
    public String getName() {
        if (userAuthentication != null && userAuthentication.getName() != null) {
            return userAuthentication.getName();
        }
        return oAuth2Request != null ? oAuth2Request.getClientId() : null;
    }

    /**
     * 获取用户主体名称
     * @return 用户主体名称
     */
    public String getUserAuthentication() {
        return userAuthentication != null ? userAuthentication.getName() : null;
    }

    /**
     * 从旧版OAuth2Authentication创建（兼容性构造函数）
     */
    @Deprecated
    public static YeelightOAuth2Authentication fromLegacyOAuth2Authentication(Object legacyAuth) {
        if (legacyAuth == null) {
            return null;
        }
        
        try {
            YeelightOAuth2Authentication auth = new YeelightOAuth2Authentication();
            
            // 使用反射获取字段值
            Object legacyOAuth2Request = getFieldValue(legacyAuth, "oAuth2Request", Object.class);
            if (legacyOAuth2Request != null) {
                auth.oAuth2Request = YeelightOAuth2Request.fromLegacyOAuth2Request(legacyOAuth2Request);
            }
            
            auth.userAuthentication = getFieldValue(legacyAuth, "userAuthentication", Authentication.class);
            
            Boolean authenticated = getFieldValue(legacyAuth, "authenticated", Boolean.class);
            if (authenticated != null) {
                auth.authenticated = authenticated;
            }
            
            return auth;
        } catch (Exception e) {
            return new YeelightOAuth2Authentication();
        }
    }

    /**
     * 反射工具方法
     */
    private static <T> T getFieldValue(Object obj, String fieldName, Class<T> type) {
        try {
            Class<?> clazz = obj.getClass();
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return type.cast(value);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建器模式
     */
    public static class Builder {
        private YeelightOAuth2Request oAuth2Request;
        private Authentication userAuthentication;
        private boolean authenticated = true;

        public Builder oAuth2Request(YeelightOAuth2Request oAuth2Request) {
            this.oAuth2Request = oAuth2Request;
            return this;
        }

        public Builder userAuthentication(Authentication userAuthentication) {
            this.userAuthentication = userAuthentication;
            return this;
        }

        public Builder authenticated(boolean authenticated) {
            this.authenticated = authenticated;
            return this;
        }

        public YeelightOAuth2Authentication build() {
            YeelightOAuth2Authentication auth = new YeelightOAuth2Authentication();
            auth.oAuth2Request = this.oAuth2Request;
            auth.userAuthentication = this.userAuthentication;
            auth.authenticated = this.authenticated;
            return auth;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        YeelightOAuth2Authentication that = (YeelightOAuth2Authentication) obj;
        
        if (oAuth2Request != null ? !oAuth2Request.equals(that.oAuth2Request) : that.oAuth2Request != null) {
            return false;
        }
        return userAuthentication != null ? userAuthentication.equals(that.userAuthentication) : that.userAuthentication == null;
    }

    @Override
    public int hashCode() {
        int result = oAuth2Request != null ? oAuth2Request.hashCode() : 0;
        result = 31 * result + (userAuthentication != null ? userAuthentication.hashCode() : 0);
        return result;
    }
}
