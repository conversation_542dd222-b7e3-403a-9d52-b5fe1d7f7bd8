package com.yeelight.service.user.client.oauth2;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Yeelight自建OAuth2RefreshToken
 * 完全兼容org.springframework.security.oauth2.common.OAuth2RefreshToken
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Data
public class YeelightOAuth2RefreshToken implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 刷新令牌值
     */
    private String value;

    /**
     * 过期时间（可选）
     */
    private Date expiration;

    /**
     * 默认构造函数
     */
    public YeelightOAuth2RefreshToken() {
    }

    /**
     * 构造函数
     * @param value 刷新令牌值
     */
    public YeelightOAuth2RefreshToken(String value) {
        this.value = value;
    }

    /**
     * 构造函数
     * @param value 刷新令牌值
     * @param expiration 过期时间
     */
    public YeelightOAuth2RefreshToken(String value, Date expiration) {
        this.value = value;
        this.expiration = expiration;
    }

    /**
     * 检查刷新令牌是否已过期
     * @return true如果已过期
     */
    public boolean isExpired() {
        if (expiration != null) {
            return expiration.before(new Date());
        }
        // 如果没有过期时间，认为不会过期
        return false;
    }

    /**
     * 获取过期时间（秒）
     * @return 过期时间秒数，如果没有过期时间返回null
     */
    public Integer getExpiresIn() {
        if (expiration != null) {
            long now = System.currentTimeMillis();
            long expirationTime = expiration.getTime();
            if (expirationTime > now) {
                return (int) ((expirationTime - now) / 1000);
            } else {
                // 已过期
                return 0;
            }
        }
        return null;
    }

    /**
     * 从旧版OAuth2RefreshToken创建（兼容性构造函数）
     */
    @Deprecated
    public static YeelightOAuth2RefreshToken fromLegacyOAuth2RefreshToken(Object legacyRefreshToken) {
        if (legacyRefreshToken == null) {
            return null;
        }
        
        try {
            YeelightOAuth2RefreshToken token = new YeelightOAuth2RefreshToken();
            
            // 使用反射获取字段值
            token.value = getFieldValue(legacyRefreshToken, "value", String.class);
            
            // 尝试获取过期时间（某些实现可能有此字段）
            try {
                token.expiration = getFieldValue(legacyRefreshToken, "expiration", Date.class);
            } catch (Exception e) {
                // 如果没有过期时间字段，忽略异常
                token.expiration = null;
            }
            
            return token;
        } catch (Exception e) {
            return new YeelightOAuth2RefreshToken();
        }
    }

    /**
     * 反射工具方法
     */
    private static <T> T getFieldValue(Object obj, String fieldName, Class<T> type) {
        try {
            Class<?> clazz = obj.getClass();
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return type.cast(value);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建器模式
     */
    public static class Builder {
        private String value;
        private Date expiration;

        public Builder value(String value) {
            this.value = value;
            return this;
        }

        public Builder expiration(Date expiration) {
            this.expiration = expiration;
            return this;
        }

        public YeelightOAuth2RefreshToken build() {
            return new YeelightOAuth2RefreshToken(value, expiration);
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        YeelightOAuth2RefreshToken that = (YeelightOAuth2RefreshToken) obj;
        return value != null ? value.equals(that.value) : that.value == null;
    }

    @Override
    public int hashCode() {
        return value != null ? value.hashCode() : 0;
    }

    @Override
    public String toString() {
        return value;
    }
}
