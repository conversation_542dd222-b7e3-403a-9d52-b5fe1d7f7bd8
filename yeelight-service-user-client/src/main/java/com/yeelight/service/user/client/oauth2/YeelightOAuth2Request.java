package com.yeelight.service.user.client.oauth2;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * Yeelight自建OAuth2Request
 * 完全兼容org.springframework.security.oauth2.provider.OAuth2Request
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Data
public class YeelightOAuth2Request implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求参数
     */
    private Map<String, String> requestParameters = new HashMap<>();

    /**
     * 授权范围
     */
    private Set<String> scope = new HashSet<>();

    /**
     * 授权类型集合
     */
    private Set<String> authorities = new HashSet<>();

    /**
     * 是否已批准
     */
    private boolean approved = false;

    /**
     * 资源ID集合
     */
    private Set<String> resourceIds = new HashSet<>();

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 响应类型集合
     */
    private Set<String> responseTypes = new HashSet<>();

    /**
     * 扩展属性
     */
    private Map<String, Serializable> extensions = new HashMap<>();

    /**
     * 默认构造函数
     */
    public YeelightOAuth2Request() {
    }

    /**
     * 完整构造函数
     */
    public YeelightOAuth2Request(Map<String, String> requestParameters, String clientId,
                                Set<String> authorities, boolean approved, Set<String> scope,
                                Set<String> resourceIds, String redirectUri, Set<String> responseTypes,
                                Map<String, Serializable> extensions) {
        this.requestParameters = requestParameters != null ? requestParameters : new HashMap<>();
        this.clientId = clientId;
        this.authorities = authorities != null ? authorities : new HashSet<>();
        this.approved = approved;
        this.scope = scope != null ? scope : new HashSet<>();
        this.resourceIds = resourceIds != null ? resourceIds : new HashSet<>();
        this.redirectUri = redirectUri;
        this.responseTypes = responseTypes != null ? responseTypes : new HashSet<>();
        this.extensions = extensions != null ? extensions : new HashMap<>();
    }

    /**
     * 从旧版OAuth2Request创建（兼容性构造函数）
     */
    @Deprecated
    public static YeelightOAuth2Request fromLegacyOAuth2Request(Object legacyRequest) {
        if (legacyRequest == null) {
            return null;
        }
        
        try {
            YeelightOAuth2Request request = new YeelightOAuth2Request();
            
            // 使用反射获取字段值
            request.clientId = getFieldValue(legacyRequest, "clientId", String.class);
            request.requestParameters = getFieldValue(legacyRequest, "requestParameters", Map.class);
            request.scope = getFieldValue(legacyRequest, "scope", Set.class);
            request.approved = getFieldValue(legacyRequest, "approved", Boolean.class);
            request.resourceIds = getFieldValue(legacyRequest, "resourceIds", Set.class);
            request.redirectUri = getFieldValue(legacyRequest, "redirectUri", String.class);
            request.responseTypes = getFieldValue(legacyRequest, "responseTypes", Set.class);
            request.extensions = getFieldValue(legacyRequest, "extensions", Map.class);
            
            // 处理authorities字段
            Collection<GrantedAuthority> grantedAuthorities = getFieldValue(legacyRequest, "authorities", Collection.class);
            if (grantedAuthorities != null) {
                Set<String> authStrings = new HashSet<>();
                for (GrantedAuthority authority : grantedAuthorities) {
                    authStrings.add(authority.getAuthority());
                }
                request.authorities = authStrings;
            }
            
            return request;
        } catch (Exception e) {
            return new YeelightOAuth2Request();
        }
    }

    /**
     * 反射工具方法
     */
    private static <T> T getFieldValue(Object obj, String fieldName, Class<T> type) {
        try {
            Class<?> clazz = obj.getClass();
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return type.cast(value);
        } catch (Exception e) {
            return getDefaultValue(type);
        }
    }

    /**
     * 获取类型默认值
     */
    @SuppressWarnings("unchecked")
    private static <T> T getDefaultValue(Class<T> type) {
        if (type == String.class) {
            return (T) "";
        } else if (type == Boolean.class) {
            return (T) Boolean.FALSE;
        } else if (type == Map.class) {
            return (T) new HashMap<>();
        } else if (type == Set.class) {
            return (T) new HashSet<>();
        } else if (type == Collection.class) {
            return (T) new ArrayList<>();
        }
        return null;
    }

    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建器模式
     */
    public static class Builder {
        private Map<String, String> requestParameters = new HashMap<>();
        private String clientId;
        private String grantType;
        private Set<String> authorities = new HashSet<>();
        private boolean approved = false;
        private Set<String> scope = new HashSet<>();
        private Set<String> resourceIds = new HashSet<>();
        private String redirectUri;
        private Set<String> responseTypes = new HashSet<>();
        private Map<String, Serializable> extensions = new HashMap<>();

        public Builder requestParameters(Map<String, String> requestParameters) {
            this.requestParameters = requestParameters;
            return this;
        }

        public Builder clientId(String clientId) {
            this.clientId = clientId;
            return this;
        }

        public Builder grantType(String grantType) {
            this.grantType = grantType;
            return this;
        }

        public Builder authorities(Set<String> authorities) {
            this.authorities = authorities;
            return this;
        }

        public Builder approved(boolean approved) {
            this.approved = approved;
            return this;
        }

        public Builder scope(Set<String> scope) {
            this.scope = scope;
            return this;
        }

        public Builder resourceIds(Set<String> resourceIds) {
            this.resourceIds = resourceIds;
            return this;
        }

        public Builder redirectUri(String redirectUri) {
            this.redirectUri = redirectUri;
            return this;
        }

        public Builder responseTypes(Set<String> responseTypes) {
            this.responseTypes = responseTypes;
            return this;
        }

        public Builder extensions(Map<String, Serializable> extensions) {
            this.extensions = extensions;
            return this;
        }

        public YeelightOAuth2Request build() {
            return new YeelightOAuth2Request(requestParameters, clientId, authorities, approved,
                    scope, resourceIds, redirectUri, responseTypes, extensions);
        }
    }
}
