package com.yeelight.service.user.client.oauth2;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * Yeelight自建TokenRequest
 * 完全兼容org.springframework.security.oauth2.provider.TokenRequest
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Data
@Builder
@NoArgsConstructor
public class YeelightTokenRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 请求参数
     */
    @Builder.Default
    private Map<String, String> requestParameters = new HashMap<>();

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 授权范围
     */
    @Builder.Default
    private Set<String> scope = new HashSet<>();

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * 构造函数 - 兼容旧版TokenRequest
     */
    public YeelightTokenRequest(Map<String, String> requestParameters, String clientId, 
                               Set<String> scope, String grantType) {
        this.requestParameters = requestParameters != null ? new HashMap<>(requestParameters) : new HashMap<>();
        this.clientId = clientId;
        this.scope = scope != null ? new HashSet<>(scope) : new HashSet<>();
        this.grantType = grantType;
    }

    /**
     * 从请求参数创建TokenRequest
     */
    public static YeelightTokenRequest fromParameters(Map<String, String> parameters) {
        String clientId = parameters.get("client_id");
        String grantType = parameters.get("grant_type");
        String scopeString = parameters.get("scope");
        
        Set<String> scopes = new HashSet<>();
        if (scopeString != null && !scopeString.trim().isEmpty()) {
            scopes.addAll(Arrays.asList(scopeString.trim().split("\\s+")));
        }

        return new YeelightTokenRequest(parameters, clientId, scopes, grantType);
    }

    /**
     * 获取授权范围字符串
     */
    public String getScopeString() {
        return scope.isEmpty() ? null : String.join(" ", scope);
    }

    /**
     * 设置授权范围字符串
     */
    public void setScopeString(String scopeString) {
        if (scopeString != null && !scopeString.trim().isEmpty()) {
            this.scope = new HashSet<>(Arrays.asList(scopeString.trim().split("\\s+")));
        } else {
            this.scope = new HashSet<>();
        }
    }

    /**
     * 检查是否包含指定的授权范围
     */
    public boolean hasScope(String scope) {
        return this.scope != null && this.scope.contains(scope);
    }

    /**
     * 添加授权范围
     */
    public void addScope(String scope) {
        if (this.scope == null) {
            this.scope = new HashSet<>();
        }
        this.scope.add(scope);
    }

    /**
     * 移除授权范围
     */
    public void removeScope(String scope) {
        if (this.scope != null) {
            this.scope.remove(scope);
        }
    }

    /**
     * 获取请求参数值
     */
    public String getRequestParameter(String key) {
        return requestParameters != null ? requestParameters.get(key) : null;
    }

    /**
     * 设置请求参数
     */
    public void setRequestParameter(String key, String value) {
        if (this.requestParameters == null) {
            this.requestParameters = new HashMap<>();
        }
        this.requestParameters.put(key, value);
    }

    /**
     * 检查是否为刷新令牌请求
     */
    public boolean isRefreshTokenRequest() {
        return "refresh_token".equals(this.grantType);
    }

    /**
     * 检查是否为授权码请求
     */
    public boolean isAuthorizationCodeRequest() {
        return "authorization_code".equals(this.grantType);
    }

    /**
     * 检查是否为客户端凭证请求
     */
    public boolean isClientCredentialsRequest() {
        return "client_credentials".equals(this.grantType);
    }

    /**
     * 检查是否为密码模式请求
     */
    public boolean isPasswordRequest() {
        return "password".equals(this.grantType);
    }

    /**
     * 检查是否为隐式授权请求
     */
    public boolean isImplicitRequest() {
        return "implicit".equals(this.grantType);
    }

    /**
     * 复制当前对象
     */
    public YeelightTokenRequest copy() {
        return YeelightTokenRequest.builder()
                .requestParameters(this.requestParameters != null ? new HashMap<>(this.requestParameters) : new HashMap<>())
                .clientId(this.clientId)
                .scope(this.scope != null ? new HashSet<>(this.scope) : new HashSet<>())
                .grantType(this.grantType)
                .build();
    }

    /**
     * 合并另一个TokenRequest的信息
     */
    public void merge(YeelightTokenRequest other) {
        if (other == null) {
            return;
        }

        if (other.scope != null) {
            if (this.scope == null) {
                this.scope = new HashSet<>();
            }
            this.scope.addAll(other.scope);
        }

        if (other.requestParameters != null) {
            if (this.requestParameters == null) {
                this.requestParameters = new HashMap<>(0);
            }
            this.requestParameters.putAll(other.requestParameters);
        }

        if (other.clientId != null && this.clientId == null) {
            this.clientId = other.clientId;
        }

        if (other.grantType != null && this.grantType == null) {
            this.grantType = other.grantType;
        }
    }

    /**
     * 验证TokenRequest的有效性
     */
    public void validate() {
        if (this.clientId == null || this.clientId.trim().isEmpty()) {
            throw new IllegalArgumentException("Client ID cannot be null or empty");
        }

        if (this.grantType == null || this.grantType.trim().isEmpty()) {
            throw new IllegalArgumentException("Grant type cannot be null or empty");
        }

        // 根据不同的授权类型进行特定验证
        switch (this.grantType) {
            case "authorization_code":
                if (getRequestParameter("code") == null) {
                    throw new IllegalArgumentException("Authorization code is required for authorization_code grant");
                }
                break;
            case "refresh_token":
                if (getRequestParameter("refresh_token") == null) {
                    throw new IllegalArgumentException("Refresh token is required for refresh_token grant");
                }
                break;
            case "password":
                if (getRequestParameter("username") == null || getRequestParameter("password") == null) {
                    throw new IllegalArgumentException("Username and password are required for password grant");
                }
                break;
            case "client_credentials":
                // 客户端凭证模式不需要额外参数
                break;
            default:
                // 其他授权类型的验证可以在这里添加
                break;
        }
    }

    @Override
    public String toString() {
        return "YeelightTokenRequest{" +
                "clientId='" + clientId + '\'' +
                ", grantType='" + grantType + '\'' +
                ", scope=" + scope +
                ", requestParameters=" + requestParameters +
                '}';
    }
}
