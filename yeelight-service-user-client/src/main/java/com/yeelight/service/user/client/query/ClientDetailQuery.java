package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.ClientDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientDetailQuery extends BaseQuery {
    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * access 令牌有效性
     */
    private Integer accessTokenValidity;

    /**
     * 刷新令牌有效性
     */
    private Integer refreshTokenValidity;

    /**
     * 自动授权
     */
    private String autoApprove;

}
