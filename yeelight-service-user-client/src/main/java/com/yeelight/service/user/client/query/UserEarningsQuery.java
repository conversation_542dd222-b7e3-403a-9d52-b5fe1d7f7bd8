package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.UserEarnings;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserEarningsQuery extends BaseQuery<UserEarnings> {
    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 收入类型
     */
    private String earningType;

    /**
     * 收入来源
     */
    private String earningFrom;


    /**
     * 是否入账 0：未入账 1：已入账
     */
    private Integer isCredit;

    /**
     * 来源ID
     */
    private Long fromId;

    /**
     * 收入流水号
     */
    private String earningNo;


    /**
     * 收入时间 开始
     */
    private Integer earningTimeStart;

    /**
     * 收入时间 结束
     */
    private Integer earningTimeEnd;

    /**
     * 审核时间 开始
     */
    private Integer auditTimeStart;

    /**
     * 审核时间 结束
     */
    private Integer auditTimeEnd;

    /**
     * 是否作废 0：未作废 1：已作废
     */
    private Integer isInvalid;

    /**
     * 作废时间 开始
     */
    private Integer invalidTimeStart;

    /**
     * 作废时间 结束
     */
    private Integer invalidTimeEnd;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人uid
     */
    private Long auditUid;

    /**
     * 审核人
     */
    private String auditName;
}