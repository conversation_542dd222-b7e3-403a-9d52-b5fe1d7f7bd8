package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.UserLevelLog;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserLevelLogQuery extends BaseQuery<UserLevelLog> {

    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 等级类型
     */
    private String levelType;

    /**
     * 创建人id
     */
    private Long createUid;


    /**
     * 修改人id
     */
    private Long updateUid;
}