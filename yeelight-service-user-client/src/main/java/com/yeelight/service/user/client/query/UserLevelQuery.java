package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.UserLevel;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserLevelQuery extends BaseQuery<UserLevel> {

    /**
     * 等级类型
     */
    private String levelType;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 等级描述
     */
    private String levelDesc;

}