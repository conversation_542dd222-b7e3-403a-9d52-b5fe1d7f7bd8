package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.UserLevel;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserLevelRelQuery extends BaseQuery<UserLevel> {

    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 等级ID
     */
    private Long levelId;

    /**
     * 等级类型
     */
    private String levelType;

    /**
     * 等级名称
     */
    private String levelName;
}