package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.UserWithdraws;
import lombok.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserWithdrawsQuery extends BaseQuery<UserWithdraws> {
    /**
     * 用户ID
     */
    private Long yeelightUserId;

    /**
     * 提现类型
     */
    private String withdrawType;

    /**
     * 提现流水号
     */
    private String withdrawNo;

    /**
     * 提现状态 0- 待审核 1- 提现成功 -1- 提现失败
     */
    private Byte status;

    /**
     * 提现申请时间 开始
     */
    private Integer applyTimeStart;

    /**
     * 提现申请时间 结束
     */
    private Integer applyTimeEnd;

    /**
     * 持卡人姓名
     */
    private String bankUserName;

    /**
     * 开户行所在地
     */
    private String bankLocation;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 审核时间 开始
     */
    private Integer auditTimeStart;

    /**
     * 审核时间 结束
     */
    private Integer auditTimeEnd;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核人uid
     */
    private Long auditUid;

    /**
     * 审核人
     */
    private String auditName;
}