package com.yeelight.service.user.client.query;

import com.yeelight.service.framework.util.BaseQuery;
import com.yeelight.service.user.client.domain.YeelightUser;
import lombok.*;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class YeelightUserQuery extends BaseQuery<YeelightUser> {
    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户名
     */
    private String usernameLike;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 手机号
     */
    private String phoneNumberLike;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 邮箱
     */
    private String emailLike;

    /**
     * 昵称
     */
    private String name;

    /**
     * 昵称
     */
    private String nameLike;
}