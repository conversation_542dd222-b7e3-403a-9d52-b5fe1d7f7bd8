package com.yeelight.service.user.client.request;

import com.yeelight.service.user.client.enums.UserLevelTypes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: yeelight-service-c
 * @description:
 * @author: <PERSON>
 * @create: 2020-07-02 14:33
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddUserGrowthRequest implements Serializable {
    /**
     * 要升级的Yeelight用户ID
     */
    private Long yeelightUserId;
    /**
     * 等级类型
     */
    private UserLevelTypes userLevelType;

    /**
     * 增加的成长值
     */
    private Integer increasedGrowth;

    /**
     * 操作人
     */
    private Long operateUid;
}
