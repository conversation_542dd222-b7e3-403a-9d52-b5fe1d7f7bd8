package com.yeelight.service.user.client.request;

import com.yeelight.service.user.client.enums.UserWithdrawTypes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: yeelight-service-c
 * @description:
 * @author: <PERSON>
 * @create: 2020-07-02 14:33
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplyWithdrawRequest implements Serializable {
    /**
     * 要提现的Yeelight用户ID
     */
    @NotNull(message = "要提现的Yeelight用户ID必传")
    private Long yeelightUserId;
    /**
     * 提现类型
     */
    @NotNull(message = "提现类型必传")
    private UserWithdrawTypes withdrawType;

    /**
     * 提现金额
     */
    @NotNull(message = "提现金额必传")
    private BigDecimal applyMoney;
}
