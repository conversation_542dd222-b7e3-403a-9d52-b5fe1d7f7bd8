package com.yeelight.service.user.client.request;

import com.yeelight.service.user.client.enums.UserWithdrawStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @program: yeelight-service-c
 * @description:
 * @author: Sheldon
 * @create: 2020-07-02 14:33
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuditWithdrawRequest implements Serializable {
    /**
     * 要审核的提现ID
     */
    @NotNull(message = "要审核的提现ID必传")
    private Long withdrawId;

    /**
     * 提现状态
     */
    @NotNull(message = "提现状态必传")
    private UserWithdrawStatus status;

    /**
     * 审核人
     */
    private Long auditId;

    private String auditName;

    /**
     * 备注
     */
    private String remark;
}
