package com.yeelight.service.user.client.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ClientDetailRequest implements Serializable {

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 客户端密钥
     */
    private String secret;

    /**
     * 资源ID
     */
    private String resourceIds;

    /**
     * 作用域
     */
    private String scope;

    /**
     * 授权类型
     */
    private String authorizedGrantTypes;

    /**
     * 回调地址

     */
    private String webServerRedirectUri;

    /**
     * 权限
     */
    private String authorities;

    /**
     * access 令牌有效性
     */
    private Integer accessTokenValidity;

    /**
     * 刷新令牌有效性
     */
    private Integer refreshTokenValidity;

    /**
     * 附加信息
     */
    private String additionalInformation;

    /**
     * 自动授权
     */
    private String autoApprove;

}
