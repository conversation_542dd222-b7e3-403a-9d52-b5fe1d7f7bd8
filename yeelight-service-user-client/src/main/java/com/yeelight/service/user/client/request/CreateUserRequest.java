/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.client.request
 * Description: 添加用户请求
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-06 17:37:17:37
 */
package com.yeelight.service.user.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;

/**
 * Desc: 添加用户请求
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-05-06 17:37:17:37
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateUserRequest implements java.io.Serializable {

    /**
     * 名称
     */
    @Length(min = 1, max = 150, message = "用户名长度必须在1-150之间")
    @Pattern(regexp = "^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$", message = "用户名只能包含字母、数字、下划线和中文")
    private String name;

    /**
     * 密码
     */
    @Length(min = 6, max = 150, message = "密码长度必须在1-150之间")
    private String password;

    /**
     * 电话号码
     */
    @Length(min = 6, max = 30, message = "电话号码长度必须在6-30之间")
    @Pattern(regexp = "^[0-9+\\-*/\\s()]+$", message = "手机号码格式不正确")
    private String phoneNumber;

    /**
     * 电子邮箱
     */
    @Length(min = 6, max = 50, message = "电子邮箱长度必须在6-50之间")
    @Email(message = "电子邮箱格式不正确")
    private String email;

    @Length(min = 1, max = 500, message = "头像长度必须在1-500之间")
    private String avatar;

    /**
     * 用户地域
     */
    @Length(min = 2, max = 10, message = "用户地域长度必须在2-10之间")
    private String region;

    /**
     * 用户名 (不必传，会自动生成)
     */
    private String username;
}
