/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.client.request
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-11-04 18:10:18:10
 */
package com.yeelight.service.user.client.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotBlank;

/**
 * Desc: 添加用户请求
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-11-04 18:10:18:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JwtCreateUserRequest extends CreateUserRequest {
    /**
     * 验证码
     */
    @NotBlank(message = "{Domain.RegisterUser.captcha.notBlank}")
    private String captcha;

    /**
     * 验证码key
     */
    @NotBlank(message = "{Domain.RegisterUser.captchaKey.notBlank}")
    private String captchaKey;
}
