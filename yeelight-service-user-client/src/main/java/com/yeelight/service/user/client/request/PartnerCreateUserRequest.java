/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.client.request
 * Description: 添加用户请求
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-06 17:37:17:37
 */
package com.yeelight.service.user.client.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 可信合作伙伴批量注册用户请求
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-12-09 17:37:17:37
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartnerCreateUserRequest implements Serializable {
    /**
     * 客户端ID
     */
    @NotBlank(message = "客户端ID不能为空")
    private String clientId;

    /**
     * 客户端密钥
     */
    @NotBlank(message = "客户端密钥不能为空")
    private String clientSecret;

    /**
     * 用户名列表
     * 可信合作伙伴端用户的手机号列表或者邮箱列表,每批次最大 100 个
     */
    @NotEmpty(message = "机号列表或者邮箱列表不能为空")
    @Length(min = 1, max = 100, message = "用户名列表每批次长度必须在1-100之间")
    private List<String> usernames;
}
