package com.yeelight.service.user.client.request;

import com.yeelight.service.user.client.domain.UserLevel;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: yeelight-service-c
 * @description:
 * @author: <PERSON>
 * @create: 2020-07-02 14:33
 **/
@Data
public class UserLevelUpgradeRule implements Serializable {
    /**
     * 用户信息
     */
    private YeelightUserDto yeelightUser;

    /**
     * 成长值
     */
    private Integer increasedGrowth;

    /**
     * 用户等级列表
     */
    private List<UserLevel> userLevelList;
}
