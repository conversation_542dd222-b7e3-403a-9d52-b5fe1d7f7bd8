package com.yeelight.service.user.client.service;


import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.user.client.domain.ClientDetail;
import com.yeelight.service.user.client.query.ClientDetailQuery;
import com.yeelight.service.user.client.request.ClientDetailRequest;

/**
 * Oauth client服务
 *
 * <AUTHOR>
 */
public interface ClientDetailService {
    /**
     * 分页查询客户端详情
     *
     * @param query 包含查询条件和分页信息的查询参数
     * @return 返回分页数据，包含当前页的客户端详情列表
     */
    PageResultSet<ClientDetail> pageClientDetail(ClientDetailQuery query);

    /**
     * 根据客户端ID获取客户端详细信息
     *
     * @param clientId 客户端的唯一标识符
     * @return 返回对应客户端的详细信息
     */
    ClientDetail getClient(String clientId);

    /**
     * 添加新的客户端信息
     *
     * @param request 包含客户端详细信息的请求参数
     * @throws BizException 业务异常，可能由于数据验证或其他业务规则失败抛出
     * @return null
     */
    Void addClient(ClientDetailRequest request) throws BizException;

    /**
     * 更新已存在的客户端信息
     *
     * @param request 包含更新后的客户端详细信息的请求参数
     * @throws BizException 业务异常，可能由于数据验证或其他业务规则失败抛出
     * @return null
     */
    Void updateClient(ClientDetailRequest request) throws BizException;

    /**
     * 重置指定客户端的秘钥
     *
     * @param clientId     客户端的唯一标识符
     * @param clientSecret 想要设置的新客户端秘钥
     * @return null，操作成功或失败通过异常处理
     */
    Void resetSecret(String clientId, String clientSecret);

    /**
     * 删除指定的客户端
     *
     * @param clientId 客户端的唯一标识符
     * @return 删除的行数，通常为1；若客户端不存在或发生业务异常，则可能返回其他值或抛出异常
     * @throws BizException 业务异常，可能由于尝试删除不存在的客户端或其他业务规则失败抛出
     */
    int removeClinet(String clientId) throws BizException;

    /**
     * 校验客户端ID和客户端秘钥是否匹配
     *
     * @param clientId     客户端ID
     * @param clientSecret 客户端秘钥
     * @return 是否匹配
     */
    boolean validateClientSecret(String clientId, String clientSecret);
}
