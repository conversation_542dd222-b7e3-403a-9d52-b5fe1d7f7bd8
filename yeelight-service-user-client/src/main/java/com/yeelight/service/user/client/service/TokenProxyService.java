package com.yeelight.service.user.client.service;

import com.yeelight.service.user.client.domain.ProxyInfo;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;

/**
 * 代理token服务
 *
 * <AUTHOR>
 * @date 2020-02-17 14:48
 */
public interface TokenProxyService {

    /**
     * 获取用户的代理信息
     *
     * @param token 被代理token
     * @return 代理信息
     */
    OAuth2AuthenticationDto getAuthentication(String token);

    /**
     * 绑定代理
     *
     * @param proxyUserId 代理人用户id
     * @param proxyInfo  代理信息
     */
    void bindProxy(long proxyUserId, ProxyInfo proxyInfo);

    /**
     * 获取被代理token
     *
     * @param proxyUserId 代理人用户id
     * @return 被代理token
     */
    ProxyInfo getProxyInfo(long proxyUserId);

    /**
     * 解除代理关系
     *
     * @param proxyUserId 代理人用户id
     */
    void unbindProxy(long proxyUserId);
}
