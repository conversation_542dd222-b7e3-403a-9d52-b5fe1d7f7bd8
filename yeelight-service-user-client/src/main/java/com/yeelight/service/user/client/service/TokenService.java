package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.LoginTokenInfo;
import com.yeelight.service.user.client.domain.UserMonitoringProperty;
import com.yeelight.service.user.client.dto.CheckedToken;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * Token服务
 *
 * <AUTHOR>
 */
public interface TokenService {
	/**
	 * 根据用户名获取所有登录token信息
	 * @param username 用户名
	 * @return List<LoginTokenInfo> token信息
	 */
	List<LoginTokenInfo> getTokenInfos(String username);

	/**
	 * 根据用户名获取所有登录token信息
	 * @param username 用户名
	 * @param vendor 厂商
	 * @return List<LoginTokenInfo> token信息
	 */
	List<LoginTokenInfo> getTokenInfos(String username, String vendor);

	/**
	 * 通过token解析用户信息
	 * @param token token
	 * @return OAuth2AuthenticationDto 用户信息
	 */
	OAuth2AuthenticationDto getAuthenticationByToken(String token);

	/**
	 * 通过token获取用户信息
	 * @param token token
	 * @return String 用户名
	 */
	String getUserNameByToken(String token);

	/**
	 * 根据用户ID模拟登陆
	 * @param clientId 应用ID
	 * @param yeelightId 用户ID
	 * @param extendParameters 额外参数，一般需要配置一个device参数
	 * @return OAuth2Token token信息
	 * @throws BizException 异常
	 */
	OAuth2Token mockAuthenticationByYeelightId(String clientId, Long yeelightId, Map<String, String> extendParameters) throws BizException;

	/**
	 * 根据用户名模拟登陆
	 * @param clientId 应用ID
	 * @param username 用户名
	 * @param extendParameters 额外参数，一般需要配置一个device参数
	 * @return OAuth2Token token信息
	 * @throws BizException 异常
	 */
	OAuth2Token mockAuthenticationByUserName(String clientId, String username, Map<String, String> extendParameters) throws BizException;

	/**
	 * 根据授权码模拟登陆
	 * @param clientId 应用ID
	 * @param code 授权码
	 * @param parameters 额外参数，一般需要配置一个device参数
	 * @return OAuth2Token token信息
	 * @throws BizException 异常
	 */
	OAuth2Token authenticationByCode(String clientId, String code, Map<String, String> parameters) throws BizException;

	/**
	 * 检测token
	 * @param token token
	 * @return Result<CheckedToken> 检测结果
	 */
	Result<CheckedToken> checkToken(@RequestParam("token") String token);

	/**
	 * 检测token
	 * @param token token
	 * @throws BizException 异常
	 */
	void checkValidToken(@RequestParam("token") String token) throws BizException;

	/**
	 * 使用token登录
	 *
	 * @param token token
	 * @param request request
	 * @return boolean 是否登录成功
	 */
	boolean loginByToken(String token, HttpServletRequest request);

	/**
	 * 根据用户名获取token - 使用自建OAuth2AccessToken
	 * @param userName 用户名
	 * @return List<YeelightOAuth2AccessToken> token集合
	 */
	List<YeelightOAuth2AccessToken> findTokensByUserName(String userName);

	/**
	 * 设置token 过期
	 * @param token token
	 * @return boolean 是否设置成功
	 */
	boolean expireAccessToken(String token);

	/**
	 * 设置token过期时间
	 * 设置后，AccessToken和refreshToken 都会按过期时间过期
	 * @param token token
	 * @param seconds 过期时间
	 * @return boolean 是否设置成功
	 */
	boolean expireAccessTokenTime(String token, int seconds);

	/**
	 * 设置该用户下token过期
	 *
	 * @param username 用户名
	 * @return boolean 是否设置成功
	 */
	boolean revokeTokenByUserName(String username);

	/**
	 * 登出Token
	 * 删除用户指定token
	 *
	 * @param token token
	 * @return boolean 是否登出成功
	 */
	boolean logoutCurrentToken(String token);

	/**
	 * 登出应用
	 * 删除当前应用下用户所有的 token
	 *
	 * @param token token
	 * @return boolean 是否登出成功
	 */
	boolean logoutCurrentApplication(String token);

	/**
	 * 登出用户
	 * 删除当前用户下所有的 token
	 *
	 * @param token token
	 * @return boolean 是否登出成功
	 */
	boolean logoutCurrentUser(String token);

	/**
	 * 设置用户监控
	 *
	 * @param monitoringProperty 监控属性
	 * @throws BizException 异常
	 */
	void setUserMonitoring(UserMonitoringProperty monitoringProperty) throws BizException;

	/**
	 * 获取所有用户监控
	 *
	 * @return List<UserMonitoringProperty> 用户监控信息
	 */
	List<UserMonitoringProperty> getAllUserMonitoring();

	/**
	 * 获取指定用户的监控信息。
	 *
	 * @param userId 用户的唯一标识符，用于指定要获取监控信息的用户。
	 * @return UserMonitoringProperty 返回一个包含指定用户监控信息的对象。
	 */
	UserMonitoringProperty getUserMonitoring(Long userId);

}
