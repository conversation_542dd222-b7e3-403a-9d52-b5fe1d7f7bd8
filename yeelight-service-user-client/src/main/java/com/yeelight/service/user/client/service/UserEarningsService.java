package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.domain.UserEarnings;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import com.yeelight.service.user.client.query.UserEarningsQuery;
import com.yeelight.service.framework.service.BaseService;


/**
 * 用户收入服务
 * <AUTHOR>
 */
public interface UserEarningsService {
    /**
     * 增加收入
     * isCredit 0 预估收入 1 实际收入
     * @param userEarnings 收入对象
     * @param userLevelType 等级类型
     * @throws BizException 业务异常
     */
    void updateUserEarning(UserEarnings userEarnings, UserLevelTypes userLevelType)  throws BizException;

    /**
     * 收入作废
     * @param userEarningsQuery 收入查询对象
     * @throws BizException 业务异常
     */
    void invalidUserEarning(UserEarningsQuery userEarningsQuery)  throws BizException ;

    /**
     * 根据用户ID移除用户的收益记录。
     *
     * @param userId 需要移除收益记录的用户的ID，类型为Long。
     * @throws BizException 如果操作过程中出现业务异常，则抛出BizException。
     *
     * 该方法通过用户ID来定位并移除特定用户的收益记录，旨在处理用户收益数据的清理或重置操作。
     */
    void removeUserEarningByUserId(Long userId) throws BizException ;
}
