package com.yeelight.service.user.client.service;

import com.yeelight.service.user.client.domain.UserLevelRel;
import com.yeelight.service.user.client.query.UserLevelRelQuery;
import com.yeelight.service.framework.service.BaseService;


/**
 * 用户等级关系服务
 * <AUTHOR>
 */
public interface UserLevelRelService extends BaseService<UserLevelRel, UserLevelRelQuery> {
    /**
     * 替换用户等级关系数据
     * @param userLevelRel 用户等级关系数据
     */
    void replaceLevelRel(UserLevelRel userLevelRel);

    /**
     * 根据用户ID获取用户等级关系信息。
     *
     * @param userId 用户的唯一标识ID。
     * @return 返回与该用户ID对应的UserLevelRel对象，如果不存在，则返回null。
     */
    UserLevelRel getUserLevel(Long userId);

    /**
     * 根据用户ID和等级类型获取用户等级关系信息。
     *
     * @param userId 用户的唯一标识ID。
     * @param levelType 等级的类型，用于筛选特定类型的等级关系。
     * @return 返回与给定用户ID和等级类型对应的UserLevelRel对象，如果不存在，则返回null。
     */
    UserLevelRel getUserLevelByLevelType(Long userId, String levelType);
}
