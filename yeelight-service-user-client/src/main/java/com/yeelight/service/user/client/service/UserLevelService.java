package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.domain.UserLevel;
import com.yeelight.service.user.client.dto.UserLevelAndRulesData;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import com.yeelight.service.user.client.query.UserLevelQuery;
import com.yeelight.service.user.client.request.AddUserGrowthRequest;
import com.yeelight.service.framework.service.BaseService;

import java.math.BigDecimal;


/**
 * 用户等级服务
 * <AUTHOR>
 */
public interface UserLevelService extends BaseService<UserLevel, UserLevelQuery> {
    /**
     * 成长值增加
     * 成长值的修改, 只允许通过该方法,不要直接修改
     * @param addUserGrowthRequest 成长值增加请求
     * @throws BizException 业务异常
     */
    void addGrowth(AddUserGrowthRequest addUserGrowthRequest)  throws BizException;

    /**
     * 获取等级的分成规则
     * @param levelId 等级id
     * @return 分成规则
     */
    UserLevel.CommissionRule getCommissionRule(Long levelId);

    /**
     * 获取等级的成长规则
     * @param levelId 等级id
     * @return 成长规则
     */
    UserLevel.GrowthRule getGrowthRule(Long levelId);

    /**
     * 获取等级和分成相关信息
     * @param yeelightUserId 用户id
     * @param userLevelType 用户等级类型
     * @param money     金额
     * @return 等级和分成相关信息
     * @throws BizException 业务异常
     */
    UserLevelAndRulesData getLevelAndRules(Long yeelightUserId, UserLevelTypes userLevelType, BigDecimal money)  throws BizException ;
}
