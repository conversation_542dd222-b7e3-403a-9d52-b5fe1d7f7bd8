/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.config
 * Description: 会话管理服务
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-11-07 14:11:14:11
 */
package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.domain.LoginSessionInfo;

import java.util.List;

/**
 * Desc: 会话管理服务
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-11-07 14:11:14:11
 */
public interface UserSessionManagementService {
    /**
     * 强制用户下线指定厂商下所有会话
     * @param username 用户名
     * @param vendor 厂商
     * @throws BizException 业务异常
     */
    void expireUserSessions(String username, String vendor) throws BizException;

    /**
     * 强制用户下线所有会话。
     * 对于指定用户，无论其在哪个厂商下的会话，都将被强制下线。
     * @param username 需要被强制下线会话的用户名称。
     * @throws BizException 业务异常
     */
    void expireUserSessions(String username) throws BizException;

    /**
     * 强制用户下线指定会话。
     * 单个会话将被标记为失效，以便于服务端在后续的验证中拒绝该会话的请求。
     * @param sessionId 需要被强制下线的会话ID。
     * @throws BizException 业务异常
     */
    void expireSession(String sessionId) throws BizException;

    /**
     * 获取指定用户会话信息。
     * 通过会话ID查询登录会话的详细信息。
     * @param sessionId 会话ID
     * @return LoginSessionInfo 返回会话信息对象，包含会话的各种详情。
     */
    LoginSessionInfo getSessionInfo(String sessionId);

    /**
     * 获取指定用户的所有会话信息。
     * 针对一个特定用户，返回其所有有效会话的信息列表。
     * @param username 用户名
     * @return List<LoginSessionInfo> 返回一个包含用户所有会话信息的列表。
     */
    List<LoginSessionInfo> getSessionInfos(String username);

    /**
     * 获取指定用户的指定厂商的所有会话信息。
     * 可以根据用户和特定的厂商查询用户在该厂商下的所有会话信息。
     * @param username 用户名
     * @param vendor 厂商
     * @return List<LoginSessionInfo> 返回一个包含指定用户在指定厂商下所有会话信息的列表。
     */
    List<LoginSessionInfo> getSessionInfos(String username, String vendor);

}
