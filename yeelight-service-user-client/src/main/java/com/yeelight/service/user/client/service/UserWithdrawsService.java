package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.user.client.domain.UserWithdraws;
import com.yeelight.service.user.client.dto.UserWithdrawDetailDto;
import com.yeelight.service.user.client.query.UserWithdrawsDetailQuery;
import com.yeelight.service.user.client.query.UserWithdrawsQuery;
import com.yeelight.service.user.client.request.ApplyWithdrawRequest;
import com.yeelight.service.user.client.request.AuditWithdrawRequest;
import com.yeelight.service.framework.service.BaseService;

import java.util.List;


/**
 * 用户提现服务
 * <AUTHOR>
 */
public interface UserWithdrawsService extends BaseService<UserWithdraws, UserWithdrawsQuery> {
    /**
     * 申请提现
     * @param applyWithdrawRequest 申请提现请求
     * @throws BizException 业务异常
     */
    void applyWithdraw(ApplyWithdrawRequest applyWithdrawRequest)  throws BizException;

    /**
     * 审核提现
     * @param auditWithdrawRequest 审核提现请求
     * @throws BizException 业务异常
     */
    void auditWithdraw(AuditWithdrawRequest auditWithdrawRequest)  throws BizException;

    /**
     * 分页查询
     * @param query 查询条件
     * @return 提现信息
     */
    PageResultSet<UserWithdrawDetailDto> pageWithdrawsDetailList(UserWithdrawsDetailQuery query);

    /**
     * 导出查询
     * @param query 查询条件
     * @return 提现信息
     */
    List<UserWithdrawDetailDto> exportWithdrawsDetailList(UserWithdrawsDetailQuery query);
}
