/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.client.service
 * Description: 用户服务
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-29 09:17:09:17
 */
package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.dto.AuthToken;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.exception.NotLoginException;

import java.util.List;

/**
 * Desc: 用户三方绑定服务
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-29 09:17:09:17
 */
public interface YeelightSocialUserService {
    /**
     * 修改三方用户token
     *
     * @param socialUserId 三方用户id
     * @param authToken  认证token
     * @throws BizException 异常
     */
    void updateSocialUserTokenInfo(Long socialUserId, AuthToken authToken) throws BizException;

    /**
     * 修改三方用户token
     *
     * @param source 三方来源
     * @param uuid   三方用户uuid
     * @param authToken  认证token
     * @throws BizException 异常
     */
    void updateSocialUserTokenInfo(String source, String uuid, AuthToken authToken) throws BizException;

    /**
     * 按照三方来源和易来ID查找三方用户
     *
     * @param yeelightId 业务用户id
     * @param source 三方来源
     * @return 三方用户
     * @throws BizException 异常
     */
    SocialUserDto findSocialUserBySource(Long yeelightId, String source);

    /**
     * 按照三方来源和uuid查找三方用户
     *
     * @param uuid   三方用户uuid
     * @param source 三方来源
     * @return 三方用户
     */
    SocialUserDto findSocialUserBySourceAndUuid(String uuid, String source);

    /**
     * 按照用户ID查询三方用户列表
     *
     * @param yeelightId 业务用户id
     * @return 三方用户列表
     */
    List<SocialUserDto> findSocialUsers(Long yeelightId);

    /**
     * 解绑三方用户
     *
     * @param yeelightId 业务用户id
     * @param source     三方来源
     * @throws BizException 异常
     */
    void unBindSocialUser(Long yeelightId, String source) throws BizException;

    /**
     * 按照token和三方来源查找三方用户
     *
     * @param token 三方token
     * @param source 三方来源
     * @return 三方用户
     * @throws BizException 异常
     * @throws NotLoginException 异常
     */
    SocialUserDto findSocialUserBySource(String token, String source) throws BizException, NotLoginException;

    /**
     * 按照token查找三方用户列表
     *
     * @param token 三方token
     * @return 三方用户列表
     * @throws BizException 异常
     * @throws NotLoginException 异常
     */
    List<SocialUserDto> findSocialUsers(String token) throws BizException, NotLoginException;

    /**
     * 按照token用户供应商查找三方用户列表
     *
     * @param vendor 供应商
     * @param token  三方token
     * @return 三方用户列表
     * @throws BizException 异常
     * @throws NotLoginException 异常
     */
    List<SocialUserDto> findSocialUsersByVendor(String vendor, String token) throws BizException, NotLoginException;

    /**
     * 按照供应商和易来用户ID查找三方用户列表
     *
     * @param vendor 供应商
     * @param yeelightUserId 易来用户ID
     * @return 三方用户列表
     * @throws BizException 异常
     * @throws NotLoginException 异常
     */
    List<SocialUserDto> findSocialUsersByVendor(String vendor, Long yeelightUserId) throws BizException, NotLoginException;

    /**
     * 解绑三方用户
     *
     * @param token 三方token
     * @param source 三方来源
     * @throws BizException 异常
     * @throws NotLoginException 异常
     */
    void unBindSocialUser(String token, String source) throws BizException, NotLoginException;

    /**
     * 自动注册社交用户。
     *
     * @param authUser 认证用户信息。
     * @param currentLoginUserId 当前登录用户的ID。
     * @return 返回 YeelightUserDto 用户数据传输对象。
     * @throws BizException 业务异常。
     */
    YeelightUserDto autoRegisterSocialUser(AuthUser authUser, Long currentLoginUserId) throws BizException;
}
