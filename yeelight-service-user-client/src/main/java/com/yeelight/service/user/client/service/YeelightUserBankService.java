package com.yeelight.service.user.client.service;

import com.yeelight.service.user.client.domain.YeelightUserBank;
import com.yeelight.service.user.client.domain.YeelightUserBankExample;
import com.yeelight.service.framework.service.BaseService;

import java.util.List;

/**
 * Yeelight用户银行卡服务
 * <AUTHOR>
 */
public interface YeelightUserBankService extends BaseService<YeelightUserBank, YeelightUserBankExample> {
    /**
     * deleteByIds
     * @param bankCardIds bankCardIds
     */
    void deleteByIds(List<Long> bankCardIds);

}
