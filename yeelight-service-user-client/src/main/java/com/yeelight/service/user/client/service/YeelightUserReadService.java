/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.client.service
 * Description: 用户服务
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-29 09:17:09:17
 */
package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.user.client.domain.YeelightUserEntity;
import com.yeelight.service.user.client.domain.YeelightUserExample;
import com.yeelight.service.user.client.dto.MonitoringYeelightUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.query.YeelightUserQuery;

import java.util.List;
import java.util.Set;

/**
 * Desc: 用户服务
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-29 09:17:09:17
 */
public interface YeelightUserReadService {
    /**
     * 分页查询用户
     * @param query 查询条件，包含分页信息和筛选条件
     * @return PageResultSet<YeelightUserDto> 用户列表，包含分页信息和结果集
     */
    PageResultSet<YeelightUserDto> page(YeelightUserQuery query);

    /**
     * 查询所有系统用户
     * @return List<YeelightUserDto> 返回系统中所有用户的列表
     */
    List<YeelightUserDto> list();

    /**
     * 通过用户名获取用户
     * @param username 用户名
     * @return YeelightUserDto 如果找到匹配的用户，返回用户详情；否则返回null
     */
    YeelightUserDto findUserByUsername(String username);

    /**
     * 通过用户ID获取用户
     * @param id 用户的唯一标识符
     * @return YeelightUserDto 如果找到匹配的用户，返回用户详情；否则返回null
     */
    YeelightUserDto findUserById(Long id);

    /**
     * 通过用户ID列表获取用户
     * @param ids 用户ID的列表
     * @return List<YeelightUserDto> 返回与ID列表匹配的用户列表
     */
    List<YeelightUserDto> findUserByIds(List<Long> ids);

    /**
     * 通过手机号获取用户
     * @param phoneNumber 手机号码
     * @return YeelightUserDto 如果找到匹配的用户，返回用户详情；否则返回null
     */
    YeelightUserDto findUserByPhoneNumber(String phoneNumber);

    /**
     * 通过邮箱获取用户
     * @param email 邮箱地址
     * @return YeelightUserDto 如果找到匹配的用户，返回用户详情；否则返回null
     */
    YeelightUserDto findUserByEmail(String email);

    /**
     * 通过用户名/手机号/邮箱获取用户
     * @param account 用户名、手机号或邮箱地址
     * @return YeelightUserDto 如果找到匹配的用户，返回用户详情；否则返回null
     */
    YeelightUserDto findUserByAccount(String account);

    /**
     * 校验给定ID集合中是否有无效的ID
     * @param ids 用户ID的集合
     * @return Set<Long> 返回在数据库中不存在的用户ID集合
     */
    Set<Long> notValidIds(Set<Long> ids);

    /**
     * 根据条件查询用户
     * @param example 查询条件实例，包含各种筛选条件
     * @return List<YeelightUserEntity> 返回满足条件的用户实体列表
     */
    List<YeelightUserEntity> findUser(YeelightUserExample example);

    /**
     * 获取所有监控用户
     * @return List<MonitoringYeelightUserDto> 监控用户列表，包含详细信息
     */
    List<MonitoringYeelightUserDto> getAllMonitoringUser();

}
