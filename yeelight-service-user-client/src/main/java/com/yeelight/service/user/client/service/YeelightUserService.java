package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.YeelightUserEntity;
import com.yeelight.service.user.client.domain.YeelightUserExample;
import com.yeelight.service.user.client.dto.*;
import com.yeelight.service.user.client.exception.NotLoginException;
import com.yeelight.service.user.client.query.YeelightUserQuery;
import com.yeelight.service.user.client.request.CreateUserRequest;

import java.util.List;
import java.util.Set;

/**
 * @program: yeelight-service-user
 * @description: 用户服务
 * @author: Sheldon
 * @create: 2019-06-20 17:31
 **/
@Deprecated
public interface YeelightUserService {
    /**
     * 分页查询用户
     * @param query 查询条件，包含页码、每页数量等信息
     * @return PageResultSet<YeelightUserDto> 用户列表，包含当前页的用户信息和分页 metadata
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#page(YeelightUserQuery)}方法
     */
    @Deprecated
    PageResultSet<YeelightUserDto> page(YeelightUserQuery query);

    /**
     * 创建用户
     *
     * @param yeelightUserDto 用户信息对象，包含创建用户所需的所有信息
     * @return Result 操作结果，成功或失败
     * @throws BizException 业务异常，如果创建过程中遇到问题
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#create(CreateUserRequest)}方法
     */
    @Deprecated
    Result insert(YeelightUserDto yeelightUserDto) throws BizException;

    /**
     * 修改用户信息
     *
     * @param yeelightUserDto 用户信息对象，包含需要修改的用户信息
     * @return Result 操作结果，成功或失败
     * @throws BizException 业务异常，如果修改过程中遇到问题
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#updateUsername(Long, String)}、{@link YeelightUserWriteService#updatePhoneNumber(Long, String)}, {@link YeelightUserWriteService#updateEmail(Long, String)} ，{@link YeelightUserWriteService#updateName(Long, String)}, {@link YeelightUserWriteService#updateAvatar(Long, String)}, {@link YeelightUserWriteService#updateUsername(Long, String)} 方法替代
     */
    @Deprecated
    Result update(YeelightUserDto yeelightUserDto) throws BizException;

    /**
     * 修改用户信息(抛异常)
     * 与 update 方法相似，但当遇到特定业务异常时抛出而不是返回错误结果
     *
     * @param yeelightUserDto 用户信息对象，包含需要修改的用户信息
     * @throws BizException 业务异常，如果修改过程中遇到特定问题
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#updateUsername(Long, String)}、{@link YeelightUserWriteService#updatePhoneNumber(Long, String)}, {@link YeelightUserWriteService#updateEmail(Long, String)} ，{@link YeelightUserWriteService#updateName(Long, String)}, {@link YeelightUserWriteService#updateAvatar(Long, String)}, {@link YeelightUserWriteService#updateUsername(Long, String)} 方法替代
     */
    @Deprecated
    void updateThrows(YeelightUserDto yeelightUserDto) throws BizException;

    /**
     * 查询系统用户
     *
     * @return List<YeelightUserDto> 系统中所有用户的列表
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#list()}方法
     */
    @Deprecated
    List<YeelightUserDto> list();

    /**
     * 重置用户密码
     * 为指定ID的用户重置密码，通常用于管理员操作或用户忘记密码的情况
     *
     * @param id 用户ID，指定需要重置密码的用户
     * @throws BizException 业务异常，如果重置密码过程中遇到问题
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#resetPassword(Long)}方法
     */
    @Deprecated
    void doResetPassword(Long id) throws BizException;


    /**
     * 禁用用户
     *
     * @param id 用户的唯一标识符
     * @return 返回更新后的用户信息
     * @throws BizException 业务操作异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#disable(Long)}方法
     */
    @Deprecated
    YeelightUserDto doDisable(Long id) throws BizException;

    /**
     * 启用用户
     *
     * @param id 用户的唯一标识符
     * @return 返回更新后的用户信息
     * @throws BizException 业务操作异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#enable(Long)}方法
     */
    @Deprecated
    YeelightUserDto doEnable(Long id) throws BizException;


    /**
     * 锁定用户
     *
     * @param id 用户的唯一标识符
     * @return 返回更新后的用户信息
     * @throws BizException 业务操作异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#lock(Long)}方法
     */
    @Deprecated
    YeelightUserDto doLock(Long id) throws BizException;

    /**
     * 解锁账号
     *
     * @param id 用户的唯一标识符
     * @return 返回更新后的用户信息
     * @throws BizException 业务操作异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#unlock(Long)}方法
     */
    @Deprecated
    YeelightUserDto doUnlock(Long id) throws BizException;

    /**
     * 修改密码
     * 需要提供用户ID、旧密码和新密码
     * @param id 用户的ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @throws BizException 业务异常，可能因为旧密码不匹配或其他业务规则导致
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#updatePassword(Long, String)}方法
     */
    @Deprecated
    void updatePassword(Long id, String oldPassword, String newPassword) throws BizException;

    /**
     * 修改密码
     * 仅需要提供用户ID和新密码，通常用于忘记密码的场景，通过邮件或短信验证后重设密码
     * @param id 用户的ID
     * @param newPassword 新密码
     * @throws BizException 业务异常，可能因为密码强度不达标或其他业务规则导致
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#updatePassword(Long, String)}方法
     */
    @Deprecated
    void updatePassword(Long id, String newPassword) throws BizException;

    /**
     * 通过用户名获取用户
     * 用于验证登录或根据用户名查询用户信息
     * @param username 用户的用户名
     * @return 返回匹配的用户信息，如果没有找到返回null
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUserByUsername(String)}方法
     */
    @Deprecated
    YeelightUserDto findUserByUsername(String username);

    /**
     * 通过用户ID获取用户
     * 常用于需要根据用户ID查询用户详细信息的场景
     * @param id 用户的ID
     * @return 返回匹配的用户信息，如果没有找到返回null
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUserById(Long)}方法
     */
    @Deprecated
    YeelightUserDto findUserById(Long id);

    /**
     * 通过用户ID列表获取用户列表
     * 用于批量查询用户信息，例如管理员查看多个用户的信息
     * @param ids 用户ID的列表
     * @return 返回对应ID列表的用户信息列表
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUserByIds(List)}方法
     */
    @Deprecated
    List<YeelightUserDto> findUserByIds(List<Long> ids);

    /**
     * 通过手机号获取用户
     * 用于手机登录或验证手机绑定
     * @param phoneNumber 手机号码
     * @return 返回匹配的用户信息，如果没有找到返回null
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUserByPhoneNumber(String)}方法
     */
    @Deprecated
    YeelightUserDto findUserByPhoneNumber(String phoneNumber);

    /**
     * 通过邮箱获取用户
     * 用于邮箱登录或验证邮箱绑定
     * @param email 邮箱地址
     * @return 返回匹配的用户信息，如果没有找到返回null
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUserByEmail(String)}方法
     */
    @Deprecated
    YeelightUserDto findUserByEmail(String email);

    /**
     * 通过用户名/手机号/邮箱获取用户
     * 提供灵活的用户查找方式，用于用户登录验证或其他需要根据账户信息查找用户场景
     * @param account 用户的用户名、手机号或邮箱
     * @return 返回匹配的用户信息，如果没有找到返回null
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUserByAccount(String)}方法
     */
    @Deprecated
    YeelightUserDto findUserByAccount(String account);

    /**
     * 删除用户。
     *
     * @param recordId 用户记录的ID
     * @return 删除操作影响的记录数
     * @throws BizException 业务异常，如果删除过程中出现错误
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#removeUserByUserId(Long)} 方法
     */
    @Deprecated
    int deleteUser(Long recordId) throws BizException;

    /**
     * 如果手机号对应的用户不存在，则通过手机号和密码创建新用户。
     *
     * @param phone 用户的手机号
     * @param password 用户的密码
     * @return 创建的用户信息
     * @throws BizException 业务异常，如果创建过程中出现错误
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#getUserIdOrCreateByPhoneOrEmail(String, String)} 方法
     */
    @Deprecated
    YeelightUserDto createUserByPhoneIfNotExist(String phone, String password) throws BizException;

    /**
     * 如果给定的用户信息不存在，则创建新用户。
     *
     * @param userDto 用户信息数据传输对象
     * @return 创建的用户信息
     * @throws BizException 业务异常，如果创建过程中出现错误
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#getUserIdOrCreateByPhoneOrEmail(String, String)} 方法
     */
    @Deprecated
    YeelightUserDto createUserIfAbsent(YeelightUserDto userDto) throws BizException;

    /**
     * 检查给定ID集合的有效性，返回无效的ID集合。
     *
     * @param ids 待检查的ID集合
     * @return 无效的ID集合
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#notValidIds(Set)} 方法
     */
    @Deprecated
    Set<Long> notValidIds(Set<Long> ids);

    /**
     * 设置指定用户下的所有token过期。
     *
     * @param username 用户名
     * @return 操作结果，成功为true，失败为false
     * @deprecated 该方法已过时，建议使用 {@link TokenService#revokeTokenByUserName(String)} 方法
     */
    @Deprecated
    boolean revokeTokenByUserName(String username);

    /**
     * 通过用户名登出用户。
     *
     * @param username 用户名
     * @return 操作结果，成功为true，失败为false
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#logoutByUserName(String)} 方法
     */
    @Deprecated
    boolean logoutByUserName(String username);

    /**
     * 登出当前Token，删除与之关联的用户信息。
     *
     * @param token 当前的Token
     * @return 操作结果，成功为true，失败为false
     * @deprecated 该方法已过时，建议使用 {@link TokenService#logoutCurrentToken(String)} 方法
     */
    @Deprecated
    boolean logoutCurrentToken(String token);

    /**
     * 登出当前应用，删除该应用下用户的所有token。
     *
     * @param token 当前应用的Token
     * @return 操作结果，成功为true，失败为false
     * @deprecated 该方法已过时，建议使用 {@link TokenService#logoutCurrentApplication(String)} 方法
     */
    @Deprecated
    boolean logoutCurrentApplication(String token);

    /**
     * 登出当前用户，删除该用户下的所有token。
     *
     * @param token 当前用户的Token
     * @return 操作结果，成功为true，失败为false
     * @deprecated 该方法已过时，建议使用 {@link TokenService#logoutCurrentUser(String)} 方法
     */
    @Deprecated
    boolean logoutCurrentUser(String token);


    /**
     * 注销用户
     * @param userName 需要注销的用户名
     * @return 返回一个布尔值，表示用户是否成功注销
     * @throws BizException 如果注销过程中出现业务错误，则抛出BizException
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#removeUserByUserName(String)} 方法
     */
    @Deprecated
    boolean removeUser(String userName) throws BizException;

    /**
     * 更新用户名
     * <p>
     * 本方法用于更新用户表中的用户名，同时写入用户扩展表，并更新社交表信息。
     *
     * @param userDto 包含新用户名等信息的用户数据传输对象
     * @param user 包含旧用户名等信息的用户数据传输对象
     * @throws BizException 如果更新过程中出现业务错误，则抛出BizException
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserWriteService#updateUsername(Long, String)} 方法
     */
    @Deprecated
    void updateUsername(YeelightUserDto userDto, YeelightUserDto user)  throws BizException;

    /**
     * 根据条件查询用户
     * @param example 包含查询条件的用户示例对象
     * @return 返回一个用户实体列表，符合查询条件的所有用户都将被包含在列表中
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#findUser(YeelightUserExample)} 方法
     */
    @Deprecated
    List<YeelightUserEntity> findUser(YeelightUserExample example);

    /**
     * 自动注册社交用户
     * <p>
     * 本方法用于自动注册来自社交平台的用户。如果用户已存在，则进行登录；否则，创建新用户。
     *
     * @param authUser 包含社交平台认证信息的用户对象
     * @param currentLoginUserId 当前登录用户的ID，如果有的话
     * @return 返回一个用户数据传输对象，包含注册或登录后的用户信息
     * @throws BizException 如果注册过程中出现业务错误，则抛出BizException
     * <AUTHOR> Yu
     * @date 9/23/21 10:27 AM
     * @deprecated 该方法已过时，不再开放使用
     */
    @Deprecated
    YeelightUserDto autoRegisterSocialUser(AuthUser authUser, Long currentLoginUserId) throws BizException;

    /**
     * 更新社交用户token信息
     * @param socialUserId 社交用户的ID
     * @param authToken 包含新token信息的认证令牌对象
     * @throws BizException 如果更新过程中出现业务错误，则抛出BizException
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#updateSocialUserTokenInfo(String, String, AuthToken)} 方法
     */
    @Deprecated
    void updateSocialUserTokenInfo(Long socialUserId, AuthToken authToken) throws BizException;

    /**
     * 更新社交用户token信息
     * <p>
     * 通过社交平台标识和用户UUID更新用户的token信息。
     *
     * @param source 社交平台的标识（例如：github, facebook等）
     * @param uuid 用户在社交平台的UUID
     * @param authToken 包含新token信息的认证令牌对象
     * @throws BizException 如果更新过程中出现业务错误，则抛出BizException
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#updateSocialUserTokenInfo(String, String, AuthToken)} 方法
     */
    @Deprecated
    void updateSocialUserTokenInfo(String source, String uuid, AuthToken authToken) throws BizException;

    /**
     * 根据源和yeelightId查找社交用户信息。
     * @param yeelightId yeelight用户ID
     * @param source 用户来源标识
     * @return SocialUserDto 对应的社交用户数据传输对象
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUserBySource(Long, String)} 方法
     */
    @Deprecated
    SocialUserDto findSocialUserBySource(Long yeelightId, String source);

    /**
     * 根据源和uuid查找社交用户信息。
     * @param uuid 用户的唯一标识
     * @param source 用户来源标识
     * @return SocialUserDto 对应的社交用户数据传输对象
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUserBySourceAndUuid(String, String)} 方法
     */
    @Deprecated
    SocialUserDto findSocialUserBySourceAndUuid(String uuid, String source);

    /**
     * 根据yeelightId查找所有社交用户信息。
     * @param yeelightId yeelight用户ID
     * @return List<SocialUserDto> 对应的社交用户列表
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUsers(Long)} 方法
     */
    @Deprecated
    List<SocialUserDto> findSocialUsers(Long yeelightId);

    /**
     * 解除指定源的社交用户绑定。
     * @param yeelightId yeelight用户ID
     * @param source 用户来源标识
     * @throws BizException 业务异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#unBindSocialUser(Long, String)} 方法
     */
    @Deprecated
    void unBindSocialUser(Long yeelightId, String source) throws BizException;

    /**
     * 根据token和源查找社交用户信息。
     * @param token 用户的登录令牌
     * @param source 用户来源标识
     * @return SocialUserDto 对应的社交用户数据传输对象
     * @throws BizException 业务异常
     * @throws NotLoginException 用户未登录异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUserBySource(String, String)} 方法
     */
    @Deprecated
    SocialUserDto findSocialUserBySource(String token, String source) throws BizException, NotLoginException;

    /**
     * 根据token查找所有社交用户信息。
     * @param token 用户的登录令牌
     * @return List<SocialUserDto> 对应的社交用户列表
     * @throws BizException 业务异常
     * @throws NotLoginException 用户未登录异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUsers(String)} 方法
     */
    @Deprecated
    List<SocialUserDto> findSocialUsers(String token) throws BizException, NotLoginException;

    /**
     * 根据供应商和token查找社交用户信息。
     * @param vendor 供应商标识
     * @param token 用户的登录令牌
     * @return List<SocialUserDto> 对应的社交用户列表
     * @throws BizException 业务异常
     * @throws NotLoginException 用户未登录异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUsersByVendor(String, String)} 方法
     */
    @Deprecated
    List<SocialUserDto> findSocialUsersByVendor(String vendor, String token) throws BizException, NotLoginException;

    /**
     * 根据供应商和yeelight用户ID查找社交用户信息。
     * @param vendor 供应商标识
     * @param yeelightUserId yeelight用户ID
     * @return List<SocialUserDto> 对应的社交用户列表
     * @throws BizException 业务异常
     * @throws NotLoginException 用户未登录异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#findSocialUsersByVendor(String, Long)} 方法
     */
    @Deprecated
    List<SocialUserDto> findSocialUsersByVendor(String vendor, Long yeelightUserId) throws BizException, NotLoginException;

    /**
     * 根据token和源解除社交用户绑定。
     * @param token 用户的登录令牌
     * @param source 用户来源标识
     * @throws BizException 业务异常
     * @throws NotLoginException 用户未登录异常
     * @deprecated 该方法已过时，建议使用 {@link YeelightSocialUserService#unBindSocialUser(String, String)} 方法
     */
    @Deprecated
    void unBindSocialUser(String token, String source) throws BizException, NotLoginException;

    /**
     * 获取所有监控用户
     * <p>
     * 本函数不需要接受任何参数，它将返回一个包含所有监控用户信息的列表。
     * </p>
     *
     * @return List<MonitoringYeelightUserDto> 监控用户列表，列表中的每个元素都是一个包含监控用户详细信息的数据传输对象（DTO）
     * @deprecated 该方法已过时，建议使用 {@link YeelightUserReadService#getAllMonitoringUser()} 方法
     */
    @Deprecated
    List<MonitoringYeelightUserDto> getAllMonitoringUser();
}
