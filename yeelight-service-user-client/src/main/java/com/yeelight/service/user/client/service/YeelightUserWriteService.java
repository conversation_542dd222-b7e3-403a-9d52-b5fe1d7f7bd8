/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.client.service
 * Description: 用户服务
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-29 09:17:09:17
 */
package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.request.CreateUserRequest;

/**
 * Desc: 用户服务
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-29 09:17:09:17
 */
public interface YeelightUserWriteService {
    /**
     * 创建用户
     *
     * @param request 包含用户信息的请求对象
     * @return 新创建的用户的ID
     * @throws BizException 如果创建用户过程中出现业务错误，则抛出BizException
     */
    Long create(CreateUserRequest request) throws BizException;

    /**
     * 根据手机号/邮箱查询用户，如果查询不到则创建新用户
     *
     * @param phoneOrEmail 用户的手机号/邮箱
     * @param password 用户的密码
     * @return 用户的ID
     * @throws BizException 如果查询或创建用户过程中出现业务错误，则抛出BizException
     */
    Long getUserIdOrCreateByPhoneOrEmail(String phoneOrEmail, String password) throws BizException;

    /**
     * 更新用户的用户名
     *
     * @param userId 用户的ID
     * @param userName 新的用户名
     * @throws BizException 如果更新用户名过程中出现业务错误，则抛出BizException
     */
    void updateUsername(Long userId, String userName) throws BizException;

    /**
     * 更新用户的手机号
     *
     * @param userId 用户的ID
     * @param phoneNumber 新的手机号
     * @throws BizException 如果更新手机号过程中出现业务错误，则抛出BizException
     */
    void updatePhoneNumber(Long userId, String phoneNumber) throws BizException;

    /**
     * 更新用户的邮箱地址
     *
     * @param userId 用户的ID
     * @param email 新的邮箱地址
     * @throws BizException 如果更新邮箱地址过程中出现业务错误，则抛出BizException
     */
    void updateEmail(Long userId, String email) throws BizException;

    /**
     * 更新用户的昵称
     *
     * @param userId 用户的ID
     * @param name 新的昵称
     * @throws BizException 如果更新昵称过程中出现业务错误，则抛出BizException
     */
    void updateName(Long userId, String name) throws BizException;

    /**
     * 更新用户的头像URL
     *
     * @param userId 用户的ID
     * @param avatar 新的头像URL
     * @throws BizException 如果更新头像过程中出现业务错误，则抛出BizException
     */
    void updateAvatar(Long userId, String avatar) throws BizException;


    /**
     * 重置用户密码。此操作将直接设置一个新的密码，不需要提供旧密码。
     *
     * @param id 用户ID，用于标识需要重置密码的用户。
     * @throws BizException 如果操作失败，比如用户ID不存在。
     */
    void resetPassword(Long id) throws BizException;

    /**
     * 修改密码。此操作需要提供旧密码以验证身份，然后设置新密码。
     *
     * @param id 用户ID，用于标识需要修改密码的用户。
     * @param oldPassword 旧密码，用于验证用户身份。
     * @param newPassword 新密码，将替换掉旧密码。
     * @throws BizException 如果操作失败，比如用户ID不存在，或者旧密码不正确。
     */
    void updatePassword(Long id, String oldPassword, String newPassword) throws BizException;

    /**
     * 修改密码（简化版）。此操作直接设置新密码，不需要提供旧密码。可能用于忘记旧密码的情况。
     *
     * @param id 用户ID，用于标识需要修改密码的用户。
     * @param newPassword 新密码，将直接替换掉旧密码。
     * @throws BizException 如果操作失败，比如用户ID不存在。
     */
    void updatePassword(Long id, String newPassword) throws BizException;

    /**
     * 禁用用户。禁用后的用户将无法登录。
     *
     * @param id 用户ID，用于标识需要被禁用的用户。
     * @throws BizException 如果操作失败，比如用户ID不存在。
     */
    void disable(Long id) throws BizException;

    /**
     * 启用用户。启用后的用户可以正常登录。
     *
     * @param id 用户ID，用于标识需要被启用的用户。
     * @throws BizException 如果操作失败，比如用户ID不存在。
     */
    void enable(Long id) throws BizException;

    /**
     * 锁定用户。锁定后的用户无法登录，通常用于多次尝试登录失败的情况。
     *
     * @param id 用户ID，用于标识需要被锁定的用户。
     * @throws BizException 如果操作失败，比如用户ID不存在。
     */
    void lock(Long id) throws BizException;

    /**
     * 解锁用户账号。被锁定的用户可以通过此操作解锁，恢复登录能力。
     *
     * @param id 用户ID，用于标识需要被解锁的用户。
     * @throws BizException 如果操作失败，比如用户ID不存在。
     */
    void unlock(Long id) throws BizException;

    /**
     * 登出用户
     *
     * @param username 用户名，用于标识需要登出的用户
     * @return boolean 返回登出操作是否成功
     */
    boolean logoutByUserName(String username);

    /**
     * 按照用户名注销用户
     *
     * @param userName 需要注销的用户的用户名
     * @throws BizException 业务异常，如果注销过程中遇到问题抛出
     */
    void removeUserByUserName(String userName) throws BizException;

    /**
     * 按照用户ID注销用户
     *
     * @param userId 需要注销的用户的ID
     * @throws BizException 业务异常，如果注销过程中遇到问题抛出
     */
    void removeUserByUserId(Long userId) throws BizException;

}
