package com.yeelight.service.user.client.service;

import com.yeelight.service.framework.service.BaseService;
import com.yeelight.service.user.client.domain.YeelightUserExtend;
import com.yeelight.service.user.client.domain.YeelightUserExtendExample;
import com.yeelight.service.user.client.dto.YeelightUserExtendDto;

import java.util.List;

/**
 * 用户扩展信息服务接口。
 * <AUTHOR>
 */
public interface YeelightUsersExtendService extends BaseService<YeelightUserExtend, YeelightUserExtendExample> {
    /**
     * 根据示例查询所有匹配的用户扩展信息列表。
     *
     * @param example 作为查询条件的示例对象，包含需要匹配的字段及其条件。
     * @return 返回匹配条件的用户扩展信息列表，列表类型为 {@link List<YeelightUserExtendDto>}。
     */
    List<YeelightUserExtendDto> selectByExample(YeelightUserExtendDto example);

    /**
     * 根据示例查询一个匹配的用户扩展信息。
     *
     * @param example 作为查询条件的示例对象，包含需要匹配的字段及其条件。
     * @return 返回匹配条件的第一个用户扩展信息对象，类型为 {@link YeelightUserExtendDto}。
     */
    YeelightUserExtendDto selectOneByExample(YeelightUserExtendExample example);

    /**
     * 插入一个新的用户扩展信息。
     *
     * @param userExtendDto 需要插入的用户扩展信息对象。
     */
    void insert(YeelightUserExtendDto userExtendDto);

    /**
     * 根据unionId更新用户扩展信息。
     *
     * @param extend 需要更新的用户扩展信息对象。
     * @param unionId 用户的unionId，用于指定更新哪一个用户的信息。
     * @return 返回void，方法执行结果无返回值。
     */
    Void updateByUnionId(YeelightUserExtend extend, Long unionId);


}
