package com.yeelight.service.user.client.token;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * 邮箱验证码认证用到的 Authentication，封装登录信息。 认证前，放入邮箱；认证成功之后，放入用户信息。
 * <p>
 * 参考 {@link UsernamePasswordAuthenticationToken}
 *
 * <AUTHOR>
 */
public class EmailCodeAuthenticationToken extends AbstractAuthenticationToken {

    /**
     * 邮箱
     */
    private final Object principal;

    public EmailCodeAuthenticationToken(Object principal) {
        super(null);
        this.principal = principal;
        setAuthenticated(false);
    }

    public EmailCodeAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    /**
     * 设置认证状态。
     * 该方法重写了父类的setAuthenticated方法，用于禁止将认证状态设置为信任（true）。
     * 如果尝试将认证状态设置为true，将会抛出IllegalArgumentException异常。
     *
     * @param isAuthenticated 指定是否认证通过。此处只能传入false，若传入true将抛出异常。
     * @throws IllegalArgumentException 如果传入参数为true，则抛出此异常，提示应使用带有GrantedAuthority列表的构造函数。
     */
    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        // 当尝试设置认证状态为true时，抛出异常
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                            "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }
        // 禁止设置为认证通过，始终设置为未认证
        super.setAuthenticated(false);
    }

    /**
     * 清除凭据信息。
     * 该方法重写了父类的eraseCredentials方法，用于清除当前对象的凭据信息。
     * 无参数。
     * 无返回值。
     */
    @Override
    public void eraseCredentials() {
        // 调用父类的eraseCredentials方法，确保凭据被正确清除
        super.eraseCredentials();
    }
}
