package com.yeelight.service.user.client.token;

import lombok.Getter;
import lombok.Setter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class GoogleCodeAuthenticationToken extends AbstractAuthenticationToken {

    private final Object principal;

    private final Object credentials;

    /**
     * Google二次验证生成的code
     */
    @Setter
    @Getter
    private String code;

    public GoogleCodeAuthenticationToken(Object principal, Object credentials) {
        super(null);
        this.principal = principal;
        this.credentials = credentials;
        this.setAuthenticated(false);
    }

    public GoogleCodeAuthenticationToken(Object principal, Object credentials, String code) {
        super(null);
        this.principal = principal;
        this.credentials = credentials;
        this.code = code;
        setAuthenticated(false);
    }

    public GoogleCodeAuthenticationToken(Object principal, Object credentials, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.credentials = credentials;
        super.setAuthenticated(true);
    }

    public GoogleCodeAuthenticationToken(Object principal, Object credentials, String code, Collection<? extends GrantedAuthority> authorities) {
        this(principal, credentials, authorities);
        this.code = code;
    }

    @Override
    public Object getCredentials() {
        return this.credentials;
    }

    @Override
    public Object getPrincipal() {
        return this.principal;
    }
}
