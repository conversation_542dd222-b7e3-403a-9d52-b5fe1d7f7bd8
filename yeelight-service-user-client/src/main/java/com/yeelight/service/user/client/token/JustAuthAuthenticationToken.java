package com.yeelight.service.user.client.token;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * 社会化登录认证用到的 Authentication，封装登录信息。
 * 认证前，放入社会化登录用户信息；认证成功之后，放入用户信息。
 *
 * <AUTHOR>
 * @date 9/13/21 3:22 PM
 */
public class JustAuthAuthenticationToken extends AbstractAuthenticationToken {
    private static final long serialVersionUID = -8516955579929308983L;

    /**
     * JustAuth user information
     */
    private final Object principal;

    private final Object credentials;

    /**
     * After AuthAuthentication
     */
    public JustAuthAuthenticationToken(Object authUser) {
        super(null);
        this.principal = authUser;
        this.credentials = authUser;
        this.setAuthenticated(false);
    }

    /**
     * Before AuthAuthentication
     */
    public JustAuthAuthenticationToken(Object principal, Object credentials, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.principal = principal;
        this.credentials = principal;
        this.setDetails(credentials);
        super.setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return this.credentials;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        if (isAuthenticated) {
            throw new IllegalArgumentException(
                    "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        }

        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
    }
}
