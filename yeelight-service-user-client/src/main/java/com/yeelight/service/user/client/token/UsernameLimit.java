package com.yeelight.service.user.client.token;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * @program: yeelight-oauth-api
 * @description: 用户登录数量限制类
 * @author: Sheldon
 * @create: 2020-02-11 17:51
 **/
@Data
public class UsernameLimit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用于身份验证的访问密钥
     */
    private String accessKey;

    /**
     * 身份验证密钥
     */
    private String authKey;

    /**
     * 将身份验证映射到访问密钥的键
     */
    private String authToAccessKey;

    /**
     * 授权批准的密钥
     */
    private String approvalKey;

    /**
     * 客户端标识符
     */
    private String clientId;

    /**
     * 用于身份验证的用户名键
     */
    private String usernameKey;

    /**
     * 将刷新令牌映射到访问令牌的键
     */
    private String refreshToAccessKey;

    /**
     * 将访问令牌映射到刷新令牌的键
     */
    private String accessToRefreshKey;

    /**
     * 刷新令牌
     */
    private String refreshKey;

    /**
     * 刷新令牌的身份验证密钥
     */
    private String refreshAuthKey;

    public boolean isSameToken(UsernameLimit other) {
        if (other == null) return false;
        return Objects.equals(accessKey, other.accessKey) &&
                Objects.equals(authKey, other.authKey) &&
                Objects.equals(authToAccessKey, other.authToAccessKey) &&
                Objects.equals(refreshKey, other.refreshKey) &&
                Objects.equals(refreshAuthKey, other.refreshAuthKey) &&
                Objects.equals(refreshToAccessKey, other.refreshToAccessKey) &&
                Objects.equals(accessToRefreshKey, other.accessToRefreshKey);
    }

}
