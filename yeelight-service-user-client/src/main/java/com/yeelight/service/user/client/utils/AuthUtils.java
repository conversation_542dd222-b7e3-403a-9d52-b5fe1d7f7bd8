package com.yeelight.service.user.client.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeelight.service.framework.util.IPUtils;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.ResultCodeEnum;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.constant.UserConstant;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * @program: yeelight-service-user
 * @description: 认证工具类
 * @author: lixia<PERSON>ong
 * @create: 2023-11-22 15:49
 **/
public class AuthUtils {
    public static final String SPRING_SECURITY_CONTEXT_KEY = "SPRING_SECURITY_CONTEXT";

    /**
     * 处理认证失败的响应。
     * @param response 用于设置响应状态码和响应体的HttpServletResponse对象。
     * @throws IOException 当设置响应体时发生IO异常。
     */
    public static void authFail(HttpServletResponse response) throws IOException {
        authFail(response, Integer.valueOf(ResultCodeEnum.UNAUTHORIZED.getCode()), ResultCodeEnum.UNAUTHORIZED.getMsg());
    }

    /**
     * 处理认证失败的响应，允许自定义错误信息。
     * @param response 用于设置响应状态码和响应体的HttpServletResponse对象。
     * @param msg 错误信息。
     * @throws IOException 当设置响应体时发生IO异常。
     */
    public static void authFail(HttpServletResponse response, String msg) throws IOException {
        authFail(response, Integer.valueOf(ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode()), msg, null);
    }

    /**
     * 处理认证失败的响应，允许自定义错误码和错误信息。
     * @param response 用于设置响应状态码和响应体的HttpServletResponse对象。
     * @param code 错误码。
     * @param msg 错误信息。
     * @throws IOException 当设置响应体时发生IO异常。
     */
    public static void authFail(HttpServletResponse response, Integer code, String msg) throws IOException {
        authFail(response, code, msg, null);
    }

    /**
     * 处理认证失败的响应，允许自定义错误信息和返回数据。
     * @param response 用于设置响应状态码和响应体的HttpServletResponse对象。
     * @param msg 错误信息。
     * @param data 返回的数据。
     * @throws IOException 当设置响应体时发生IO异常。
     */
    public static void authFail(HttpServletResponse response, String msg, Object data) throws IOException {
        authFail(response, Integer.valueOf(ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode()), msg, data);
    }

    /**
     * 处理认证失败的响应，允许自定义错误码、错误信息和返回数据。
     * @param response 用于设置响应状态码和响应体的HttpServletResponse对象。
     * @param code 错误码。
     * @param msg 错误信息。
     * @param data 返回的数据。
     * @throws IOException 当设置响应体时发生IO异常。
     */
    public static void authFail(HttpServletResponse response, Integer code, String msg, Object data) throws IOException {
        authFail(response, HttpServletResponse.SC_BAD_REQUEST, code, msg, data);
    }


    /**
     * 处理认证失败的响应。
     *
     * @param response 用于设置HTTP状态码、字符编码和内容类型的响应对象。
     * @param httpCode HTTP响应状态码。
     * @param code 业务错误码，如果为null，则默认为内部服务器错误码。
     * @param msg 错误信息。
     * @param data 返回给客户端的数据。
     * @throws IOException 如果在写入响应时发生IO错误。
     */
    public static void authFail(HttpServletResponse response, int httpCode, Integer code, String msg, Object data) throws IOException {
        // 设置响应状态码、字符编码和内容类型
        response.setStatus(httpCode);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        PrintWriter writer = response.getWriter();
        // 如果业务错误码为null，则默认为内部服务器错误码
        if (Objects.isNull(code)) {
            code = Integer.valueOf(ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode());
        }
        // 构建失败的结果对象
        Result result = Result.failure(String.valueOf(code), msg);
        result.setData(data);

        // 将结果对象转换为JSON字符串并写入响应体
        ObjectMapper mapper = new ObjectMapper();
        writer.println(mapper.writeValueAsString(result));

        // 刷新并关闭写入器
        writer.flush();
        writer.close();
    }

    /**
     * 认证成功响应的处理方法。
     * 该方法将设置HTTP响应状态为200（OK），并以JSON格式返回成功的消息。
     *
     * @param response 用于设置HTTP响应的HttpServletResponse对象。
     * @throws IOException 如果在写入响应时发生IO异常。
     */
    public static void authSuccess(HttpServletResponse response) throws IOException {
        authSuccess(response, ResultCodeEnum.OK.getMsg());
    }

    /**
     * 认证成功响应的处理方法，允许自定义返回消息。
     * 该方法将设置HTTP响应状态为200（OK），并以JSON格式返回自定义的成功消息。
     *
     * @param response 用于设置HTTP响应的HttpServletResponse对象。
     * @param msg 成功时要返回的消息对象。
     * @throws IOException 如果在写入响应时发生IO异常。
     */
    public static void authSuccess(HttpServletResponse response, Object msg) throws IOException {
        // 设置响应状态为HTTP 200 OK
        response.setStatus(HttpServletResponse.SC_OK);
        // 设置响应的字符编码和内容类型为JSON
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        // 设置响应的内容类型为JSON
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);

        // 获取响应的 PrintWriter 对象用于写入响应体
        PrintWriter writer = response.getWriter();

        // 创建表示成功的Result对象，封装msg
        Result<?> result = Result.success(msg);

        // 使用ObjectMapper将Result对象转换为JSON字符串
        ObjectMapper mapper = new ObjectMapper();
        writer.println(mapper.writeValueAsString(result));

        // 刷新并关闭Writer
        writer.flush();
        writer.close();
    }

    /**
     * 检查请求是否为前端与后端分离的请求。
     * 这个方法通过检查请求头中的 "x-requested-with" 字段来判断请求是否来自 XMLHttpRequest，
     * 从而判断前端和后端是否分离。
     *
     * @param request HttpServletRequest对象，代表一个HTTP请求。
     * @return boolean 返回true如果请求是前端与后端分离的（即请求头中 "x-requested-with" 字段的值为 "XMLHttpRequest"），
     *                 否则返回false。
     */
    public static boolean isFrontendBackendSeparatedRequest(HttpServletRequest request) {
        // 从请求头中获取 "x-requested-with" 字段的值
        String xRequestedWith = request.getHeader("x-requested-with");
        // 判断字段值是否为 "XMLHttpRequest"
        return "XMLHttpRequest".equals(xRequestedWith);
    }

    /**
     * 记录用户登录环境信息到Session中。
     * 该方法会捕获用户的User-Agent、服务器IP地址、用户的实际IP地址以及安全上下文信息，并将这些信息保存到用户的Session中。
     *
     * @param request HttpServletRequest对象，用于获取用户请求的相关信息。
     */
    public static void logUserLoginEnvironmentInfo(HttpServletRequest request) {
        if (Objects.nonNull(request.getSession())) {
            // 记录用户代理信息
            request.getSession().setAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_UA, request.getHeader("User-Agent"));

            String localAddr = request.getLocalAddr();
            if (StringUtils.isNotBlank(localAddr)) {
                // 记录服务器IP地址，放弃使用服务器名称以提高性能
                request.getSession().setAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_SERVER_IP, localAddr);
                // 耗时操作，放弃使用服务器名称以提高性能
                //request.getSession().setAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_SERVER_NAME, request.getLocalName());
                request.getSession().setAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_SERVER_NAME, localAddr);
            }

            // 记录用户实际IP地址
            request.getSession().setAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_IP, IPUtils.getRealIp(request));

            // 记录Spring Security上下文
            request.getSession().setAttribute(SPRING_SECURITY_CONTEXT_KEY, SecurityContextHolder.getContext());
        }
    }
}
