package com.yeelight.service.user.client.utils;

import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeSet;

/**
 * Yeelight自建认证密钥生成器
 * 替代CustomAuthenticationKeyGenerator，使用自建OAuth2对象
 * 保持与原版完全相同的逻辑和行为
 * 
 * <AUTHOR>
 * @since 2025-01
 */
public class CustomAuthenticationKeyGenerator {
    private static final String USERNAME = "username";
    private static final String DEVICE = "device";
    private static final String SCOPE = "scope";

    /**
     * 从OAuth2认证信息中提取关键信息生成键
     * @param authentication OAuth2认证信息
     * @return 生成的键，包含了用户名、授权范围和设备信息（如果存在）
     */
    public String extractKey(YeelightOAuth2Authentication authentication) {
        Map<String, String> values = new LinkedHashMap<>();
        YeelightOAuth2Request authorizationRequest = authentication.getOAuth2Request();

        // 如果认证信息中不仅包含客户端信息，还包含用户信息，则添加用户名
        if (!authentication.isClientOnly()) {
            values.put(USERNAME, authentication.getName());
        }

        // 如果授权请求中包含范围，则添加范围信息
        if (authorizationRequest.getScope() != null && !authorizationRequest.getScope().isEmpty()) {
            values.put(SCOPE, formatParameterList(new TreeSet<>(authorizationRequest.getScope())));
        }

        // 获取设备信息，并且如果该信息不为空，则添加到值映射中
        String device = authorizationRequest.getRequestParameters().get(DEVICE);
        if (StringUtils.isNotBlank(device)) {
            values.put(DEVICE, device);
        }

        // 根据收集到的值生成并返回键
        return generateKey(values);
    }

    /**
     * 格式化参数列表
     * 替代OAuth2Utils.formatParameterList方法
     * @param parameters 参数集合
     * @return 格式化后的字符串
     */
    private String formatParameterList(TreeSet<String> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return "";
        }
        return String.join(" ", parameters);
    }

    /**
     * 生成密钥
     * 替代原版的generateKey方法
     * @param values 值映射
     * @return 生成的密钥
     */
    private String generateKey(Map<String, String> values) {
        if (values.isEmpty()) {
            return "";
        }
        
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : values.entrySet()) {
            if (!builder.isEmpty()) {
                builder.append(":");
            }
            builder.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return builder.toString();
    }

    /**
     * 创建默认实例
     * @return YeelightAuthenticationKeyGenerator实例
     */
    public static CustomAuthenticationKeyGenerator createDefault() {
        return new CustomAuthenticationKeyGenerator();
    }
}
