package com.yeelight.service.user.client.utils;

import com.yeelight.service.framework.trace.BizTraceEntity;
import lombok.Data;

/**
 * @program: yeelight-c-api
 * @description: 用户行为统计工具类
 * @author: <PERSON>
 * @create: 2019-09-23 15:37
 **/
public class MetricUtils {

    public enum UserActionType {
        // 用户行为类型
        注册("reg"),
        登录("login"),
        绑定手机号("bind_phone"),
        第三方绑定("third_bind"),
        第三方解绑("third_unbind"),
        锁定("lock"),
        解锁("unlock"),
        重置("reset"),
        禁用("disable"),
        更新("update"),
        修改密码("update_password"),
        合并用户("merge"),
        ;

        UserActionType(String type) {
        }
    }

    public enum UserThirdParty {
        // 第三方类型
        小程序("1"),
        微信公众号("2"),
        小米("3"),
        ;

        UserThirdParty(String type) {
        }
    }

    public enum UserFromType {
        // 用户来源类型
        自动注册("1"),
        自动登录("2"),
        手机号验证码登录("3"),
        绑定小米账号("4"),
        解绑小米账号("5"),
        ;

        UserFromType(String type) {
        }
    }

    @Data
    public static class UserMetricEntry {
        private String opuname;
        private String opuid;
        private Float metric1;
        private Float metric2;
        private Float metric3;
        private String key1;
        private String key2;
        private String key3;
        private String key4;
        private String key5;
    }

    public static void metric(UserMetricEntry params) {
        BizTraceEntity.builder().
                biz_code("00070")
                .biz_type("00070")
                .client_ip("")
                .client_type("c-api")
                .key1(params.getKey1())
                .key2(params.getKey2())
                .key3(params.getKey3())
                .key4(params.getKey4())
                .key5(params.getKey5())
                .metric1(params.getMetric1())
                .metric2(params.getMetric2())
                .metric3(params.getMetric3())
                .opuid(params.getOpuid())
                .opuname(params.getOpuname())
                .timestamp(System.currentTimeMillis())
                .build()
                .trace();
    }
}
