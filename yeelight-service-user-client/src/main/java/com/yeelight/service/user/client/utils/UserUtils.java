package com.yeelight.service.user.client.utils;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.regex.Pattern;

/**
 * @program: yeelight-service-user
 * @description: 用户工具类
 * @author: <PERSON>
 * @create: 2019-06-21 15:49
 **/
public class UserUtils {
    public final static Pattern EMAIL = Pattern.compile("(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)])", Pattern.CASE_INSENSITIVE);
    /**
     * 移动电话
     */
    public final static Pattern MOBILE = Pattern.compile("(?:0|86|\\+86)?1[3-9]\\d{9}");

    public final static Pattern NUMBER = Pattern.compile("[0-9]+");

    public UserUtils() {

    }

    /**
     * 返回一个密码加密器，使用BCrypt算法。
     *
     * @return PasswordEncoder 返回一个实现了PasswordEncoder接口的BCryptPasswordEncoder实例。
     */
    public static PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 生成一个长度为64的随机字符串作为盐值。
     *
     * @return String 返回一个64位的随机字符串。
     */
    public static String saltEncoder() {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return randomStr(64, chars);
    }

    /**
     * 为用户名生成一个格式化后的字符串，用于特定的编码需求。
     *
     * @param username 用户名，将与一个随机字符串组合。
     * @return String 返回格式化后的用户名字符串，前缀为"user_"，后接12位随机字符串。
     */
    public static String usernameEncoder(String username) {
        return "user_" + randomStr(12);
    }

    /**
     * 生成一个指定长度的随机字符串。
     *
     * @param length 随机字符串的长度。
     * @return String 返回一个由小写字母和大写字母组成的指定长度的随机字符串。
     */
    public static String randomStr(int length) {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

        return RandomStringUtils.secure().next(length, chars);
    }

    /**
     * 生成一个指定长度的随机字符串，可自定义字符集。
     *
     * @param length 随机字符串的长度。
     * @param chars  用于生成随机字符串的字符集。
     * @return String 返回一个由指定字符集组成的指定长度的随机字符串。
     */
    public static String randomStr(int length, String chars) {
        return RandomStringUtils.secure().next(length, chars);
    }

    /**
     * 验证手机号码是否合法。
     *
     * @param phoneNumber 待验证的手机号码字符串。
     * @return 返回{@code true}如果手机号码合法，否则返回{@code false}。
     */
    public static boolean isPhoneNumber(String phoneNumber) {
        // 首先检查传入的字符串是否为null
        if (phoneNumber == null) {
            return false;
        }
        // 使用预定义的手机号正则表达式进行匹配
        return MOBILE.matcher(phoneNumber).matches();
    }

    /**
     * 验证电子邮件地址是否合法。
     *
     * @param email 待验证的电子邮件地址字符串。
     * @return 返回{@code true}如果电子邮件地址合法，否则返回{@code false}。
     */
    public static boolean isEmail(String email) {
        // 首先检查传入的字符串是否为null
        if (email == null) {
            return false;
        }
        // 使用预定义的电子邮件正则表达式进行匹配
        return EMAIL.matcher(email).matches();
    }

    /**
     * 验证数字字符串是否合法。
     *
     * @param number 待验证的数字字符串。
     * @return 返回{@code true}如果字符串全为数字，否则返回{@code false}。
     */
    public static boolean isNumber(String number) {
        // 首先检查传入的字符串是否为null
        if (number == null) {
            return false;
        }
        // 使用预定义的数字正则表达式进行匹配
        return NUMBER.matcher(number).matches();
    }
}
