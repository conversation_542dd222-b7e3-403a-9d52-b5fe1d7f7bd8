package com.yeelight.service.user.client.utils;

import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.exception.NotLoginException;

/**
 * 用户上下文工具类
 * <AUTHOR>
 */
public class YeelightUserContextUtils {

    private static final ThreadLocal<YeelightUser> THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<YeelightUser> PROXY_USER = new ThreadLocal<>();
    private static final ThreadLocal<String> CLIENT_LOCAL = new ThreadLocal<>();

    /**
     * 获取当前线程的Yeelight用户信息。
     * 如果用户信息不存在，则抛出NotLoginException异常。
     * @return YeelightUser 当前线程的用户信息对象。
     * @throws NotLoginException 如果用户未登录。
     */
    public static YeelightUser getYeelightUser() {
        YeelightUser user = THREAD_LOCAL.get();
        if (user == null) {
            throw new NotLoginException();
        }
        return user;
    }

    /**
     * 获取当前线程的Yeelight用户信息，如果不存在则返回null。
     * @return YeelightUser 当前线程的用户信息对象，可能为null。
     */
    public static YeelightUser getYeelightUserOrNull() {
        return THREAD_LOCAL.get();
    }

    /**
     * 设置当前线程的Yeelight用户信息。
     * @param yeelightUser 要设置的用户信息对象。
     */
    public static void setYeelightUser(YeelightUser yeelightUser) {
        THREAD_LOCAL.remove();
        THREAD_LOCAL.set(yeelightUser);
    }

    /**
     * 获取代理人的Yeelight用户信息。
     * 如果用户信息不存在，则抛出NotLoginException异常。
     * @return YeelightUser 代理人的用户信息对象。
     * @throws NotLoginException 如果用户未登录。
     */
    public static YeelightUser getProxyYeelightUser() {
        YeelightUser user = PROXY_USER.get();
        if (user == null) {
            throw new NotLoginException();
        }
        return user;
    }

    /**
     * 获取代理人的Yeelight用户信息，如果不存在则返回null。
     * @return YeelightUser 代理人的用户信息对象，可能为null。
     */
    public static YeelightUser getProxyYeelightUserOrNull() {
        return PROXY_USER.get();
    }

    /**
     * 设置代理人的Yeelight用户信息。
     * @param yeelightUser 要设置的代理人的用户信息对象。
     */
    public static void setProxyYeelightUser(YeelightUser yeelightUser) {
        PROXY_USER.remove();
        PROXY_USER.set(yeelightUser);
    }

    public static void setClientId(String clientId) {
        CLIENT_LOCAL.remove();
        CLIENT_LOCAL.set(clientId);
    }

    public static String getClientId() {
        return CLIENT_LOCAL.get();
    }
}
