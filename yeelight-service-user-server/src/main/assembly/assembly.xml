<assembly>
    <id>all</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>src/main/resources-${env}/bin</directory>
            <outputDirectory>/bin</outputDirectory>
            <includes>
                <include>*.sh</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>src/main/resources-${env}</directory>
            <outputDirectory>/bin</outputDirectory>
            <includes>
                <include>*.properties</include>
                <include>*.yml</include>
            </includes>
        </fileSet>

        <!-- 把项目自己编译出来的jar文件，打包进zip文件的根目录 -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
