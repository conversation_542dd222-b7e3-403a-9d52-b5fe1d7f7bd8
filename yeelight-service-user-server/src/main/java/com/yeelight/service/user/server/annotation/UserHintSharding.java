/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.annotation
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-10-08 15:50:15:50
 */
package com.yeelight.service.user.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Desc: 用户分表注解
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-10-08 15:50:15:50
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
public @interface UserHintSharding {
    String tableName() default "";
}
