package com.yeelight.service.user.server.aop;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * @program: yeelight-oauth-api
 * @description: token切面类
 * @author: Sheldon
 * @create: 2020-03-19 14:44
 **/
@Component
@Aspect
@Slf4j
public class AuthTokenAspect {
    /**
     * 对OAuth2认证提供者的authenticate方法执行环绕通知。
     * 该方法会在方法执行前后打印日志，用于记录方法的入参和返回结果。
     * 新版Spring Authorization Server使用AuthenticationProvider架构
     *
     * @param pjp 环绕通知的 ProceedingJoinPoint 对象，包含关于目标方法的信息
     * @return 目标方法的执行结果
     * @throws Throwable 如果目标方法执行过程中发生异常，则抛出
     */
    @Around("execution(* org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeAuthenticationProvider.authenticate(..))" +
            " || execution(* org.springframework.security.oauth2.server.authorization.authentication.OAuth2RefreshTokenAuthenticationProvider.authenticate(..))" +
            " || execution(* org.springframework.security.oauth2.server.authorization.authentication.OAuth2ClientCredentialsAuthenticationProvider.authenticate(..))" +
            " || execution(* com.yeelight.service.user.server.custom.grant.OAuth2ResourceOwnerPasswordAuthenticationProvider.authenticate(..))")
    public Object handleTokenAuthentication(ProceedingJoinPoint pjp) throws Throwable {
        String vendor = UserVendorHolder.getSimpleVendor();
        String methodName = pjp.getSignature().getName();
        String className = pjp.getTarget().getClass().getSimpleName();

        // 打印方法入参日志
        Object[] params = pjp.getArgs();
        for(int i=0; i<params.length; i++) {
            log.info("vendor {} {}#{} 入参{}:{}", vendor, className, methodName, i, JSON.toJSONString(params[i]));
        }
        // 执行目标方法并打印返回结果日志
        Object proceed = pjp.proceed();
        log.info("vendor {} {}#{} 结果:{}", vendor, className, methodName, JSON.toJSONString(proceed));
        return proceed;
    }

    /**
     * 绕过Spring Security OAuth2授权端点的授权处理过程。
     * 对授权请求的入参和返回结果进行日志记录。
     * 新版Spring Authorization Server使用AuthenticationProvider架构
     *
     * @param pjp ProceedingJoinPoint 切面联合点，代表当前执行的方法。
     * @return Object 方法的执行结果，即授权处理流程中的数据。
     * @throws Throwable 如果方法执行过程中发生异常，则抛出。
     */
    @Around("execution(* org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationProvider.authenticate(..))" +
            " || execution(* com.yeelight.service.user.server.oauth2.YeelightAuthorizationEndpoint.approveOrDeny(..))")
    public Object handleOauthAuthorize(ProceedingJoinPoint pjp) throws Throwable {
        // 获取当前用户所代表的供应商
        String vendor = UserVendorHolder.getSimpleVendor();
        String methodName = pjp.getSignature().getName();
        String className = pjp.getTarget().getClass().getSimpleName();

        // 获取方法参数并进行日志记录
        Object[] params = pjp.getArgs();
        for(int i=0; i<params.length; i++) {
            log.info("vendor {} {}#{} 入参{}:{}", vendor, className, methodName, i, JSON.toJSONString(params[i]));
        }
        // 执行方法，并记录执行结果
        Object proceed = pjp.proceed();
        log.info("vendor {} {}#{} 结果:{}", vendor, className, methodName, JSON.toJSONString(proceed));
        return proceed;
    }

    /**
     * 对OAuth2相关Controller方法执行环绕通知。
     * 监控JwtAuthController和OauthController的关键方法
     *
     * @param pjp 环绕通知的 ProceedingJoinPoint 对象，包含关于目标方法的信息
     * @return 目标方法的执行结果
     * @throws Throwable 如果目标方法执行过程中发生异常，则抛出
     */
    @Around("execution(* com.yeelight.service.user.server.controller.JwtAuthController.*(..))" +
            " || execution(* com.yeelight.service.user.server.controller.OauthController.getAccessConfirmation(..))")
    public Object handleOauth2Controllers(ProceedingJoinPoint pjp) throws Throwable {
        String vendor = UserVendorHolder.getSimpleVendor();
        String methodName = pjp.getSignature().getName();
        String className = pjp.getTarget().getClass().getSimpleName();

        // 打印方法入参日志
        Object[] params = pjp.getArgs();
        for(int i=0; i<params.length; i++) {
            // 对于敏感参数（如HttpServletRequest, HttpServletResponse）只记录类型
            Object param = params[i];
            String paramInfo;
            if (param instanceof jakarta.servlet.http.HttpServletRequest) {
                paramInfo = "HttpServletRequest[" + ((jakarta.servlet.http.HttpServletRequest) param).getRequestURI() + "]";
            } else if (param instanceof jakarta.servlet.http.HttpServletResponse) {
                paramInfo = "HttpServletResponse";
            } else {
                paramInfo = JSON.toJSONString(param);
            }
            log.info("vendor {} {}#{} 入参{}:{}", vendor, className, methodName, i, paramInfo);
        }

        // 执行目标方法并打印返回结果日志
        Object proceed = pjp.proceed();
        log.info("vendor {} {}#{} 结果:{}", vendor, className, methodName, JSON.toJSONString(proceed));
        return proceed;
    }
}
