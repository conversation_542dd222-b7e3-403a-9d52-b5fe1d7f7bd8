/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.aop
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-10-08 15:48:15:48
 */
package com.yeelight.service.user.server.aop;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.annotation.UserHintSharding;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * Desc: 用户分表切面
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-10-08 15:48:15:48
 */
@Aspect
@Component
public class UserHintShardingAspect {
    /**
     * 在标注了 {@code userHintSharding} 注解的类级别的方法执行前，应用用户提示分片逻辑。
     * 这个方法主要作为一个包装器，将类级别的分片逻辑应用到指定的方法执行上。
     *
     * @param joinPoint 表示当前执行的方法的 ProceedingJoinPoint 对象，包含关于方法调用的信息。
     * @param userHintSharding 表示一个 UserHintSharding 注解，该注解标注在当前方法所在的类上。
     *                         通过这个注解，可以获取到类级别的分片配置信息。
     * @return 返回方法执行的结果，即 Object 类型。这个结果可能是任何类型，具体取决于被拦截方法的返回类型。
     * @throws Throwable 如果方法执行过程中发生异常，则抛出 Throwable。
     */
    @Around("@within(userHintSharding)")
    public Object applyUserHintShardingClassLevel(ProceedingJoinPoint joinPoint, UserHintSharding userHintSharding) throws Throwable {
        // 将类级别的分片逻辑应用到方法执行中
        return applyUserHintSharding(joinPoint, userHintSharding);
    }

    /**
     * 在方法级别应用用户提示分片注解的环绕通知。
     * <p>此方法会环绕被 {@link UserHintSharding} 注解标记的方法执行，实现在方法执行前后的额外逻辑处理。
     * 主要用于通过用户提示进行分片逻辑的处理。
     *
     * @param joinPoint 切面连接点，代表当前被拦截的方法。
     * @param userHintSharding 用户提示分片注解实例，包含了注解上的配置信息。
     * @return 返回方法执行的结果。
     * @throws Throwable 如果方法执行过程中发生异常，则抛出。
     */
    @Around("@annotation(userHintSharding)")
    public Object applyUserHintShardingMethodLevel(ProceedingJoinPoint joinPoint, UserHintSharding userHintSharding) throws Throwable {
        return applyUserHintSharding(joinPoint, userHintSharding);
    }

    /**
     * 根据用户提供的分片提示应用分片逻辑。
     * 此方法会在方法执行前，根据用户提供的分片信息（如果有的话），设置分片提示，
     * 然后执行目标方法。
     *
     * @param joinPoint 切面编程中的连接点，代表当前被拦截的方法。
     * @param userHintSharding 包含用户提供的分片信息的对象，如表名。
     * @return 返回目标方法的执行结果。
     * @throws Throwable 如果目标方法执行过程中抛出异常，则此处也会抛出。
     */
    private Object applyUserHintSharding(ProceedingJoinPoint joinPoint, UserHintSharding userHintSharding) throws Throwable {
        // 根据用户提供的分片信息，设置分片表名；如果未提供，则使用默认的vendor
        String vendor = StringUtils.defaultIfBlank(userHintSharding.tableName(), UserVendorHolder.getVendor());

        if (StringUtils.isNotBlank(vendor)) {
            // 清除任何现有的分片提示
            HintManager.clear();
            try (HintManager hintManager = HintManager.getInstance()) {
                // 设置分片提示，为指定的vendor
                hintManager.addTableShardingValue(UserVendorEnum.DEFAULT.getCode().toLowerCase(), vendor);
                // 执行目标方法
                return joinPoint.proceed();
            }
        } else {
            // 如果没有提供分片信息，直接执行目标方法
            return joinPoint.proceed();
        }
    }

}
