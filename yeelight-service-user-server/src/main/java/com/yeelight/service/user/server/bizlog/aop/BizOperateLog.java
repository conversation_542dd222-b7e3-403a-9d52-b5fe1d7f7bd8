package com.yeelight.service.user.server.bizlog.aop;


import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;

import java.lang.annotation.*;

/**
 * @program: yeelight-service-app
 * @description: 业务操作日志注解类
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-06-29 16:04
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface BizOperateLog {
    /**
     * 业务ID
     */
    String bizId() default "";

    /**
     * 操作类型
     */
    OpTypeEnums opType() default OpTypeEnums.新增;

    /**
     * 业务类型
     */
    BizTypeEnums bizType() default BizTypeEnums.通用操作;

    /**
     * 业务子类型
     */
    String bizSubType() default "";

    String bizBody() default "";
}
