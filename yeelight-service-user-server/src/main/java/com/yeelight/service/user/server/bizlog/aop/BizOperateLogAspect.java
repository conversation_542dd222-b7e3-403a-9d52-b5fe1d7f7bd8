package com.yeelight.service.user.server.bizlog.aop;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.service.base.BaseDomain;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.utils.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: yeelight-service-app
 * @description: 业务操作日志切面类
 * @author: lixiaodong
 * @create: 2021-06-29 16:40
 **/
@Slf4j
@Aspect
public class BizOperateLogAspect {
    private static final Pattern PATTERN = Pattern.compile("(?<=\\{)(.+?)(?=})");

    public static final String CURRENT_USER_ID = "{currentUserId}";
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    

    /**
     * 绕过当前方法，在方法执行前后添加业务操作日志的功能。
     * @param point 方法执行的连接点
     * @param bizOperateLog 注解上的业务操作日志信息
     * @return 方法执行的结果
     * @throws Throwable 方法执行中抛出的异常
     */
    @Around("@annotation(bizOperateLog))")
    public Object around(final ProceedingJoinPoint point, final BizOperateLog bizOperateLog) throws Throwable {
        // 获取当前执行的方法和参数
        final Method method = this.getMethod(point);
        final Object[] args = point.getArgs();

        // 解析业务类型、子业务类型和操作类型
        final BizTypeEnums bizType = bizOperateLog.bizType();
        final String bizSubType = StringUtils.isBlank(bizOperateLog.bizSubType()) ? this.getClassName(point) + "." + method.getName(): bizOperateLog.bizSubType();
        final OpTypeEnums opType = bizOperateLog.opType();

        // 绑定参数到上下文中
        final EvaluationContext context = bindParam(method, args);

        // 尝试从注解或上下文中获取业务ID
        String bizId = getBizId(bizOperateLog.bizId(), context);

        // 尝试获取业务内容
        final Object bizBody = getBizBody(bizOperateLog.bizBody(), context);

        // 执行目标方法
        Object proceedResult = point.proceed();

        // 处理结果，转换为字符串和编码
        String proceedResultString = null;
        String proceedResultCode = null;
        if (proceedResult instanceof Result) {
            proceedResultString = JSON.toJSONString(proceedResult);
            proceedResultCode = ((Result<?>) proceedResult).getCode();
        }

        // 如果业务ID为空，则尝试从结果中提取业务ID
        if (StringUtils.isBlank(bizId)) {
            // 根据结果类型提取业务ID
            bizId = extractBizIdFromResult(proceedResult);
        } else if (CURRENT_USER_ID.equals(bizId)) {
            // 如果业务ID是特殊值CURRENT_USER_ID，则替换为当前用户ID
            bizId = Objects.nonNull(ContextHolder.getUser()) ? ContextHolder.getUser().getId().toString() : "0";
        }

        // 发送业务操作日志
        BizOperateLogUtils.sendBizOperateLog(bizId, bizType.code, bizSubType, opType.code, bizBody, proceedResultCode, proceedResultString, null, null);

        // 返回方法执行结果
        return proceedResult;
    }

    /**
     * 从结果对象中提取业务ID。
     * 该方法首先检查结果对象的类型，然后根据不同的类型提取业务ID。
     * 如果结果对象是基本类型（String, Boolean, Number, Enum）直接转换为字符串返回。
     * 如果结果对象是自定义Result类型，会进一步检查Result中的数据类型，
     * 如果数据是基本类型或者BaseDomain类型，则提取相应的ID或转换为字符串返回。
     * 如果无法匹配上述任何类型，则默认返回"0"。
     *
     * @param proceedResult 方法执行结果，可以是任意类型。
     * @return 业务ID，如果无法提取则返回"0"。
     */
    private String extractBizIdFromResult(Object proceedResult) {
        // 提取业务ID逻辑
        if (proceedResult instanceof String ||
                proceedResult instanceof Boolean ||
                proceedResult instanceof Number ||
                proceedResult instanceof Enum) {
            // 结果为基本类型时，直接转换为字符串返回
            return proceedResult.toString();
        } else if (proceedResult instanceof Result) {
            Object data = ((Result<?>) proceedResult).getData();
            if (data instanceof String ||
                    data instanceof Boolean ||
                    data instanceof Number ||
                    data instanceof Enum) {
                // Result中的数据为基本类型时，转换为字符串返回
                return data.toString();
            } else if (data instanceof BaseDomain) {
                // Result中的数据为BaseDomain类型时，提取ID并转换为字符串返回
                return ((BaseDomain) data).getId().toString();
            } else {
                // Result中的数据为其他类型时，转换为字符串返回
                return ((Result<?>) proceedResult).getData().toString();
            }
        } else {
            // 无法匹配上述类型时，返回默认值"0"
            return "0";
        }
    }


    /**
     * 获取当前执行的方法。
     * 这个方法通过给定的 ProceedingJoinPoint 对象来获取当前执行的方法的详细信息。
     *
     * @param pjp ProceedingJoinPoint 对象，代表当前执行的连接点。
     * @return Method 对象，表示当前执行的方法。
     * @throws NoSuchMethodException 如果无法找到方法，则抛出此异常。
     */
    private Method getMethod(final ProceedingJoinPoint pjp) throws NoSuchMethodException {
        // 将 ProceedingJoinPoint 对象转换为 MethodSignature 对象
        final MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        // 通过 MethodSignature 对象获取当前执行的方法
        final Method method = methodSignature.getMethod();
        // 获取目标对象的实际方法，包括参数类型，用于后续的逻辑处理或拦截
        return pjp.getTarget().getClass().getMethod(method.getName(), method.getParameterTypes());
    }


    /**
     * 获取当前执行的类的名称。
     *
     * @param pjp 代表当前执行的JoinPoint，提供访问目标对象等信息的能力。
     * @return 返回当前执行方法所在类的简单名称。
     */
    private String getClassName(final ProceedingJoinPoint pjp) {
        // 通过ProceedingJoinPoint对象获取目标对象，再通过目标对象获取类的简单名称
        return pjp.getTarget().getClass().getSimpleName();
    }


    /**
     * 将方法的参数名和参数值绑定到EvaluationContext中。
     * 这个方法通过反射获取方法的参数名，并将参数名与对应的参数值绑定到EvaluationContext对象中，
     * 以便在后续的表达式求值中可以方便地访问方法参数。
     *
     * @param method 要执行的方法，用于获取参数名。
     * @param args 方法调用时传入的实际参数数组。
     * @return 绑定了参数名和参数值的EvaluationContext对象。
     */
    private EvaluationContext bindParam(final Method method, final Object[] args) {
        // 获取方法的参数名
        final String[] params = this.discoverer.getParameterNames(method);

        // 将参数名与参数值对应起来，创建并返回EvaluationContext对象
        final EvaluationContext context = new StandardEvaluationContext();
        if (null != params) {
            for (int i = 0; i < params.length; i++) {
                // 绑定参数名和参数值
                context.setVariable(params[i], args[i]);
            }
        }
        return context;
    }


    /**
     * 根据给定的表达式字符串和评估上下文，解析并获取业务ID。
     * <p>该方法首先检查表达式字符串是否为空，如果为空则直接返回空字符串。
     * 接着使用表达式解析器解析传入的表达式字符串，然后在给定的评估上下文中求值，
     * 尝试获取一个字符串类型的值作为业务ID。如果解析或求值过程中发生异常，
     * 则会返回原始的表达式字符串。</p>
     *
     * @param expressionStr 表达式字符串，用于获取业务ID。
     * @param context 评估上下文，提供给表达式求值所需的环境信息。
     * @return 返回解析得到的业务ID，如果解析失败则返回原始的表达式字符串。
     */
    private String getBizId(final String expressionStr, final EvaluationContext context){
        // 检查表达式字符串是否为空
        if (StringUtils.isBlank(expressionStr)) {
            return "";
        }
        // 确保context不为空
        if (context == null) {
            return "";
        }
        // 解析表达式
        final Expression idExpression = this.parser.parseExpression(expressionStr);
        try {
            // 在上下文中求值并返回结果
            return idExpression.getValue(context, String.class);
        } catch (Exception e) {
            // 如果求值失败，返回原始表达式字符串
            return expressionStr;
        }
    }



    /**
     * 处理业务主体字符串，解析并替换其中的特定模式为实际的JSON对象。
     *
     * @param bizBody 业务主体字符串，可能包含需要解析和替换的表达式。
     * @param context 上下文环境，用于解析表达式时获取变量和函数的值。
     * @return 解析并替换后的对象，如果解析失败则返回原始字符串。
     */
    private Object getBizBody(String bizBody, final EvaluationContext context){
        // 使用预定义的模式匹配器查找需要替换的表达式
        final Matcher successMatcher = PATTERN.matcher(bizBody);
        while(successMatcher.find()){
            String temp = successMatcher.group();
            // 解析找到的表达式
            final Expression tempExpression = this.parser.parseExpression(temp);
            // 评估表达式并在上下文中求值
            final String result = JSON.toJSONString(tempExpression.getValue(context));
            // 如果结果非空，用求值结果替换原始表达式
            if (StringUtils.isNotBlank(result)) {
                temp = "{"+temp+"}";
                bizBody = bizBody.replace(temp,result);
            }
        }
        // 尝试将处理后的字符串解析为JSON对象，如果失败则返回原始字符串
        try {
            return JSON.parseObject(bizBody, Object.class);
        } catch (Exception e) {
            return bizBody;
        }
    }

}
