package com.yeelight.service.user.server.bizlog.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 业务操作日志基础信息类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BizBase implements Serializable {
    private Integer opUid;

    private String opUname;
}
