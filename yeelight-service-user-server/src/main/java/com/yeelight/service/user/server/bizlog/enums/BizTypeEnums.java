package com.yeelight.service.user.server.bizlog.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * @description: 业务类型枚举类
 * <AUTHOR>
 */
@Getter
public enum BizTypeEnums implements BaseEnum<String> {
    /**
     * 业务类型枚举
     */
    通用操作("normal"),
    异常("exception"),
    限频("limit"),
    公共("public"),
    OAUTH应用("oauthClient"),
    OAUTH("oauth"),
    会话("session"),
    用户("yeelightUser"),
    三方集成("thirdPartySocial"),
    扫码登陆("scanLogin"),
    ;
    public final String code;

    BizTypeEnums(String code) {
        this.code = code;
    }
}
