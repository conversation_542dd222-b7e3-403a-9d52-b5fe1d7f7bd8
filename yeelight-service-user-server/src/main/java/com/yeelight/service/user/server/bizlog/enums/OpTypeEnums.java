package com.yeelight.service.user.server.bizlog.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * @description: 操作类型枚举类
 * <AUTHOR>
 */
@Getter
public enum OpTypeEnums implements BaseEnum<String> {
    /**
     * 操作类型枚举
     */
    新增("add"),
    变更("update"),
    删除("remove"),
    恢复("restore"),
    读取("read"),
    ;

    public final String code;

    OpTypeEnums(String code) { this.code = code; }
}
