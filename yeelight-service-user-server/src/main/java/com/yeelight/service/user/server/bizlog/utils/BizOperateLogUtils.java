package com.yeelight.service.user.server.bizlog.utils;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.dubbo.ContextKeys;
import com.yeelight.service.framework.log.PlatformLogger;
import com.yeelight.service.framework.log.PlatformLoggerFactory;
import com.yeelight.service.framework.log.entity.BizOperateLog;
import com.yeelight.service.user.client.constant.UserConstant;
import com.yeelight.service.user.server.bizlog.domain.BizBase;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.utils.ContextHolder;
import com.yeelight.service.user.server.utils.DiffUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class BizOperateLogUtils {
    private static final PlatformLogger<BizOperateLog> BIZ_LOGGER = PlatformLoggerFactory.getBizOperateLogger();
    private static final String LOCAL_IPV4 = "127.0.0.1";
    private static final String LOCAL_IPV6 = "0:0:0:0:0:0:0:1";
    private static final String UNKNOWN = "unknown";
    private static final String DIFF_BEFORE = "before";
    private static final String DIFF_AFTER = "after";

    /**
     * 发送业务操作日志。
     * 该方法用于记录业务操作的日志信息，包括业务ID、业务类型、操作类型、结果码等详细信息。
     * 日志信息会通过指定的日志记录器进行记录。
     *
     * @param bizId 业务ID，标识唯一的业务操作。
     * @param bizType 业务类型，用于区分不同的业务操作。
     * @param bizSubType 业务子类型，进一步细分业务操作。
     * @param opType 操作类型，标识对业务的具体操作。
     * @param bizBody 业务主体内容，具体业务操作涉及的数据。
     * @param resultCode 结果码，标识操作的结果状态。
     * @param resultMsg 结果消息，对操作结果的描述。
     * @param diff 差异数据，用于记录操作前后的数据变化。
     * @param bizBase 业务基础信息，包含业务操作相关的基础信息。
     */
    public static void sendBizOperateLog(Object bizId, String bizType, String bizSubType, String opType, Object bizBody, String resultCode, String resultMsg, Object diff, BizBase bizBase) {
        // 构建业务操作日志对象
        BizOperateLog log = BizOperateLog.builder()
                .bizId(String.valueOf(bizId))
                .opType(opType)
                .bizSubType(bizSubType)
                .bizType(bizType)
                .isSuccess(true)
                .resultCode(resultCode)
                .resultMsg(resultMsg)
                .service(Constants.SERVICE_NAME)
                .bizBody(Objects.isNull(bizBody) ? null : JSON.toJSONString(bizBody))
                .diff(Objects.isNull(diff) ? null : JSON.toJSONString(diff))
                .build();

        // 填充用户信息到日志对象
        buildUserInfo(log, bizBase);

        // 记录日志
        BIZ_LOGGER.log(log);
    }


    /**
     * 发送简单的业务操作日志。
     * 该方法用于记录业务操作日志，特别适用于简单操作场景，自动将操作类型设置为“新增”。
     *
     * @param bizId 业务ID，用于标识唯一的业务操作实例。可以是任意类型的业务标识。
     * @param bizType 业务类型，用于区分不同的业务操作类别。
     * @param subBizType 子业务类型，用于进一步细分业务操作类别。
     * @param bizBody 业务主体内容，通常为业务操作的相关数据或信息。可以是任意类型。
     */
    public static void sendSimpleBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody) {
        // 调用发送业务操作日志的通用方法，设置操作类型为新增，其他参数为null
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.新增.code, bizBody, null, null, null, null);
    }

    /**
     * 发送业务操作日志：新增
     * @param bizId 业务ID，标识唯一的业务操作实例
     * @param bizType 业务类型，用于区分不同的业务操作
     * @param subBizType 子业务类型，进一步细分业务操作的类型
     * @param bizBody 业务体，包含业务操作的具体内容
     * 该方法通过调用sendBizOperateLog方法，记录一种特定类型的业务操作日志——新增操作。
     * 它封装了日志记录过程中的操作类型和其他必填参数的设置，简化了日志发送的过程。
     */
    public static void sendAddBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody) {
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.新增.code, bizBody, null, null, null, null);
    }


    /**
     * 发送业务操作日志的辅助方法，用于记录删除操作的日志。
     *
     * @param bizId 业务ID，标识特定的业务实体。
     * @param bizType 业务类型，用于区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细分业务操作的类型。
     * 该方法通过调用 sendBizOperateLog 方法，传入特定的参数来记录删除操作的日志。
     * 不返回任何结果，操作完成后直接返回。
     */
    public static void sendRemoveBizOperateLog(Object bizId, String bizType, String subBizType) {
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.删除.code, null, null, null, null, null);
    }


    /**
     * 发送业务操作日志的删除操作。
     * <p>此方法封装了发送特定类型业务操作日志的逻辑，专用于删除操作的日志记录。</p>
     *
     * @param bizId     业务ID，标识唯一的业务操作实例。
     * @param bizType   业务类型，用于区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细分业务操作的类型。
     * @param bizBody   业务体，包含业务操作的具体内容。
     *
     * 注意：此方法不返回任何结果，直接发送删除操作的日志，并不影响业务流程的继续执行。
     */
    public static void sendRemoveBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody) {
        // 调用sendBizOperateLog方法，传入删除操作的特定参数，完成日志发送
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.删除.code, bizBody, null, null, null, null);
    }


    /**
     * 发送删除业务操作日志的请求。
     * 该方法会遍历给定的业务ID列表，针对每个ID发送一条删除操作的日志。
     *
     * @param bizIds 业务ID列表，不可为空。如果为空，则方法直接返回。
     * @param bizType 业务类型，标识操作所属的业务领域。
     * @param subBizType 子业务类型，进一步细化业务类型的分类。
     */
    public static void sendRemoveBizOperateLog(List<?> bizIds, String bizType, String subBizType) {
        // 检查传入的业务ID列表是否为空，如果为空则直接返回，不执行任何操作
        if (CollectionUtils.isEmpty(bizIds)) {
            return;
        }
        // 遍历业务ID列表，为每个ID调用sendBizOperateLog方法，记录删除操作的日志
        for (Object bizId : bizIds) {
            sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.删除.code, null, null, null, null, null);
        }
    }

    /**
     * 发送业务操作日志，用于记录业务数据的更新操作。
     *
     * @param bizId 业务ID，标识唯一的业务实体。
     * @param bizType 业务类型，用于区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细分业务类型。
     * @param bizBody 业务数据体，包含业务操作的详细信息。
     * @param newBizData 新业务数据，此次更新后的数据。
     * @param oldBizData 旧业务数据，此次更新前的数据。
     * <p>
     * 此方法首先利用DiffUtil计算新旧业务数据的差异，然后调用sendBizOperateLog发送操作日志，
     * 包括差异数据在内的一系列操作信息。
     */
    public static void sendUpdateBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody, Object newBizData, Object oldBizData) {
        // 计算新旧数据的差异
        Map<String, Map<String, Object>> diff = DiffUtil.diff(newBizData, oldBizData);
        // 发送业务操作日志，包括差异数据（如果存在）在内的操作信息
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.变更.code, bizBody, null, null, MapUtils.isEmpty(diff) ? null : diff, null);
    }

    /**
     * 发送业务操作日志，用于记录业务数据的更新操作。
     *
     * @param bizId 业务ID，标识唯一的业务实体。
     * @param bizType 业务类型，区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细化业务分类。
     * @param bizBody 业务主体数据，包含业务操作的具体内容。
     * @param newBizData 新业务数据，即更新后的业务数据。
     * @param oldBizData 旧业务数据，即更新前的业务数据。
     * @param bizBase 业务基础信息，包含业务操作的共用信息。
     * <p>
     * 此方法通过比较新旧业务数据，计算出数据差异后，调用
     * sendBizOperateLog方法发送业务操作日志。
     */
    public static void sendUpdateBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody, Object newBizData, Object oldBizData, BizBase bizBase) {
        // 计算新旧业务数据的差异
        Map<String, Map<String, Object>> diff = DiffUtil.diff(newBizData, oldBizData);
        // 发送业务操作日志，若数据无差异，则不传递差异数据
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.变更.code, bizBody, null, null, MapUtils.isEmpty(diff) ? null : diff, bizBase);
    }

    /**
     * 发送业务操作日志的更新。
     * 该方法专门用于发送关于业务操作的日志更新，将业务操作的相关信息封装成日志并发送。
     *
     * @param bizId 业务ID，标识特定的业务操作。
     * @param bizType 业务类型，区分不同的业务操作类别。
     * @param subBizType 子业务类型，进一步细分业务操作类型。
     * @param bizBody 业务体，包含业务操作的具体内容。
     * @param diff 变更内容，描述业务操作的变更信息。如果无变更，则可以为空。
     */
    public static void sendUpdateBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody, String diff) {
        // 调用sendBizOperateLog方法，传入业务相关参数及操作类型，将变更内容作为diff参数传递，若无变更内容则不传递
        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.变更.code, bizBody, null, null, StringUtils.isBlank(diff) ? null : diff, null);
    }


    /**
     * 发送更新业务操作日志的简略版本，此方法将业务日志的相关信息发送出去，用于记录业务操作的审计日志。
     * 该方法是sendUpdateBizOperateLog的重载版本，允许不指定额外的参数。
     *
     * @param bizId 业务ID，标识特定的业务操作。
     * @param bizType 业务类型，用于区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细分业务操作的类型。
     * @param bizBody 业务体，包含业务操作的具体内容。
     */
    public static void sendUpdateBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody) {
        sendUpdateBizOperateLog(bizId, bizType, subBizType, bizBody, null, null);
    }


    /**
     * 发送更新业务操作日志的简略版本方法。
     * 此方法是sendUpdateBizOperateLog的重载版本，提供了更简化的参数选项。
     *
     * @param bizId 业务ID，标识特定的业务操作。
     * @param bizType 业务类型，用于区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细分业务类型。
     * @param bizBody 业务体，包含业务操作的具体内容。
     * @param bizBase 业务基础信息，包含业务操作的共用信息。
     */
    public static void sendUpdateBizOperateLog(Object bizId, String bizType, String subBizType, Object bizBody, BizBase bizBase) {
        sendUpdateBizOperateLog(bizId, bizType, subBizType, bizBody, null, null, bizBase);
    }


    /**
     * 发送更新业务操作日志的简化版本方法。此方法将调用另一个重载方法，
     * 通过传入业务ID、业务类型、子业务类型和可选的扩展参数来发送更新操作日志。
     * 其中扩展参数在此简化版本中默认为null。
     *
     * @param bizId 业务ID，标识特定的业务操作。
     * @param bizType 业务类型，区分不同的业务操作。
     * @param subBizType 子业务类型，进一步细分业务操作。
     * @see #sendUpdateBizOperateLog(Object, String, String, Object)
     */
    public static void sendUpdateBizOperateLog(Object bizId, String bizType, String subBizType) {
        sendUpdateBizOperateLog(bizId, bizType, subBizType, null);
    }


    /**
     * 发送更新业务操作日志的请求。
     * 该方法会遍历给定的业务ID列表，并为每个ID发送一条更新业务操作日志的请求。
     *
     * @param bizIds 业务ID的列表，不可为null或空。列表中的每个元素都将发送一条日志更新请求。
     * @param bizType 业务类型，标识这条日志所属的业务范畴。
     * @param subBizType 子业务类型，进一步细化业务类型的分类。
     * @param bizBody 业务体，包含日志的具体内容或数据。
     */
    public static void sendUpdateBizOperateLog(List<?> bizIds, String bizType, String subBizType, Object bizBody) {
        // 如果业务ID列表为空，则直接返回，不执行任何操作
        if (CollectionUtils.isEmpty(bizIds)) {
            return;
        }
        // 遍历业务ID列表，为每个ID调用单个ID的日志发送方法
        for (Object bizId : bizIds) {
            sendUpdateBizOperateLog(bizId, bizType, subBizType, bizBody);
        }
    }

    /**
     * 构建用户信息，填充到业务操作日志中。
     *
     * @param bizLog 业务操作日志对象，需要填充用户信息和操作时间等。
     * @param bizBase 业务基础数据对象，如果存在，从中提取用户信息；如果不存在，尝试从上下文中获取用户信息。
     */
    private static void buildUserInfo(BizOperateLog bizLog, BizBase bizBase) {
        // 首先尝试从bizBase中获取用户信息
        if (Objects.nonNull(bizBase)) {
            bizLog.setOpUid(bizBase.getOpUid());
            bizLog.setOpUname(bizBase.getOpUname());
        } else if(Objects.nonNull(ContextHolder.getUser())) {
            // 如果bizBase不存在，尝试从上下文获取当前用户信息
            bizLog.setOpUid(Math.toIntExact(ContextHolder.getUser().getId()));
            bizLog.setOpUname(ContextHolder.getUser().getName());
        } else {
            // 如果上下文中也不存在用户信息，则设置为系统用户
            bizLog.setOpUid(0);
            bizLog.setOpUname("系统");
        }
        // 设置操作时间为当前时间
        bizLog.setOpTime(Instant.now().getEpochSecond());
        // 提取并设置操作IP
        bizLog.setIp(extractIp());
    }

    public static void sendUpdateBizOperateLog(String name, Object bizId, String bizType, String subBizType, Object bizBody, Object newBizData, Object oldBizData) {
        Map<String, Map<String, Object>> diff = DiffUtil.diff(newBizData, oldBizData);
        if (MapUtils.isEmpty(MapUtils.getMap(diff, DIFF_AFTER)) && MapUtils.isEmpty(MapUtils.getMap(diff, DIFF_AFTER))) {
            return;
        }

        sendBizOperateLog(bizId, bizType, subBizType, OpTypeEnums.变更.code, bizBody, null, null, MapUtils.isEmpty(diff) ? null : diff, null);
    }

    /**
     * 从当前上下文中提取客户端IP地址。
     * 首先尝试从RpcContext中获取IP附件，如果不存在，则从HTTP请求头中获取。
     * 考虑到可能通过多个代理的情况，优先使用"x-forwarded-for"头，其次是"Proxy-Client-IP"、"WL-Proxy-Client-IP"、"HTTP_CLIENT_IP"、"HTTP_X_FORWARDED_FOR"。
     * 如果所有头都未提供有效的IP地址，则回退到请求的RemoteAddr。
     * 如果RemoteAddr是本地地址，则尝试获取本机的主机地址。
     * 返回格式化的客户端IP地址或"unknown"。
     *
     * @return 客户端IP地址或"unknown"
     */
    private static String extractIp() {
        // 尝试从RpcContext获取IP地址
        if (StringUtils.isNoneBlank(RpcContext.getServerAttachment().getAttachment(ContextKeys.IP))) {
            return RpcContext.getServerContext().getAttachment(ContextKeys.IP);
        }

        // 获取当前请求属性
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            String ip = parseIpFromServletRequest((ServletRequestAttributes) requestAttributes);
            //对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ip != null) {
                int commaIndex = ip.indexOf(UserConstant.COMMA_DELIMITERS);
                // 如果找到了逗号并且 IP 地址长度大于逗号的位置
                if (commaIndex > 0) {
                    ip = ip.substring(0, commaIndex);
                }
            }
            return ip;
        }
        return UNKNOWN;
    }

    private static boolean isInvalidOrUnknown(String ip) {
        return ip == null || ip.isEmpty() || UNKNOWN.equalsIgnoreCase(ip);
    }

    private static String parseIpFromServletRequest(ServletRequestAttributes requestAttributes) {
        HttpServletRequest request = requestAttributes.getRequest();
        String ip = request.getHeader("x-forwarded-for");
        if (!isInvalidOrUnknown(ip)) {
            return ip;
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (!isInvalidOrUnknown(ip)) {
            return ip;
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (!isInvalidOrUnknown(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_CLIENT_IP");
        if (!isInvalidOrUnknown(ip)) {
            return ip;
        }

        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (!isInvalidOrUnknown(ip)) {
            return ip;
        }

        ip = request.getRemoteAddr();
        if (isInvalidOrUnknown(ip)) {
            if ((LOCAL_IPV4.equals(ip) || LOCAL_IPV6.equals(ip))) {
                // 根据网卡取本机配置的IP
                InetAddress inet;
                try {
                    inet = InetAddress.getLocalHost();
                    ip = inet.getHostAddress();
                } catch (UnknownHostException e) {
                    ip = UNKNOWN;
                }
            }
        }
        return ip;
    }

}
