/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.config
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-07-06 10:00:10:00
 */
package com.yeelight.service.user.server.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.*;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;

/**
 * Desc: 记录访问日志的过滤器
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-07-06 10:00:10:00
 */
public class AccessLogFilter implements Filter {

    private final Logger logger = LoggerFactory.getLogger(AccessLogFilter.class);

    /**
     * 该方法重写了Filter接口的doFilter方法，用于拦截请求并进行处理。
     * 主要功能是在请求经过过滤链之后，检查响应状态码，如果为302（临时移动），
     * 则记录请求的基本信息、头部信息和Cookie信息到日志中。
     *
     * @param request  ServletRequest，表示客户端的请求。
     * @param response ServletResponse，表示对客户端的响应。
     * @param chain    FilterChain，表示过滤链，用于将请求传递给下一个过滤器或目标 servlet。
     * @throws IOException 如果在处理请求或响应时发生IO错误。
     * @throws ServletException 如果在处理请求或响应时发生Servlet相关错误。
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        chain.doFilter(request, response);

        // 检查响应状态码是否为302
        if (httpResponse.getStatus() == HttpServletResponse.SC_MOVED_TEMPORARILY) {
            // 记录请求的基本信息：请求URI、方法和客户端远程地址
            String requestUri = httpRequest.getRequestURI();
            String method = httpRequest.getMethod();
            String remoteAddress = httpRequest.getRemoteAddr();

            // 收集并记录请求的头部信息
            Enumeration<String> headerNames = httpRequest.getHeaderNames();
            StringBuilder headers = new StringBuilder();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = httpRequest.getHeader(headerName);
                headers.append(headerName).append(": ").append(headerValue).append(System.lineSeparator());
            }

            // 收集并记录请求的Cookie信息
            Cookie[] cookies = httpRequest.getCookies();
            StringBuilder cookieInfo = new StringBuilder();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    cookieInfo.append(cookie.getName()).append(": ").append(cookie.getValue()).append(System.lineSeparator());
                }
            }

            // 将请求信息记录到日志中
            logger.info("Received request [{}] {} from {}", method, requestUri, remoteAddress);
            logger.info("Request headers: {}", headers);
            logger.info("Request cookies: {}", cookieInfo);
        }
    }

    // 省略其他方法
}
