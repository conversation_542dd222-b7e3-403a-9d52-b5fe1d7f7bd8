package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.OriginalRequestContextHolder;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.utils.UrlUtil;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Optional;

/**
 * @description: 应用配置类
 * <AUTHOR>
 */
@Getter
@RefreshScope
@Configuration
public class AppConfig {

    /**
     *  自旋锁失效时间，单位毫秒
     */
    @Getter
    @Value("${redis_lock_expire:2000}")
    private Integer redisLockExpire;

    /**
     *  自旋锁自旋间隔，单位毫秒
     */
    @Getter
    @Value("${redis_lock_loop_interval:50}")
    private Integer redisLockLoopInterval;

    /**
     *  自旋锁自旋次数
     */
    @Getter
    @Value("${redis_lock_loop_num:40}")
    private Integer redisLockLoopNum;

    @Value("${region:CN}")
    private String region;

    @Value("${kafka.topic.suffix:}")
    private String kafkaTopicSuffix;

    @Value("${frontend-backend-separated.switch:false}")
    private Boolean isFrontendBackendSeparated;

    @Getter
    @Value("${frontend-backend-separated.frontend.domain:}")
    private String frontendDomain;

    @Getter
    @Value("${frontend-backend-separated.frontend.path:}")
    private String frontendPath;

    @Value("${frontend-backend-separated.frontend.uris.index-uri:}")
    private String frontendIndexPageUri;

    @Value("${frontend-backend-separated.frontend.uris.login-uri:}")
    private String frontendLoginPageUri;

    @Value("${frontend-backend-separated.frontend.uris.register-uri:}")
    private String frontendRegisterPageUri;

    @Value("${frontend-backend-separated.frontend.uris.grant-page-uri:}")
    private String frontendGrantPageUri;

    @Value("${frontend-backend-separated.frontend.uris.reset-password-uri:}")
    private String frontendResetPasswordUri;

    @Value("${frontend-backend-separated.frontend.uris.error-uri:}")
    private String frontendErrorUri;


    /**
     * 获取系统是否采用前后端分离的架构模式。
     * <p>
     * 该方法不接受任何参数，主要用于查询当前系统是否实施了前后端分离的架构。
     *
     * @return Boolean 返回一个布尔值，指示前后端是否分离。如果分离，则返回true；否则返回false。
     */
    public Boolean getFrontendBackendSeparated() {
        return isFrontendBackendSeparated;
    }

    /**
     * 获取首页页面的URL。
     * 此方法不接受参数。
     *
     * @return 返回首页页面的URL。如果系统配置了前后端分离，则返回前端URL；否则，返回默认的首页页面URL。
     */
    public String getIndexPageUrl() {
        // 判断是否前后端分离，根据结果构建或返回默认的索引页面URL
        return getFrontendBackendSeparated() ? buildFrontendUrl(Optional.ofNullable(frontendIndexPageUri).orElse(SecurityConstants.INDEX_PAGE)) : SecurityConstants.INDEX_PAGE;
    }

    /**
     * 获取登录页面的URL。
     * 此方法会根据系统配置决定返回的登录页URL是前端还是后端的登录页面地址。
     *
     * @return 返回登录页面的URL地址。如果系统配置了前端和后端分离，则返回前端的登录页面URL；否则，返回默认的登录页面URL。
     */
    public String getLoginPageUrl() {
        // 根据是否前后端分离决定返回的登录页URL
        return getFrontendBackendSeparated() ? buildFrontendUrl(Optional.ofNullable(frontendLoginPageUri).orElse(SecurityConstants.LOGIN_PAGE)) : SecurityConstants.LOGIN_PAGE;
    }

    /**
     * 获取注册页面的URL。
     * 该方法会根据系统配置决定返回前端还是后端的注册页面URL。
     *
     * @return 返回注册页面的URL。如果系统配置为前后端分离，则返回前端的注册页面URL；否则，返回默认的注册页面URL。
     */
    public String getRegisterPageUrl() {
        // 根据是否前后端分离决定返回的URL
        return getFrontendBackendSeparated() ? buildFrontendUrl(Optional.ofNullable(frontendRegisterPageUri).orElse(SecurityConstants.REGISTER_PAGE)) : SecurityConstants.REGISTER_PAGE;
    }

    /**
     * 获取授权页面的URL。
     * 该方法不接受参数。
     *
     * @return 返回授权页面的URL。如果应用了前后端分离的架构，则返回构建的前端URL；否则，返回默认的安全授权确认页面URL。
     */
    public String getGrantPageUrl() {
        // 判断是否前后端分离，根据结果决定返回的URL
        return getFrontendBackendSeparated() ? buildFrontendUrl(Optional.ofNullable(frontendGrantPageUri).orElse(SecurityConstants.GRANT_CONFIRM_ACCESS)) : SecurityConstants.GRANT_CONFIRM_ACCESS;
    }

    /**
     * 生成重置密码的URL。
     * 该方法不接受任何参数，根据当前系统的配置，返回一个用于重置密码的页面URL。
     * 如果系统有前后端分离的配置，则会构建一个面向前端的URL；否则，返回默认的重置密码页面URL。
     *
     * @return 返回一个字符串类型的URL，用于重置密码。
     */
    public String getResetPasswordUrl() {
        // 根据系统是否前后端分离来构建不同的URL
        return getFrontendBackendSeparated() ? buildFrontendUrl(Optional.ofNullable(frontendResetPasswordUri).orElse(SecurityConstants.GETPASS_PAGE)) : SecurityConstants.GETPASS_PAGE;
    }

    /**
     * 获取错误页面的URL。
     * 该方法会根据当前应用的前后端分离状态来决定返回的错误页面URL。如果应用是前后端分离的，则会构建一个面向前端的错误页面URL；如果不是，则直接返回系统定义的错误页面URL。
     *
     * @return 返回错误页面的URL。如果是前后端分离应用，则返回构建的前端错误页面URL；否则，返回系统默认的错误页面URL。
     */
    public String getErrorUrl() {
        // 根据是否前后端分离决定返回的错误页面URL
        return getFrontendBackendSeparated() ? buildFrontendUrl(Optional.ofNullable(frontendErrorUri).orElse(SecurityConstants.ERROR_PAGE)) : SecurityConstants.ERROR_PAGE;
    }

    /**
     * 获取前端域名。
     * 该方法接受HttpServletRequest，主要用于获取前端域名。
     *
     * @return 返回前端域名。
     */
    public String getRealFrontendDomain() {
        String originalUrl = OriginalRequestContextHolder.getOriginalUrl();
        String realFrontendDomain = getFrontendDomain();
        try {
            String topDomain = UrlUtil.getTopDomainName(originalUrl);
            if(Arrays.stream(UserVendorEnum.values()).anyMatch(userVendorEnum -> topDomain.equals(userVendorEnum.getDomain()))) {
                realFrontendDomain = UrlUtil.replaceTopDomain(getFrontendDomain(), topDomain);
            }
        } catch (Exception ignored) {
        }
        return realFrontendDomain;
    }

    /**
     * 构建前端URL
     * @param uri 传入的URI字符串
     * @return 返回拼接后的前端URL
     */
    private String buildFrontendUrl(String uri) {
        // 拼接前端URL
        return getRealFrontendDomain() + getFrontendPath() + uri;
    }

}
