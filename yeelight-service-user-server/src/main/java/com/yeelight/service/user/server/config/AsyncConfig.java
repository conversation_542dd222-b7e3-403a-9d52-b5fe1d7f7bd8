package com.yeelight.service.user.server.config;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * 注意：已升级到Spring Boot 3.x，使用AsyncConfigurer接口替代已弃用的AsyncConfigurerSupport
 */
@Configuration
@Slf4j
public class AsyncConfig implements AsyncConfigurer {

    /**
     * 配置并返回一个用于异步任务执行的线程池。
     *
     * @return Executor，配置好的线程池实例，允许异步方式执行任务。
     */
    @Override
    @Bean("taskExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 线程池的核心线程数设置为50，即初始化时创建50个线程
        executor.setCorePoolSize(1);
        // 线程池的最大线程数设置为100，超过核心线程数的线程在空闲时会被销毁
        executor.setMaxPoolSize(10);
        // 线程池所维护的队列容量最大为1000个任务
        executor.setQueueCapacity(100);
        // 设置线程空闲 60 秒后销毁
        executor.setKeepAliveSeconds(60);
        // 允许核心线程在空闲时销毁
        executor.setAllowCoreThreadTimeOut(true);
        // 设置线程名字前缀为"AsyncThreadPool-"，方便识别
        executor.setThreadNamePrefix("AsyncThreadPool-");

        // 设置拒绝策略为CallerRunsPolicy，当线程池和队列都满时，由调用线程执行该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化线程池
        executor.initialize();

        return executor;
    }


    /**
     * 获取异步未捕获异常处理器
     * 该方法重写了获取异步未捕获异常处理器的方法，用于定义当异步执行的方法中发生未捕获异常时的处理逻辑。
     *
     * @return 返回一个自定义的异步未捕获异常处理器，该处理器会记录异常信息并进行相应的处理。
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            // 记录当前线程名及捕获到的异常，用于问题定位
            log.warn("Async Exception Caught in Thread - {}", Thread.currentThread().getName());
            log.warn("Async method: {} has uncaught exception,params:{}", method.getName(), JSON.toJSONString(params));

            // 如果异常类型为BizException，记录特定的业务异常信息
            if (ex instanceof BizException asyncException) {
                log.warn("async BizException:{}", asyncException.getMessage());
            } else {
                // 如果异常类型不是BizException，记录通用异常堆栈信息
                log.warn("asyncException:{}", ExceptionUtils.getStackTrace(ex));
            }
        };
    }


    /**
     * 配置并行流的并行度为50。
     * <p>此方法通过设置系统属性`java.util.concurrent.ForkJoinPool.common.parallelism`来指定
     * ForkJoinPool（用于支持Java 8中引入的并行流）的默认并行度。并行度的值设定为50，这可能会
     * 对采用并行流处理大量数据的场景性能产生积极影响。
     *
     * @return 返回设置操作的结果，即系统属性的旧值。如果之前没有设置过该属性，则返回null。
     */
    @Bean
    public String parallelStreamConfig() {
        // 设置ForkJoinPool的并行度为50
        return System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", "10");
    }
}
