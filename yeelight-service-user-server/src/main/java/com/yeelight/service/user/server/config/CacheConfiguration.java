package com.yeelight.service.user.server.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

/**
 * Spring Cache兼容性配置
 * <p>
 * 配置Redis作为Spring Cache的存储后端，使用兼容性序列化器
 * 确保能够读取历史缓存数据，同时写入新格式数据
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Configuration
public class CacheConfiguration {

    /**
     * 配置兼容性Redis缓存管理器
     * 使用UniversalCompatibleRedisSerializer确保向前兼容
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        log.info("🔧 配置Spring Cache兼容性Redis缓存管理器");
        
        // 创建兼容性序列化器
        UniversalCompatibleRedisSerializer compatibleSerializer = new UniversalCompatibleRedisSerializer();
        
        // 配置Redis缓存
        RedisCacheConfiguration cacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
                // 设置缓存过期时间为1小时
                .entryTtl(Duration.ofHours(1))
                // 禁用缓存null值
                .disableCachingNullValues()
                // 设置键序列化器为String
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                // 设置值序列化器为兼容性序列化器
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(compatibleSerializer))
                // 设置缓存键前缀
                .prefixCacheNameWith("CACHE::");
        
        // 创建Redis缓存管理器
        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(cacheConfiguration)
                // 启用事务支持
                .transactionAware()
                .build();
        
        log.info("✅ Spring Cache兼容性Redis缓存管理器配置完成");
        return cacheManager;
    }
    
    /**
     * 配置特定缓存的TTL
     * 可以为不同的缓存设置不同的过期时间
     */
    @Bean("customCacheManager")
    public CacheManager customCacheManager(RedisConnectionFactory redisConnectionFactory) {
        log.info("🔧 配置自定义缓存管理器，支持不同TTL");
        
        // 创建兼容性序列化器
        UniversalCompatibleRedisSerializer compatibleSerializer = new UniversalCompatibleRedisSerializer();
        
        // 默认缓存配置（1小时）
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))
                .disableCachingNullValues()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(compatibleSerializer))
                .prefixCacheNameWith("CACHE::");
        
        // 长期缓存配置（24小时）
        RedisCacheConfiguration longTermConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(24))
                .disableCachingNullValues()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(compatibleSerializer))
                .prefixCacheNameWith("LONG_CACHE::");
        
        // 短期缓存配置（15分钟）
        RedisCacheConfiguration shortTermConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(15))
                .disableCachingNullValues()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(compatibleSerializer))
                .prefixCacheNameWith("SHORT_CACHE::");
        
        // 创建缓存管理器并配置不同缓存的TTL
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withCacheConfiguration("userCache", longTermConfig)
                .withCacheConfiguration("tokenCache", shortTermConfig)
                .withCacheConfiguration("sessionCache", longTermConfig)
                .withCacheConfiguration("authCache", shortTermConfig)
                .transactionAware()
                .build();
    }
}
