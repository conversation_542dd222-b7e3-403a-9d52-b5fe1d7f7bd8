package com.yeelight.service.user.server.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.utils.AuthUtils;
import com.yeelight.service.user.server.utils.ContextHolder;
import com.yeelight.service.user.server.utils.TokenUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @description: 上下文拦截器
 * <AUTHOR>
 */
@Slf4j
@Getter
public class ContextInterceptor implements HandlerInterceptor {

    private static final String LOCAL_ENV_NAME = "local";

    private final TokenService tokenService;

    private final JwtAuthService jwtAuthService;

    private final YeelightUserReadService yeelightUserReadService;


    @Value("${server.env.suffix}")
    private String envName;

    private final LoadingCache<Long, Optional<YeelightUserDto>> userCache;

    /**
     * 构造函数用于创建一个ContextInterceptor实例。
     * 主要负责初始化用户缓存机制，以及注入必要的服务。
     *
     * @param yeelightUserReadService 用于用户操作的服务，例如查找用户等。
     * @param tokenService 用于处理JWT token的服务。
     * @param jwtAuthService 用于JWT认证的服务。
     */
    public ContextInterceptor(YeelightUserReadService yeelightUserReadService, TokenService tokenService, JwtAuthService jwtAuthService) {
        this.tokenService = tokenService;
        this.jwtAuthService = jwtAuthService;
        this.yeelightUserReadService = yeelightUserReadService;

        // 初始化用户缓存，设置缓存的过期时间和最大容量。
        this.userCache = Caffeine
                .newBuilder()
                // 缓存过期时间设置为30分钟
                .expireAfterWrite(30, TimeUnit.MINUTES)
                // 缓存最大容量设置为1000000
                .maximumSize(1000000)
                // 使用用户ID查找用户的函数作为缓存的加载机制
                .build(key -> Optional.ofNullable(this.yeelightUserReadService.findUserById(key)));
    }

    /**
     * 在处理请求之前进行拦截，用于统一处理认证和用户上下文设置。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送HTTP响应。
     * @param handler  将要处理请求的处理器对象。
     * @return boolean值，如果返回true，则表示请求继续处理；如果返回false，则表示请求终止。
     */
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        // 清除上下文持有者中的用户信息
        ContextHolder.clear();

        // 从请求中提取访问令牌
        String accessToken = TokenUtils.extractToken(request);
        // 检查是否有访问令牌，并尝试通过访问令牌获取用户认证信息
        if (StringUtils.isNotBlank(accessToken)) {
            extractUserByAccessToken(accessToken);
        } else if (Objects.nonNull(SecurityContextHolder.getContext()) && Objects.nonNull(SecurityContextHolder.getContext().getAuthentication())) {
            extractUserBySecurityContext();
        } else if(StringUtils.isNotBlank(AuthUtils.getJwtAuthToken(request))) {
            extractUserByJwtAuthToken(AuthUtils.getJwtAuthToken(request));
        }
        // 在本地环境时，直接通过
        if (isLocalEnv()) {
            return true;
        }
        // 默认通过
        return true;
    }

    private void extractUserByJwtAuthToken(String jwtAuthToken) {
        // 尝试从请求头中获取JWT令牌，并使用该令牌获取用户信息
        YeelightUserDto yeelightUser = jwtAuthService.getUserInfoByToken(UserVendorHolder.getVendor(), jwtAuthToken);
        // 尝试从用户缓存中获取用户信息，并设置到上下文持有者中
        Optional<YeelightUserDto> userDtoOptional = this.userCache.get(yeelightUser.getId());
        if (Objects.nonNull(userDtoOptional) && userDtoOptional.isPresent()) {
            ContextHolder.setUser(userDtoOptional.get());
        }
    }

    private void extractUserBySecurityContext() {
        // 尝试从安全上下文中获取用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth.isAuthenticated() && auth.getPrincipal() instanceof YeelightUser yeelightUser) {
            // 尝试从用户缓存中获取用户信息，并设置到上下文持有者中
            Optional<YeelightUserDto> userDtoOptional = this.userCache.get(yeelightUser.getId());
            if (Objects.nonNull(userDtoOptional) && userDtoOptional.isPresent()) {
                ContextHolder.setUser(userDtoOptional.get());
            }
        }
    }

    private void extractUserByAccessToken(String accessToken) {
        OAuth2AuthenticationDto authentication = tokenService.getAuthenticationByToken(accessToken);
        // 验证令牌是否有效并获取用户信息
        if (Objects.nonNull(authentication) && authentication.isAuthenticated() && Objects.nonNull(authentication.getUserAuthentication()) && Objects.nonNull(authentication.getUserAuthentication().getPrincipal())) {
            if (authentication.getUserAuthentication().getPrincipal() instanceof YeelightUser yeelightUser) {
                // 尝试从用户缓存中获取用户信息，并设置到上下文持有者中
                Optional<YeelightUserDto> userDtoOptional = this.userCache.get(yeelightUser.getId());
                if (Objects.nonNull(userDtoOptional) && userDtoOptional.isPresent()) {
                    ContextHolder.setUser(userDtoOptional.get());
                }
            }
        }
    }

    /**
     * 检查当前环境是否为本地开发环境。
     * <p>
     * 该方法首先检查环境名称（envName）是否包含指定的本地环境名称（不区分大小写）。
     * 如果是，它将进一步检查当前上下文中是否存在用户信息。
     * 如果不存在用户信息，则认为当前环境为本地环境，并设置相应的本地环境数据。
     * </p>
     *
     * @return boolean - 如果当前环境被认为是本地环境，则返回true；否则返回false。
     */
    private boolean isLocalEnv() {
        // 检查环境名称是否包含本地环境的名称，不区分大小写
        if (StringUtils.containsIgnoreCase(envName, LOCAL_ENV_NAME)) {
            // 如果当前上下文中的用户信息为空，则设置本地环境数据并返回true
            if (Objects.isNull(ContextHolder.getUser())) {
                setLocalEnvData();
                return true;
            }
        }
        // 如果不是本地环境，则返回false
        return false;
    }

    /**
     * 设置本地环境数据。
     * 该方法用于初始化本地测试环境的数据，包括用户信息和当前时间。
     * 无参数。
     * 无返回值。
     */
    private void setLocalEnvData() {
        // 创建并初始化用户信息
        YeelightUserDto userDto = new YeelightUserDto();
        userDto.setStatus("1");
        userDto.setId(122433L);
        userDto.setName("本地测试用户");

        // 将用户信息设置到上下文中
        ContextHolder.setUser(userDto);

        // 设置当前时间戳到上下文中
        ContextHolder.now((int) Instant.now().getEpochSecond());
    }
}
