package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import org.springframework.ui.Model;

import java.util.Map;
import java.util.Objects;

/**
 * @description: 动态静态资源配置类
 * <AUTHOR>
 */
public class DynamicStaticResourceConfig {
    /**
     * 厂商名称属性
     */
    private static final String VENDOR_NAME = "VENDOR_NAME";

    /**
     * 静态图片资源路径前缀
     */
    private static final String RESOURCES_STATIC_IMG_PREFIX = "../static/img/";

    /**
     * 静态图片资源属性：favicon
     */
    private static final String RESOURCES_STATIC_IMG_FAVICON = "RESOURCES_STATIC_IMG_FAVICON";

    /**
     * 静态图片资源属性：logo
     */
    private static final String RESOURCES_STATIC_IMG_LOGO = "RESOURCES_STATIC_IMG_LOGO";

    /**
     * 静态图片资源属性：移动版logo
     */
    private static final String RESOURCES_STATIC_IMG_MOBILE_LOGO = "RESOURCES_STATIC_IMG_MOBILE_LOGO";

    /**
     * 静态图片资源属性：个人版logo
     */
    private static final String RESOURCES_STATIC_IMG_PERSON_LOGO = "RESOURCES_STATIC_IMG_PERSON_LOGO";

    /**
     * 静态图片资源属性：黑色logo
     */
    private static final String RESOURCES_STATIC_IMG_BLACK_LOGO = "RESOURCES_STATIC_IMG_BLACK_LOGO";

    /**
     * 静态图片资源属性：logo样式
     */
    private static final String RESOURCES_STATIC_IMG_LOGO_STYLE = "RESOURCES_STATIC_IMG_LOGO_STYLE";

    /**
     * 静态图片资源值：favicon
     */
    private static final String RESOURCES_STATIC_IMG_FAVICON_VALUE = "favicon.ico";

    /**
     * 静态图片资源值：logo
     */
    private static final String RESOURCES_STATIC_IMG_LOGO_VALUE = "icon-top.png";

    /**
     * 静态图片资源值：移动版logo
     */
    private static final String RESOURCES_STATIC_IMG_MOBILE_LOGO_VALUE = "logo-mb.png";

    /**
     * 静态图片资源值：个人版logo
     */
    private static final String RESOURCES_STATIC_IMG_PERSON_LOGO_VALUE = "logo-person.png";

    /**
     * 静态图片资源值：黑色logo
     */
    private static final String RESOURCES_STATIC_IMG_BLACK_LOGO_VALUE = "<EMAIL>";

    /**
     * 静态图片资源值：logo样式
     */
    private static final String RESOURCES_STATIC_IMG_LOGO_STYLE_VALUE = "background-image:url(###REPLACE###); background-repeat:no-repeat;  background-size:100%;";


    /**
     * 向模型中动态添加视图相关的属性。
     * 该方法会检查模型对象是否为null，如果非null，则向模型中添加一系列的静态图片资源属性和厂商相关信息属性，
     * 这些属性主要用于前端视图的展示。
     *
     * @param model 用于视图渲染的数据模型，不可为null。
     */
    public static void dynamicViewAttributes(Model model) {
        if (Objects.isNull(model)) {
            return;
        }
        // 为模型添加favicon图标资源路径
        model.addAttribute(RESOURCES_STATIC_IMG_FAVICON, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_FAVICON_VALUE).toLowerCase());
        // 为模型添加常规Logo图片资源路径
        model.addAttribute(RESOURCES_STATIC_IMG_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_LOGO_VALUE).toLowerCase());
        // 为模型添加移动端Logo图片资源路径
        model.addAttribute(RESOURCES_STATIC_IMG_MOBILE_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_MOBILE_LOGO_VALUE).toLowerCase());
        // 为模型添加个人Logo图片资源路径
        model.addAttribute(RESOURCES_STATIC_IMG_PERSON_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_PERSON_LOGO_VALUE).toLowerCase());
        // 为模型添加黑色版本Logo图片资源路径
        model.addAttribute(RESOURCES_STATIC_IMG_BLACK_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_BLACK_LOGO_VALUE).toLowerCase());
        // 为模型添加Logo样式，其中包含动态替换的Logo图片资源路径
        model.addAttribute(RESOURCES_STATIC_IMG_LOGO_STYLE, RESOURCES_STATIC_IMG_LOGO_STYLE_VALUE.replace("###REPLACE###", RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_LOGO_VALUE).toLowerCase()));
        // 为模型添加当前厂商的简化名称
        model.addAttribute(VENDOR_NAME, UserVendorHolder.getSimpleVendor());
    }


    /**
     * 动态添加视图属性到模型中。
     * 该方法会检查模型是否为null，如果是，则直接返回。否则，会向模型中动态添加一系列的静态图片资源和厂商信息，
     * 这些资源包括favicon、logo、移动版logo、个人版logo、黑色logo以及logo的样式。同时，还会添加厂商名称到模型中。
     *
     * @param model 用于存储视图属性的Map对象，键值对形式。
     */
    public static void dynamicViewAttributes(Map<String, Object> model) {
        if (Objects.isNull(model)) {
            return;
        }
        // 为模型添加静态图片资源：favicon
        model.put(RESOURCES_STATIC_IMG_FAVICON, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_FAVICON_VALUE).toLowerCase());
        // 为模型添加静态图片资源：logo
        model.put(RESOURCES_STATIC_IMG_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_LOGO_VALUE).toLowerCase());
        // 为模型添加静态图片资源：移动版logo
        model.put(RESOURCES_STATIC_IMG_MOBILE_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_MOBILE_LOGO_VALUE).toLowerCase());
        // 为模型添加静态图片资源：个人版logo
        model.put(RESOURCES_STATIC_IMG_PERSON_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_PERSON_LOGO_VALUE).toLowerCase());
        // 为模型添加静态图片资源：黑色logo
        model.put(RESOURCES_STATIC_IMG_BLACK_LOGO, RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_BLACK_LOGO_VALUE).toLowerCase());
        // 为模型添加logo样式，其中包含动态替换的logo路径
        model.put(RESOURCES_STATIC_IMG_LOGO_STYLE, RESOURCES_STATIC_IMG_LOGO_STYLE_VALUE.replace("###REPLACE###", RESOURCES_STATIC_IMG_PREFIX + UserVendorHolder.attachVendor(RESOURCES_STATIC_IMG_LOGO_VALUE).toLowerCase()));
        // 为模型添加厂商名称
        model.put(VENDOR_NAME, UserVendorHolder.getSimpleVendor());
    }

}
