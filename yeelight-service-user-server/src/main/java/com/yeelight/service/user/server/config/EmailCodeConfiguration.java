package com.yeelight.service.user.server.config;

import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.user.server.custom.EmailCodeAuthenticationDetailsSource;
import com.yeelight.service.user.server.custom.YeelightUserDetailsService;
import com.yeelight.service.user.server.handler.EmailCodeAuthenticationFailureHandler;
import com.yeelight.service.user.server.provider.EmailCodeAuthenticationProvider;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * 邮箱验证码登录配置
 * <AUTHOR>
 */
@Configuration
public class EmailCodeConfiguration {

    @Resource
    private YeelightUserDetailsService userDetailsService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageHelper;

    /**
     * 创建并返回一个EmailCodeAuthenticationFailureHandler实例。
     * 这个@Bean注解表明该方法将返回一个Spring容器管理的bean实例，
     * 即一个EmailCodeAuthenticationFailureHandler对象，
     * 它用于处理邮箱验证码认证失败的逻辑。
     *
     * @return EmailCodeAuthenticationFailureHandler 返回一个EmailCodeAuthenticationFailureHandler的新实例，
     * 用于处理邮箱验证码认证失败的情况。
     */
    @Bean
    public EmailCodeAuthenticationFailureHandler emailCodeAuthenticationFailureHandler() {
        return new EmailCodeAuthenticationFailureHandler();
    }

    /**
     * 创建并返回一个EmailCodeAuthenticationDetailsSource实例。
     * 这个@Bean注解表明该方法将返回一个Spring容器管理的bean实例，
     * 该实例是EmailCodeAuthenticationDetailsSource类型的。
     *
     * @return EmailCodeAuthenticationDetailsSource 返回一个用于存储和管理电子邮件验证码认证详情的实例。
     */
    @Bean
    public EmailCodeAuthenticationDetailsSource emailCodeAuthenticationDetailsSource() {
        return new EmailCodeAuthenticationDetailsSource();
    }

    /**
     * 创建并返回一个EmailCodeAuthenticationProvider实例。
     * 这个提供者用于处理基于邮箱验证码的认证流程。
     *
     * @return EmailCodeAuthenticationProvider 一个配置好的EmailCodeAuthenticationProvider实例，
     *         用于处理用户认证请求。
     */
    @Bean
    public EmailCodeAuthenticationProvider emailCodeAuthenticationProvider() {
        return new EmailCodeAuthenticationProvider(userDetailsService, captchaMessageHelper);
    }

}
