package com.yeelight.service.user.server.config;

import com.yeelight.service.user.server.filter.EmailCodeAuthenticationFilter;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.Assert;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 邮箱验证码登录配置器
 * <AUTHOR>
 */
public class EmailCodeLoginConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    private static final String DEFAULT_LOGIN_PROCESS_URL = "/authentication/email";

    private final EmailCodeAuthenticationFilter authFilter;

    private AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource;

    private AuthenticationSuccessHandler successHandler;

    private AuthenticationFailureHandler failureHandler;

    /**
     * 默认手机+短信验证码 登录处理地址 [POST "/authentication/email"]. 默认邮箱参数 - email
     */
    public EmailCodeLoginConfigurer() {
        authFilter = new EmailCodeAuthenticationFilter();
        loginProcessingUrl(DEFAULT_LOGIN_PROCESS_URL);
        emailParameter("email");
    }

    /**
     * 设置认证过滤器中的电子邮件参数。
     * 该方法用于将给定的电子邮件参数设置到认证过滤器中，以便于后续的认证流程中使用。
     *
     * @param emailParameter 一个字符串，代表电子邮件参数。此参数将被设置到认证过滤器中。
     */
    public void emailParameter(String emailParameter) {
        // 将传入的电子邮件参数设置到认证过滤器中
        authFilter.setEmailParameter(emailParameter);
    }

    /**
     * 设置登录处理URL的匹配器。
     * 该方法用于配置认证过滤器，以指定哪些请求需要进行认证。
     *
     * @param loginProcessingUrl 登录处理的URL字符串。该URL将用于创建一个请求匹配器，
     *                           以便确定哪些HTTP请求应该被认证过滤器拦截和处理。
     */
    public void loginProcessingUrl(String loginProcessingUrl) {
        // 创建一个登录处理URL的匹配器，并设置到认证过滤器中
        authFilter.setRequiresAuthenticationRequestMatcher(createLoginProcessingUrlMatcher(loginProcessingUrl));
    }

    /**
     * 配置认证详情源。
     * <p>
     * 该方法用于设置在进行邮箱验证码登录时的认证详情源。认证详情源负责从HTTP请求中构建认证详情对象。
     * 通过这个方法，可以自定义如何从请求中提取必要的登录信息。
     *
     * @param authenticationDetailsSource 认证详情源，负责从HttpServletRequest中构建认证详情对象。
     * @return 返回EmailCodeLoginConfigurer的实例，支持链式调用。
     */
    public EmailCodeLoginConfigurer authenticationDetailsSource(
                    AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource) {
        this.authenticationDetailsSource = authenticationDetailsSource;
        return this;
    }

    /**
     * 设置认证成功后的处理逻辑。
     *
     * @param successHandler 认证成功后执行的处理逻辑，类型为AuthenticationSuccessHandler。
     * @return 返回EmailCodeLoginConfigurer的实例，支持链式调用。
     */
    public EmailCodeLoginConfigurer successHandler(AuthenticationSuccessHandler successHandler) {
        // 设置认证成功后的处理处理器
        this.successHandler = successHandler;
        // 支持链式调用
        return this;
    }

    /**
     * 设置认证失败处理器。
     * 该方法用于配置当用户认证失败时（如用户名或密码错误）的处理逻辑。
     *
     * @param failureHandler 认证失败处理器，实现了AuthenticationFailureHandler接口的对象。
     */
    public EmailCodeLoginConfigurer failureHandler(AuthenticationFailureHandler failureHandler) {
        this.failureHandler = failureHandler;
        return this;
    }

    /**
     * 创建登录处理URL的匹配器。
     * 本方法用于创建一个AntPathRequestMatcher对象，它将根据指定的登录处理URL和请求方法（POST）来匹配请求。
     *
     * @param loginProcessingUrl 登录处理URL，即用户提交登录表单的URL。
     * @return 返回一个配置好的AntPathRequestMatcher对象，用于匹配登录请求。
     */
    protected RequestMatcher createLoginProcessingUrlMatcher(String loginProcessingUrl) {
        return new AntPathRequestMatcher(loginProcessingUrl, "POST");
    }

    /**
     * 配置HttpSecurity以增强安全性。
     * 该方法会将自定义的认证过滤器添加到Spring Security的过滤链中，
     * 并设置认证成功和失败的处理器，以及认证详情的来源。
     *
     * @param http 用于配置HTTP安全性的对象。
     */
    @Override
    public void configure(HttpSecurity http) {
        // 确保successHandler和failureHandler不为null
        Assert.notNull(successHandler, "successHandler should not be null.");
        Assert.notNull(failureHandler, "failureHandler should not be null.");
        // 设置认证管理器
        authFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        // 如果successHandler非null，则设置认证成功处理器
        if (this.successHandler != null) {
            authFilter.setAuthenticationSuccessHandler(this.successHandler);
        }
        // 如果failureHandler非null，则设置认证失败处理器
        if (this.failureHandler != null) {
            authFilter.setAuthenticationFailureHandler(this.failureHandler);
        }
        // 如果authenticationDetailsSource非null，则设置认证详情来源
        if (this.authenticationDetailsSource != null) {
            authFilter.setAuthenticationDetailsSource(this.authenticationDetailsSource);
        }
        // 将自定义的认证过滤器添加到UsernamePasswordAuthenticationFilter之后
        http.addFilterAfter(authFilter, UsernamePasswordAuthenticationFilter.class);
    }

}
