package com.yeelight.service.user.server.config;

import com.xkcoding.http.exception.SimpleHttpException;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.exception.GlobalExceptionHandler;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.ResultCodeEnum;
import com.yeelight.service.user.client.exception.NotLoginException;
import com.yeelight.service.user.server.controller.*;
import com.yeelight.service.user.server.controller.thirdparty.AlexaController;
import com.yeelight.service.user.server.controller.thirdparty.AppleController;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.exception.AuthException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

/**
 * @description: 全局异常处理器
 * <AUTHOR>
 */
@ControllerAdvice(assignableTypes = {
        AppleController.class,
        AlexaController.class,
        ScanLoginController.class,
        ThirdPartyAuthController.class,
        UserController.class,
        PublicController.class,
        PersonalInfoManageController.class,
        JwtAuthController.class,
        JwtSocialController.class,
        JwtUserController.class,
        JwtUnAuthController.class,
        JwtUserSessionController.class,
        JwtUserTokenController.class,
})
@Slf4j
public class ExceptionHandler extends GlobalExceptionHandler {

    /**
     * 处理身份验证异常的异常处理器。
     * 当遇到AuthException异常时，返回一个指定的状态码和错误信息。
     *
     * @param ex AuthException 异常对象，包含具体的认证错误信息。
     * @return Result 返回一个结果对象，其中包含错误状态码和错误信息。
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @org.springframework.web.bind.annotation.ExceptionHandler({AuthException.class})
    @ResponseBody
    public Result<?> authException(AuthException ex) {
        // 记录身份验证请求错误的警告日志
        log.warn("auth request error:{}", ExceptionUtils.getStackTrace(ex));
        // 返回一个表示身份验证异常的结果对象
        return Result.failure(ResultCodeEnum.BAD_REQUEST, "身份验证异常");
    }

    /**
     * 处理业务异常的异常处理器。
     * 当抛出 {@link BizException} 时，此处理器将被调用，并返回一个表示失败的结果对象。
     *
     * @param ex 业务异常对象，包含错误的详细信息。
     * @return 返回一个表示操作失败的结果对象，其中会包含异常的信息。
     */
    @Override
    @ResponseStatus(HttpStatus.OK)
    @org.springframework.web.bind.annotation.ExceptionHandler({BizException.class})
    @ResponseBody
    public Result<?> bizException(BizException ex) {
        // 记录业务异常信息
        log.info("BizException error:{}", ex.getMessage());
        // 返回一个表示失败的结果对象
        return Result.failure(ex);
    }

    /**
     * 处理SimpleHttpException异常的处理器。
     * 当出现SimpleHttpException异常时，返回一个指定的错误结果。
     *
     * @param ex SimpleHttpException 异常实例，表示发生的简单HTTP异常。
     * @return Result 返回一个表示操作结果的对象，其中包含错误代码和消息。
     *         这里返回的是一个指定的错误代码和"网络请求异常"消息。
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @org.springframework.web.bind.annotation.ExceptionHandler({SimpleHttpException.class})
    @ResponseBody
    public Result<?> simpleHttpException(SimpleHttpException ex) {
        // 记录异常信息，包括堆栈跟踪
        log.warn("http request error:{}", ExceptionUtils.getStackTrace(ex));
        // 返回一个表示失败结果的对象，其中包含预定义的错误代码和消息
        return Result.failure(ResultCodeEnum.BAD_REQUEST, "网络请求异常");
    }

    /**
     * 处理用户未登录或用户信息不存在的异常。
     * 当用户在进行需要登录状态的操作时，如果未登录或用户信息无法查到，会抛出此异常。
     *
     * @param ex 未登录异常对象，包含具体的错误信息。
     * @return 返回一个结果对象，标识操作失败并给出未授权（UNAUTHORIZED）的错误码和错误信息。
     *         该结果对象会作为HTTP响应体返回给客户端。
     */
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    @org.springframework.web.bind.annotation.ExceptionHandler({NotLoginException.class})
    @ResponseBody
    public Result<?> notLoginException(NotLoginException ex) {
        // 记录未登录或用户不存在的异常信息
        log.warn("用户未登录或查不到:{}", ExceptionUtils.getStackTrace(ex));
        // 返回未授权错误结果
        return Result.failure(ResultCodeEnum.UNAUTHORIZED, ex.getMessage());
    }

    /**
     * 处理认证服务异常的控制器方法。
     * 当发生{@link AuthenticationServiceException}时，此方法将被调用，并返回一个表示认证失败的结果对象。
     *
     * @param ex 异常对象，包含认证失败的详细信息。
     * @return 返回一个表示认证失败的结果对象，其中包含错误代码和错误消息。
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @org.springframework.web.bind.annotation.ExceptionHandler({AuthenticationServiceException.class})
    @ResponseBody
    public Result<?> authenticationServiceException(AuthenticationServiceException ex) {
        // 记录认证失败的警告日志，并包含异常的堆栈跟踪
        log.warn("Authentication failed:{}", ExceptionUtils.getStackTrace(ex));
        // 返回一个认证失败的结果，包含错误代码和错误消息
        return Result.failure(ResultCodeEnum.BAD_REQUEST, "认证失败:" + ex.getMessage());
    }

    /**
     * 处理文件上传大小超限异常。
     * Spring Boot 3.x中使用MaxUploadSizeExceededException替代commons-fileupload的异常。
     *
     * @param ex 文件上传大小超限异常对象。
     * @return 返回一个结果对象，包含文件大小超限的错误信息。
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @org.springframework.web.bind.annotation.ExceptionHandler({MaxUploadSizeExceededException.class})
    @ResponseBody
    public Result<?> maxUploadSizeExceededException(MaxUploadSizeExceededException ex) {
        log.warn("文件上传大小超限:{}", ex.getMessage());
        return Result.failure(ResultCodeEnum.BAD_REQUEST, "上传文件大小超过限制");
    }

    /**
     * 处理所有未被其他异常处理器捕获的异常。
     *
     * @param ex 异常对象，表示发生的错误。
     * @return 返回一个结果对象，该结果对象表示操作失败，并包含异常信息。
     */
    @Override
    @org.springframework.web.bind.annotation.ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public Result<?> exception(Exception ex) {
        // 记录异常堆栈信息到日志
        log.error(ExceptionUtils.getStackTrace(ex));
        // 返回一个失败结果，包含异常信息
        return Result.failure(ex.getMessage());
    }
}
