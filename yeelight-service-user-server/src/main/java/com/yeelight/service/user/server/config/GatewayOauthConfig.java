package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.OriginalRequestContextHolder;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.utils.UrlUtil;
import lombok.Data;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Objects;

/**
 * @program: yeelight-oauth-api
 * @description: 网关Oauth配置类
 * @author: Sheldon
 * @create: 2019-11-25 19:44
 **/
@Component
@Data
public class GatewayOauthConfig {
    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private AppConfig appConfig;

    /**
     * 始终使用HTTP的域名前缀
     */
    private static final String ALWAYS_HTTP_DOMAIN_TEST_PREFIX = "test";

    private static final String ALWAYS_HTTP_DOMAIN_DEV_PREFIX = "dev";

    /**
     * 获取指定请求的前缀URL。
     * 该方法通过调用{@code getUrl}方法，使用预定义的前缀路径常量{@code SecurityConstants.PREFIX_PATH}，
     * 并将当前请求对象{@code httpServletRequest}和{@code null}作为参数传递给{@code getUrl}方法，
     * 以获取处理后的URL前缀。
     *
     * @param httpServletRequest 当前的HTTP请求对象，用于获取请求信息。
     * @return 返回处理后的URL前缀字符串。
     */
    public String getPrefixUrl(HttpServletRequest httpServletRequest) {
        return getUrl(httpServletRequest, SecurityConstants.PREFIX_PATH, null);
    }

    /**
     * 根据给定的 HttpServletRequest 和路径获取配置的URL。
     * @param httpServletRequest 当前HTTP请求对象。
     * @param path 配置路径。
     * @return 拼接好的完整URL字符串。
     */
    public String getConfigUrl(HttpServletRequest httpServletRequest, String path) {
        return getUrl(httpServletRequest, SecurityConstants.PREFIX_PATH + path, null);
    }

    /**
     * 根据给定的信息构建完整的URL。
     * @param httpServletRequest 当前HTTP请求对象。
     * @param path URL的路径部分。
     * @param query 查询参数字符串，可以为null。
     * @return 拼接好的完整URL字符串。
     */
    public String getUrl(HttpServletRequest httpServletRequest, String path, String query) {
        // 如果请求的前缀主机地址为空，则只返回路径部分
        if (StringUtils.isBlank(getPrefixHost(httpServletRequest))) {
            return path;
        }
        // 否则，构建并返回完整的URL
        return UrlUtil.buildFullRequestUrl(getPrefixProtocol(httpServletRequest), getPrefixHost(httpServletRequest), getPrefixPort(httpServletRequest), path, query);
    }

    /**
     * 获取请求的协议前缀。
     * @param httpServletRequest 当前HTTP请求对象。
     * @return 协议字符串，如"http"或"https"。
     */
    public String getPrefixProtocol(HttpServletRequest httpServletRequest) {
        String prefixHost = getPrefixHost(httpServletRequest);
        // 如果前缀主机不为空且不包含测试或开发环境的前缀，则检查是否为供应商域名, 如果是供应商域名则返回https
        if (StringUtils.isNotBlank(prefixHost) && !prefixHost.contains(ALWAYS_HTTP_DOMAIN_TEST_PREFIX) && !prefixHost.contains(ALWAYS_HTTP_DOMAIN_DEV_PREFIX)) {
            if (Arrays.stream(UserVendorEnum.values()).anyMatch(userVendorEnum -> prefixHost.contains(userVendorEnum.getDomain()))) {
                return "https";
            }
        }
        // 优先使用原始请求的协议，若无则默认为"http"
        if (StringUtils.isBlank(OriginalRequestContextHolder.getOriginalProtocol())) {
            // 从请求中获取原始协议
            return StringUtils.defaultIfBlank(OriginalRequestContextHolder.getOriginalProtocolByRequest(httpServletRequest), Constants.HTTP_KEY);
        }
        // 返回原始协议
        return StringUtils.defaultIfBlank(OriginalRequestContextHolder.getOriginalProtocol(), Constants.HTTP_KEY);
    }


    /**
     * 获取请求的前缀主机名。
     * 该方法用于从HttpServletRequest中提取原始请求的域名，并去除"http://"或"https://"前缀。
     * 如果无法获取原始域名，则返回空字符串。
     *
     * @param httpServletRequest 当前的HTTP请求对象，用于获取原始请求信息。
     * @return 原始请求的主机名，去除了"http://"或"https://"前缀；如果无法获取，则返回空字符串。
     */
    public String getPrefixHost(HttpServletRequest httpServletRequest) {
        // 尝试从OriginalRequestContextHolder中获取原始域名，优先使用已缓存的域名
        String originalHost;
        if (StringUtils.isBlank(OriginalRequestContextHolder.getOriginalDomain())) {
            // 如果缓存中没有原始域名，则尝试从当前请求中获取
            originalHost = StringUtils.defaultIfBlank(OriginalRequestContextHolder.getOriginalDomainByRequest(httpServletRequest), "");

        } else {
            // 使用缓存中的原始域名
            originalHost = StringUtils.defaultIfBlank(OriginalRequestContextHolder.getOriginalDomain(), "");
        }

        // 如果获取到的原始域名为空，则直接返回空字符串
        if (StringUtils.isBlank(originalHost)) {
            return "";
        } else {
            // 移除"http://"或"https://"前缀，返回处理后的主机名
            return originalHost.replace("http://", "").replace("https://", "");
        }
    }

    /**
     * 获取请求的前缀路径。
     * 该方法会优先尝试从OriginalRequestContextHolder中获取原始路径，如果未设置，则通过HttpServletRequest请求再次尝试获取。
     *
     * @param httpServletRequest 当前的HttpServletRequest请求对象，用于获取原始路径信息。
     * @return 返回请求的前缀路径，如果未设置则返回空字符串。
     */
    public String getPrefixPath(HttpServletRequest httpServletRequest) {
        // 尝试从缓存中获取原始路径，如果为空则通过请求对象尝试获取
        if (StringUtils.isBlank(OriginalRequestContextHolder.getOriginalPath())) {
            return StringUtils.defaultIfBlank(OriginalRequestContextHolder.getOriginalPathByRequest(httpServletRequest), "");
        }
        return StringUtils.defaultIfBlank(OriginalRequestContextHolder.getOriginalPath(), "");
    }

    /**
     * 获取请求的前缀端口。
     * 该方法会优先尝试从OriginalRequestContextHolder中获取原始端口，如果未设置，则通过HttpServletRequest请求再次尝试获取。
     * 若获取的端口字符串为空或无法解析为整数，则默认返回80。
     *
     * @param httpServletRequest 当前的HttpServletRequest请求对象，用于获取原始端口信息。
     * @return 返回请求的前缀端口，如果未设置或无法解析则返回80。
     */
    public Integer getPrefixPort(HttpServletRequest httpServletRequest) {
        // 尝试从缓存中获取原始端口，如果为空则通过请求对象尝试获取
        if (Objects.isNull(OriginalRequestContextHolder.getOriginalPort())) {
            String originalPortStr = OriginalRequestContextHolder.getOriginalPortByRequest(httpServletRequest);
            return StringUtils.isNotBlank(originalPortStr) ? Integer.parseInt(originalPortStr) : 80;
        }
        // 检查获取的端口是否有效，无效则默认返回80
        return (Objects.nonNull(OriginalRequestContextHolder.getOriginalPort()) && OriginalRequestContextHolder.getOriginalPort() > 0) ? OriginalRequestContextHolder.getOriginalPort() : 80;
    }

    /**
     * 获取原始URL
     * @param httpServletRequest 当前HTTP请求对象
     * @return 原始URL字符串。如果前缀主机为空，则返回空字符串。
     */
    public String getOriginalUrl(HttpServletRequest httpServletRequest) {
        // 检查前缀主机是否为空，为空则直接返回空字符串
        if (StringUtils.isBlank(getPrefixHost(httpServletRequest))) {
            return "";
        }
        // 获取原始路径的URL
        return getUrl(httpServletRequest, OriginalRequestContextHolder.getOriginalPath(), null);
    }

    /**
     * 获取首页页面URL
     * @param httpServletRequest 当前HTTP请求对象
     * @return 首页页面的URL字符串。根据配置确定是绝对URL还是基于前缀的URL。
     */
    public String getIndexPageUrl(HttpServletRequest httpServletRequest) {
        // 获取配置中的首页页面URL
        String indexPageUrl = appConfig.getIndexPageUrl();
        // 判断URL是否为绝对路径，是则使用真实供应商域名，否则基于请求前缀构建URL
        return UrlUtils.isAbsoluteUrl(indexPageUrl) ? realVendorDomain(httpServletRequest, indexPageUrl) : getPrefixUrl(httpServletRequest) + indexPageUrl;
    }

    /**
     * 判断前后端是否分离
     * @return 前后端是否分离的布尔值。
     */
    public Boolean isFrontendBackendSeparated() {
        // 返回配置中是否前后端分离的设置
        return appConfig.getFrontendBackendSeparated();
    }

    /**
     * 获取登录页面URL
     * @param httpServletRequest 当前HTTP请求对象
     * @return 登录页面的URL字符串。根据配置确定是绝对URL还是基于前缀的URL。
     */
    public String getLoginPageUrl(HttpServletRequest httpServletRequest) {
        // 获取配置中的登录页面URL
        String loginPageUrl = appConfig.getLoginPageUrl();
        // 判断URL是否为绝对路径，是则使用真实供应商域名，否则基于请求前缀构建URL
        return UrlUtils.isAbsoluteUrl(loginPageUrl) ? realVendorDomain(httpServletRequest, loginPageUrl) : getPrefixUrl(httpServletRequest) + loginPageUrl;
    }


    public String getRegisterPageUrl(HttpServletRequest httpServletRequest) {
        String registerPageUrl = appConfig.getRegisterPageUrl();
        return UrlUtils.isAbsoluteUrl(registerPageUrl) ? realVendorDomain(httpServletRequest, registerPageUrl) : getPrefixUrl(httpServletRequest) + registerPageUrl;
    }

    /**
     * 获取授权页面的URL。
     * 如果配置的授权页面URL是绝对URL，则直接返回该URL；如果是相对URL，则将其与请求的前缀URL拼接后返回。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求的前缀URL。
     * @return 授权页面的完整URL。
     */
    public String getGrantPageUrl(HttpServletRequest httpServletRequest) {
        String grantPageUrl = appConfig.getGrantPageUrl();
        // 判断URL是否为绝对路径，是则直接返回，否则拼接前缀返回
        return UrlUtils.isAbsoluteUrl(grantPageUrl) ? realVendorDomain(httpServletRequest, grantPageUrl) : getPrefixUrl(httpServletRequest) + grantPageUrl;
    }

    /**
     * 获取重置密码页面的URL。
     * 如果配置的重置密码页面URL是绝对URL，则直接返回该URL；如果是相对URL，则将其与请求的前缀URL拼接后返回。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求的前缀URL。
     * @return 重置密码页面的完整URL。
     */
    public String getResetPasswordUrl(HttpServletRequest httpServletRequest) {
        String resetPasswordUrl = appConfig.getResetPasswordUrl();
        // 判断URL是否为绝对路径，是则直接返回，否则拼接前缀返回
        return UrlUtils.isAbsoluteUrl(resetPasswordUrl) ? realVendorDomain(httpServletRequest, resetPasswordUrl) : getPrefixUrl(httpServletRequest) + resetPasswordUrl;
    }

    /**
     * 获取错误页面的URL。
     * 如果配置的错误页面URL是绝对URL，则直接返回该URL；如果是相对URL，则将其与请求的前缀URL拼接后返回。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求的前缀URL。
     * @return 错误页面的完整URL。
     */
    public String getErrorUrl(HttpServletRequest httpServletRequest) {
        String errorUrl = appConfig.getErrorUrl();
        // 判断URL是否为绝对路径，是则直接返回，否则拼接前缀返回
        return UrlUtils.isAbsoluteUrl(errorUrl) ? realVendorDomain(httpServletRequest, errorUrl) : getPrefixUrl(httpServletRequest) + errorUrl;
    }

    /**
     * 获取实际的供应商域名。
     * 该方法会检查给定的URL是否包含当前服务器的主机名，如果不包含，则将其替换为默认供应商的域名。
     *
     * @param httpServletRequest 当前HTTP请求对象，用于获取服务器的主机名。
     * @param url 需要检查和修改的URL字符串。
     * @return 经过处理后的URL，如果原始URL已经包含服务器主机名则不变，否则替换为默认供应商域名。
     */
    private String realVendorDomain(HttpServletRequest httpServletRequest, String url) {
        // 如果URL为空或白名单，则直接返回原始URL
        if (StringUtils.isBlank(url)) {
            return url;
        }
        // 获取当前服务器的主机名
        String serverHost = getPrefixHost(httpServletRequest);
        // 默认供应商枚举
        UserVendorEnum defaultVendorEnum = UserVendorEnum.DEFAULT;

        // 如果URL中已经包含服务器主机名，则直接返回原始URL
        if (url.contains(serverHost)) {
            return url;
        }
        // 将URL中的服务器主机名替换为默认供应商的域名
        return url.replace(serverHost, defaultVendorEnum.getDomain());
    }
}
