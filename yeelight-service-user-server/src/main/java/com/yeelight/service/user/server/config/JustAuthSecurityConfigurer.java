package com.yeelight.service.user.server.config;

import com.yeelight.service.user.server.filter.JustAuthAuthenticationFilter;
import com.yeelight.service.user.server.handler.CustomAuthenticationSuccessHandler;
import com.yeelight.service.user.server.handler.CustomThirdpartyAuthenticationFailureHandler;
import com.yeelight.service.user.server.provider.JustAuthAuthenticationProvider;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import me.zhyd.oauth.config.JustAuthLogConfig;
import me.zhyd.oauth.log.Log;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import jakarta.annotation.Resource;

/**
 * JustAuthSecurityConfigurer
 * @description: JustAuth安全配置器，用于配置JustAuth的安全认证流程。
 * <AUTHOR> Yu
 * @date 9/14/21 4:22 PM
 */
@Configuration
public class JustAuthSecurityConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {
    @Resource
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;
    @Resource
    private JustAuthAuthenticationProvider justAuthAuthenticationProvider;
    @Resource
    private CustomAuthenticationSuccessHandler successHandler;
    @Resource
    private CustomThirdpartyAuthenticationFailureHandler failureHandler;

    /**
     * 配置HTTP安全设置，用于定制应用程序的安全需求。
     * 这个方法会设置认证管理器、成功和失败的处理器，以及将自定义认证过滤器添加到HTTP安全链中。
     *
     * @param http 用于配置HTTP安全性的对象，允许定制请求的过滤和安全规则。
     */
    @Override
    public void configure(HttpSecurity http) {
        // 设置日志级别为错误
        JustAuthLogConfig.setLevel(Log.Level.ERROR);

        // 创建并配置JustAuth认证过滤器
        JustAuthAuthenticationFilter filter = new JustAuthAuthenticationFilter();
        // 设置认证管理器
        filter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
        // 设置认证成功处理器
        filter.setAuthenticationSuccessHandler(successHandler);
        // 设置认证失败处理器
        filter.setAuthenticationFailureHandler(failureHandler);
        // 设置Yeelight认证请求工厂
        filter.setYeelightAuthRequestFactory(yeelightAuthRequestFactory);

        // 将认证提供者和过滤器添加到HttpSecurity配置中
        // 设置认证提供者
        http.authenticationProvider(justAuthAuthenticationProvider)
                // 将认证过滤器添加到用户名密码认证过滤器之前
                .addFilterBefore(filter, UsernamePasswordAuthenticationFilter.class);
    }
}
