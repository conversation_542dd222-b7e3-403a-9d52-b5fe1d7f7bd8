/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.config
 * Description: jwt 登录方式 配置
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-25 11:12:11:12
 */
package com.yeelight.service.user.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Desc: jwt 登录方式 配置
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-25 11:12:11:12
 */
@Data
@ConfigurationProperties(prefix = "jwt")
@Configuration
public class JwtAuthProperties {
    private Claims claims = new Claims();

    private String secret;

    private Type type = Type.RANDOM;

    private Long maxTokens = 20L;

    public enum Type {
        // 随机生成
        RANDOM, FOREVER
    }

    @Data
    public static class Claims {
        private String issuer = "Certification Center";
        private String audience = "Client";
        private String subject = "JwtAuth";
        private Long expirationTimeDays = 30L;
    }
}
