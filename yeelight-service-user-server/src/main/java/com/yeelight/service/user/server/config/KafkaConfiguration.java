package com.yeelight.service.user.server.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: yeelight-service-user
 * @description: Kafka配置类
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-04-18 10:44
 **/
@Configuration
@ConditionalOnProperty("kafka.enable")
public class KafkaConfiguration {
    @Value("${kafka.bootstrap.servers}")
    private String kafkaBrokers;

    /**
     * 配置Kafka监听器容器工厂。该方法创建一个并发的Kafka监听器容器工厂，用于监听Kafka消息。
     * 该配置仅在属性"kafka.enable"为true时生效。
     *
     * @return ConcurrentKafkaListenerContainerFactory<String, String> 返回配置好的并发Kafka监听器容器工厂，
     *         该工厂可用于创建能够监听字符串类型键值对消息的监听器容器。
     */
    @Bean
    @ConditionalOnProperty("kafka.enable")
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        // 创建并发Kafka监听器容器工厂
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();

        // 设置消费者工厂，使用提供的Kafka服务器配置创建
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(consumerProps(this.kafkaBrokers)));

        // 设置消息确认模式为手动立即确认，以确保消息被正确处理后才进行确认
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);

        // 设置并发度为1，即同时只有一个线程处理消息
        factory.setConcurrency(1);

        return factory;
    }


    /**
     * 创建并返回一个用户事件的Kafka模板，用于发送字符串类型的消息。
     * 这个方法只有在属性"kafka.enable"为真时才会被调用。
     *
     * @return KafkaTemplate<String, String> 一个配置好的Kafka模板，用于发送字符串键值对的消息。
     */
    @Bean(name = "userEventKafkaTemplate")
    @ConditionalOnProperty("kafka.enable")
    public KafkaTemplate<String, String> userEventKafkaTemplate() {
        // 使用提供的配置创建KafkaProducerFactory，并基于该工厂创建KafkaTemplate实例
        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(producerProps(this.kafkaBrokers)));
    }

    /**
     * 配置消费者的参数
     *
     * @param kafkaBrokers Kafka broker的连接地址，多个地址用逗号分隔
     * @return 包含消费者配置的Map对象
     */
    private Map<String, Object> consumerProps(String kafkaBrokers) {
        Map<String, Object> props = new HashMap<>(7);

        // 初始化基本的消费者配置
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
        // 设置是否自动提交offset
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        // 设置offset重置策略为earliest
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        // 设置Session超时时间
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "15000");
        // 设置每次poll返回的最大消息条数
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, "1");
        // 设置键的反序列化方式为StringDeserializer
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        // 设置值的反序列化方式为StringDeserializer
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        return props;
    }


    /**
     * 配置Kafka生产者的参数。
     *
     * @param kafkaBrokers Kafka broker的地址，多个地址用逗号分隔。
     * @return 包含配置属性的Map对象，可用于创建Kafka生产者实例。
     */
    private Map<String, Object> producerProps(String kafkaBrokers){
        Map<String, Object> props = new HashMap<>(12);
        // 设置连接到Kafka集群的初始地址。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBrokers);
        // 设置消息发送的重试次数。0表示不重试。
        props.put(ProducerConfig.RETRIES_CONFIG, 5);
        // 设置消息发送失败后的重试间隔时间。默认100ms，建议不要设置太大或者太小。
        props.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 300);
        // 设置生产者批处理的大小，单位为字节
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 32768);
        // 设置消息在本地等待时间，以便将多个消息组成一批批量发送，延迟为1毫秒，启用该功能能有效减少生产者发送消息次数，从而提高并发量
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        // 设置生产者可用于缓冲等待发送消息的最大内存。
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 1024000);
        // 设置消息写入成功的确认机制。ISR列表里面，所有副本都写完以后，消息才算写入成功
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        // 启用幂等性，确保消息不被重复处理。
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        // 限制每个连接的最大未完成请求数量，以启用幂等性。启用幂等需要使用max.in.flight.requests.per.connection,连接小于或等于5，重试大于0且ack必须为“all”
        props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1);
        // 设置消息的压缩类型。建议采用lz4压缩类型，具备较高的压缩比及吞吐量；由于Kafka对CPU的要求并不高，所以，可以通过压缩，充分利用CPU资源以提升网络吞吐量
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4");
        // 设置键的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        // 设置值的序列化方式。
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return props;
    }

}
