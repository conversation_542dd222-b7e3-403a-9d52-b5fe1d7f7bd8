package com.yeelight.service.user.server.config;

import com.yeelight.service.user.server.custom.grant.OAuth2ResourceOwnerPasswordAuthenticationConverter;
import com.yeelight.service.user.server.exception.NewCustomAuthenticationEntryPoint;
import com.yeelight.service.user.server.exception.NewCustomOAuth2ErrorHandler;
import com.yeelight.service.user.server.custom.NewCustomRedirectUriValidator;
import com.yeelight.service.user.server.custom.NewCustomAuthorizationConsentCustomizer;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationContext;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationProvider;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationValidator;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.security.web.util.matcher.MediaTypeRequestMatcher;
import org.springframework.security.web.authentication.AuthenticationConverter;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;

import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

/**
 * JDK 21兼容的OAuth2授权服务器配置
 * 使用Spring Boot 3.x的标准OAuth2配置，避免CGLIB代理问题
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Configuration
public class OAuth2Configuration {

    /**
     * 注入自定义组件
     */
    @Resource
    private NewCustomRedirectUriValidator newCustomRedirectUriValidator;

    /**
     * 注入新版自定义授权同意定制器
     * ✅ 已替代 AuthorizationServerConfiguration.userApprovalHandler()
     */
    @Resource
    private NewCustomAuthorizationConsentCustomizer newCustomAuthorizationConsentCustomizer;

    /**
     * 注入新版自定义认证入口点
     */
    @Resource
    private NewCustomAuthenticationEntryPoint newCustomAuthenticationEntryPoint;

    /**
     * 注入新版自定义OAuth2错误处理器
     */
    @Resource
    private NewCustomOAuth2ErrorHandler newCustomOAuth2ErrorHandler;

    /**
     * 注入认证管理器 - 与旧版保持一致，使用authenticationManagerBean
     */
    @Resource
    @Qualifier("authenticationManagerBean")
    private AuthenticationManager authenticationManager;

    /**
     * 注入密码编码器
     */
    @Resource
    @Qualifier("newPasswordEncoder")
    private PasswordEncoder passwordEncoder;

    /**
     * 注入JWT解码器
     */
    @Resource
    @Qualifier("newJwtDecoder")
    private JwtDecoder jwtDecoder;

    /**
     * 注入JWT Token定制器
     */
    @Resource
    @Qualifier("newJwtTokenCustomizer")
    private OAuth2TokenCustomizer<JwtEncodingContext> jwtTokenCustomizer;

    /**
     * 注入Password模式认证转换器
     */
    @Resource
    @Qualifier("newPasswordAuthenticationConverter")
    private OAuth2ResourceOwnerPasswordAuthenticationConverter passwordAuthenticationConverter;

    /**
     * 注入Password模式认证提供者 - 使用接口类型避免代理问题
     */
    @Resource
    @Qualifier("newPasswordAuthenticationProvider")
    private AuthenticationProvider passwordAuthenticationProvider;

    /**
     * 注入Implicit模式认证提供者 - 使用接口类型避免代理问题
     */
    @Resource
    @Qualifier("newImplicitAuthenticationProvider")
    private AuthenticationProvider implicitAuthenticationProvider;



    /**
     * OAuth2授权服务器安全过滤链
     * 完整的OAuth2配置，包含所有必要的端点和自定义处理器
     */
    @Bean
    @Order(0)
    public SecurityFilterChain authorizationServerSecurityFilterChain(
            HttpSecurity http,
            @Qualifier("newRegisteredClientRepository") RegisteredClientRepository registeredClientRepository,
            @Qualifier("newCustomRedisAuthorizationService") OAuth2AuthorizationService authorizationService,
            @Qualifier("newOAuth2AuthorizationConsentService") OAuth2AuthorizationConsentService authorizationConsentService,
            @Qualifier("newAuthorizationServerSettings") AuthorizationServerSettings authorizationServerSettings,
            @Qualifier("newOAuth2TokenGenerator") OAuth2TokenGenerator tokenGenerator,
            @Qualifier("newDefaultTokenSettings") TokenSettings tokenSettings) throws Exception {
        log.info("🔧 配置JDK 21兼容的完整OAuth2授权服务器");
        log.info("🔧 使用自定义授权同意服务: {}", authorizationConsentService.getClass().getSimpleName());
        log.info("🔧 使用Token设置: 访问令牌有效期={}天", tokenSettings.getAccessTokenTimeToLive().toDays());
        log.info("🔧 使用认证管理器: {}", authenticationManager.getClass().getSimpleName());
        log.info("🔧 使用密码编码器: {}", passwordEncoder.getClass().getSimpleName());
        log.info("🔧 使用JWT Token定制器: {}", jwtTokenCustomizer.getClass().getSimpleName());

        // 使用标准的OAuth2授权服务器配置 - 修复endpointsMatcher为null的问题
        OAuth2AuthorizationServerConfigurer authorizationServerConfigurer = OAuth2AuthorizationServerConfigurer.authorizationServer();

        http
                .securityMatcher(authorizationServerConfigurer.getEndpointsMatcher())
                .with(authorizationServerConfigurer, (authorizationServer) -> {
                    // 配置所有OAuth2授权服务器组件 - 使用同一个配置器实例
                    authorizationServer
                        .registeredClientRepository(registeredClientRepository)
                        .authorizationService(authorizationService)
                        .authorizationConsentService(authorizationConsentService)
                        .authorizationServerSettings(authorizationServerSettings)
                        .tokenGenerator(tokenGenerator)
                        .authorizationEndpoint(authorizationEndpoint ->
                            authorizationEndpoint.authenticationProviders(configureAuthenticationValidators())
                        )
                        .tokenEndpoint(tokenEndpoint -> {
                            // 添加PKCE客户端认证转换器 - 与旧版YeelightClientCredentialsTokenEndpointFilter保持兼容
                            tokenEndpoint.accessTokenRequestConverter(newYeelightClientAuthenticationConverter());
                            // 添加Password模式认证转换器 - 与旧版保持兼容
                            tokenEndpoint.accessTokenRequestConverter(passwordAuthenticationConverter);
                            // 添加自定义认证提供者
                            tokenEndpoint.authenticationProvider(passwordAuthenticationProvider);
                            tokenEndpoint.authenticationProvider(implicitAuthenticationProvider);
                        })
                        .oidc(Customizer.withDefaults()); // 启用OpenID Connect 1.0
                })
                .authorizeHttpRequests((authorize) ->
                        authorize.anyRequest().authenticated()
                );

        // 配置OAuth2资源服务器 - 使用自定义JWT解码器
        http.oauth2ResourceServer(oauth2ResourceServer ->
            oauth2ResourceServer.jwt(jwt -> jwt.decoder(jwtDecoder))
        );

        // 配置异常处理
        http.exceptionHandling((exceptions) -> exceptions
            .defaultAuthenticationEntryPointFor(
                new LoginUrlAuthenticationEntryPoint("/login"),
                new MediaTypeRequestMatcher(MediaType.TEXT_HTML)
            )
            .authenticationEntryPoint(newCustomAuthenticationEntryPoint)
            .accessDeniedHandler(newCustomOAuth2ErrorHandler)
        );

        // 配置CSRF和会话管理
        http
            .csrf(AbstractHttpConfigurer::disable)
            .sessionManagement(session ->
                session.sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
            );

        return http.build();
    }

    /**
     * 新版客户端认证转换器 - 与旧版YeelightClientCredentialsTokenEndpointFilter保持兼容
     * 处理PKCE模式的客户端认证
     * ✅ 已替代 AuthorizationServerConfiguration.yeelightClientCredentialsTokenEndpointFilter()
     */
    private AuthenticationConverter newYeelightClientAuthenticationConverter() {
        return request -> {
            // 检查是否是PKCE请求（包含code_verifier参数）
            String codeVerifier = request.getParameter("code_verifier");
            if (codeVerifier != null) {
                String clientId = request.getParameter("client_id");
                if (clientId != null) {
                    // 创建客户端认证Token - 与旧版YeelightClientCredentialsTokenEndpointFilter逻辑一致
                    return new PreAuthenticatedAuthenticationToken(
                        clientId, null, Collections.emptyList());
                }
            }
            return null;
        };
    }

    /**
     * 配置自定义认证验证器
     * 集成重定向URI验证器和用户审批逻辑，与旧版CustomRedirectResolver和CustomUserApprovalHandler保持完全兼容
     */
    private Consumer<List<AuthenticationProvider>> configureAuthenticationValidators() {
        return (authenticationProviders) ->
            authenticationProviders.forEach((authenticationProvider) -> {
                if (authenticationProvider instanceof OAuth2AuthorizationCodeRequestAuthenticationProvider) {
                    Consumer<OAuth2AuthorizationCodeRequestAuthenticationContext> authenticationValidator =
                        // 使用自定义重定向URI验证器 - 与旧版CustomRedirectResolver保持兼容
                        newCustomRedirectUriValidator
                        // 链接用户审批定制器 - 与旧版CustomUserApprovalHandler保持兼容
                        .andThen(newCustomAuthorizationConsentCustomizer)
                        // 链接默认的范围验证器
                        .andThen(OAuth2AuthorizationCodeRequestAuthenticationValidator.DEFAULT_SCOPE_VALIDATOR);

                    ((OAuth2AuthorizationCodeRequestAuthenticationProvider) authenticationProvider)
                        .setAuthenticationValidator(authenticationValidator);
                }
            });
    }
}
