package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.monitor.sql.SqlMonitorInterceptor;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * OauthDatasourceConfig
 * <AUTHOR>
 * @description: Oauth数据源配置类，用于配置Oauth数据源的相关信息。
 */
@Configuration
@MapperScan(value = {"com.yeelight.service.user.server.mapper.oauth"},
        sqlSessionFactoryRef = "oauthSqlSessionFactory")
public class OauthDatasourceConfig {
    /**
     * 创建并配置一个Hikari数据源配置对象，专门用于OAuth相关的数据库操作。
     * 这个方法没有参数。
     *
     * @return HikariConfig 返回一个配置好的HikariConfig对象，它从应用配置文件中的"datasource.oauth"部分读取配置。
     */
    @Bean
    @ConfigurationProperties("datasource.oauth")
    public HikariConfig oauthHikariConfig() {
        return new HikariConfig();
    }

    /**
     * 创建并配置一个数据源（DataSource），用于OAuth认证过程中的数据库连接。
     *
     * @param hikariConfig HikariConfig对象，含有配置Hikari连接池所需的所有信息。
     * @return 返回配置好的HikariDataSource实例，作为一个Bean注入到应用中。
     */
    @Bean
    public DataSource oauthDataSource(@Qualifier("oauthHikariConfig") HikariConfig hikariConfig) {
        // 使用提供的HikariConfig配置创建HikariDataSource实例
        return new HikariDataSource(hikariConfig);
    }

    /**
     * 创建并配置OAuth的SqlSessionFactory Bean。
     *
     * @param dataSource 指定用于OAuth数据操作的DataSource，通过@Qualifier注解进行限定。
     * @param meterRegistry 用于监控SQL执行情况的度量注册表。
     * @return SqlSessionFactory 用于OAuth相关操作的SqlSessionFactory实例。
     * @throws Exception 如果配置过程中发生错误，则抛出异常。
     */
    @Bean
    public SqlSessionFactory oauthSqlSessionFactory(@Qualifier("oauthDataSource") DataSource dataSource, MeterRegistry meterRegistry) throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        // 设置数据源
        factoryBean.setDataSource(dataSource);

        // 设置拦截器，用于SQL监控
        Interceptor[] interceptors = new Interceptor[1];
        interceptors[0] = new SqlMonitorInterceptor(meterRegistry);
        factoryBean.setPlugins(interceptors);

        // 设置mapper文件位置
        Resource[] resources = ArrayUtils.addAll(
                new PathMatchingResourcePatternResolver().getResources("classpath:/mybatis/mapper/oauth/*.xml"),
                new PathMatchingResourcePatternResolver().getResources("classpath:/mybatis/mapper/oauth/*.xml")
        );
        factoryBean.setMapperLocations(resources);
        // 返回配置好的SqlSessionFactory实例
        return factoryBean.getObject();
    }


    /**
     * 创建并返回一个SqlSessionTemplate实例，用于OAuth相关的数据库操作。
     * 这个模板使用了指定的SqlSessionFactory来进行数据库会话的创建。
     *
     * @param sqlSessionFactory OAuth数据库操作所需的SqlSessionFactory。
     * @return SqlSessionTemplate 用于OAuth流程的数据库操作模板。
     */
    @Bean
    public SqlSessionTemplate oauthSqlSessionTemplate(@Qualifier("oauthSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 创建并返回一个PlatformTransactionManager实例，用于管理OAuth相关的数据库事务。
     * 这个事务管理器使用了指定的数据源来控制事务。
     *
     * @param dataSource OAuth数据库操作所使用的数据源。
     * @return DataSourceTransactionManager 用于OAuth流程的数据库事务管理器。
     */
    @Bean
    public PlatformTransactionManager oauthTransactionManager(@Qualifier("oauthDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
