package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * redis配置类
 *
 * <AUTHOR>
 * @date 2020-04-14 14:00
 */
@Configuration
@Component
public class RedisManager {
    @Value("${spring.data.redis.database:0}")
    private int database;

    @Value("${spring.data.redis.host:localhost}")
    private String host;

    @Value("${spring.data.redis.password}")
    private String password;

    @Value("${spring.data.redis.port:6379}")
    private int port;

    @Getter
    @Value("${spring.data.redis.timeout:5000}")
    private long timeout;

    private final Map<Integer, LettuceConnectionFactory> factoryHashMap = new HashMap<>();

    private final Map<String, RedisTemplate<String, Object>> redisTemplateMap = new HashMap<>();

    private final Map<String, StringRedisTemplate> stringStringRedisTemplateHashMap = new HashMap<>();



    /**
     * 初始化方法，该方法在类的构造函数之后调用。
     * 该方法遍历UserVendorEnum中的所有枚举值，对每个枚举值执行一系列初始化操作，
     * 包括获取连接工厂、初始化OAuth Redis模板、初始化Redis模板以及初始化String Redis模板。
     */
    @PostConstruct
    private void init() {
        // 遍历所有用户供应商枚举值并执行初始化逻辑
        Arrays.stream(UserVendorEnum.values()).forEach(userVendorEnum -> {
            // 根据枚举值的代码获取供应商索引，并基于索引获取连接工厂
            int index = UserVendorEnum.getVendorIndexByCode(userVendorEnum.getCode());
            getConnectionFactory(index + 1);
            // 初始化OAuth相关的Redis模板
            oauthRedisTemplate();
            // 初始化通用的Redis模板
            redisTemplate();
            // 初始化String类型的Redis模板
            stringRedisTemplate();
        });
    }

    public LettuceConnectionFactory getConnectionFactory() {
        return getConnectionFactory(getCurrentDatabase());
    }

    /**
     * 根据当前数据库索引获取对应的OAuth Redis模板。
     * 如果该模板已存在，则直接返回；若不存在，则创建并缓存该模板。
     *
     * @return RedisTemplate<String, Object> OAuth相关的Redis模板
     */
    public RedisTemplate<String, Object> oauthRedisTemplate() {
        // 获取当前数据库索引
        int currentDatabase = getCurrentDatabase();
        // 生成用于标识特定数据库的Redis模板键
        String key = String.format("%s-%s", "oauth-redis-template", currentDatabase);

        // 检查是否已存在该数据库索引对应的Redis模板
        if (redisTemplateMap.containsKey(key)) {
            // 如果存在，直接返回该模板
            return redisTemplateMap.get(key);
        }

        // 获取指定数据库的连接工厂
        RedisConnectionFactory factory = getConnectionFactory(currentDatabase);
        // 创建新的Redis模板实例
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 使用UniversalCompatibleRedisSerializer，它已经集成了多格式兼容功能
        UniversalCompatibleRedisSerializer compatibleSerializer = new UniversalCompatibleRedisSerializer();

        // 配置字符串序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();

        // 配置模板的序列化方式：键使用字符串序列化，值使用兼容序列化器
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用兼容序列化器
        template.setValueSerializer(compatibleSerializer);
        // hash的value序列化方式采用兼容序列化器
        template.setHashValueSerializer(compatibleSerializer);

        // 初始化模板
        template.afterPropertiesSet();

        // 将新创建的模板缓存起来
        redisTemplateMap.put(key, template);

        // 返回新创建或获取的Redis模板
        return template;
    }


    /**
     * 获取当前数据库对应的RedisTemplate实例。
     * 如果该数据库的RedisTemplate已存在，则直接返回缓存中的实例；
     * 如果不存在，则创建新的RedisTemplate实例并配置后返回。
     *
     * @return RedisTemplate 返回与当前数据库关联的RedisTemplate实例。
     */
    public RedisTemplate<String, Object> redisTemplate() {
        // 获取当前数据库编号
        int currentDatabase = getCurrentDatabase();
        // 生成RedisTemplate的唯一标识符
        String key = String.format("%s-%s", "redis-template", currentDatabase);
        // 检查是否已经存在该数据库的RedisTemplate实例
        if (redisTemplateMap.containsKey(key)) {
            return redisTemplateMap.get(key);
        }

        // 获取当前数据库的Redis连接工厂
        RedisConnectionFactory factory = getConnectionFactory(currentDatabase);
        // 创建新的RedisTemplate实例并配置
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // 设置连接工厂
        template.setConnectionFactory(factory);

        // 使用通用兼容性序列化器 - 支持Jackson/FastJSON/JDK序列化的向前兼容
        UniversalCompatibleRedisSerializer compatibleSerializer = new UniversalCompatibleRedisSerializer();

        // 设置键的序列化方式
        template.setKeySerializer(new StringRedisSerializer());
        // 设置值的序列化方式 - 使用兼容性序列化器
        template.setValueSerializer(compatibleSerializer);
        // 设置Hash键值的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(compatibleSerializer);

        // 初始化RedisTemplate
        template.afterPropertiesSet();
        // 将新的RedisTemplate实例缓存起来
        redisTemplateMap.put(key, template);
        return template;
    }


    /**
     * 获取一个针对当前数据库的StringRedisTemplate实例。
     * 如果该数据库已有一个StringRedisTemplate实例，则直接返回该实例，
     * 实现Redis模板的缓存复用。
     *
     * @return StringRedisTemplate 对应当前数据库的StringRedisTemplate实例
     */
    public StringRedisTemplate stringRedisTemplate() {
        // 获取当前数据库编号
        int currentDatabase = getCurrentDatabase();
        // 生成基于当前数据库编号的模板标识键
        String key = String.format("%s-%s", "string-redis-template", currentDatabase);

        // 检查是否存在已缓存的StringRedisTemplate实例
        if (stringStringRedisTemplateHashMap.containsKey(key)) {
            return stringStringRedisTemplateHashMap.get(key);
        }

        // 创建新的StringRedisTemplate实例并配置
        StringRedisTemplate template = new StringRedisTemplate();
        // 设置连接工厂
        template.setConnectionFactory(getConnectionFactory(currentDatabase));
        // 初始化模板
        template.afterPropertiesSet();

        // 将新创建的模板实例缓存起来
        stringStringRedisTemplateHashMap.put(key, template);

        return template;
    }


    /**
     * 获取当前使用的数据库索引。
     * 此方法会根据当前的用户供应商编码（如果不是默认供应商）来调整数据库索引。
     *
     * @return int 当前数据库的索引。如果用户是默认供应商，则返回基于类成员变量的数据库索引；否则，返回考虑用户供应商编码后的数据库索引。
     */
    public int getCurrentDatabase() {
        // 初始时，将当前数据库索引设为类成员变量中存储的值
        int currentDatabase = this.database;
        // 如果当前的用户供应商不是默认供应商
        if (!UserVendorEnum.DEFAULT.getCode().equals(UserVendorHolder.getVendor())) {
            // 根据当前用户供应商的编码，获取对应的数据库索引，并加1，然后更新当前数据库索引
            currentDatabase = UserVendorEnum.getVendorIndexByCode(UserVendorHolder.getVendor()) + 1;
        }
        // 返回计算后的当前数据库索引
        return currentDatabase;
    }


    /**
     * 获取指定数据库的LettuceConnectionFactory实例。
     * 如果该数据库的工厂实例已存在，则直接返回；否则，创建新的实例并缓存。
     *
     * @param database 指定的数据库索引。
     * @return LettuceConnectionFactory 对应数据库的LettuceConnectionFactory实例。
     */
    public LettuceConnectionFactory getConnectionFactory(int database) {
        // 检查工厂缓存中是否已存在该数据库的工厂实例
        if (factoryHashMap.containsKey(database)) {
            return factoryHashMap.get(database);
        }

        // 配置Redis独立服务器设置
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();
        configuration.setHostName(host);
        configuration.setPort(port);
        configuration.setPassword(password);
        configuration.setDatabase(database);

        // 创建新的LettuceConnectionFactory实例
        LettuceConnectionFactory factory = new LettuceConnectionFactory(configuration);
        // 初始化工厂
        factory.afterPropertiesSet();

        // 将新的工厂实例添加到缓存中
        factoryHashMap.put(database, factory);

        return factory;
    }

    /**
     * 提供默认的RedisConnectionFactory bean
     * Spring Boot 3.x需要这个bean用于Redis自动配置
     *
     * @return RedisConnectionFactory 默认的Redis连接工厂
     */
    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        return getConnectionFactory();
    }

}
