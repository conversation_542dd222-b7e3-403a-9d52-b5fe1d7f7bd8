package com.yeelight.service.user.server.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 * @program: yeelight-service-user
 * @description: Redisson配置类
 */
@Configuration
public class RedissonConfig {

    /**
     * 创建并返回一个Redisson客户端实例。
     * 这个方法通过读取配置文件"redisson-config.yml"来配置Redisson客户端，并基于配置创建实例。
     *
     * @return RedissonClient - Redisson客户端实例，用于与Redis服务器进行交互。
     * @throws IOException 如果在读取配置文件时发生错误，则抛出IOException。
     */
    @Bean
    public RedissonClient redissonClient() throws IOException {
        // 从资源文件中加载Redisson的配置
        Config config = Config.fromYAML(RedissonConfig.class.getClassLoader().getResource("redisson-config.yml"));
        // 根据配置创建并返回Redisson客户端实例
        return Redisson.create(config);
    }
}
