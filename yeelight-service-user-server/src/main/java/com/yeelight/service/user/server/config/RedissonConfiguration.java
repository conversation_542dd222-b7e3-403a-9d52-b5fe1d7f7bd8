package com.yeelight.service.user.server.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;

/**
 * Redisson兼容性配置
 * <p>
 * 配置Redisson客户端使用兼容性编解码器
 * 确保能够读取历史数据，同时写入新格式数据
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Configuration
public class RedissonConfiguration {

    @Value("${spring.profiles.active:local}")
    private String activeProfile;

    /**
     * 配置兼容性Redisson客户端
     */
    @Bean
    public RedissonClient redissonClient() {
        log.info("🔧 配置Redisson兼容性客户端，当前环境: {}", activeProfile);
        
        try {
            // 根据环境加载对应的配置文件
            String configFile = "redisson-config.yml";
            ClassPathResource resource = new ClassPathResource(configFile);
            
            Config config;
            if (resource.exists()) {
                // 从配置文件加载
                config = Config.fromYAML(resource.getInputStream());
                log.info("✅ 从配置文件加载Redisson配置: {}", configFile);
            } else {
                // 使用默认配置
                config = createDefaultConfig();
                log.warn("⚠️ 配置文件不存在，使用默认Redisson配置");
            }
            
            // 使用默认的JsonJacksonCodec，避免复杂的兼容性问题
            // 如果需要兼容性，可以在应用层处理
            log.info("✅ 使用默认Redisson JsonJacksonCodec编解码器");
            
            // 创建Redisson客户端
            RedissonClient client = Redisson.create(config);
            log.info("✅ Redisson兼容性客户端创建成功");
            
            return client;
            
        } catch (IOException e) {
            log.error("❌ 创建Redisson客户端失败", e);
            throw new RuntimeException("Failed to create Redisson client", e);
        }
    }
    
    /**
     * 创建默认配置
     */
    private Config createDefaultConfig() {
        Config config = new Config();
        
        // 单机模式配置
        config.useSingleServer()
                .setAddress("redis://localhost:6379")
                .setDatabase(1)
                .setConnectionPoolSize(64)
                .setConnectionMinimumIdleSize(32)
                .setIdleConnectionTimeout(10000)
                .setPingConnectionInterval(1000)
                .setConnectTimeout(10000)
                .setTimeout(3000)
                .setRetryAttempts(3);
        
        log.info("🔧 创建默认Redisson配置");
        return config;
    }
}
