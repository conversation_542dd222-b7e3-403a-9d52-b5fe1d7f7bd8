package com.yeelight.service.user.server.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.session.data.redis.config.ConfigureRedisAction;
import org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration;

import java.time.Duration;

/**
 * Spring Session Redis序列化兼容性配置
 * <p>
 * 解决Spring Security版本升级后DefaultSavedRequest序列化兼容性问题
 * 使用JSON序列化器替代JDK序列化器，同时保持向前兼容性
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
@Configuration
public class SessionRedisConfiguration extends RedisHttpSessionConfiguration {



    /**
     * 构造函数 - 设置Session超时时间为30天
     */
    public SessionRedisConfiguration() {
        super();
        // 设置Session最大非活跃时间为30天（2592000秒）
        setMaxInactiveInterval(Duration.ofSeconds(2592000));
    }

    /**
     * 自定义Session Redis序列化器
     * 使用兼容性JSON序列化器，解决Spring Security版本升级后的序列化问题
     */
    @Bean("sessionRedisSerializer")
    public RedisSerializer<Object> sessionRedisSerializer() {
        log.info("🔧 配置Session Redis兼容性序列化器");
        
        // 创建兼容性ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        
        // 使用LaissezFaireSubTypeValidator以提供更好的兼容性
        objectMapper.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance, 
            ObjectMapper.DefaultTyping.NON_FINAL
        );
        
        // 创建兼容性JSON序列化器
        return new CompatibleGenericJackson2JsonRedisSerializer(objectMapper);
    }

    /**
     * 自定义Session RedisTemplate
     * 使用兼容性序列化器配置
     */
    @Bean("sessionRedisTemplate")
    public RedisTemplate<String, Object> sessionRedisTemplate(
            RedisConnectionFactory redisConnectionFactory,
            RedisSerializer<Object> sessionRedisSerializer) {
        
        log.info("🔧 配置Session RedisTemplate使用兼容性序列化器");
        
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        // 使用String序列化器处理键
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        
        // 使用兼容性JSON序列化器处理值
        template.setValueSerializer(sessionRedisSerializer);
        template.setHashValueSerializer(sessionRedisSerializer);
        
        template.afterPropertiesSet();
        return template;
    }

    /**
     * Session兼容性序列化器
     * 使用UniversalCompatibleRedisSerializer处理Session数据
     */
    public static class CompatibleGenericJackson2JsonRedisSerializer extends GenericJackson2JsonRedisSerializer {

        private final UniversalCompatibleRedisSerializer universalSerializer;

        public CompatibleGenericJackson2JsonRedisSerializer(ObjectMapper objectMapper) {
            super(objectMapper);
            this.universalSerializer = new UniversalCompatibleRedisSerializer();
        }

        @Override
        public Object deserialize(byte[] bytes) {
            if (bytes == null || bytes.length == 0) {
                return null;
            }

            // 委托给通用兼容序列化器处理
            return universalSerializer.deserialize(bytes);
        }
    }

    /**
     * Spring Session使用的RedisTemplate
     * 通过Bean名称"springSessionRedisTemplate"来覆盖默认配置
     */
    @Bean("springSessionRedisTemplate")
    public RedisTemplate<String, Object> springSessionRedisTemplate(
            RedisConnectionFactory redisConnectionFactory) {
        return sessionRedisTemplate(redisConnectionFactory, sessionRedisSerializer());
    }

    /**
     * 覆盖默认的Session序列化器配置
     * 这是Spring Session内部使用的关键方法
     */
    @Bean
    @Primary
    public RedisSerializer<Object> springSessionDefaultRedisSerializer() {
        log.info("🔧 覆盖Spring Session默认序列化器为兼容性序列化器");
        return sessionRedisSerializer();
    }

    /**
     * 禁用Redis配置命令
     * 在安全的Redis环境中，config命令可能被禁用
     */
    @Bean
    public static ConfigureRedisAction configureRedisAction() {
        log.info("🔧 禁用Redis自动配置命令");
        return ConfigureRedisAction.NO_OP;
    }
}
