package com.yeelight.service.user.server.config;

import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.user.server.custom.SmsAuthenticationDetailsSource;
import com.yeelight.service.user.server.handler.SmsAuthenticationFailureHandler;
import com.yeelight.service.user.server.provider.SmsAuthenticationProvider;
import com.yeelight.service.user.server.custom.YeelightUserDetailsService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * 短信登录配置
 * <AUTHOR>
 */
@Configuration
public class SmsLoginConfiguration {

    @Resource
    private YeelightUserDetailsService userDetailsService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageHelper;

    /**
     * 配置短信认证失败处理器 Bean。
     *
     * @return SmsAuthenticationFailureHandler 短信认证失败处理器实例
     */
    @Bean
    public SmsAuthenticationFailureHandler smsAuthenticationFailureHandler() {
        return new SmsAuthenticationFailureHandler();
    }

    /**
     * 配置短信认证详情源 Bean。
     *
     * @return SmsAuthenticationDetailsSource 短信认证详情源实例
     */
    @Bean
    public SmsAuthenticationDetailsSource smsAuthenticationDetailsSource() {
        return new SmsAuthenticationDetailsSource();
    }

    /**
     * 配置短信认证提供者 Bean。
     * 这个提供者会使用用户详情服务和验证码消息帮助器来处理认证请求。
     *
     * @return SmsAuthenticationProvider 短信认证提供者实例
     */
    @Bean
    public SmsAuthenticationProvider smsAuthenticationProvider() {
        return new SmsAuthenticationProvider(userDetailsService, captchaMessageHelper);
    }


}
