package com.yeelight.service.user.server.config;

import com.yeelight.service.user.server.filter.SmsAuthenticationFilter;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.Assert;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 短信登录配置器
 * <AUTHOR>
 */
public class SmsLoginConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    private static final String SMS_DEFAULT_LOGIN_PROCESS_URL = "/authentication/phone_number";

    private final SmsAuthenticationFilter authFilter;

    private AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource;

    private AuthenticationSuccessHandler successHandler;

    private AuthenticationFailureHandler failureHandler;

    /**
     * 默认手机+短信验证码 登录处理地址 [POST "/authentication/phone_number"]. 默认手机参数 - phoneNumber
     */
    public SmsLoginConfigurer() {
        authFilter = new SmsAuthenticationFilter();
        loginProcessingUrl(SMS_DEFAULT_LOGIN_PROCESS_URL);
        phoneNumberParameter("phoneNumber");
    }

    /**
     * 设置电话号码参数名称。
     * @param phoneNumberParameter 用于设置认证过滤器中的电话号码参数名称。
     */
    public void phoneNumberParameter(String phoneNumberParameter) {
        authFilter.setPhoneNumberParameter(phoneNumberParameter);
    }

    /**
     * 设置登录处理URL。
     * 使用指定的登录处理URL创建并设置认证过滤器的请求匹配器。
     * @param loginProcessingUrl 登录处理请求的URL。
     */
    public void loginProcessingUrl(String loginProcessingUrl) {
        authFilter.setRequiresAuthenticationRequestMatcher(createLoginProcessingUrlMatcher(loginProcessingUrl));
    }

    /**
     * 配置认证详情源。
     * @param authenticationDetailsSource 用于设置认证详情源，提供认证请求的额外详情。
     * @return SmsLoginConfigurer 返回当前配置器实例，支持链式调用。
     */
    public SmsLoginConfigurer authenticationDetailsSource(
                    AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource) {
        this.authenticationDetailsSource = authenticationDetailsSource;
        return this;
    }

    /**
     * 配置登录成功处理器。
     * @param successHandler 登录成功时执行的处理器。
     * @return SmsLoginConfigurer 返回当前配置器实例，支持链式调用。
     */
    public SmsLoginConfigurer successHandler(AuthenticationSuccessHandler successHandler) {
        this.successHandler = successHandler;
        return this;
    }

    /**
     * 配置登录失败处理器。
     * @param failureHandler 登录失败时执行的处理器。
     */
    public void failureHandler(AuthenticationFailureHandler failureHandler) {
        this.failureHandler = failureHandler;
    }

    /**
     * 创建登录处理URL的请求匹配器。
     * 用于创建一个匹配指定登录处理URL的AntPathRequestMatcher实例，仅匹配POST请求。
     * @param loginProcessingUrl 登录处理请求的URL。
     * @return RequestMatcher 返回配置好的请求匹配器。
     */
    protected RequestMatcher createLoginProcessingUrlMatcher(String loginProcessingUrl) {
        return new AntPathRequestMatcher(loginProcessingUrl, "POST");
    }


    /**
     * 配置HttpSecurity以增强安全性。
     * 该方法会将自定义的认证过滤器添加到Spring Security的过滤链中，
     * 并设置认证成功和失败的处理器，以及认证细节源。
     *
     * @param http 用于配置HTTP安全性的对象。
     */
    @Override
    public void configure(HttpSecurity http) {
        // 确保successHandler和failureHandler不为null
        Assert.notNull(successHandler, "successHandler should not be null.");
        Assert.notNull(failureHandler, "failureHandler should not be null.");

        // 设置认证管理器
        authFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));

        // 如果successHandler非null，则设置认证成功处理器
        if (this.successHandler != null) {
            authFilter.setAuthenticationSuccessHandler(this.successHandler);
        }

        // 如果failureHandler非null，则设置认证失败处理器
        if (this.failureHandler != null) {
            authFilter.setAuthenticationFailureHandler(this.failureHandler);
        }

        // 如果authenticationDetailsSource非null，则设置认证细节源
        if (this.authenticationDetailsSource != null) {
            authFilter.setAuthenticationDetailsSource(this.authenticationDetailsSource);
        }

        // 将自定义认证过滤器添加到UsernamePasswordAuthenticationFilter之后
        http.addFilterAfter(authFilter, UsernamePasswordAuthenticationFilter.class);
    }

}
