package com.yeelight.service.user.server.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2RefreshToken;
import com.yeelight.service.user.server.custom.token.DefaultOauth2AccessTokenSerializer;
import com.yeelight.service.user.server.custom.token.DefaultOauth2RefreshTokenSerializer;
import com.yeelight.service.user.server.custom.token.Oauth2AuthenticationSerializer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;

import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用兼容性Redis序列化器
 * <p>
 * 支持多种序列化格式的向前兼容：
 * 1. Jackson JSON序列化（新格式，优先使用）
 * 2. FastJSON序列化（历史格式，兼容处理）
 * 3. JDK序列化（最老格式，兜底处理）
 * <p>
 * 序列化策略：统一使用Jackson JSON格式
 * 反序列化策略：自动检测格式并选择合适的反序列化器
 * 
 * <AUTHOR>
 * @since 2024-07-29
 */
@Slf4j
public class UniversalCompatibleRedisSerializer implements RedisSerializer<Object> {

    private final ObjectMapper jacksonMapper;
    private final List<DeserializationStrategy> deserializationStrategies;
    
    /**
     * 构造函数 - 初始化Jackson ObjectMapper和反序列化策略链
     */
    public UniversalCompatibleRedisSerializer() {
        this.jacksonMapper = new ObjectMapper();
        this.jacksonMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        this.jacksonMapper.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance,
            ObjectMapper.DefaultTyping.NON_FINAL
        );

        // 初始化反序列化策略链（按优先级排序）
        this.deserializationStrategies = initializeDeserializationStrategies();

        log.info("✅ UniversalCompatibleRedisSerializer 初始化完成，支持{}种反序列化策略",
                deserializationStrategies.size());
    }

    /**
     * 初始化反序列化策略链
     * 按优先级从高到低排序
     */
    private List<DeserializationStrategy> initializeDeserializationStrategies() {
        List<DeserializationStrategy> strategies = new ArrayList<>();

        // 策略1：JDK序列化策略（针对检测到的JDK格式数据）
        strategies.add(new JdkDeserializationStrategy());

        // 策略2：Jackson JSON策略（新格式，优先使用）
        strategies.add(new JacksonDeserializationStrategy(jacksonMapper));

        // 策略3：OAuth2专用FastJSON策略（历史OAuth2数据）
        strategies.add(new OAuth2FastJsonDeserializationStrategy());

        // 策略4：通用FastJSON策略（历史格式兼容）
        strategies.add(new FastJsonDeserializationStrategy());

        // 策略5：字符串策略（纯字符串数据兜底）
        strategies.add(new StringDeserializationStrategy());

        return strategies;
    }

    @Override
    public byte[] serialize(Object object) throws SerializationException {
        if (object == null) {
            return new byte[0];
        }
        
        try {
            // 统一使用Jackson JSON序列化
            return jacksonMapper.writeValueAsBytes(object);
        } catch (Exception e) {
            log.error("Jackson序列化失败，对象类型: {}, 错误: {}", 
                object.getClass().getSimpleName(), e.getMessage());
            throw new SerializationException("序列化失败", e);
        }
    }

    @Override
    public Object deserialize(byte[] bytes) throws SerializationException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }

        // 检测数据格式
        DataFormat format = detectDataFormat(bytes);
        DeserializationContext context = new DeserializationContext(bytes, format);

        // 使用策略链进行反序列化
        for (DeserializationStrategy strategy : deserializationStrategies) {
            try {
                if (strategy.canHandle(context)) {
                    Object result = strategy.deserialize(context);
                    if (result != null) {
                        log.debug("使用{}反序列化成功，类型: {}",
                                strategy.getClass().getSimpleName(),
                                result.getClass().getSimpleName());
                        return result;
                    }
                }
            } catch (Exception e) {
                log.debug("{}反序列化失败: {}", strategy.getClass().getSimpleName(), e.getMessage());
                // 继续尝试下一个策略
            }
        }

        // 所有策略都失败，记录错误但不抛出异常，避免影响用户体验
        log.warn("所有反序列化策略都失败，数据长度: {} bytes，格式: {}，返回null", bytes.length, format);
        return null;
    }
    

    
    /**
     * 检测数据格式
     */
    private DataFormat detectDataFormat(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return DataFormat.UNKNOWN;
        }
        
        // 检测JSON格式（以 { 或 [ 开头）
        if (bytes[0] == '{' || bytes[0] == '[') {
            return DataFormat.JSON;
        }
        
        // 检测JDK序列化格式（以特定魔数开头）
        if (bytes.length >= 2 && bytes[0] == (byte) 0xAC && bytes[1] == (byte) 0xED) {
            return DataFormat.JDK_SERIALIZATION;
        }
        
        // 检测是否为UTF-8字符串
        try {
            new String(bytes, StandardCharsets.UTF_8);
            return DataFormat.STRING;
        } catch (Exception e) {
            return DataFormat.UNKNOWN;
        }
    }
    
    /**
     * 数据格式枚举
     */
    private enum DataFormat {
        // JSON格式
        JSON,
        // JDK序列化格式
        JDK_SERIALIZATION,
        // 字符串格式
        STRING,
        // 未知格式
        UNKNOWN
    }

    // ==================== 反序列化策略接口和实现 ====================

    /**
     * 反序列化策略接口
     */
    private interface DeserializationStrategy {
        /**
         * 判断是否可以处理该数据
         * @param context 反序列化上下文
         * @return true表示可以处理该数据，false表示无法处理
         */
        boolean canHandle(DeserializationContext context);

        /**
         * 执行反序列化
         * @param context 反序列化上下文
         * @return 反序列化结果
         * @throws Exception 反序列化异常
         */
        Object deserialize(DeserializationContext context) throws Exception;
    }

    /**
     * 反序列化上下文
     */
    private static class DeserializationContext {
        @Getter
        private final byte[] bytes;
        @Getter
        private final DataFormat format;
        private String jsonString; // 懒加载

        public DeserializationContext(byte[] bytes, DataFormat format) {
            this.bytes = bytes;
            this.format = format;
        }

        public String getJsonString() {
            if (jsonString == null && bytes != null) {
                jsonString = new String(bytes, StandardCharsets.UTF_8);
            }
            return jsonString;
        }
    }

    /**
     * JDK序列化策略
     */
    private static class JdkDeserializationStrategy implements DeserializationStrategy {
        @Override
        public boolean canHandle(DeserializationContext context) {
            return context.getFormat() == DataFormat.JDK_SERIALIZATION;
        }

        @Override
        public Object deserialize(DeserializationContext context) throws Exception {
            try (ByteArrayInputStream bis = new ByteArrayInputStream(context.getBytes());
                 ObjectInputStream ois = new ObjectInputStream(bis)) {
                return ois.readObject();
            }
        }
    }

    /**
     * Jackson JSON序列化策略
     */
    private static class JacksonDeserializationStrategy implements DeserializationStrategy {
        private final ObjectMapper objectMapper;

        public JacksonDeserializationStrategy(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public boolean canHandle(DeserializationContext context) {
            return context.getFormat() == DataFormat.JSON;
        }

        @Override
        public Object deserialize(DeserializationContext context) throws Exception {
            return objectMapper.readValue(context.getBytes(), Object.class);
        }
    }

    /**
     * OAuth2专用FastJSON策略
     */
    private static class OAuth2FastJsonDeserializationStrategy implements DeserializationStrategy {
        @Override
        public boolean canHandle(DeserializationContext context) {
            if (context.getFormat() != DataFormat.JSON && context.getFormat() != DataFormat.STRING) {
                return false;
            }

            try {
                String jsonStr = context.getJsonString();
                JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(jsonStr);
                if (jsonObject == null) {
                    return false;
                }

                // 检测OAuth2相关字段
                return jsonObject.containsKey("tokenType") ||
                       jsonObject.containsKey("value") ||
                       jsonObject.containsKey("oAuth2Request") ||
                       jsonObject.containsKey("userAuthentication");
            } catch (Exception e) {
                return false;
            }
        }

        @Override
        public Object deserialize(DeserializationContext context) {
            String jsonStr = context.getJsonString();
            JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(jsonStr);

            // 检测YeelightOAuth2AccessToken
            if (jsonObject.containsKey("value") && jsonObject.containsKey("tokenType")) {
                DefaultOauth2AccessTokenSerializer tokenSerializer = new DefaultOauth2AccessTokenSerializer();
                com.alibaba.fastjson.parser.DefaultJSONParser parser =
                    new com.alibaba.fastjson.parser.DefaultJSONParser(jsonStr);
                return tokenSerializer.deserialze(parser, YeelightOAuth2AccessToken.class, null);
            }

            // 检测YeelightOAuth2RefreshToken
            if (jsonObject.containsKey("value") && !jsonObject.containsKey("tokenType")) {
                DefaultOauth2RefreshTokenSerializer refreshTokenSerializer = new DefaultOauth2RefreshTokenSerializer();
                com.alibaba.fastjson.parser.DefaultJSONParser parser =
                    new com.alibaba.fastjson.parser.DefaultJSONParser(jsonStr);
                return refreshTokenSerializer.deserialze(parser, YeelightOAuth2RefreshToken.class, null);
            }

            // 检测YeelightOAuth2Authentication
            if (jsonObject.containsKey("oAuth2Request") || jsonObject.containsKey("userAuthentication")) {
                Oauth2AuthenticationSerializer authSerializer = new Oauth2AuthenticationSerializer();
                com.alibaba.fastjson.parser.DefaultJSONParser parser =
                    new com.alibaba.fastjson.parser.DefaultJSONParser(jsonStr);
                return authSerializer.deserialze(parser, YeelightOAuth2Authentication.class, null);
            }

            return null;
        }
    }

    /**
     * 通用FastJSON策略
     */
    private static class FastJsonDeserializationStrategy implements DeserializationStrategy {
        @Override
        public boolean canHandle(DeserializationContext context) {
            return context.getFormat() == DataFormat.JSON || context.getFormat() == DataFormat.STRING;
        }

        @Override
        public Object deserialize(DeserializationContext context) {
            String jsonStr = context.getJsonString();
            return JSON.parse(jsonStr);
        }
    }

    /**
     * 字符串策略（兜底）
     */
    private static class StringDeserializationStrategy implements DeserializationStrategy {
        @Override
        public boolean canHandle(DeserializationContext context) {
            return true; // 总是可以处理，作为最后的兜底策略
        }

        @Override
        public Object deserialize(DeserializationContext context) {
            return context.getJsonString();
        }
    }
}
