package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.monitor.sql.SqlMonitorInterceptor;
import com.zaxxer.hikari.HikariConfig;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.shardingsphere.driver.api.yaml.YamlShardingSphereDataSourceFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;

/**
 * @description: 用户数据源配置类
 * <AUTHOR>
 */
@Configuration
@Primary
@MapperScan(value = {"com.yeelight.service.user.server.mapper.user"},
        sqlSessionFactoryRef = "userSqlSessionFactory")
public class UserDatasourceConfig {
    /**
     * 创建并配置一个用于用户数据源的HikariConfig Bean。
     * 这个方法通过读取应用配置文件中"datasource.user"部分的属性来初始化HikariConfig对象。
     *
     * @return HikariConfig 返回配置好的HikariConfig实例。
     */
    @Bean
    @ConfigurationProperties("datasource.user")
    public HikariConfig userHikariConfig() {
        return new HikariConfig();
    }

    /**
     * 基于用户指定的HikariConfig创建并配置一个用户数据源的Bean。
     * 这个方法通过使用YAML配置文件("sharding-config.yml")来创建一个分片数据源。
     *
     * @param hikariConfig 通过@Qualifier注解指定的HikariConfig Bean。
     * @return DataSource 返回配置好的DataSource实例。
     * @throws IOException 如果读取配置文件发生错误。
     * @throws SQLException 如果创建数据源时发生错误。
     */
    @Bean
    public DataSource userDataSource(@Qualifier("userHikariConfig") HikariConfig hikariConfig) throws IOException, SQLException {
        // 从类路径中加载分片配置文件
        ClassPathResource resource = new ClassPathResource("sharding-config.yml");
        InputStream inputStream = resource.getInputStream();
        // 将配置文件内容读取为字节数组
        byte[] bytes = IOUtils.toByteArray(inputStream);
        // 使用YAML配置创建分片数据源
        return YamlShardingSphereDataSourceFactory.createDataSource(bytes);
        // 注释掉的代码是另一种创建数据源的方式，使用HikariDataSource直接基于配置创建
        // return new HikariDataSource(hikariConfig);
    }


    /**
     * 创建并配置用户相关的SqlSessionFactory Bean。
     *
     * @param dataSource 指定用于用户数据操作的数据源。
     * @param meterRegistry 用于监控SQL执行情况的度量注册表。
     * @return 配置好的SqlSessionFactory实例，用于执行MyBatis操作。
     * @throws Exception 如果配置失败或无法创建SqlSessionFactory实例，则抛出异常。
     */
    @Bean
    public SqlSessionFactory userSqlSessionFactory(@Qualifier("userDataSource") DataSource dataSource, MeterRegistry meterRegistry) throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        // 设置数据源
        factoryBean.setDataSource(dataSource);

        // 设置拦截器，用于SQL监控
        Interceptor[] interceptors = new Interceptor[1];
        interceptors[0] = new SqlMonitorInterceptor(meterRegistry);
        factoryBean.setPlugins(interceptors);

        // 设置Mapper文件位置
        Resource[] resources = ArrayUtils.addAll(
                new PathMatchingResourcePatternResolver().getResources("classpath:/mybatis/mapper/user/*.xml"),
                new PathMatchingResourcePatternResolver().getResources("classpath:/mybatis/mapper/user/*.xml")
        );
        factoryBean.setMapperLocations(resources);
        // 返回配置好的SqlSessionFactory实例
        return factoryBean.getObject();
    }


    /**
     * 创建并返回一个用户专属的SqlSessionTemplate Bean。
     * SqlSessionTemplate是MyBatis与Spring集成时使用的，用于执行MyBatis操作的模板类。
     * 它通过SqlSessionFactory来创建，确保了每个操作都对应到正确的数据库会话。
     *
     * @param sqlSessionFactory 用户的SqlSessionFactory Bean，用于创建SqlSessionTemplate。
     * @return SqlSessionTemplate 用于执行MyBatis操作的模板实例。
     */
    @Bean
    public SqlSessionTemplate userSqlSessionTemplate(@Qualifier("userSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    /**
     * 创建并返回一个用户专属的事务管理器Bean。
     * 该事务管理器用于管理用户数据源的事务，确保数据操作的一致性和隔离性。
     *
     * @param dataSource 用户的DataSource Bean，事务管理器将基于此DataSource管理事务。
     * @return PlatformTransactionManager 用户事务管理器的实例。
     */
    @Bean
    @Primary
    public PlatformTransactionManager userTransactionManager(@Qualifier("userDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
