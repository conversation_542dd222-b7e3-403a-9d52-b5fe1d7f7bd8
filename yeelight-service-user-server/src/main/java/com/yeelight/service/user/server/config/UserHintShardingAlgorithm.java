/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.config
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-10-08 15:26:15:26
 */
package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingValue;

import java.util.Collection;
import java.util.HashSet;

/**
 * Desc: 用户分片算法
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-10-08 15:26:15:26
 */
public class UserHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    /**
     * 根据提供的分片值和可用目标表名集合，执行分片逻辑，返回分片后的目标表名集合。
     *
     * @param availableTargetNames 可用的目标表名集合。这些表名代表了配置文件中定义的逻辑表的真实表名。
     * @param shardingValue 包含分片键信息的HintShardingValue对象。该对象提供了根据分片策略进行表分片所需的分片键值等信息。
     * @return 分片后的目标表名集合。这个集合是从availableTargetNames中根据分片逻辑筛选后得到的。
     */
    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, HintShardingValue<String> shardingValue) {
        /*
          availableTargetNames参数：

          类型：Collection<String>
          包含了当前可用的目标表名集合。
          这个参数是在配置文件中定义的逻辑表的真实表名集合。

          shardingValue参数：

          类型：HintShardingValue<T>
          包含了分片键的相关信息。
          HintShardingValue是一个泛型类，其中的泛型类型T表示分片键的数据类型。
          HintShardingValue对象中存储了分片键的值以及一些其他属性，比如逻辑表名等。
         */
        Collection<String> shardingResult = new HashSet<>();

        // 根据ThreadLocal中存储的vendor信息，筛选出对应的真实表名
        String vendor = UserVendorHolder.getVendor();

        // 根据vendor区分不同的表名
        for (String targetName : availableTargetNames) {
            if (targetName.equalsIgnoreCase(vendor)) {
                shardingResult.add(targetName);
            }
        }
        // 如果没有匹配到特定的vendor表，就选择集合中的第一个表作为默认目标表
        if (shardingResult.isEmpty()) {
            availableTargetNames.stream().findFirst().ifPresent(shardingResult::add);
        }
        return shardingResult;
    }
}
