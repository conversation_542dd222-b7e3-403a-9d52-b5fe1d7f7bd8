package com.yeelight.service.user.server.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xkcoding.justauth.autoconfigure.JustAuthProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import com.yeelight.service.framework.actuator.ActuatorFilter;
import com.yeelight.service.framework.actuator.EnableExtendPromthuesMetricFilter;
import com.yeelight.service.framework.request.original.filter.OriginalRequestInfoFilter;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import com.yeelight.service.user.server.service.JwtAuthService;
import me.zhyd.oauth.cache.AuthStateCache;
import org.springframework.data.redis.core.RedisTemplate;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @program: yeelight-oauth-api
 * @description: WebMvc配置类
 * @author: Sheldon
 * @create: 2019-06-25 18:02
 **/
@Configuration
@EnableWebMvc
@EnableExtendPromthuesMetricFilter
public class WebMvcConfiguration implements WebMvcConfigurer {
    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private TokenService tokenService;

    @Resource
    private JwtAuthService jwtAuthService;

    @Value("${actuator_whitelist:************,************,*************,************,127.0.0.1,0:0:0:0:0:0:0:1}")
    private String actuatorWhitelist;

    /**
     * 创建并返回一个ContextInterceptor实例。
     * 该拦截器用于在请求中上下文中添加或处理相关信息。
     *
     * @return ContextInterceptor 返回一个配置好的ContextInterceptor实例。
     */
    @Bean
    public ContextInterceptor contextInterceptor() {
        return new ContextInterceptor(yeelightUserReadService, tokenService, jwtAuthService);
    }

    /**
     * 创建并返回一个BizOperateLogAspect实例。
     * 该切面用于记录业务操作日志。
     *
     * @return BizOperateLogAspect 返回一个配置好的BizOperateLogAspect实例。
     */
    @Bean
    public BizOperateLogAspect bizOperateLogAspect() {
        return new BizOperateLogAspect();
    }

    /**
     * 配置静态资源处理器。
     * 使应用能够处理静态资源请求，如CSS、JS、图片等。
     *
     * @param registry ResourceHandlerRegistry 用于注册资源处理器的实例。
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加静态资源路径
        registry.addResourceHandler("/static/**", "/webjars/**").addResourceLocations("classpath:/static/", "classpath:/META-INF/resources/webjars/");
    }


    /**
     * 向Spring MVC添加拦截器配置。
     * 这个方法用于注册一个全局的拦截器，并定义哪些路径应该被这个拦截器拦截，哪些路径应该被排除不进行拦截。
     *
     * @param registry 拦截器注册表，用于注册和配置拦截器。
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 定义不需要被拦截的路径集合，这些路径通常是一些静态资源或系统内部调用路径
        List<String> excludePathPatterns = Lists.newArrayList(
                // Spring Boot Actuator端点
                "/actuator/**",
                // 错误处理页面
                "/error",
                // WebJars资源
                "/webjars/**",
                // 静态资源
                "/static/**",
                // JavaScript资源
                "/js/**",
                // Swagger资源
                "/swagger-resources/**",

                // 公开的API路径，不需要认证和授权
                "/public/v1/**"
        );
        // 注册拦截器，并配置拦截所有路径，然后排除上面定义的不需要拦截的路径
        registry.addInterceptor(contextInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns(excludePathPatterns);
    }


    /**
     * 创建并配置Actuator过滤器的注册 bean。
     * 该过滤器用于拦截以"/actuator/*"为路径的请求，对其进行处理。
     *
     * @return FilterRegistrationBean<ActuatorFilter> 返回Actuator过滤器的注册配置 bean。
     */
    @Bean
    public FilterRegistrationBean<ActuatorFilter> actuatorFilterRegistrationBean() {
        FilterRegistrationBean<ActuatorFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        // 设置使用的过滤器为ActuatorFilter
        filterRegistrationBean.setFilter(new ActuatorFilter());
        // 启用过滤器
        filterRegistrationBean.setEnabled(true);
        // 指定过滤器的URL模式
        filterRegistrationBean.addUrlPatterns("/actuator/*");
        // 初始化参数
        Map<String, String> initParameters = Maps.newHashMap();
        // 设置白名单参数
        initParameters.put("whitelist", actuatorWhitelist);
        // 设置初始化参数
        filterRegistrationBean.setInitParameters(initParameters);
        return filterRegistrationBean;
    }

    /**
     * 创建并配置访问日志过滤器的注册 bean。
     * 该过滤器用于拦截以"/user/info"为路径的请求，记录访问日志。
     *
     * @return FilterRegistrationBean<AccessLogFilter> 返回访问日志过滤器的注册配置 bean。
     */
    @Bean
    public FilterRegistrationBean<AccessLogFilter> accessLogFilterRegistrationBean() {
        FilterRegistrationBean<AccessLogFilter> registrationBean = new FilterRegistrationBean<>();
        // 设置使用的过滤器为AccessLogFilter
        registrationBean.setFilter(new AccessLogFilter());
        // 设置过滤器的优先级为最高
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        // 指定过滤器的URL模式
        registrationBean.addUrlPatterns("/user/info");
        return registrationBean;
    }


    /**
     * 创建JustAuthProperties Bean
     * 手动创建JustAuth配置属性Bean，解决自动配置不生效的问题
     *
     * @return JustAuthProperties实例
     */
    @Bean
    @ConfigurationProperties(prefix = "justauth")
    @ConditionalOnProperty(prefix = "justauth", value = "enabled", havingValue = "true", matchIfMissing = true)
    public JustAuthProperties justAuthProperties() {
        return new JustAuthProperties();
    }

    /**
     * 创建AuthStateCache Bean
     * 用于JustAuth的状态缓存，使用Redis作为缓存存储
     *
     * @param redisManager Redis管理器
     * @return AuthStateCache实例
     */
    @Bean
    @ConditionalOnProperty(prefix = "justauth", value = "enabled", havingValue = "true", matchIfMissing = true)
    public AuthStateCache authStateCache(RedisManager redisManager) {
        // 使用Redis作为JustAuth的状态缓存
        return new AuthStateCache() {
            private final RedisTemplate<String, Object> redisTemplate = redisManager.oauthRedisTemplate();
            private final String keyPrefix = "JUSTAUTH::STATE::";

            @Override
            public void cache(String key, String value) {
                redisTemplate.opsForValue().set(keyPrefix + key, value, 1, TimeUnit.HOURS);
            }

            @Override
            public void cache(String key, String value, long timeout) {
                redisTemplate.opsForValue().set(keyPrefix + key, value, timeout, TimeUnit.MILLISECONDS);
            }

            @Override
            public String get(String key) {
                Object value = redisTemplate.opsForValue().get(keyPrefix + key);
                return value != null ? value.toString() : null;
            }

            @Override
            public boolean containsKey(String key) {
                return redisTemplate.hasKey(keyPrefix + key);
            }
        };
    }

    /**
     * 创建并返回一个YeelightAuthRequestFactory实例。
     * 该实例的创建取决于属性justauth.enabled的值是否为true。
     * 如果该属性不存在或值为false，则不会创建该实例。
     *
     * @param properties 用于配置JustAuth的属性对象。
     * @param authStateCache 用于缓存认证状态的对象。
     * @return 返回一个配置好的YeelightAuthRequestFactory实例。
     */
    @Bean
    @ConditionalOnProperty(prefix = "justauth", value = "enabled", havingValue = "true", matchIfMissing = true)
    public YeelightAuthRequestFactory yeelightAuthRequestFactory(JustAuthProperties properties, AuthStateCache authStateCache) {
        return new YeelightAuthRequestFactory(properties, authStateCache);
    }

    /**
     * 创建并返回一个OriginalRequestInfoFilter实例。
     * 该过滤器用于保留和处理原始请求信息。
     *
     * @return 返回一个配置好的OriginalRequestInfoFilter实例。
     */
    @Bean
    public OriginalRequestInfoFilter originalRequestInfoFilter() {
        return new OriginalRequestInfoFilter();
    }

}
