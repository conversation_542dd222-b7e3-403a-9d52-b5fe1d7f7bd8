package com.yeelight.service.user.server.config;

import com.yeelight.service.framework.request.original.filter.OriginalRequestInfoFilter;
import com.yeelight.service.user.server.config.firewall.CustomStrictHttpFirewall;
import com.yeelight.service.user.server.config.firewall.RequestRejectedExceptionFilter;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.custom.*;
import com.yeelight.service.user.server.custom.endpoint.YeelightLoginTargetAuthenticationEntryPoint;
import com.yeelight.service.user.server.filter.JwtAuthenticationFilter;
import com.yeelight.service.user.server.handler.*;
import com.yeelight.service.user.server.provider.*;
import com.yeelight.service.user.server.service.JwtAuthService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.core.userdetails.UserDetailsByNameServiceWrapper;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.security.web.session.HttpSessionEventPublisher;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.context.request.RequestContextListener;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.filter.RequestContextFilter;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @program: yeelight-service-oauth
 * @description: Web安全配置类
 * @author: Sheldon
 * @create: 2019-06-05 17:30
 **/
@Configuration
@EnableWebSecurity
@Order(1000)
public class WebSecurityConfiguration {
    @Resource
    @Lazy
    private AuthenticationProvider authenticationProvider;

    @Resource
    @Lazy
    private PhoneAndCodeAuthenticationProvider phoneAndCodeAuthenticationProvider;

    @Resource
    @Lazy
    private EmailAndCodeAuthenticationProvider emailAndCodeAuthenticationProvider;

    @Resource
    @Lazy
    private PhoneAndJverifyTokenAuthenticationProvider phoneAndJverifyTokenAuthenticationProvider;

    @Resource
    @Lazy
    private ExternalIdTokenAuthenticationProvider externalIdTokenAuthenticationProvider;

    @Resource
    @Lazy
    private AccountAndCodeAuthenticationProvider accountAndCodeAuthenticationProvider;

    @Resource
    @Lazy
    private PhoneAndWechatMiniShopInfoAuthenticationProvider phoneAndWechatMiniShopInfoAuthenticationProvider;

    @Resource
    @Lazy
    private WechatMiniProgramAuthenticationProvider wechatMiniProgramAuthenticationProvider;

    @Resource
    @Lazy
    private SocialAuthenticationProvider socialAuthenticationProvider;

    @Resource
    @Lazy
    private CustomAuthenticationDetailsSource authenticationDetailsSource;
    @Resource
    @Lazy
    private CustomAuthenticationSuccessHandler authenticationSuccessHandler;

    @Resource
    @Lazy
    private CustomAuthenticationFailureHandler authenticationFailureHandler;
    @Resource
    @Lazy
    private CustomLogoutSuccessHandler logoutSuccessHandler;

    @Resource
    @Lazy
    private CustomRedirectInvalidSessionStrategy customRedirectInvalidSessionStrategy;

    @Resource
    @Lazy
    private CustomRedirectSessionInformationExpiredStrategy customRedirectSessionInformationExpiredStrategy;

    @Resource
    @Lazy
    private SmsAuthenticationFailureHandler smsAuthenticationFailureHandler;
    @Resource
    @Lazy
    private SmsAuthenticationDetailsSource smsAuthenticationDetailsSource;
    @Resource
    @Lazy
    private SmsAuthenticationProvider smsAuthenticationProvider;

    @Resource
    @Lazy
    private EmailCodeAuthenticationFailureHandler emailCodeAuthenticationFailureHandler;
    @Resource
    @Lazy
    private EmailCodeAuthenticationDetailsSource emailCodeAuthenticationDetailsSource;
    @Resource
    @Lazy
    private EmailCodeAuthenticationProvider emailCodeAuthenticationProvider;
    @Resource
    @Lazy
    private JustAuthSecurityConfigurer justAuthSecurityConfigurer;

    @Resource
    private OriginalRequestInfoFilter originalRequestInfoFilter;

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    @Resource
    private JwtAuthService jwtAuthService;

    @Resource
    @Lazy
    private YeelightUserDetailsService yeelightUserDetailsService;

    @Resource
    @Lazy
    private PasswordEncoder passwordEncoder;

    /**
     * 创建并配置LoginUrlAuthenticationEntryPoint，用于处理未认证用户的登录请求。
     * 这个EntryPoint会将用户重定向到指定的登录页面。
     *
     * @return LoginUrlAuthenticationEntryPoint 用于处理登录请求的入口点实例。
     */
    @Bean
    public LoginUrlAuthenticationEntryPoint loginUrlAuthenticationEntryPoint() {
        // 创建并初始化YeelightLoginTargetAuthenticationEntryPoint，配置登录页地址
        LoginUrlAuthenticationEntryPoint entryPoint = new YeelightLoginTargetAuthenticationEntryPoint(gatewayOauthConfig, SecurityConstants.PREFIX_PATH + SecurityConstants.LOGIN_PAGE);
        // 下面的代码行被注释，它用于设置是否使用转发来处理登录请求
        //entryPoint.setUseForward(true); // 设置为true以使用转发
        return entryPoint;
    }

    /**
     * 配置HttpSecurity来定义应用程序的安全规则。
     *
     * @param http 用于配置安全规则的HttpSecurity对象。
     * @throws Exception 配置过程中可能抛出的异常。
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        // 配置短信登录
        SmsLoginConfigurer smsLoginConfigurer = new SmsLoginConfigurer();
        smsLoginConfigurer
                .authenticationDetailsSource(smsAuthenticationDetailsSource)
                .successHandler(authenticationSuccessHandler)
                .failureHandler(smsAuthenticationFailureHandler);

        // 配置邮箱验证码登录
        EmailCodeLoginConfigurer emailCodeLoginConfigurer = new EmailCodeLoginConfigurer();
        emailCodeLoginConfigurer
                .authenticationDetailsSource(emailCodeAuthenticationDetailsSource)
                .successHandler(authenticationSuccessHandler)
                .failureHandler(emailCodeAuthenticationFailureHandler);

        return http
                .addFilterBefore(originalRequestInfoFilter, UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(new JwtAuthenticationFilter(jwtAuthService), UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(authorize -> authorize
                        // Allow OPTIONS requests
                        .requestMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                        // 允许静态资源访问 - 使用显式的AntPathRequestMatcher避免路径模式解析问题
                        .requestMatchers(
                                new AntPathRequestMatcher("/**/*.css"),
                                new AntPathRequestMatcher("/**/*.js"),
                                new AntPathRequestMatcher("/**/*.png"),
                                new AntPathRequestMatcher("/**/*.jpg"),
                                new AntPathRequestMatcher("/**/*.jpeg")
                        ).permitAll()
                        // 允许匿名访问的地址 - 使用显式的AntPathRequestMatcher避免路径模式解析问题
                        .requestMatchers(
                                new AntPathRequestMatcher("/static/**"),
                                new AntPathRequestMatcher("/webjars/**"),
                                new AntPathRequestMatcher("/public/**"),
                                new AntPathRequestMatcher("/jwt/**"),
                                new AntPathRequestMatcher("/user/**"),
                                new AntPathRequestMatcher("/partner/**"),
                                new AntPathRequestMatcher("/third-party/**"),
                                new AntPathRequestMatcher("/favicon.ico"),
                                new AntPathRequestMatcher(SecurityConstants.LOGIN_PAGE),
                                new AntPathRequestMatcher(SecurityConstants.LOGOUT_PAGE),
                                new AntPathRequestMatcher(SecurityConstants.ERROR_PAGE),
                                new AntPathRequestMatcher(SecurityConstants.REGISTER_PAGE),
                                new AntPathRequestMatcher(SecurityConstants.GETPASS_PAGE),
                                new AntPathRequestMatcher(SecurityConstants.RESET_PAGE),
                                new AntPathRequestMatcher(SecurityConstants.LOGIN_PROCESSING_URL),
                                new AntPathRequestMatcher(SecurityConstants.THIRD_PARTY_AUTH),
                                new AntPathRequestMatcher("/authentication/**"),
                                new AntPathRequestMatcher("/*.html"),
                                new AntPathRequestMatcher("/actuator/**")
                        )
                        .permitAll()
                        .anyRequest().authenticated()
                )
                .exceptionHandling(exceptions -> exceptions
                        .authenticationEntryPoint(loginUrlAuthenticationEntryPoint())
                        .accessDeniedHandler(new CustomAccessDeniedHandler())
                )
                // 启用表单登录
                .formLogin(form -> form
                        // 设置登录页面的访问路径，默认为 /login，GET 请求；该路径不设限访问
                        .loginPage(SecurityConstants.PREFIX_PATH + SecurityConstants.LOGIN_PAGE)
                        // 自定义登录表单请求路径，默认为 loginPage() 设置的路径，POST 请求
                        .loginProcessingUrl(SecurityConstants.LOGIN_PROCESSING_URL)
                        // 设置登录表单中的用户名参数，默认为 username
                        .usernameParameter(SecurityConstants.USERNAME_KEY)
                        // 设置登录表单中的密码参数，默认为 password
                        .passwordParameter(SecurityConstants.PASSWORD_KEY)
                        // 配置用户认证成功后的自定义处理器
                        .successHandler(authenticationSuccessHandler)
                        // 设置用户登录失败后的自定义错误处理器
                        .failureHandler(authenticationFailureHandler)
                        // 认证失败处理，重定向到指定地址，默认为 loginPage() + ?error；该路径不设限访问， 不和failureHandler同时使用
                        //.failureUrl(SecurityConstants.PREFIX_PATH + SecurityConstants.LOGIN_PAGE + "?error")
                        //.failureForwardUrl(String forwardUrl) // 设置用户认证失败后转发的地址。
                        .authenticationDetailsSource(authenticationDetailsSource)
                )
                .logout(logout -> logout
                        // 指定用户注销登录时请求访问的地址，默认为 POST 方式的/logout, 关闭 csrf 防护后默认为 GET 方式的/logout。
                        .logoutUrl(SecurityConstants.LOGOUT_PAGE)
                        // 指定用户成功注销登录后使用的处理器，不能和 logoutSuccessUrl 同时使用
                        .logoutSuccessHandler(logoutSuccessHandler)
                        //.logoutSuccessUrl(SecurityConstants.LOGIN_PAGE) // 指定用户成功注销登录后的重定向地址，默认为/登录页面url?logout。
                        // 指定用户注销登录后删除的 Cookie
                        .deleteCookies("OAUTH_SESSION_ID", "JSESSIONID", "SESSION")
                        // 指定用户注销登录后是否立即清除用户的 Session，默认为 true。
                        .invalidateHttpSession(true)
                        // 指定用户退出登录后是否立即清除用户认证信息对象 Authentication，默认为 true。
                        .clearAuthentication(true)
                        // 指定用户注销登录时使用的处理器。
                        //.addLogoutHandler(LogoutHandler logoutHandler)
                )
                .sessionManagement(session -> session
                        // 指定 Session 固定保护策略，即在用户认证成功后，创建一个新的 Session 并将旧的 Session 中的属性复制到新的 Session 中。
                        .sessionFixation().migrateSession()
                        .sessionCreationPolicy(SessionCreationPolicy.NEVER)
                        // session 失效后跳转的页面
                        // 指定会话失效时（请求携带无效的 JSESSIONID 访问系统）重定向的 URL，默认重定向到登录页面。
                        //.invalidSessionUrl(SecurityConstants.PREFIX_PATH + SecurityConstants.LOGIN_PAGE)
                        // 指定会话失效时（请求携带无效的 JSESSIONID 访问系统）的处理策略。
                        .invalidSessionStrategy(customRedirectInvalidSessionStrategy)
                        // 设置同一用户最多存在几个 Session，后登陆的用户将踢掉前面的用户
                        // 指定每个用户的最大并发会话数量，-1 表示不限数量。
                        .maximumSessions(20)
                        //.expiredUrl(SecurityConstants.PREFIX_PATH + SecurityConstants.LOGIN_PAGE + "?expire=true") // 如果某用户达到最大会话并发数后，新会话请求访问时，其最老会话会在下一次请求时失效并重定向到 expiredUrl。
                        // 用户失效处理
                        // 如果某用户达到最大会话并发数后，新会话请求访问时，其最老会话会在下一次请求中失效并按照该策略处理请求。注意如果本方法与 expiredUrl() 同时使用，优先使用 expiredUrl() 的配置。
                        .expiredSessionStrategy(customRedirectSessionInformationExpiredStrategy)
                        // 如果设置为 true，表示某用户达到最大会话并发数后，新会话请求会被拒绝登录；如果设置为 false，表示某用户达到最大会话并发数后，新会话请求访问时，其最老会话会在下一次请求时失效并根据 expiredUrl() 或者 expiredSessionStrategy() 方法配置的会话失效策略进行处理，默认值为 false。
                        .maxSessionsPreventsLogin(false)
                        // 设置所要使用的 sessionRegistry，默认配置的是 SessionRegistryImpl 实现类。
                        //.sessionRegistry(sessionRegistry())
                )
                // 限制iframe引用页面,
                // DENY              表示该页面不允许在 frame 中展示，即便是在相同域名的页面中嵌套也不允许。
                // SAMEORIGIN        表示该页面可以在相同域名页面的 frame 中展示。
                // ALLOW-FROM uri    表示该页面可以在指定来源的 frame 中展示。
                // disable 完全放开
                .headers(headers -> headers.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable))
                // 关闭 csrf 防护
                .csrf(AbstractHttpConfigurer::disable)
                // 关闭 cors 防护
                .cors(AbstractHttpConfigurer::disable)
                // 添加跨域过滤器
                .addFilter(corsFilter())
                .addFilterBefore(requestContextFilter(), UsernamePasswordAuthenticationFilter.class)
                
                // 配置短信登录
                .with(smsLoginConfigurer, configurer -> {})
                .authenticationProvider(smsAuthenticationProvider)
                
                // 配置邮箱验证码登录
                .with(emailCodeLoginConfigurer, configurer -> {})
                .authenticationProvider(emailCodeAuthenticationProvider)
                
                // 配置社交登录 - 使用with方法替代已弃用的apply
                .with(justAuthSecurityConfigurer, configurer -> {})
                
                // 自定义安全配置
                .addFilterBefore(new RequestRejectedExceptionFilter(), ChannelProcessingFilter.class)
                .build();
    }

    /**
     * 配置认证管理器构建器，以支持多种认证模式。
     * 本方法用于设置不同的认证提供器，从而支持多种登录方式，
     * 包括但不限于密码模式、手机号/邮箱+验证码模式、小程序商城手机号+微信信息模式、邮箱+验证码模式、用户名+三方ID模式、
     * 手机号+验证码模式（自动注册）、极光一键登录、外部系统根据用户ID一键登录。
     *
     * @return AuthenticationManager
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        List<org.springframework.security.authentication.AuthenticationProvider> providers = new ArrayList<>();
        
        // 配置支持的认证模式：手机号/邮箱+验证码模式
        providers.add(accountAndCodeAuthenticationProvider);

        // 配置微信小程序登录
        providers.add(wechatMiniProgramAuthenticationProvider);

        // 配置支持的小程序商城登录模式：手机号+微信信息模式(自动注册)
        providers.add(phoneAndWechatMiniShopInfoAuthenticationProvider);

        // 配置支持的邮箱+验证码模式(自动注册)
        providers.add(emailAndCodeAuthenticationProvider);

        // 配置支持的用户名+三方ID模式
        providers.add(socialAuthenticationProvider);

        // 重复配置支持的手机号+验证码模式(自动注册)，可能为冗余或备用配置
        providers.add(phoneAndCodeAuthenticationProvider);

        // 配置支持的极光一键登录模式
        providers.add(phoneAndJverifyTokenAuthenticationProvider);

        // 配置支持的外部系统根据用户ID一键登录模式
        providers.add(externalIdTokenAuthenticationProvider);

        // 配置支持的password模式
        providers.add(authenticationProvider);
        
        return new ProviderManager(providers);
    }


    /**
     * 支持password模式要配置AuthenticationManager
     *
     * @return the {@link AuthenticationManager}
     */
    @Bean
    public AuthenticationManager authenticationManagerBean() {
        // 创建DaoAuthenticationProvider
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setUserDetailsService(yeelightUserDetailsService);
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder);

        // 创建PreAuthenticatedAuthenticationProvider用于处理刷新令牌
        PreAuthenticatedAuthenticationProvider preAuthProvider = new PreAuthenticatedAuthenticationProvider();
        preAuthProvider.setPreAuthenticatedUserDetailsService(new UserDetailsByNameServiceWrapper<>(yeelightUserDetailsService));

        // 获取所有Provider
        List<org.springframework.security.authentication.AuthenticationProvider> providers = new ArrayList<>();

        // 获取父类配置的Provider
        AuthenticationManager parentManager = authenticationManager();
        if (parentManager instanceof ProviderManager) {
            providers.addAll(new ArrayList<>(((ProviderManager) parentManager).getProviders()));
        }

        // 添加新的Provider
        providers.add(daoAuthenticationProvider);
        providers.add(preAuthProvider);

        return new ProviderManager(providers);
    }

    /**
     * 注册 SessionRegistry，该 Bean 用于管理 Session 会话并发控制
     */
    //@Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

    /**
     * 创建并返回一个HttpSessionEventPublisher实例，它是一个Spring Bean，用于发布HTTP会话事件。
     * 配置 Session 的监听器（注意：如果使用并发 Session 控制，一般都需要配置该监听器）
     * 解决 Session 失效后, SessionRegistry 中 SessionInformation 没有同步失效的问题
     *
     * @return HttpSessionEventPublisher 新的HttpSessionEventPublisher实例
     */
    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new HttpSessionEventPublisher();
    }


    /**
     * 创建并配置RequestContextFilter，用于在每个HTTP请求中注入一个请求上下文。
     * 设置threadContextInheritable为true，允许线程上下文继承。
     *
     * @return RequestContextFilter 配置好的请求上下文过滤器实例。
     */
    @Bean
    public RequestContextFilter requestContextFilter() {
        RequestContextFilter requestContextFilter = new RequestContextFilter();
        // 设置线程上下文继承
        requestContextFilter.setThreadContextInheritable(true);
        return requestContextFilter;
    }

    /**
     * 创建RequestContextListener Bean，用于监听HTTP请求，初始化请求上下文。
     *
     * @return RequestContextListener 请求上下文监听器实例。
     */
    @Bean
    public RequestContextListener requestContextListener() {
        return new RequestContextListener();
    }

    /**
     * 配置HttpFirewall，允许URL中包含编码的斜杠字符。
     *
     * @return HttpFirewall 允许URL编码斜杠的自定义严格HTTP防火墙实例。
     */
    @Bean
    public HttpFirewall allowUrlEncodedSlashHttpFirewall() {
        CustomStrictHttpFirewall firewall = new CustomStrictHttpFirewall();
        // 允许URL中包含编码的斜杠字符
        firewall.setAllowUrlEncodedSlash(true);
        return firewall;
    }

    /**
     * 配置HttpSecurity以定制Spring Security的HTTP安全设置。
     * 注意：在Spring Security 6.0+中，这个方法已经被移除。
     * 使用SecurityFilterChain Bean替代。
     *
     * @return HttpSecurity 配置好的HTTP安全实例。
     * @deprecated 使用SecurityFilterChain Bean替代
     */
    @Deprecated
    public HttpSecurity httpSecurity() {
        // 在Spring Security 6.0+中，这个方法已经不再有效
        throw new UnsupportedOperationException("Method not supported in Spring Security 6.0+. Use SecurityFilterChain Bean instead.");
    }


    /**
     * 跨域过滤器配置
     *
     * @return CorsFilter
     */
    @Bean
    public CorsFilter corsFilter() {
        // 初始化cors配置源对象
        UrlBasedCorsConfigurationSource configurationSource = new UrlBasedCorsConfigurationSource();

        // 初始化cors配置对象
        CorsConfiguration configuration = new CorsConfiguration();

        // 设置允许跨域的域名,如果允许携带cookie的话,路径就不能写*号, *表示所有的域名都可以跨域访问
        //appConfig.getFrontendCorsAllowedOrigins().forEach(configuration::addAllowedOrigin);
        // 设置允许跨域的域名, *表示所有的域名都可以跨域访问
        configuration.setAllowedOrigins(Collections.singletonList(CorsConfiguration.ALL));
        // 设置跨域访问可以携带cookie
        configuration.setAllowCredentials(true);
        // 允许所有的请求方法 ==> GET POST PUT Delete
        configuration.addAllowedMethod(HttpMethod.GET);
        configuration.addAllowedMethod(HttpMethod.POST);
        configuration.addAllowedMethod(HttpMethod.PUT);
        configuration.addAllowedMethod(HttpMethod.DELETE);
        configuration.addAllowedMethod(HttpMethod.OPTIONS);
        configuration.addAllowedMethod(HttpMethod.HEAD);
        configuration.addAllowedMethod(HttpMethod.PATCH);
        configuration.addAllowedMethod(HttpMethod.TRACE);

        // 允许携带任何头信息
        configuration.addAllowedHeader(CorsConfiguration.ALL);

        // 配置有效时长
        configuration.setMaxAge(3600L);

        // 给配置源对象设置过滤的参数
        // 参数一: 过滤的路径 == > 所有的路径都要求校验是否跨域
        // 参数二: 配置类
        configurationSource.registerCorsConfiguration("/**", configuration);

        // 返回配置好的过滤器
        return new CorsFilter(configurationSource);
    }
}