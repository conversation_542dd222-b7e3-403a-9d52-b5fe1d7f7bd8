package com.yeelight.service.user.server.config.firewall;

import org.springframework.http.HttpMethod;
import org.springframework.security.web.firewall.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @description: 自定义HttpFirewall，用于防止URL路径遍历攻击
 * <AUTHOR>
 */
public class CustomStrictHttpFirewall implements HttpFirewall {
    /**
     * Used to specify to {@link #setAllowedHttpMethods(Collection)} that any HTTP method should be allowed.
     */
    private static final Set<String> ALLOW_ANY_HTTP_METHOD = Collections.emptySet();

    private static final String ENCODED_PERCENT = "%25";

    private static final String PERCENT = "%";

    private static final char MIN_PRINTABLE_ASCII = ' ';
    private static final char MAX_PRINTABLE_ASCII = '~';

    private static final String DOUBLE_SLASHES = "//";

    private static final List<String> FORBIDDEN_ENCODED_PERIOD = List.of("%2e", "%2E");

    private static final List<String> FORBIDDEN_SEMICOLON = List.of(";", "%3b", "%3B");

    private static final List<String> FORBIDDEN_FORWARDSLASH = List.of("%2f", "%2F");

    private static final List<String> FORBIDDEN_BACKSLASH = List.of("\\", "%5c", "%5C");

    private final Set<String> encodedUrlBlacklist = new HashSet<>();

    private final Set<String> decodedUrlBlacklist = new HashSet<>();

    private Set<String> allowedHttpMethods = createDefaultAllowedHttpMethods();

    public CustomStrictHttpFirewall() {
        urlBlacklistsAddAll(FORBIDDEN_SEMICOLON);
        urlBlacklistsAddAll(FORBIDDEN_FORWARDSLASH);
        urlBlacklistsAddAll(FORBIDDEN_BACKSLASH);

        this.encodedUrlBlacklist.add(ENCODED_PERCENT);
        this.encodedUrlBlacklist.addAll(FORBIDDEN_ENCODED_PERIOD);
        this.decodedUrlBlacklist.add(PERCENT);
    }

    /**
     * 此方法用于设置是否允许任何HTTP方法。如果设置为true，则不会对HTTP方法进行任何验证。
     * 这可能会使应用程序面临<a href="https://www.owasp.org/index.php/Test_HTTP_Methods_(OTG-CONFIG-006)">
     * HTTP方法篡改和XST攻击</a>的风险。
     * @param unsafeAllowAnyHttpMethod 如果为true，则禁用HTTP方法验证；如果为false，则重置为默认值。默认值为false。
     * @see #setAllowedHttpMethods(Collection) 有关允许的HTTP方法的更多设置，请参阅此方法。
     * @since 5.1
     */
    public void setUnsafeAllowAnyHttpMethod(boolean unsafeAllowAnyHttpMethod) {
        // 根据传入的参数决定是否允许任何HTTP方法，是则设置为允许任何方法，否则恢复为默认允许的方法集合
        this.allowedHttpMethods = unsafeAllowAnyHttpMethod ? ALLOW_ANY_HTTP_METHOD : createDefaultAllowedHttpMethods();
    }


    /**
     * 设置允许的HTTP方法集合。默认情况下，允许"DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", 和 "PUT"。
     *
     * @param allowedHttpMethods 一个包含允许的HTTP方法的不区分大小写的集合。
     * @see #setUnsafeAllowAnyHttpMethod(boolean) 有关设置为允许任何HTTP方法的更多信息。
     * @since 5.1
     */
    public void setAllowedHttpMethods(Collection<String> allowedHttpMethods) {
        // 检查传入的HTTP方法集合是否为null，若为null则抛出异常
        if (allowedHttpMethods == null) {
            throw new IllegalArgumentException("allowedHttpMethods cannot be null");
        }
        // 如果传入的集合是允许任何HTTP方法的特殊标记，直接赋值
        if (allowedHttpMethods == ALLOW_ANY_HTTP_METHOD) {
            this.allowedHttpMethods = ALLOW_ANY_HTTP_METHOD;
        } else {
            // 否则，创建一个新的HashSet实例并赋值，确保方法的无序性和唯一性
            this.allowedHttpMethods = new HashSet<>(allowedHttpMethods);
        }
    }


    /**
     * 设置是否允许URL中包含分号（即矩阵变量）。默认情况下，禁用此行为，因为这是尝试进行
     * <a href="https://www.owasp.org/index.php/Reflected_File_Download">反射文件下载攻击</a>的常见方式。
     * 它也是绕过基于URL的安全性的许多利用手段的来源。
     * <p>
     * 例如，以下CVE是与Servlet规范中如何处理分号相关的不明确性导致的问题的一小部分，
     * 这些不明确性导致了CVE：
     *
     * <ul>
     *     <li><a href="https://pivotal.io/security/cve-2016-5007">cve-2016-5007</a></li>
     *     <li><a href="https://pivotal.io/security/cve-2016-9879">cve-2016-9879</a></li>
     *     <li><a href="https://pivotal.io/security/cve-2018-1199">cve-2018-1199</a></li>
     * </ul>
     *
     * <p>
     * 如果您希望允许分号，请重新考虑，因为它是非常常见的安全绕过来源。
     * 下面是一些用户希望允许分号的常见原因及其替代方案：
     * </p>
     * <ul>
     * <li>在路径中包含JSESSIONID - 您不应该在URL中包含会话ID（或
     * 任何敏感信息），因为它可能导致泄露。应使用Cookie。
     * </li>
     * <li>矩阵变量 - 希望利用矩阵变量的用户应考虑使用HTTP参数代替。
     * </li>
     * </ul>
     *
     * @param allowSemicolon 是否允许URL中包含分号。默认为false
     */
    public void setAllowSemicolon(boolean allowSemicolon) {
        // 根据allowSemicolon的值，相应地添加或移除禁止分号的黑名单
        if (allowSemicolon) {
            urlBlacklistsRemoveAll(FORBIDDEN_SEMICOLON);
        } else {
            urlBlacklistsAddAll(FORBIDDEN_SEMICOLON);
        }
    }


    /**
     * 设置是否允许URL编码的斜杠("/")出现在路径中。
     * <p>
     * 默认情况下，这是不允许的，因为这是绕过基于URL的安全措施的常见方式。
     * </p>
     * <p>
     * 例如，由于servlet规范中的歧义，该值的解析不一致，导致{@code HttpServletRequest}
     * 中与路径相关的值不同，从而允许绕过某些安全约束。
     * </p>
     *
     * @param allowUrlEncodedSlash 是否允许URL编码的斜杠("/")出现在路径中。
     *                             默认值为false。
     */
    public void setAllowUrlEncodedSlash(boolean allowUrlEncodedSlash) {
        // 根据allowUrlEncodedSlash的值，动态调整URL黑名单
        if (allowUrlEncodedSlash) {
            urlBlacklistsRemoveAll(FORBIDDEN_FORWARDSLASH);
        } else {
            urlBlacklistsAddAll(FORBIDDEN_FORWARDSLASH);
        }
    }


    /**
     * 设置是否允许URL编码的句点字符("%2E")出现在路径中。
     * 默认情况下，这是不允许的，因为这经常成为安全漏洞的来源。
     * <p>
     * 例如，由于servlet规范的歧义，一个URL编码的句点可能会通过目录遍历攻击
     * 来绕过安全约束。这是因为路径的解析不一致，导致在{@code HttpServletRequest}
     * 的路径相关值中存在不同的值，从而允许绕过某些安全约束。
     *
     * @param allowUrlEncodedPeriod 是否允许URL编码的句点字符("%2E")在路径中。
     *                              默认值为false。
     */
    public void setAllowUrlEncodedPeriod(boolean allowUrlEncodedPeriod) {
        // 根据allowUrlEncodedPeriod的值，动态更新encodedUrlBlacklist集合
        if (allowUrlEncodedPeriod) {
            FORBIDDEN_ENCODED_PERIOD.forEach(this.encodedUrlBlacklist::remove);
        } else {
            this.encodedUrlBlacklist.addAll(FORBIDDEN_ENCODED_PERIOD);
        }
    }


    /**
     * 设置是否允许路径中包含反斜杠或URL编码的反斜杠。
     * <p>
     * 默认情况下，不允许这种行为，因为这经常是安全漏洞的来源。
     * </p>
     * <p>
     * 例如，由于Servlet规范中的歧义，一个URL编码的句点可能会通过目录遍历攻击绕过安全限制。
     * 这是因为路径的解析不一致，导致{@code HttpServletRequest}路径相关值的不同，
     * 允许绕过某些安全限制。
     * </p>
     *
     * @param allowBackSlash 是否允许路径中包含反斜杠或URL编码的反斜杠。
     *                       默认为false，表示不允许。
     */
    public void setAllowBackSlash(boolean allowBackSlash) {
        // 根据allowBackSlash的值，动态调整URL黑名单
        if (allowBackSlash) {
            urlBlacklistsRemoveAll(FORBIDDEN_BACKSLASH);
        } else {
            urlBlacklistsAddAll(FORBIDDEN_BACKSLASH);
        }
    }


    /**
     * 设置是否允许URL编码的百分号"%25"出现在路径中。
     * 默认情况下是不允许的，因为这是安全漏洞的常见来源。
     * <p>
     * 具体而言，这种做法可能导致通过双URL编码绕过安全约束的攻击。
     *
     * @param allowUrlEncodedPercent 如果希望允许URL编码的百分号"%25"出现在路径中，则为true；
     *                               如果遵循默认行为，防止潜在的安全风险，则为false。
     */
    public void setAllowUrlEncodedPercent(boolean allowUrlEncodedPercent) {
        // 根据allowUrlEncodedPercent的值，动态调整黑白名单
        if (allowUrlEncodedPercent) {
            // 允许的情况下，从黑名单中移除URL编码和解码后的百分号
            this.encodedUrlBlacklist.remove(ENCODED_PERCENT);
            this.decodedUrlBlacklist.remove(PERCENT);
        } else {
            // 不允许的情况下，将URL编码和解码后的百分号添加到黑名单中
            this.encodedUrlBlacklist.add(ENCODED_PERCENT);
            this.decodedUrlBlacklist.add(PERCENT);
        }
    }


    /**
     * 将给定的URL集合添加到编码和解码的URL黑名单中。
     * 这个方法会同时将提供的值添加到两个黑名单列表中，确保无论URL是编码前还是解码后，都能被正确识别和拦截。
     *
     * @param values 需要被添加到黑名单的URL集合。集合中的每个元素都应是一个字符串类型的URL。
     */
    private void urlBlacklistsAddAll(Collection<String> values) {
        // 同时将新值添加到编码和解码的URL黑名单中
        this.encodedUrlBlacklist.addAll(values);
        this.decodedUrlBlacklist.addAll(values);
    }


    /**
     * 从编码和解码的URL黑名单中移除所有给定的值。
     * @param values 需要被移除的值的集合，这些值将从编码和解码的URL黑名单中移除。
     */
    private void urlBlacklistsRemoveAll(Collection<String> values) {
        // 从编码的URL黑名单中移除指定的值
        this.encodedUrlBlacklist.removeAll(values);
        // 从解码的URL黑名单中移除指定的值
        this.decodedUrlBlacklist.removeAll(values);
    }


    /**
     * 创建一个经过防火墙过滤的请求对象。此方法会检查HTTP方法是否被禁止、URL是否在黑名单上、
     * 请求的URL是否被规范化以及请求URI是否只包含可打印的ASCII字符。
     * 如果检查失败，会抛出RequestRejectedException。
     *
     * @param request 代表原始HTTP请求的HttpServletRequest对象。
     * @return 经过防火墙规则处理后的FirewalledRequest对象。
     * @throws RequestRejectedException 如果请求不通过任何检查，则抛出此异常。
     */
    @Override
    public FirewalledRequest getFirewalledRequest(HttpServletRequest request) throws RequestRejectedException {
        // 拒绝使用禁止的HTTP方法
        rejectForbiddenHttpMethod(request);
        // 检查请求的URL是否在黑名单上
        rejectedBlacklistedUrls(request);

        // 检查请求的URL是否被规范化
        if (!isNormalized(request)) {
            throw new RequestRejectedException("The request was rejected because the URL was not normalized.");
        }

        String requestUri = request.getRequestURI();
        // 检查请求URI是否只包含可打印的ASCII字符
        if (!containsOnlyPrintableAsciiCharacters(requestUri)) {
            throw new RequestRejectedException("The requestURI was rejected because it can only contain printable ASCII characters.");
        }
        // 返回一个新的FirewalledRequest实例，重写了reset方法
        return new FirewalledRequest(request) {
            @Override
            public void reset() {
                // 重置操作的实现，当前为空实现
            }
        };
    }


    /**
     * 拒绝使用不在允许HTTP方法列表中的请求。
     * 该方法检查传入的HTTP请求方法是否在预定义的允许方法列表中。
     * 如果不在列表中，将抛出一个请求被拒绝的异常。
     *
     * @param request HttpServletRequest对象，代表一个客户端的HTTP请求。
     *                该参数用于获取请求的方法类型，并进行白名单检查。
     * @throws RequestRejectedException 如果请求的方法不在允许的HTTP方法列表中，则抛出此异常。
     */
    private void rejectForbiddenHttpMethod(HttpServletRequest request) {
        // 如果允许任何HTTP方法，则无需进一步检查，直接返回
        if (this.allowedHttpMethods == ALLOW_ANY_HTTP_METHOD) {
            return;
        }
        // 检查请求方法是否在允许的HTTP方法集合中，若不在则抛出异常
        if (!this.allowedHttpMethods.contains(request.getMethod())) {
            throw new RequestRejectedException("The request was rejected because the HTTP method \"" +
                    request.getMethod() +
                    "\" was not included within the whitelist " +
                    this.allowedHttpMethods);
        }
    }


    /**
     * 检查请求的URL是否包含在黑名单中。如果发现匹配，则拒绝该请求。
     * 首先检查编码后的URL黑名单，然后检查解码后的URL黑名单。
     * 如果URL包含任何黑名单上的字符串，将抛出RequestRejectedException。
     *
     * @param request HttpServletRequest对象，代表客户端的HTTP请求。
     * @throws RequestRejectedException 如果请求的URL包含在黑名单中，则抛出此异常。
     */
    private void rejectedBlacklistedUrls(HttpServletRequest request) {
        // 检查编码后的URL是否包含在黑名单中
        for (String forbidden : this.encodedUrlBlacklist) {
            if (encodedUrlContains(request, forbidden)) {
                throw new RequestRejectedException("The request was rejected because the URL contained a potentially malicious String \"" + forbidden + "\"");
            }
        }
        // 检查解码后的URL是否包含在黑名单中
        for (String forbidden : this.decodedUrlBlacklist) {
            if (decodedUrlContains(request, forbidden)) {
                throw new RequestRejectedException("The request was rejected because the URL contained a potentially malicious String \"" + forbidden + "\"");
            }
        }
    }


    /**
     * 创建并返回一个防火墙响应对象。
     * 该方法通过包装传入的HttpServletResponse对象，创建一个新的FirewalledResponse对象并返回。
     * 这样的设计允许对响应进行额外的控制或添加安全层。
     *
     * @param response 传入的HttpServletResponse对象，该对象将被包装在FirewalledResponse对象中。
     * @return 返回一个新的FirewalledResponse对象，它包装了传入的HttpServletResponse。
     */
    @Override
    public HttpServletResponse getFirewalledResponse(HttpServletResponse response) {
        // 创建一个新的FirewalledResponse实例，用于包装原始的HttpServletResponse
        return new FirewalledResponse(response);
    }

    /**
     * 创建并返回一个包含默认允许的HTTP方法的集合。
     * 这个方法不接受任何参数。
     *
     * @return 返回一个包含默认允许的HTTP方法名的Set集合，方法名使用大写字母。
     */
    private static Set<String> createDefaultAllowedHttpMethods() {
        // 创建一个空的HashSet来存储HTTP方法名
        Set<String> result = new HashSet<>();
        // 向结果集合中添加所有默认允许的HTTP方法名
        // DELETE方法
        result.add(HttpMethod.DELETE.name());
        // GET方法
        result.add(HttpMethod.GET.name());
        // HEAD方法
        result.add(HttpMethod.HEAD.name());
        // OPTIONS方法
        result.add(HttpMethod.OPTIONS.name());
        // PATCH方法
        result.add(HttpMethod.PATCH.name());
        // POST方法
        result.add(HttpMethod.POST.name());
        // PUT方法
        result.add(HttpMethod.PUT.name());
        // 返回包含所有默认允许HTTP方法的集合
        return result;
    }


    /**
     * 检查HTTP请求是否标准化。
     * 标准化在这里指请求的URI、上下文路径、Servlet路径和路径信息是否都符合预期的格式，不包含非法或异常的字符。
     *
     * @param request HttpServletRequest对象，代表一个HTTP请求。
     * @return boolean 返回true如果所有检查项都通过，即请求是标准化的；否则返回false。
     */
    private static boolean isNormalized(HttpServletRequest request) {
        // 检查请求的URI是否标准化
        if (!isNormalized(request.getRequestURI())) {
            return false;
        }
        // 检查请求的上下文路径是否标准化
        if (!isNormalized(request.getContextPath())) {
            return false;
        }
        // 检查请求的Servlet路径是否标准化
        if (!isNormalized(request.getServletPath())) {
            return false;
        }
        // 检查请求的路径信息是否标准化
        return isNormalized(request.getPathInfo());
    }


    private static boolean encodedUrlContains(HttpServletRequest request, String value) {
        if (valueContains(request.getContextPath(), value)) {
            return true;
        }
        return valueContains(request.getRequestURI(), value);
    }

    private static boolean decodedUrlContains(HttpServletRequest request, String value) {
        if (valueContains(request.getServletPath(), value)) {
            return true;
        }
        return valueContains(request.getPathInfo(), value);
    }

    private static boolean containsOnlyPrintableAsciiCharacters(String uri) {
        // 防止 NullPointerException
        if (uri == null) {
            throw new IllegalArgumentException("URI cannot be null");
        }
        // 性能优化: 将 String 转换为 char 数组
        char[] chars = uri.toCharArray();
        int length = chars.length;

        for (char c : chars) {
            if (c < MIN_PRINTABLE_ASCII || c > MAX_PRINTABLE_ASCII) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查给定的字符串是否包含指定的子字符串。
     *
     * @param value 主字符串，将被检查是否包含子字符串。
     * @param contains 子字符串，用于在主字符串中进行查找。
     * @return 如果主字符串不为null且包含子字符串，则返回true；否则返回false。
     */
    private static boolean valueContains(String value, String contains) {
        // 检查主字符串不为null且确实包含子字符串
        return value != null && value.contains(contains);
    }


    /**
     * 检查路径是否规范化（不包含路径遍历序列，如"./", "/../"或"/."）
     *
     * @param path 要测试的路径
     * @return 如果路径不包含任何路径遍历字符序列，则返回true
     */
    private static boolean isNormalized(String path) {
        // 如果路径为null，可认为是规范化的，因为没有具体的路径
        if (path == null) {
            return true;
        }

        // 检查路径中是否包含连续的两个斜杠，如果包含，则路径不规范
        if (path.contains(DOUBLE_SLASHES)) {
            return false;
        }

        // 从路径的末尾开始向前遍历，检查每个路径段
        for (int j = path.length(); j > 0;) {
            int i = path.lastIndexOf('/', j - 1);
            int gap = j - i;

            // 如果路径段是"./"或"/."，则路径不规范
            if (gap == 2 && path.charAt(i + 1) == '.') {
                // ".", "/./" or "/."
                return false;
            } else if (gap == 3 && path.charAt(i + 1) == '.' && path.charAt(i + 2) == '.') {
                // 如果路径段是"/../"，则路径不规范
                return false;
            }

            j = i;
        }

        // 如果上述情况都不满足，则路径规范
        return true;
    }


}
