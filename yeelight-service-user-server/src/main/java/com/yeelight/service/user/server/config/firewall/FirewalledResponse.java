package com.yeelight.service.user.server.config.firewall;

import java.io.IOException;
import java.util.regex.Pattern;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;

/**
 * @program: yeelight-service-user
 * @description: FirewalledResponse
 * @author: yeelight
 * @create: 2022-10-11 10:13
 **/
class FirewalledResponse extends HttpServletResponseWrapper {
    private static final Pattern CR_OR_LF = Pattern.compile("[\\r\\n]");
    private static final String LOCATION_HEADER = "Location";
    private static final String SET_COOKIE_HEADER = "Set-Cookie";

    public FirewalledResponse(HttpServletResponse response) {
        super(response);
    }

    /**
     * 重定向到指定的位置。
     * 该方法首先会验证给定的位置字符串，以确保它不包含任何可能引起安全问题的字符，例如CRLF。
     * 验证通过后，会调用超类的sendRedirect方法来执行实际的重定向操作。
     *
     * @param location 重定向的目标位置。该位置应该是一个有效的URL或URI。
     * @throws IOException 如果在执行重定向过程中遇到IO异常。
     */
    @Override
    public void sendRedirect(String location) throws IOException {
        // TODO: implement pluggable validation, instead of simple blacklisting.
        // SEC-1790. Prevent redirects containing CRLF
        // 验证location参数以防止包含CRLF等潜在危险字符
        validateCrlf(LOCATION_HEADER, location);
        super.sendRedirect(location);
    }

    /**
     * 设置HTTP头信息。
     * 该方法会首先校验name和value参数，确保它们不包含任何可能引入CRLF注入攻击的字符。
     * 校验通过后，会调用父类的setHeader方法来实际设置头信息。
     *
     * @param name 头信息的名称，不能为空。
     * @param value 头信息的值，不能为空。
     * @throws IllegalArgumentException 如果name或value为空，或者包含非法的CRLF字符，则抛出此异常。
     */
    @Override
    public void setHeader(String name, String value) {
        // 校验name和value，防止CRLF注入攻击
        validateCrlf(name, value);
        // 调用父类方法设置头信息
        super.setHeader(name, value);
    }

    /**
     * 添加一个HTTP头到消息中。
     * 在添加头之前，会对头的名称和值进行CRLF（换行符）验证，以防止CRLF注入攻击。
     *
     * @param name 头的名称，不能为空。
     * @param value 头的值，可以为空。
     */
    @Override
    public void addHeader(String name, String value) {
        // 验证name和value以防止CRLF注入
        validateCrlf(name, value);
        // 调用父类方法添加头
        super.addHeader(name, value);
    }

    /**
     * 添加一个Cookie到响应中。此方法会首先验证Cookie的各属性中是否包含了换行符（CRLF），
     * 如果包含则可能会引发异常，因为换行符在Cookie的属性值中可能会被利用进行注入攻击。
     * 验证通过后，会将Cookie添加到响应中。
     *
     * @param cookie 需要添加的Cookie对象。不能为null。
     */
    @Override
    public void addCookie(Cookie cookie) {
        if (cookie != null) {
            // 对Cookie的名称、值、路径、域进行CRLF字符的验证
            validateCrlf(SET_COOKIE_HEADER, cookie.getName());
            validateCrlf(SET_COOKIE_HEADER, cookie.getValue());
            validateCrlf(SET_COOKIE_HEADER, cookie.getPath());
            validateCrlf(SET_COOKIE_HEADER, cookie.getDomain());
        }
        // 调用父类方法，将Cookie添加到响应中
        super.addCookie(cookie);
    }


    /**
     * 验证给定的头名称和值是否包含无效字符（回车/换行符）。
     * 如果任一参数中包含回车或换行符，则抛出 IllegalArgumentException 异常。
     *
     * @param name 头名称，将被验证是否包含无效字符。
     * @param value 头值，将被验证是否包含无效字符。
     * @throws IllegalArgumentException 如果任一参数中包含回车或换行符。
     */
    void validateCrlf(String name, String value) {
        // 如果头名称或值中包含回车/换行符，则抛出异常
        if (hasCrlf(name) || hasCrlf(value)) {
            throw new IllegalArgumentException(
                    "Invalid characters (CR/LF) in header " + name);
        }
    }


    /**
     * 检查给定的字符串中是否包含换行符。
     *
     * @param value 要检查的字符串。可能为null。
     * @return 如果字符串不为null且包含至少一个换行符（CR或LF），则返回true；否则返回false。
     */
    private boolean hasCrlf(String value) {
        // 检查字符串是否非空，并使用预定义的正则表达式匹配器查找换行符
        return value != null && CR_OR_LF.matcher(value).find();
    }
}
