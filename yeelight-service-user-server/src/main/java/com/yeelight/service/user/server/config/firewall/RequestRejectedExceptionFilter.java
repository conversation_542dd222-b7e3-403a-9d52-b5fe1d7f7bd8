package com.yeelight.service.user.server.config.firewall;

import java.io.IOException;
import java.util.Objects;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.security.web.firewall.RequestRejectedException;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: yeelight-service-user
 * @description: 请求拒绝异常过滤器
 * @author: yeelight
 * @create: 2022-10-11 10:15
 **/
@Component
@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
public class RequestRejectedExceptionFilter extends GenericFilterBean {

    /**
     * 在当前过滤器链中拦截请求，并根据特定条件对请求进行过滤处理。
     * 如果请求满足特定条件（例如，被标准化），则允许通过；
     * 否则，捕获并处理{@link RequestRejectedException}异常，向客户端返回404错误。
     *
     * @param servletRequest 代表客户端发起的请求的ServletResponse对象。
     * @param servletResponse 代表服务器向客户端返回的响应的ServletResponse对象。
     * @param filterChain 过滤器链，用于将请求传递给下一个过滤器或最终的Servlet。
     * @throws IOException 如果在处理请求或响应时发生IO错误。
     * @throws ServletException 如果在处理请求时发生Servlet相关错误。
     */
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        try {
            // 尝试从请求中获取是否被拒绝的标记
            RequestRejectedException requestRejectedException=(RequestRejectedException) servletRequest.getAttribute("isNormalized");

            // 如果请求被标记为拒绝，则抛出异常
            if(Objects.nonNull(requestRejectedException)) {
                throw requestRejectedException;
            }else {
                // 如果请求未被拒绝，继续在过滤器链中传递请求
                filterChain.doFilter(servletRequest, servletResponse);
            }
        } catch (RequestRejectedException requestRejectedException) {
            // 捕获并处理请求被拒绝的异常
            HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
            HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;

            // 记录请求被拒绝的日志信息
            log
                    .warn(
                            "request_rejected: remote={}, user_agent={}, request_url={}",
                            httpServletRequest.getRemoteHost(),
                            httpServletRequest.getHeader(HttpHeaders.USER_AGENT),
                            httpServletRequest.getRequestURL(),
                            requestRejectedException
                    );

            // 向客户端返回404错误
            httpServletResponse.sendError(HttpServletResponse.SC_NOT_FOUND, "request url error, please check");
        }
    }

}
