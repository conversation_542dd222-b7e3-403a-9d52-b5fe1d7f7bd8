package com.yeelight.service.user.server.config.limiter;

import java.lang.annotation.*;

/**
 * 限量注解
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Repeatable(CountLimits.class)
public @interface CountLimit {
    String key() default "";
    String prefix() default "countLimit:"; //key前缀
    int limit() default 1;  // expire时间段内限制访问次数
    int period() default 1; // 表示时间段/秒
    boolean useWhiteList() default true; // 是否启用白名单
    String extendWhiteList() default ""; // 自定义白名单，以英文逗号分隔
    boolean useBlackList() default true; // 是否启用黑名单
    String extendBlackList() default ""; // 自定义黑名单，以英文逗号分隔

    int blockCount() default 15; // 限制规则触发多少次进入黑名单

    int blockExpire() default 30; // 黑名单过期时间/天
    LimitType limitType() default LimitType.METHOD;
}
