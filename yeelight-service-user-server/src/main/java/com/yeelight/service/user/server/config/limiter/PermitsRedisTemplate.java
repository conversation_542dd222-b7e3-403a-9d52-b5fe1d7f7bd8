package com.yeelight.service.user.server.config.limiter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.io.IOException;

/**
 * @program: yeelight-service-station
 * @description: 操作令牌数据的RedisTemplate
 * @author: lixiaodong
 * @create: 2023-02-07 09:27
 **/
@Slf4j
public class PermitsRedisTemplate extends RedisTemplate<String, RedisPermits> {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public PermitsRedisTemplate() {
        super();
        this.setKeySerializer(new StringRedisSerializer());
        this.setValueSerializer(new CompatibleRedisPermitsSerializer());
    }

    /**
     * 兼容性RedisPermits序列化器
     * 支持向前兼容的序列化/反序列化
     */
    private static class CompatibleRedisPermitsSerializer implements RedisSerializer<RedisPermits> {

        /**
         * 序列化RedisPermits对象为字节数组。
         *
         * @param redisPermits 需要被序列化的RedisPermits对象。
         * @return 返回序列化后的字节数组。如果序列化过程中发生异常，则返回null。
         * @throws SerializationException 如果序列化过程中发生错误，则抛出此异常。
         */
        @Override
        public byte[] serialize(RedisPermits redisPermits) throws SerializationException {
            try {
                // 尝试使用ObjectMapper将redisPermits对象序列化为字节数组
                return OBJECT_MAPPER.writeValueAsBytes(redisPermits);
            } catch (JsonProcessingException e) {
                // 记录序列化失败的错误日志
                log.error("fail to serialize redisPermits. ", e);
                return null;
            }
        }

        /**
         * 将字节数据反序列化为RedisPermits对象。
         * 支持向前兼容性处理
         *
         * @param bytes 待反序列化的字节数据。
         * @return 反序列化后的RedisPermits对象。如果反序列化失败或输入为null，则返回null。
         * @throws SerializationException 如果反序列化过程中发生错误。
         */
        @Override
        public RedisPermits deserialize(byte[] bytes) throws SerializationException {
            // 检查输入字节数据是否为null
            if (bytes == null || bytes.length == 0) {
                return null;
            }

            // 策略1：尝试Jackson JSON反序列化（当前格式）
            try {
                return OBJECT_MAPPER.readValue(bytes, RedisPermits.class);
            } catch (IOException jacksonException) {
                log.debug("Jackson反序列化RedisPermits失败: {}", jacksonException.getMessage());
            }

            // 策略2：尝试FastJSON反序列化（历史格式兼容）
            try {
                String jsonStr = new String(bytes, java.nio.charset.StandardCharsets.UTF_8);
                return com.alibaba.fastjson.JSON.parseObject(jsonStr, RedisPermits.class);
            } catch (Exception fastJsonException) {
                log.debug("FastJSON反序列化RedisPermits失败: {}", fastJsonException.getMessage());
            }

            // 策略3：尝试JDK反序列化（最老格式兜底）
            try {
                java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(bytes);
                java.io.ObjectInputStream ois = new java.io.ObjectInputStream(bis);
                Object result = ois.readObject();
                ois.close();
                bis.close();

                if (result instanceof RedisPermits) {
                    log.warn("使用JDK兼容性反序列化RedisPermits成功");
                    return (RedisPermits) result;
                }
            } catch (Exception jdkException) {
                log.debug("JDK反序列化RedisPermits失败: {}", jdkException.getMessage());
            }

            // 所有策略都失败，记录错误但返回null避免影响系统
            log.error("所有反序列化策略都失败，RedisPermits数据长度: {} bytes，返回null", bytes.length);
            return null;
        }
    }
}
