package com.yeelight.service.user.server.config.limiter;

import java.lang.annotation.*;

/**
 * 限速注解
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Repeatable(RateLimits.class)
public @interface RateLimit {
    String key() default "";
    String prefix() default "rateLimit:"; //key前缀
    int expire() default 60; // 表示令牌桶模型RedisPermits redis key的过期时间/秒
    double rate() default 1.0; // permitsPerSecond值, 每秒放入的令牌数
    double burst() default 1.0; // maxBurstSeconds值, 最大存储maxBurstSeconds秒生成的令牌
    int timeout() default 0; // 超时时间/秒

    boolean useWhiteList() default true; // 是否启用白名单
    String extendWhiteList() default ""; // 自定义白名单，以英文逗号分隔
    boolean useBlackList() default true; // 是否启用黑名单
    String extendBlackList() default ""; // 自定义黑名单，以英文逗号分隔

    int blockCount() default 15; // 限制规则触发多少次进入黑名单
    int blockExpire() default 30; // 黑名单过期时间/天
    LimitType limitType() default LimitType.METHOD;
}
