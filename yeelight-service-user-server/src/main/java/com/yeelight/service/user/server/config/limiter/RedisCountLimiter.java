package com.yeelight.service.user.server.config.limiter;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;

import java.util.Collections;

/**
 * @program: yeelight-service-station
 * @description: 限量控制
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023-02-07 09:30
 **/
public class RedisCountLimiter {

    private final StringRedisTemplate stringRedisTemplate;

    private static final String LUA_SCRIPT = "local c \nc = redis.call('get',KEYS[1]) \nif c and redis.call('incr',KEYS[1]) > tonumber(ARGV[1]) then return 0 end"
            + " \nif c then return 1 else \nredis.call('set', KEYS[1], 1) \nredis.call('expire', KEYS[1], tonumber(ARGV[2])) \nreturn 1 end";

    private static final int SUCCESS_RESULT = 1;
    private static final int FAIL_RESULT = 0;

    public RedisCountLimiter(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    /**
     * 尝试获取访问权限。该方法使用Redis的Lua脚本实现基于键的访问限制，确保在指定的时间段内，某个键的访问次数不超过预设的限制。
     *
     * @param key redis key，用于标识访问限制的唯一标识。
     * @param limit 限制次数，即在expire时间内允许访问的最大次数。
     * @param expire 时间段/秒，限制访问的时间段，超过这个时间段后，访问限制将失效。
     * @return 获取成功返回true，表示在限制次数和时间内可以访问；否则返回false，表示访问被限制。
     * @throws IllegalArgumentException 如果传入的参数非法，比如limit或expire小于等于0，则抛出此异常。
     */
    public boolean tryAcquire(String key, int limit, int expire) throws IllegalArgumentException {
        // 创建Redis脚本并设置Lua脚本内容
        RedisScript<Number> redisScript = new DefaultRedisScript<>(LUA_SCRIPT, Number.class);
        // 执行Redis脚本，参数包括key、limit和expire，返回值表示操作结果
        Number result = stringRedisTemplate.execute(redisScript, Collections.singletonList(key), String.valueOf(limit), String.valueOf(expire));
        // 判断执行结果，如果成功则返回true，否则返回false
        return result != null && result.intValue() == SUCCESS_RESULT;
    }

}
