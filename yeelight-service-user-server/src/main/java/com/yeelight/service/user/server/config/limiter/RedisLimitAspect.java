package com.yeelight.service.user.server.config.limiter;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.IPUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.constant.UserConstant;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @program: yeelight-service-station
 * @description: Redis限流切面
 * @author: lixiaodong
 * @create: 2023-02-07 09:31
 **/
@Aspect
@Slf4j
public class RedisLimitAspect {
    private static final String RATE_LIMIT_MESSAGE = "Requests are too frequent";

    private static final String COUNT_LIMIT_MESSAGE = "Requests over limit";

    private static final Pattern INNER_IP_REG = Pattern.compile("^(10|172\\.(1[6-9]|2\\d|3[01])|(192\\.168)|(127\\.0\\.0\\.1))\\.\\d{1,3}\\.\\d{1,3}$");

    private static final String CONTENT_VAR_PATTERN = "#\\{\\D*\\}";

    private static final String REF_DELIMITER = ".";
    @Resource
    private TokenService tokenService;

    private static final List<String> WHITE_LIST = Arrays.asList("127.0.0.1", "17554229713");

    private final RedisManager redisManager;

    private final RedisRateLimiterComponentManager redisRateLimiterComponentManager;

    public RedisLimitAspect(RedisManager redisManager, RedisRateLimiterComponentManager redisRateLimiterComponentManager) {
        this.redisManager = redisManager;
        this.redisRateLimiterComponentManager = redisRateLimiterComponentManager;
    }

    /**
     * 在方法执行前对请求进行频率限制检查。
     * @param point 切面连接点，表示被拦截的方法。
     * @param countLimits 注解上的频率限制配置。
     * @throws Throwable 如果请求超出频率限制，则抛出异常。
     */
    @Before(value = "@annotation(countLimits)")
    public void countLimit(JoinPoint point, CountLimits countLimits) throws Throwable {
        // 获取频率限制配置
        CountLimit[] limits = countLimits.value();
        // 获取Redis计数限流器和Redis模板
        RedisCountLimiter redisCountLimiter = redisRateLimiterComponentManager.redisCountLimiter();
        RedisTemplate<String, Object> redisTemplate = redisManager.redisTemplate();
        // 遍历所有的频率限制配置并检查是否超出限制
        Arrays.stream(limits).forEach(countLimit -> {
            handleCountLimit(point, countLimit, redisTemplate, redisCountLimiter);
        });
    }

    private void handleCountLimit(JoinPoint point, CountLimit countLimit, RedisTemplate<String, Object> redisTemplate, RedisCountLimiter redisCountLimiter) {
        // 黑名单键名
        String blackKey = countLimit.prefix() + countLimit.limitType() + ":blackList";
        // 计算请求键名
        String key = getKey(point, countLimit.limitType(), countLimit.key());

        // 获取白名单键名，针对IP或自定义且包含IP的限制类型
        String whiteKey = key;
        if (Arrays.asList(LimitType.IP, LimitType.CUSTOM_AND_IP).contains(countLimit.limitType())) {
            whiteKey = getIp();
        }

        // 内网请求放行
        if (isInnerIp(whiteKey)) {
            return;
        }

        // 白名单规则检查
        if (countLimit.useWhiteList()) {
            List<String> currentWhitelist = new ArrayList<>(Arrays.asList(countLimit.extendWhiteList().split(",")));
            currentWhitelist.addAll(WHITE_LIST);
            if (currentWhitelist.contains(whiteKey)) {
                return;
            }
        }

        // 黑名单规则检查
        if (countLimit.useBlackList()) {
            handleCountLimitBlackList(countLimit, key, redisTemplate, blackKey, redisCountLimiter);
        } else { // 无黑名单模式，直接尝试获取许可，失败则抛出异常
            if (!redisCountLimiter.tryAcquire(countLimit.prefix() + key, countLimit.limit(), countLimit.period())) {
                throw new BizException(ResultCodeEnum.超出频率限制.code, COUNT_LIMIT_MESSAGE);
            }
        }
    }

    private static void handleCountLimitBlackList(CountLimit countLimit, String key, RedisTemplate<String, Object> redisTemplate, String blackKey, RedisCountLimiter redisCountLimiter) {
        // 检查静态黑名单
        if (StringUtils.isNotBlank(countLimit.extendBlackList())) {
            List<String> currentBlacklist = new ArrayList<>(Arrays.asList(countLimit.extendBlackList().split(UserConstant.COMMA_DELIMITERS)));
            if (currentBlacklist.contains(key)) {
                throw new BizException(ResultCodeEnum.超出频率限制.code, COUNT_LIMIT_MESSAGE + ": Black list");
            }
        }

        // 检查动态黑名单
        RedisLimitBlackKey limitBlackKey = (RedisLimitBlackKey) redisTemplate.opsForHash().get(blackKey, key);
        long currentTime = (System.currentTimeMillis() / 1000L);
        if (Objects.isNull(limitBlackKey)) {
            limitBlackKey = new RedisLimitBlackKey();
            limitBlackKey.setKey(key);
            limitBlackKey.setCount(0);
            limitBlackKey.setExpireTime(currentTime);
            limitBlackKey.setBlock(false);
        }
        if (limitBlackKey.isBlock() && limitBlackKey.getExpireTime() > currentTime) {
            throw new BizException(ResultCodeEnum.超出频率限制.code, COUNT_LIMIT_MESSAGE + ": Blocked");
        }
        // 尝试获取许可，若失败则更新黑名单状态并抛出异常
        if (!redisCountLimiter.tryAcquire(countLimit.prefix() + key, countLimit.limit(), countLimit.period())) {
            if (limitBlackKey.getExpireTime() > currentTime) {
                limitBlackKey.setCount(limitBlackKey.getCount() + 1);
                if (limitBlackKey.getCount() >= countLimit.blockCount()) {
                    limitBlackKey.setBlock(true);
                }
            } else {
                limitBlackKey.setCount(1);
                limitBlackKey.setBlock(false);
            }
            limitBlackKey.setExpireTime(currentTime + (86400L * countLimit.blockExpire()));
            redisTemplate.opsForHash().put(blackKey, key, limitBlackKey);
            throw new BizException(ResultCodeEnum.超出频率限制.code, COUNT_LIMIT_MESSAGE + ": wait a moment please");
        }
    }


    /**
     * 在方法执行前应用速率限制的切面。
     * @param point 切面的连接点，表示被拦截的方法。
     * @param rateLimits 注解上的速率限制配置。
     */
    @Before(value = "@annotation(rateLimits)")
    public void rateLimit(JoinPoint point, RateLimits rateLimits) {
        RateLimit[] limits = rateLimits.value();
        RedisTemplate<String, Object> redisTemplate = redisManager.redisTemplate();
        RedisRateLimiterFactory redisRateLimiterFactory = redisRateLimiterComponentManager.redisRateLimiterFactory();

        Arrays.stream(limits).forEach(rateLimit -> handleRateLimit(point, rateLimit, redisTemplate, redisRateLimiterFactory));
    }

    private void handleRateLimit(JoinPoint point, RateLimit rateLimit, RedisTemplate<String, Object> redisTemplate, RedisRateLimiterFactory redisRateLimiterFactory) {
        // 黑名单键名
        String blackKey = rateLimit.prefix() + rateLimit.limitType() + ":blackList";
        // 生成基础限流键名
        String key = getKey(point, rateLimit.limitType(), rateLimit.key());

        // 白名单键名，IP或自定义IP限流时会用到真实的IP地址作为键名
        String whiteKey = key;
        if (Arrays.asList(LimitType.IP, LimitType.CUSTOM_AND_IP).contains(rateLimit.limitType())) {
            whiteKey = getIp();
        }

        // 内网IP放行逻辑
        if (isInnerIp(whiteKey)) {
            return;
        }

        // 处理白名单规则
        if (rateLimit.useWhiteList()) {
            List<String> currentWhitelist = new ArrayList<>(Arrays.asList(rateLimit.extendWhiteList().split(",")));
            currentWhitelist.addAll(WHITE_LIST);
            if (currentWhitelist.contains(whiteKey)) {
                return;
            }
        }

        // 处理黑名单规则
        if (rateLimit.useBlackList()) {
            handleRateLimitBlackList(rateLimit, key, redisTemplate, blackKey, redisRateLimiterFactory);
        } else {
            // 无黑名单模式的限流处理
            RedisRateLimiter redisRateLimiter = redisRateLimiterFactory.build(rateLimit.prefix() + key, rateLimit.rate(), rateLimit.burst(), rateLimit.expire());
            if(!redisRateLimiter.tryAcquire(rateLimit.prefix() + key, rateLimit.timeout(), TimeUnit.SECONDS)){
                redisTemplate.opsForSet().add(blackKey, key);
                throw new BizException(ResultCodeEnum.超出频率限制.code, RATE_LIMIT_MESSAGE);
            }
        }
    }

    private static void handleRateLimitBlackList(RateLimit rateLimit, String key, RedisTemplate<String, Object> redisTemplate, String blackKey, RedisRateLimiterFactory redisRateLimiterFactory) {
        // 配置黑名单检查
        if (StringUtils.isNotBlank(rateLimit.extendBlackList())) {
            List<String> currentBlacklist = new ArrayList<>(Arrays.asList(rateLimit.extendBlackList().split(",")));
            if (currentBlacklist.contains(key)) {
                throw new BizException(ResultCodeEnum.超出频率限制.code, RATE_LIMIT_MESSAGE + ": Black list");
            }
        }

        // 动态黑名单检查与更新
        RedisLimitBlackKey limitBlackKey = (RedisLimitBlackKey) redisTemplate.opsForHash().get(blackKey, key);
        long currentTime = (System.currentTimeMillis() / 1000L);
        if (Objects.isNull(limitBlackKey)) {
            limitBlackKey = new RedisLimitBlackKey();
            limitBlackKey.setKey(key);
            limitBlackKey.setCount(0);
            limitBlackKey.setExpireTime(currentTime);
            limitBlackKey.setBlock(false);
        }
        if (limitBlackKey.isBlock() && limitBlackKey.getExpireTime() > currentTime) {
            throw new BizException(ResultCodeEnum.超出频率限制.code, RATE_LIMIT_MESSAGE + ": Blocked");
        }

        RedisRateLimiter redisRateLimiter = redisRateLimiterFactory.build(rateLimit.prefix() + key, rateLimit.rate(), rateLimit.burst(), rateLimit.expire());
        // 尝试获取令牌，失败则进行限流处理
        if(!redisRateLimiter.tryAcquire(rateLimit.prefix() + key, rateLimit.timeout(), TimeUnit.SECONDS)){
            if (limitBlackKey.getExpireTime() > currentTime) {
                limitBlackKey.setCount(limitBlackKey.getCount() + 1);
                if (limitBlackKey.getCount() >= rateLimit.blockCount()) {
                    limitBlackKey.setBlock(true);
                }
            } else {
                limitBlackKey.setCount(1);
                limitBlackKey.setBlock(false);
            }
            limitBlackKey.setExpireTime(currentTime + (86400L * rateLimit.blockExpire()));
            redisTemplate.opsForHash().put(blackKey, key, limitBlackKey);
            throw new BizException(ResultCodeEnum.超出频率限制.code, RATE_LIMIT_MESSAGE + ": wait a moment please");
        }
    }

    /**
     * 根据不同的限制类型生成对应的限流键。
     *
     * @param point 切面编程的连接点，用于获取目标方法的信息。
     * @param limitType 限制类型，决定生成限流键的方式。
     * @param originKey 原始键，当限制类型为CUSTOM或CUSTOM_AND_IP时使用。
     * @return 返回根据不同限制类型生成的限流键。
     * @throws BizException 如果生成的限流键为空，则抛出业务异常。
     */
    private String getKey(JoinPoint point, LimitType limitType, String originKey) {
        String key;
        switch (limitType) {
            case IP:
                // 通过IP地址生成限流键
                key = getIp();
                break;
            case USER:
                // 通过用户信息生成限流键
                String token = TokenUtils.extractToken(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest());
                String userName = tokenService.getUserNameByToken(token);
                if (StringUtils.isNotBlank(userName)) {
                    key = userName;
                } else {
                    key = Constants.UNKNOWN;
                }
                break;
            case METHOD:
                // 通过方法信息生成限流键
                key = point.getTarget().getClass().getName() + ":" + ((MethodSignature) point.getSignature()).getMethod().getName();
                break;
            case CUSTOM:
                // 通过自定义方式生成限流键
                key = String.valueOf(resolve(point, originKey));
                break;
            case CUSTOM_AND_IP:
                // 通过自定义方式和IP地址生成限流键
                key = String.valueOf(resolve(point, originKey)) + "-" + getIp();
                break;
            default:
                key = "";
        }
        if (StringUtils.isBlank(key)) {
            // 如果生成的限流键为空，则抛出异常
            throw new BizException(ResultCodeEnum.超出频率限制.code, "limit key is empty");
        }
        return key;
    }


    /**
     * 获取当前请求的IP地址。
     * 该方法首先从RequestContextHolder中获取当前请求的属性，然后利用IPUtils工具类获取真实的客户端IP地址。
     *
     * @return 返回客户端的IP地址，如果无法获取则返回null。
     */
    private String getIp() {
        // 获取当前的ServletRequestAttributes
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 利用IPUtils获取真实的客户端IP地址
        return IPUtils.getRealIp(requestAttributes.getRequest());
    }


    /**
     * 检查提供的IP地址是否为内部IP地址。
     * 该方法使用预定义的正则表达式INNER_IP_REG来匹配IP地址。
     *
     * @param ip 待检查的IP地址，为字符串格式。
     * @return 返回匹配结果，如果匹配成功，即认为是内部IP地址，返回true；否则返回false。
     */
    public static boolean isInnerIp(String ip) {
        // 使用预定义的正则表达式对IP地址进行匹配
        Matcher match = INNER_IP_REG.matcher(ip);
        return match.find();
    }

    /**
     * 解析给定的占位符并返回相应的值。
     * @param joinPoint 切面连接点，提供当前执行的上下文信息。
     * @param placeholder 需要解析的占位符字符串。
     * @return 解析后的值。如果无法解析或占位符为空，则返回null。
     */
    public Object resolve(JoinPoint joinPoint, String placeholder) {

        // 检查占位符是否为空
        if (StringUtils.isBlank(placeholder)) {
            return null;
        }

        Object value = null;
        // 判断是否为变量形式的占位符，例如#{variable}
        if (placeholder.matches(CONTENT_VAR_PATTERN)) {
            String param = placeholder.replaceAll("#\\{", "").replaceAll("\\}", "");
            // 判断是否包含多层引用
            if (param.contains(REF_DELIMITER)) {
                try {
                    // 对多层引用的占位符进行解析
                    value = complexResolve(joinPoint, param);
                } catch (Exception e) {
                    log.warn("fail to resolve value for {}", param, e);
                }
            } else {
                // 对单层引用的占位符进行解析
                value = simpleResolve(joinPoint, param);
            }
        } else {
            // 如果不匹配变量形式的占位符，则直接返回占位符字符串
            value = placeholder;
        }
        return value;
    }

    /**
     * 解析多层引用参数的值。
     * 此方法通过给定的连接点（方法调用）和占位符，来获取方法参数中特定属性的值。
     * 占位符采用点分隔的形式，例如"param1.subParam1"，其中"param1"是参数名，"subParam1"是该参数中要获取的属性名。
     *
     * @param joinPoint 表示方法调用的连接点，用于获取方法签名和参数信息。
     * @param placeholder 一个占位符字符串，表示需要解析的属性路径。
     * @return 返回解析后的值。如果无法找到对应的值，则返回null。
     * @throws Exception 如果在解析过程中发生错误，则抛出异常。
     */
    private Object complexResolve(JoinPoint joinPoint, String placeholder) throws Exception {
        // 将连接点转换为方法签名，以便获取参数名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取方法参数名
        String[] names = methodSignature.getParameterNames();
        // 获取方法实际参数值
        Object[] args = joinPoint.getArgs();
        // 根据点分隔符分割占位符，以获取属性名链
        String[] params = placeholder.split("\\.");

        // 遍历参数列表，寻找与占位符第一部分匹配的参数
        for (int i = 0; i < names.length; i++) {
            if (params[0].equals(names[i])) {
                // 匹配到后，通过反射获取指定属性的值
                Object obj = args[i];
                Method getMethod = obj.getClass().getDeclaredMethod(getMethodName(params[1]), null);
                Object value = getMethod.invoke(args[i]);
                // 递归调用，以处理属性名链，直到获取到最后的值
                return getValue(value, 1, params);
            }
        }

        // 如果未找到匹配的参数，返回null
        return null;

    }


    /**
     * 从方法参数中根据占位符名称获取对应的参数值。
     *
     * @param joinPoint 切面编程中的连接点，表示被拦截的方法。
     * @param placeholder 占位符名称，用于查找对应参数。
     * @return 如果找到与占位符匹配的参数，则返回该参数的值；否则返回null。
     */
    private Object simpleResolve(JoinPoint joinPoint, String placeholder) {
        // 将连接点转换为方法签名
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // 获取方法参数名称
        String[] names = methodSignature.getParameterNames();
        // 获取方法实际传入的参数值
        Object[] args = joinPoint.getArgs();

        // 遍历参数名称数组，查找与占位符匹配的参数
        for (int i = 0; i < names.length; i++) {
            if (placeholder.equals(names[i])) {
                // 找到匹配参数，返回其值
                return args[i];
            }
        }
        // 未找到匹配参数，返回null
        return null;
    }


    /**
     * 通过反射机制递归获取对象的属性值。
     * @param obj 要获取属性值的对象。
     * @param index 当前处理的参数索引。
     * @param params 参数数组，包含要访问的属性名序列。
     * @return 返回最终获取到的属性值。
     * @throws Exception 反射调用过程中可能出现的异常。
     */
    private Object getValue(Object obj, int index, String[] params) throws Exception {
        // 如果对象非空且索引小于参数数组长度减1，则继续递归获取下一个属性值
        if (obj != null && index < params.length - 1) {
            // 获取当前参数所指示的属性的方法
            Method method = obj.getClass().getDeclaredMethod(getMethodName(params[index + 1]), (Class<?>) null);
            // 调用方法并更新对象为新的结果
            obj = method.invoke(obj);
            // 递归调用，继续获取下一个属性值
            getValue(obj, index + 1, params);
        }
        // 返回最终获取到的属性值
        return obj;
    }

    /**
     * 生成指定属性名称的 getter 方法名。
     *
     * @param name 属性的名称，不包含类型信息。
     * @return 返回生成的 getter 方法名，首字母大写。
     */
    private String getMethodName(String name) {
        // 通过 StringUtils.capitalize 方法将 name 的首字母大写，然后与 "get" 连接，生成 getter 方法名。
        return "get" + StringUtils.capitalize(name);
    }

}
