package com.yeelight.service.user.server.config.limiter;

import lombok.Data;

import java.io.Serializable;


/**
 * @program: yeelight-service-station
 * @description: 黑名单
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023-02-07 09:54
 **/
@Data
public class RedisLimitBlackKey implements Serializable {
    /**
     * 黑名单key
     */
    private String key;

    /**
     * 过期时间
     */
    private long expireTime;

    /**
     * 被记录次数
     */
    private int count;

    /**
     * 是否被禁止
     */
    private boolean isBlock;
}
