package com.yeelight.service.user.server.config.limiter;

import cn.hutool.core.lang.ObjectId;
import com.yeelight.service.user.server.config.limiter.lock.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * @program: yeelight-service-station
 * @description: 限速控制
 * @author: lixiaodong
 * @create: 2023-02-07 09:56
 **/
@Slf4j
public class RedisRateLimiter {

    private static final String LOCK_KEY_SUFFIX = ":lock";

    private final DistributedLock lock;
    private final PermitsRedisTemplate permitsRedisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private double permitsPerSecond;
    private double maxBurstSeconds;
    private long expire;

    private static final ThreadLocal<String> REQ_ID_THREAD_LOCAL = new ThreadLocal<>();


    public RedisRateLimiter(PermitsRedisTemplate permitsRedisTemplate, StringRedisTemplate stringRedisTemplate, DistributedLock distributedLock) {
        this.permitsRedisTemplate = permitsRedisTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        this.lock = distributedLock;
    }

    /**
     * 构建并返回一个RedisRateLimiter实例，允许配置限流器的速率、最大爆发时间和过期时间。
     *
     * @param permitsPerSecond 每秒允许的请求数量。
     * @param maxBurstSeconds 允许的最大爆发时间（秒）。在短时间内可以超过平均速率。
     * @param expire 键的过期时间（毫秒）。过期后，限流器会自动重置。
     * @return 配置后的RedisRateLimiter实例。
     */
    public RedisRateLimiter build(double permitsPerSecond,
                            double maxBurstSeconds, long expire) {
        // 设置限流器的速率、最大爆发时间和过期时间
        this.permitsPerSecond = permitsPerSecond;
        this.maxBurstSeconds = maxBurstSeconds;
        this.expire = expire;
        // 返回配置后的实例
        return this;
    }


    /**
     * 获取一个令牌，阻塞一直到获取令牌，返回阻塞等待时间
     *
     * @return time 阻塞等待时间/毫秒
     */
    public long acquire(String key) throws IllegalArgumentException {
        return acquire(key, 1);
    }

    /**
     * 获取指定数量的令牌，如果令牌数不够，则一直阻塞，返回阻塞等待的时间
     *
     * @param permits 需要获取的令牌数
     * @return time 等待的时间/毫秒
     * @throws IllegalArgumentException tokens值不能为负数或零
     */
    public long acquire(String key, int permits) throws IllegalArgumentException {
        // 检查请求的令牌数是否合法
        long millisToWait = reserve(key, permits);
        log.info("acquire {} permits for key[{}], waiting for {}ms", permits, key, millisToWait);
        try {
            // 等待指定时间
            Thread.sleep(millisToWait);
        } catch (InterruptedException e) {
            log.error("Interrupted when trying to acquire {} permits for key[{}]", permits, key, e);
        }
        return millisToWait;
    }

    /**
     * 在指定时间内获取一个令牌，如果获取不到则一直阻塞，直到超时
     *
     * @param timeout 最大等待时间（超时时间），为0则不等待立即返回
     * @param unit    时间单元
     * @return 获取到令牌则true，否则false
     * @throws IllegalArgumentException tokens为负数或零，抛出异常
     */
    public boolean tryAcquire(String key, long timeout, TimeUnit unit) throws IllegalArgumentException {
        return tryAcquire(key, 1, timeout, unit);
    }

    /**
     * 在指定时间内获取指定数量的令牌，如果在指定时间内获取不到指定数量的令牌，则直接返回false，
     * 否则阻塞直到能获取到指定数量的令牌
     *
     * @param permits 需要获取的令牌数
     * @param timeout 最大等待时间（超时时间）
     * @param unit    时间单元
     * @return 如果在指定时间内能获取到指定令牌数，则true,否则false
     * @throws IllegalArgumentException tokens为负数或零，抛出异常
     */
    public boolean tryAcquire(String key, int permits, long timeout, TimeUnit unit) throws IllegalArgumentException {
        // 检查请求的令牌数是否合法
        long timeoutMillis = Math.max(unit.toMillis(timeout), 0);
        checkPermits(permits);

        // 尝试获取锁
        long millisToWait;
        boolean locked = false;
        try {
            // 尝试获取锁
            locked = lock.lock(key + LOCK_KEY_SUFFIX, getRequestId(), 60, 2, TimeUnit.SECONDS);
            // 如果获取到锁
            if (locked) {
                long nowMillis = getNowMillis();
                // 获取令牌桶
                RedisPermits permit = getPermits(key, nowMillis);
                // 如果无法获取令牌，则直接返回false
                if (!permit.canAcquire(nowMillis, permits, timeoutMillis)) {
                    return false;
                } else {
                    // 获取令牌成功，计算等待时间
                    millisToWait = permit.reserveAndGetWaitLength(nowMillis, permits);
                    permitsRedisTemplate.opsForValue().set(key, permit, expire, TimeUnit.SECONDS);
                }
            } else {
                //超时获取不到锁，也返回false
                return false;
            }
        } finally {
            // 释放锁
            if (locked) {
                lock.unLock(key + LOCK_KEY_SUFFIX, getRequestId());
            }
        }
        // 如果需要等待时间大于0，则等待指定时间
        if (millisToWait > 0) {
            try {
                Thread.sleep(millisToWait);
            } catch (InterruptedException ignored) {

            }
        }
        return true;
    }

    /**
     * 保留指定的令牌数待用
     * <p>
     * 该方法首先会校验所需的令牌数是否合法（非负），然后尝试获取指定键的锁，确保在操作期间的原子性。
     * 一旦获得锁，将从令牌桶中保留指定数量的令牌，并更新令牌桶的状态。
     * 如果令牌数量不足以保留指定的令牌数，会计算出需要等待的时间，并在操作完成后释放锁。
     *
     * @param key 令牌桶的键
     * @param permits 需保留的令牌数，必须为正数
     * @return time 令牌可用的等待时间，单位为毫秒
     * @throws IllegalArgumentException 如果permits为负数或零，抛出此异常
     */
    private long reserve(String key, int permits) throws IllegalArgumentException {
        // 校验所需令牌数是否合法
        checkPermits(permits);
        try {
            // 尝试获取锁，确保操作的原子性
            lock.lock(key + LOCK_KEY_SUFFIX, getRequestId(), 60, 2, TimeUnit.SECONDS);
            long nowMillis = getNowMillis();
            // 从Redis获取当前的令牌桶状态
            RedisPermits permit = getPermits(key, nowMillis);
            // 保留令牌并计算等待时间
            long waitMillis = permit.reserveAndGetWaitLength(nowMillis, permits);
            // 更新Redis中的令牌桶状态
            permitsRedisTemplate.opsForValue().set(key, permit, expire, TimeUnit.SECONDS);
            return waitMillis;
        } finally {
            // 无论如何都释放锁，确保锁的正确释放
            lock.unLock(key + LOCK_KEY_SUFFIX, getRequestId());
        }
    }


    /**
     * 获取令牌桶
     * 该方法用于从Redis中获取令牌桶，如果不存在，则创建一个新的令牌桶。
     *
     * @param key 令牌桶在Redis中的键名。
     * @param nowMillis 当前时间戳（毫秒）。
     * @return RedisPermits 令牌桶实例。
     */
    private RedisPermits getPermits(String key, long nowMillis) {
        // 从Redis获取令牌桶，如果不存在则返回null
        RedisPermits permit = permitsRedisTemplate.opsForValue().get(key);
        if (permit == null) {
            // 如果令牌桶不存在，则创建一个新的令牌桶实例
            permit = new RedisPermits(permitsPerSecond, maxBurstSeconds, nowMillis);
        }
        return permit;
    }


    /**
     * 获取当前Redis服务器的时间，以毫秒为单位。
     * 该方法通过执行Redis的TIME命令获取Redis服务器的时间，
     * 如果无法连接到Redis服务器，则返回当前Java虚拟机的系统时间。
     *
     * @return 当前Redis服务器时间或Java系统时间（毫秒）
     */
    private long getNowMillis() {
        // 构造用于获取Redis服务器时间的Lua脚本
        String luaScript = "return redis.call('time')";
        // 创建Redis脚本对象
        DefaultRedisScript<List> redisScript = new DefaultRedisScript<>(luaScript, List.class);
        // 执行Redis脚本并获取结果
        List<String> now = (List<String>) stringRedisTemplate.execute(redisScript, null);
        // 如果获取结果为空，则返回当前系统时间，否则解析并返回Redis时间
        return now == null ? System.currentTimeMillis() : Long.parseLong(now.get(0))*1000+Long.parseLong(now.get(1))/1000;
    }


    @Override
    public String toString() {
        return String.format(Locale.ROOT, "RateLimiter[rate=%3.1fqps]", permitsPerSecond);
    }

    /**
     * 检查提供的许可证数量是否为正数。
     * 该方法旨在确保传递给它的许可证数量是大于0的，如果不是，则抛出IllegalArgumentException。
     *
     * @param permits 需要检查的许可证数量，类型为长整型。
     * @throws IllegalArgumentException 如果提供的许可证数量不是正数，则抛出此异常。
     */
    private void checkPermits(long permits) throws IllegalArgumentException {
        // 如果提供的许可证数量小于等于0，则抛出异常
        if (permits <= 0) {
            throw new IllegalArgumentException(String.format("Requested permits (%s) must be positive: ", permits));
        }
    }


    /**
     * 将请求ID设置到线程本地存储中。
     * <p>此方法用于将一个唯一的请求ID与当前线程关联起来，使得在整个线程的生命周期内，
     * 可以通过{@link #REQ_ID_THREAD_LOCAL}方便地获取到这个请求ID。这对于日志记录、
     * 问题追踪等需要标识当前操作请求的场景非常有用。</p>
     *
     * @param requestId 要设置的请求ID。此ID应该是一个不为空的字符串。
     *                  它将被绑定到当前线程，用于后续操作。
     */
    public static void setRequestId(String requestId) {
        // 将请求ID绑定到当前线程
        REQ_ID_THREAD_LOCAL.set(requestId);
    }

    /**
     * 获取当前线程的请求ID。
     * <p>此方法首先尝试从线程本地存储（ThreadLocal）中获取请求ID。如果未找到（即请求ID未被设置过），
     * 则生成一个新的请求ID，并将其设置到线程本地存储中，随后返回这个新的请求ID。</p>
     *
     * @return 当前线程的请求ID。这是一个唯一标识，用于追踪和审计请求。
     */
    public static String getRequestId(){
        // 尝试从线程本地存储中获取请求ID
        String requestId = REQ_ID_THREAD_LOCAL.get();
        // 如果请求ID未被设置，生成一个新的请求ID并设置到线程本地存储
        if(requestId == null) {
            requestId = ObjectId.next();
            REQ_ID_THREAD_LOCAL.set(requestId);
        }
        // 返回请求ID
        return requestId;
    }

    /**
     * 移除请求ID
     * 该方法用于移除当前线程绑定的请求ID。在某些框架或应用中，为了追踪和日志记录，会给每个请求分配一个唯一的ID，并将其绑定到当前线程。
     * 调用此方法将移除这种绑定，通常在请求处理完成后或在不需要请求ID的场景下调用。
     */
    public static void removeRequestId() {
        // 移除当前线程绑定的请求ID
        REQ_ID_THREAD_LOCAL.remove();
    }
}
