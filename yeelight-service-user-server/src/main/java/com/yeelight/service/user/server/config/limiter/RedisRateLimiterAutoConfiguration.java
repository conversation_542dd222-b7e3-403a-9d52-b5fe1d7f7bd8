package com.yeelight.service.user.server.config.limiter;

import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.config.limiter.lock.DistributedLockAspect;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

/**
 * @program: yeelight-service-station
 * @description: 限速控制自动配置类
 * @author: lixiaodong
 * @create: 2023-02-07 10:02
 **/
@Configuration
@AutoConfigureAfter(RedisManager.class)
public class RedisRateLimiterAutoConfiguration {
    @Resource
    private RedisRateLimiterComponentManager redisRateLimiterComponentManager;


    /**
     * 创建并返回一个DistributedLockAspect实例，用于实现分布式锁的切面处理。
     * 该方法只有在类路径上存在Aspect类时才会被调用，这是通过@ConditionalOnClass注解来控制的。
     *
     * @return DistributedLockAspect 返回一个初始化好的DistributedLockAspect实例，
     *         利用redisRateLimiterComponentManager来管理分布式锁的生命周期。
     */
    @Bean
    @ConditionalOnClass(Aspect.class)
    public DistributedLockAspect syncLockHandle() {
        // 创建DistributedLockAspect实例并注入redisRateLimiterComponentManager
        return new DistributedLockAspect(redisRateLimiterComponentManager);
    }

    /**
     * 创建并返回一个RedisLimitAspect实例，该实例用于实现基于Redis的限流功能。
     * 该方法只有在类路径上存在Aspect类时才会被调用，这是通过@ConditionalOnClass注解来控制的。
     *
     * @param redisManager 用于操作Redis的管理器，提供对Redis资源的访问。
     * @return RedisLimitAspect 实例，封装了对Redis限流逻辑的切面。
     */
    @Bean
    @ConditionalOnClass(Aspect.class)
    public RedisLimitAspect redisLimitAspect(RedisManager redisManager) {
        // 通过RedisManager和redisRateLimiterComponentManager创建RedisLimitAspect实例
        return new RedisLimitAspect(redisManager, redisRateLimiterComponentManager);
    }


}
