/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.config.limiter
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-10-17 10:12:10:12
 */
package com.yeelight.service.user.server.config.limiter;

import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.config.limiter.lock.DistributedLock;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Desc: 限速控制组件管理器
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-10-17 10:12:10:12
 */
@Component
public class RedisRateLimiterComponentManager {
    private final RedisManager redisManager;
    private final Map<Integer, RedisCountLimiter> redisCountLimiterHashMap = new HashMap<>();

    private final Map<Integer, RedisRateLimiter> redisRateLimiterHashMap = new HashMap<>();

    private final Map<Integer, RedisRateLimiterFactory> redisRateLimiterFactoryHashMap = new HashMap<>();
    private final Map<Integer, DistributedLock> distributedLockHashMap = new HashMap<>();

    private final Map<String, PermitsRedisTemplate> permitsRedisTemplateHashMap = new HashMap<>();


    public RedisRateLimiterComponentManager(RedisManager redisManager) {
        this.redisManager = redisManager;
    }

    /**
     * 根据指定的数据库索引创建或获取一个PermitsRedisTemplate实例。
     * 如果已存在对应数据库索引的PermitsRedisTemplate实例，则直接返回该实例，
     * 否则创建新的实例并缓存起来供后续使用。
     *
     * @param database 指定的Redis数据库索引。
     * @return PermitsRedisTemplate 对应数据库索引的Redis模板实例。
     */
    public PermitsRedisTemplate permitsRedisTemplate(int database) {
        // 生成用于在HashMap中唯一标识PermitsRedisTemplate的key
        String key = String.format("%s-%s", "permits-redis-template", database);

        // 检查是否已存在该key对应的PermitsRedisTemplate实例
        if (permitsRedisTemplateHashMap.containsKey(key)) {
            return permitsRedisTemplateHashMap.get(key);
        }

        // 创建新的PermitsRedisTemplate实例并配置
        PermitsRedisTemplate template = new PermitsRedisTemplate();
        // 设置ConnectionFactory
        template.setConnectionFactory(redisManager.getConnectionFactory());
        template.afterPropertiesSet(); // 初始化模板

        // 将新创建的模板实例添加到HashMap中
        permitsRedisTemplateHashMap.put(key, template);

        return template;
    }


    /**
     * 创建或获取当前数据库对应的RedisCountLimiter实例。
     * 这个方法会检查当前数据库是否已经在redisCountLimiterHashMap中存在对应的RedisCountLimiter。
     * 如果存在，直接返回该实例；如果不存在，则创建一个新的RedisCountLimiter实例并加入到hashMap中。
     *
     * @return RedisCountLimiter 对应当前数据库的RedisCountLimiter实例。
     */
    public RedisCountLimiter redisCountLimiter() {
        // 获取当前数据库编号
        int currentDatabase = redisManager.getCurrentDatabase();

        // 检查hashMap中是否已存在当前数据库的RedisCountLimiter实例
        if (redisCountLimiterHashMap.containsKey(currentDatabase)) {
            return redisCountLimiterHashMap.get(currentDatabase);
        }

        // 为当前数据库创建一个新的RedisCountLimiter实例
        RedisCountLimiter redisCountLimiter = new RedisCountLimiter(redisManager.stringRedisTemplate());

        // 将新创建的实例加入到hashMap中
        redisCountLimiterHashMap.put(currentDatabase, redisCountLimiter);

        return redisCountLimiter;
    }


    /**
     * 根据指定的数据库索引创建或获取一个RedisRateLimiter实例。
     * 如果给定的数据库索引已经存在一个RedisRateLimiter实例，则直接返回该实例，
     * 否则创建一个新的实例并存储到HashMap中，然后返回。
     *
     * @param database 指定的Redis数据库索引。
     * @return RedisRateLimiter 对应数据库索引的Redis限流器实例。
     */
    public RedisRateLimiter redisRateLimiter(int database) {
        // 检查HashMap中是否已存在该数据库索引的RedisRateLimiter实例
        if (redisRateLimiterHashMap.containsKey(database)) {
            return redisRateLimiterHashMap.get(database);
        }
        // 创建一个新的RedisRateLimiter实例并初始化
        RedisRateLimiter redisRateLimiter = new RedisRateLimiter(permitsRedisTemplate(database), redisManager.stringRedisTemplate(), distributedLock());
        // 将新创建的RedisRateLimiter实例存储到HashMap中
        redisRateLimiterHashMap.put(database, redisRateLimiter);
        return redisRateLimiter;
    }


    /**
     * 创建或获取与当前数据库关联的RedisRateLimiterFactory实例。
     * 如果已存在与当前数据库关联的RedisRateLimiterFactory实例，则直接返回该实例；
     * 否则，创建一个新的RedisRateLimiterFactory实例并将其与当前数据库关联后返回。
     *
     * @return RedisRateLimiterFactory 与当前数据库关联的RedisRateLimiterFactory实例。
     */
    public RedisRateLimiterFactory redisRateLimiterFactory() {
        // 获取当前使用的数据库索引
        int currentDatabase = redisManager.getCurrentDatabase();

        // 检查是否已经为当前数据库创建了RedisRateLimiterFactory
        if (redisRateLimiterFactoryHashMap.containsKey(currentDatabase)) {
            // 如果已存在，直接返回该工厂实例
            return redisRateLimiterFactoryHashMap.get(currentDatabase);
        }

        // 为当前数据库创建一个新的RedisRateLimiterFactory实例
        RedisRateLimiterFactory redisRateLimiterFactory = new RedisRateLimiterFactory(redisRateLimiter(currentDatabase));

        // 将新创建的工厂实例与当前数据库索引关联
        redisRateLimiterFactoryHashMap.put(currentDatabase, redisRateLimiterFactory);

        // 返回新创建的工厂实例
        return redisRateLimiterFactory;
    }


    /**
     * 获取当前数据库对应的分布式锁。如果该数据库已存在对应的分布式锁，则直接返回该锁；
     * 如果不存在，则创建一个新的分布式锁，并将其与当前数据库关联后返回。
     *
     * @return DistributedLock 当前数据库对应的分布式锁实例。
     */
    public DistributedLock distributedLock() {
        // 获取当前操作的数据库索引
        int currentDatabase = redisManager.getCurrentDatabase();

        // 检查当前数据库是否已关联了分布式锁
        if (distributedLockHashMap.containsKey(currentDatabase)) {
            // 如果已关联，直接返回该分布式锁
            return distributedLockHashMap.get(currentDatabase);
        }

        // 为当前数据库创建一个新的分布式锁实例
        DistributedLock distributedLock = new DistributedLock(redisManager.stringRedisTemplate());

        // 将新创建的分布式锁与当前数据库关联
        distributedLockHashMap.put(currentDatabase, distributedLock);

        // 返回新创建的分布式锁
        return distributedLock;
    }

}
