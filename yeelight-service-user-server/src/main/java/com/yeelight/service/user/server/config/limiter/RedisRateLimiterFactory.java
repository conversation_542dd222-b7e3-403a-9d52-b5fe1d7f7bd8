package com.yeelight.service.user.server.config.limiter;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yeelight.service.user.server.config.limiter.lock.DistributedLock;

import java.util.concurrent.TimeUnit;

/**
 * @program: yeelight-service-station
 * @description:
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023-02-07 10:03
 **/
public class RedisRateLimiterFactory {
    private final RedisRateLimiter redisRateLimiter;
    private final Cache<String, RedisRateLimiter> cache = CacheBuilder.newBuilder()
            //初始大小
            .initialCapacity(100)
            // 缓存的最大容量
            .maximumSize(10000)
            // 缓存在最后一次访问多久之后失效
            .expireAfterAccess(30, TimeUnit.MINUTES)
            // 设置并发级别
            .concurrencyLevel(Runtime.getRuntime().availableProcessors())
            .build();

    public RedisRateLimiterFactory(RedisRateLimiter redisRateLimiter) {
        this.redisRateLimiter = redisRateLimiter;
    }

    /**
     * 创建并返回一个RedisRateLimiter实例，用于令牌桶限流。
     * 如果给定的key在缓存中不存在，则会创建一个新的RedisRateLimiter实例，
     * 并将其与该key关联，以供后续请求使用。
     *
     * @param key              Redis中用于标识令牌桶的键。
     * @param permitsPerSecond 每秒放入令牌桶中的令牌数量。
     * @param maxBurstSeconds  令牌桶允许存储的最大突发令牌数量（以秒为单位）。
     * @param expire           令牌桶在Redis中的过期时间（秒）。
     * @return 返回与给定key关联的RedisRateLimiter实例。
     */
    public RedisRateLimiter build(String key, double permitsPerSecond, double maxBurstSeconds, int expire) {
        // 如果缓存中不存在指定key的RedisRateLimiter实例，则进行加锁创建
        if (cache.getIfPresent(key) == null) {
            synchronized (this) {
                // 双重检查锁定，确保仅在必要时创建新的RedisRateLimiter实例
                if (cache.getIfPresent(key) == null) {
                    cache.put(key, redisRateLimiter.build(permitsPerSecond,
                            maxBurstSeconds, expire));
                }
            }
        }
        // 返回缓存中的RedisRateLimiter实例
        return cache.getIfPresent(key);
    }

}
