package com.yeelight.service.user.server.config.limiter.lock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * @program: yeelight-service-station
 * @description: 分布式锁
 * @author: l<PERSON><PERSON>od<PERSON>
 * @create: 2023-02-07 10:04
 **/
@Slf4j
public class DistributedLock {
    /**
     * RedisTemplate
     */
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 每次尝试获取锁的等待时长
     * 重试间隔/毫秒
     */
    private static final long WAIT_MILLIS_PER = 50L;


    private static final String RELEASE_LOCK_LUA_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
    private static final Long RELEASE_LOCK_SUCCESS_RESULT = 1L;

    public DistributedLock(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }


    /**
     * 尝试获取锁（立即返回）
     * @param key  锁的redis key
     * @param value 锁的value
     * @param expire 过期时间/秒
     * @return 是否获取成功
     */
    public boolean lock(String key, String value, long expire) {
        return Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, value, expire, TimeUnit.SECONDS));
    }

    /**
     * 尝试获取锁，并至多等待timeout时长。使用Redis的ValueOperations来实现锁的设置，如果锁未被设置（即获取锁成功），
     * 则在指定的key上设置value作为锁，并设定过期时间。如果锁已被其他进程设置，则在指定的超时时间内循环尝试获取锁，
     * 直到获取成功或超时。
     *
     * @param key 锁的redis key，用于标识唯一的锁。
     * @param value 锁的value，通常是一个唯一标识当前请求的值，以避免死锁。
     * @param expire 过期时间/秒，锁的自动释放时间，防止锁永久占用。
     * @param timeout 超时时长，尝试获取锁的最大等待时间。
     * @param unit 时间单位，指定timeout的单位。
     * @return 是否获取成功。在超时时间内获取到锁则返回true，否则返回false。
     */
    public boolean lock(String key, String value, long expire, long timeout, TimeUnit unit) {
        // 将超时时间转换为毫秒，作为循环等待的总时间上限
        long waitMillis = unit.toMillis(timeout);
        // 已等待时间
        long waitAlready = 0;

        // 当未获取到锁且未超时，则继续尝试
        while (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, value, expire, TimeUnit.SECONDS)) && waitAlready < waitMillis) {
            // 重试间隔/毫秒
            try {
                // 每次尝试间隔一定时间，避免快速重复尝试造成性能问题
                Thread.sleep(WAIT_MILLIS_PER);
            } catch (InterruptedException e) {
                // 线程中断异常处理，记录错误日志
                log.error("Interrupted when trying to get a lock. key: {}", key, e);
            }
            // 更新已等待时间
            waitAlready += WAIT_MILLIS_PER;
        }

        // 判断是否成功获取锁
        if (waitAlready < waitMillis) {
            // 在超时时间内获取到锁
            return true;
        }
        // 记录未获取到锁的警告日志
        log.warn("<====== lock {} failed after waiting for {} ms", key, waitAlready);
        // 超时仍未获取到锁
        return false;
    }


    /**
     * 释放锁
     * @param key  锁的redis key
     * @param value 锁的value
     */
    public boolean unLock(String key, String value) {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(RELEASE_LOCK_LUA_SCRIPT, Long.class);
        Long result = stringRedisTemplate.execute(redisScript, Collections.singletonList(key), value);
        return Objects.equals(result, RELEASE_LOCK_SUCCESS_RESULT);
    }
}
