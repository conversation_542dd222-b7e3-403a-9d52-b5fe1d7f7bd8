package com.yeelight.service.user.server.config.limiter.lock;

import com.yeelight.service.user.server.config.limiter.RedisRateLimiter;
import com.yeelight.service.user.server.config.limiter.RedisRateLimiterComponentManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

/**
 * @program: yeelight-service-station
 * @description:
 * @author: lixiaod<PERSON>
 * @create: 2023-02-07 10:05
 **/
@Aspect
@Slf4j
public class DistributedLockAspect {
    private final RedisRateLimiterComponentManager redisRateLimiterComponentManager;

    public DistributedLockAspect(RedisRateLimiterComponentManager redisRateLimiterComponentManager) {
        this.redisRateLimiterComponentManager = redisRateLimiterComponentManager;
    }

    /**
     * 使用分布式锁同步执行方法。
     * 此方法会在注解了@lockable的method执行前获取分布式锁，如果获取成功，则正常执行method，
     * 执行完成后释放锁。如果获取锁失败，则记录日志并返回null。
     *
     * @param point 切面连接点，代表被拦截的方法。
     * @param lockable 分布式锁注解，包含锁的前缀和键。
     * @return 如果获取锁并执行方法成功，则返回方法的执行结果；如果获取锁失败，则返回null。
     * @throws Throwable 如果方法执行或锁操作抛出异常，则抛出该异常。
     */
    @Around(value = "@annotation(lockable)")
    public Object distLock(ProceedingJoinPoint point, DistributedLockable lockable) throws Throwable {
        boolean locked = false;
        // 构造锁的键
        String key = lockable.prefix() + lockable.key();
        // 获取分布式锁实例
        DistributedLock lock = redisRateLimiterComponentManager.distributedLock();
        try {
            // 尝试获取锁
            locked = lock.lock(key, RedisRateLimiter.getRequestId(), lockable.expire());
            if(locked) {
                // 如果获取锁成功，则执行被拦截的方法
                return point.proceed();
            } else {
                // 获取锁失败，记录日志
                log.info("Did not get a lock for key {}", key);
                return null;
            }
        } catch (Exception e) {
            log.debug("Error occurred while executing method or locking. , key: {}", key, e);
            // 方法执行或锁操作异常，直接抛出
            throw e;
        } finally {
            if(locked) {
                // 执行方法前后都尝试释放锁
                if(!lock.unLock(key, RedisRateLimiter.getRequestId())){
                    // 释放锁失败，记录警告日志
                    log.warn("Unlock {} failed, maybe locked by another client already. ", lockable.key());
                }
            }
        }
    }

}
