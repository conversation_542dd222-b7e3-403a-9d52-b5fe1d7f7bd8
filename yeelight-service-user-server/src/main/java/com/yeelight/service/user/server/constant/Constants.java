package com.yeelight.service.user.server.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @description: 常量
 * <AUTHOR>
 */
public interface Constants {
    String SERVICE_NAME = "service-user";
    String WX_USER_REPLACE_BY_UNION_ID = "service_user:wx_user:replace_by_union_Id";

    String YEELIGHT_USER_CREATE_USER_BY_PHONE_OR_EMAIL_IF_NOT_EXIST = "service_user:yeelight_user:create_user_by_phone_or_email_if_not_exist";

    String AUTO_REGISTER_SOCIAL_USER_LOCK_PREFIX = "service_user:auto_register_social_user:";

    String DEFAULT_AVATAR = "https://yeelight-user-resources-bj.yeelight.com/avatars/default.png";

    String DEFAULT_REGION = "cn";

    String DEFAULT_USER_NICKNAME_PREFIX = "YL_";

    String SMARTLICHT_USER_NICKNAME_PREFIX = "SL_";

    String INVALID_REFRESH_TOKEN = "invalidRefreshToken";

    /**
     * 需要标准响应的client id
     */
    List<String> REQUIRE_STANDARD_RESPONSE_CLIENT_IDS = Arrays.asList(INVALID_REFRESH_TOKEN, "google-home");

    /**
     * 需要延迟删除refresh token的client id
     */
    List<String> DELAY_REMOVE_REFRESH_TOKEN_CLIENT_IDS = Collections.singletonList("google-home");

    String DEFAULT_LANG = "zh";

    String EMAIL_KEY = "email";
    String PHONE_KEY = "phone";
    String MOBILE_KEY = "mobile";
    String NICKNAME_KEY = "nickname";
    String AVATAR_KEY = "avatar";
    String CAPTCHA_KEY = "captcha";
    String CAPTCHA_KEY_KEY = "captchaKey";
    String PHONE_NUMBER_KEY = "phone_number";
    String PHONE_NUMBER_CAMEL_KEY = "phoneNumber";
    String PASSWORD_KEY = "password";
    String PASSWORD2_KEY = "password2";
    String IS_PRIVATE_EMAIL_KEY = "is_private_email";
    String SUB_KEY = "sub";
    String ERROR_KEY = "error";
    String ERROR_DESCRIPTION_KEY = "error_description";
    String ERROR_URI_KEY = "error_uri";
    String ERR_CODE_KEY = "errcode";
    String CODE_KEY = "code";
    String STATE_KEY = "state";
    String MESSAGE_KEY = "message";
    String MSG_KEY = "msg";
    String ERR_MSG_KEY = "errmsg";
    String WARNING_KEY = "warning";
    String UNION_ID_KEY = "unionid";
    String UNKNOWN = "unknown";
    String RESULT_KEY = "result";
    String TIMESTAMP_KEY = "timestamp";
    String HTTP_KEY = "http";
    String HTTPS_KEY = "https";
    String TOKEN_KEY = "token";

    /**
     * 前端后端分离请求第三方状态前缀
     */
    String FRONTEND_BACKEND_SEPARATED_REQUEST_THIRD_PARTY_STATE_PREFIX = "FBSR";
}
