package com.yeelight.service.user.server.constant;

/**
 * @program: yeelight-oauth-api
 * @description: 安全常量类
 * @author: <PERSON>
 * @create: 2019-06-26 11:26
 **/
public class SecurityConstants {
    /**
     * 是否开启验证码
     */
    public static final boolean IS_ENABLE_CAPTCHA = false;

    public static final String CAPTCHA_CACHE_PREFIX = "yeelight:oauth";

    public static final String REDIS_OAUTH_PREFIX = "oauth2:";

    public static final String CHECK_LAST_RESULT = "CHECK_LAST_RESULT";

    public final static String PREFIX_PATH = "/apis/account";

    /**
     * 登录页面
     */
    public static final String LOGIN_PAGE = "/oauth/login";

    /**
     * 退出登录URL
     */
    public static final String LOGOUT_PAGE = "/oauth/logout";

    /**
     * 登录处理地址
     */
    public static final String LOGIN_PROCESSING_URL = "/oauth/do_login";

    /**
     * 授权地址
     */
    public static final String GRANT_CONFIRM_ACCESS = "/oauth/confirm_access";

    /**
     * 注册页面
     */
    public static final String REGISTER_PAGE = "/oauth/register";

    /**
     * 重置密码页面
     */
    public static final String GETPASS_PAGE = "/oauth/getpass";

    /**
     * 重置密码处理
     */
    public static final String RESET_PAGE = "/oauth/reset";

    /**
     * Oauth index 页面
     */
    public static final String INDEX_PAGE = "/oauth/index";

    /**
     * 错误页面
     */
    public static final String ERROR_PAGE = "/oauth/error";

    /**
     * Oauth 授权 页面
     */
    public static final String AUTHORIZE_PAGE = "/oauth/authorize";

    /**
     * 注册用户锁前缀
     */
    public static final String CUSTOM_PHONE_AND_WXINFO_AUTHENTICATION = "oauth:CustomPhoneAndWxInfoAuthentication:";

    /**
     * Third-party Auth prefix
     */
    public static final String THIRD_PARTY_AUTH = "/third-party-auth/**";

    /**
     * Third-party callback prefix
     */
    public static final String THIRD_PARTY_CALLBACK = "/third-party-auth/callback/**";

    public static final String LOCALE_KEY = "locale";

    public static final String USERNAME_KEY = "username";

    public static final String PASSWORD_KEY = "password";

    public static final String TOKEN_KEY = "token";
    public static final String DEFAULT_SAVED_REQUEST_ATTR = "SPRING_SECURITY_SAVED_REQUEST";

    public static final String REDIRECT_URL_KEY = "redirectUrl";

    public static final String EMAIL_KEY = "email";

    public static final String PHONE_NUMBER_KEY = "phoneNumber";


    /**
     * JWT 认证token key
     */
    public static final String JWT_AUTH_TOKEN_KEY = "jwt-auth-token";

    /**
     * PKCE模式随机字符串请求头名字
     */
    public static final String CODE_CHALLENGE = "code_challenge";

    /**
     * PKCE模式请求方法头名字
     */
    public static final String CODE_CHALLENGE_METHOD = "code_challenge_method";

    /**
     * PKCE模式校验码请求头名字
     */
    public static final String CODE_VERIFIER = "code_verifier";
    public static final String CODE_CHALLENGE_METHOD_S256 = "S256";
    public static final String CODE_CHALLENGE_METHOD_PLAIN = "plain";

    public static final String PKCE_MODE_CACHE_PREFIX = "oauth2:pkceCache:";

    public static final String THIRD_PARTY_AUTH_TOKEN_PREFIX = "oauth2:thirdParty:auth:jwtToken:";
}
