/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.constant
 * Description: Token相关常量定义
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-01-25
 */
package com.yeelight.service.user.server.constant;

/**
 * Token相关常量定义
 * 从YeelightTokenEnhancer中迁移的常量，用于JWT令牌增强
 * 
 * <AUTHOR>
 * @description: JWT令牌附加信息的常量定义
 */
public final class TokenConstants {
    
    /**
     * 设备信息
     */
    public static final String ADDITIONAL_INFORMATION_DEVICE = "device";
    
    /**
     * 用户ID
     */
    public static final String ADDITIONAL_INFORMATION_ID = "id";
    
    /**
     * 用户名
     */
    public static final String ADDITIONAL_INFORMATION_USERNAME = SecurityConstants.USERNAME_KEY;
    
    /**
     * 客户端ID
     */
    public static final String ADDITIONAL_INFORMATION_CLIENT_ID = "client_id";
    
    /**
     * 客户端密钥
     */
    public static final String ADDITIONAL_INFORMATION_CLIENT_SECRET = "client_secret";
    
    /**
     * 创建时间
     */
    public static final String ADDITIONAL_INFORMATION_CREATE_TIME = "create_time";
    
    /**
     * 供应商信息
     */
    public static final String ADDITIONAL_INFORMATION_VENDOR = "vendor";
    
    /**
     * 区域信息
     */
    public static final String ADDITIONAL_INFORMATION_REGION = "region";
    
    /**
     * 用户代理
     */
    public static final String ADDITIONAL_INFORMATION_USER_AGENT = "user_agent";
    
    /**
     * 语言环境
     */
    public static final String ADDITIONAL_INFORMATION_LOCALE = "locale";
    
    /**
     * IP地址
     */
    public static final String ADDITIONAL_INFORMATION_IP = "ip";
    
    /**
     * 私有构造函数，防止实例化
     */
    private TokenConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
