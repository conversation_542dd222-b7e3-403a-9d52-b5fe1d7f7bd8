package com.yeelight.service.user.server.controller;

import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.utils.TokenUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;

import jakarta.servlet.http.HttpServletRequest;


/**
 * @program: yeelight-service-oauth
 * @description: 基础控制器
 * @author: <PERSON>
 * @create: 2019-06-24 14:22
 */

@Controller
public class BaseController {
    /**
     * 处理访问首页的请求。
     *
     * @param model 用于在视图和控制器之间传递数据的Model对象。
     * @return 返回重定向到OAuth索引页面的字符串路径。
     * <p>
     * @ PreAuthorize("isAuthenticated()")
     * @ RequestMapping(value = {"/index", "/"})
     */
    public String index(Model model) {
        // 将当前locale信息添加到model中，用于在首页展示多语言内容
        model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
        // 重定向到OAuth首页
        return "redirect:/oauth/index";
    }

    /**
     * 提取token
     *
     * @param token   token
     * @param request 请求
     * @return token
     */
    protected String extractToken(String token, HttpServletRequest request) {
        // 如果token为空，则尝试从请求头中提取token
        return StringUtils.isBlank(token) ? TokenUtils.extractToken(request) : token;
    }
}
