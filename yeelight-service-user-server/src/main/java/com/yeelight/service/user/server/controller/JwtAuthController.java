package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.custom.NewCustomAuthorizationConsentCustomizer;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.vo.JwtAuthGrantInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.URLBuilder;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.client.oauth2.YeelightAuthorizationRequest;
import com.yeelight.service.user.server.oauth2.YeelightOAuth2RequestFactory;
import com.yeelight.service.user.server.oauth2.YeelightAuthorizationEndpoint;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.support.SimpleSessionStatus;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * Jwt认证接口
 * 通过jwt-auth-token作为认证凭证
 * <AUTHOR>
 * 新版实现通过标准OAuth2端点提供相同功能
 */
@Slf4j
@RestController
@RequestMapping("/jwt/auth")
public class JwtAuthController extends BaseController {
    @Resource
    private JwtAuthService jwtAuthService;

    @Resource
    private YeelightOAuth2RequestFactory oauth2RequestFactory;

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    @Resource
    private YeelightAuthorizationEndpoint authorizationEndpoint;
    /**
     * 获取授权页面重定向地址
     * 该方法用于根据请求信息构建并返回一个授权页面的重定向URL。
     * @param request 请求对象，用于获取请求信息。
     * @param response 响应对象，用于设置响应信息（本方法中未使用）。
     * @param model 模型对象，用于存储和传递数据（本方法中用于获取授权请求信息）。
     * @return Result<String> 授权页面重定向地址的结果对象，其中包含重定向URL。
     */
    @ResponseBody
    @GetMapping(value = "/grant/redirect")
    public Result<String> grantRedirect(HttpServletRequest request,
                                        HttpServletResponse response,
                                        Map<String, Object> model) {

        // 从配置中获取授权页面的基础URL
        String redirectUrl = gatewayOauthConfig.getGrantPageUrl(request);
        // 构建授权页面的完整URL
        URLBuilder urlBuilder = URLBuilder.from(URL.valueOf(redirectUrl));
        // 从模型中获取授权请求，并将其参数添加到URL中
        YeelightAuthorizationRequest authorizationRequest = (YeelightAuthorizationRequest) model.get("authorizationRequest");
        urlBuilder.addParameters(authorizationRequest.getRequestParameters());
        // 完成URL构建
        String redirectUri = urlBuilder.build().toJavaURL().toString();

        // 将重定向URL封装成结果对象返回，由前端进行跳转
        return Result.success(redirectUri);
    }


    /**
     * 获取授权页面所需的参数
     *
     * @param request 请求对象，用于获取当前请求的详细信息
     * @param clientId 客户端ID，标识请求的客户端
     * @param responseType 响应类型，通常用于指定授权流程的处理方式
     * @param scope 授权范围，指定应用可以请求的权限范围
     * @param redirectUri 重定向地址，授权成功或失败后，用户会被重定向到这个地址
     * @param skipConfirm 跳过确认，用于指定是否跳过用户授权确认步骤
     * @param forceConfirm 强制确认，用于指定是否强制用户进行授权确认
     * @param codeChallenge 验证码挑战，用于PKCE（Proof Key for Code Exchange）流程中的验证码
     * @param codeChallengeMethod 验证码挑战方法，指定生成验证码的方式
     * @param state 状态参数，可用于保持请求和回调之间的状态
     *
     * @return Result<JwtAuthGrantInfoVo> 包含授权页面所需参数的结果对象
     */
    @ResponseBody
    @GetMapping(value = "/grant/parameters")
    public Result<JwtAuthGrantInfoVo> grantParameters(HttpServletRequest request,
                                                      @RequestParam(OAuth2Utils.CLIENT_ID) String clientId,
                                                      @RequestParam(OAuth2Utils.RESPONSE_TYPE) String responseType,
                                                      @RequestParam String scope,
                                                      @RequestParam(value = OAuth2Utils.REDIRECT_URI) String redirectUri,
                                                      @RequestParam(value = NewCustomAuthorizationConsentCustomizer.SKIP_CONFIRM_PARAM, required = false) String skipConfirm,
                                                      @RequestParam(value = NewCustomAuthorizationConsentCustomizer.FORCE_CONFIRM_SCOPE, required = false) String forceConfirm,
                                                      @RequestParam(value = SecurityConstants.CODE_CHALLENGE, required = false) String codeChallenge,
                                                      @RequestParam(value = SecurityConstants.CODE_CHALLENGE_METHOD, required = false) String codeChallengeMethod,
                                                      @RequestParam(required = false) String state) {
        // 通过服务获取授权页面所需的参数，并进行相应设置
        JwtAuthGrantInfoVo grantParameters = jwtAuthService.getAuthGrantInfoByToken(UserVendorHolder.getVendor(), jwtAuthService.getTokenThrowException(request), clientId, scope);
        grantParameters.setResponseType(responseType);
        grantParameters.setRedirectUri(redirectUri);
        grantParameters.setSkipConfirm(skipConfirm);
        grantParameters.setForceConfirm(forceConfirm);
        grantParameters.setState(state);
        grantParameters.setCodeChallenge(codeChallenge);
        grantParameters.setCodeChallengeMethod(codeChallengeMethod);

        return Result.success(grantParameters);
    }

    /**
     * 授权接口
     * 处理用户授权请求的接口。该接口用于处理用户是否同意OAuth2客户端应用访问其资源的请求。
     *
     * @param approvalParameters 包含授权过程中所需参数的Map，例如客户端ID、请求的权限范围等。
     * @return 返回一个View对象，用于呈现授权结果页面。
     * @throws InsufficientAuthenticationException 如果当前用户未通过Spring Security认证，则抛出此异常。
     */
    @RequestMapping(value = "/grant/approval", method = RequestMethod.POST, params = {OAuth2Utils.USER_OAUTH_APPROVAL, OAuth2Utils.CLIENT_ID})
    public Result<?> approveOrDeny(@RequestParam Map<String, String> approvalParameters) {
        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            // 如果当前用户未通过Spring Security认证，则抛出异常
            throw new InsufficientAuthenticationException("User must be authenticated before authorizing an access token.");
        }

        // 准备授权请求参数
        Map<String, String> authorizationParameters = new HashMap<>(8);
        // 从approvalParameters中提取并复制关键参数到authorizationParameters中
        authorizationParameters.put(OAuth2Utils.CLIENT_ID, approvalParameters.get(OAuth2Utils.CLIENT_ID));
        authorizationParameters.put(OAuth2Utils.SCOPE, approvalParameters.get(OAuth2Utils.SCOPE));
        authorizationParameters.put(OAuth2Utils.STATE, approvalParameters.get(OAuth2Utils.STATE));
        authorizationParameters.put(OAuth2Utils.REDIRECT_URI, approvalParameters.get(OAuth2Utils.REDIRECT_URI));
        authorizationParameters.put(OAuth2Utils.RESPONSE_TYPE, approvalParameters.get(OAuth2Utils.RESPONSE_TYPE));
        authorizationParameters.put(SecurityConstants.CODE_CHALLENGE, approvalParameters.get(SecurityConstants.CODE_CHALLENGE));
        authorizationParameters.put(SecurityConstants.CODE_CHALLENGE_METHOD, approvalParameters.get(SecurityConstants.CODE_CHALLENGE_METHOD));
        authorizationParameters.put(NewCustomAuthorizationConsentCustomizer.SKIP_CONFIRM_PARAM, approvalParameters.get(NewCustomAuthorizationConsentCustomizer.SKIP_CONFIRM_PARAM));
        authorizationParameters.put(NewCustomAuthorizationConsentCustomizer.FORCE_CONFIRM_SCOPE, approvalParameters.get(NewCustomAuthorizationConsentCustomizer.FORCE_CONFIRM_SCOPE));
        authorizationParameters.put(OAuth2Utils.USER_OAUTH_APPROVAL, approvalParameters.get(OAuth2Utils.USER_OAUTH_APPROVAL));

        // 定义一个字符串常量，用于表示“true”
        String tureStr = "true";

        // 创建YeelightAuthorizationRequest对象，并设置批准状态
        YeelightAuthorizationRequest authorizationRequest = oauth2RequestFactory.createAuthorizationRequest(authorizationParameters);
        authorizationRequest.setApproved(tureStr.equals(approvalParameters.get(OAuth2Utils.USER_OAUTH_APPROVAL)));

        // 处理 approvalParameters 没有授权项的情况, 当用户授权但是没有授权项时, 默认添加 read 和 write 授权项
        if (approvalParameters.get(OAuth2Utils.SCOPE) == null && authorizationRequest.isApproved()) {
            approvalParameters.put(OAuth2Utils.SCOPE + ".read", tureStr);
            approvalParameters.put(OAuth2Utils.SCOPE + ".write", tureStr);
        }

        // 准备模型数据，用于在视图中展示授权请求信息
        Map<String, ?> model = Collections.singletonMap("authorizationRequest", authorizationRequest);
        // 处理批准或拒绝授权请求，并返回相应的视图
        try {
            // 调用授权端点的 approveOrDeny 方法，处理授权请求
            View view = authorizationEndpoint.approveOrDeny(approvalParameters, model, new SimpleSessionStatus(), authentication);
            // 从视图中获取重定向地址
            if (view instanceof RedirectView redirectView) {
                return Result.success(redirectView.getUrl());
            }
        } catch (InsufficientAuthenticationException e) {
            return Result.failure(e.getMessage());
        } catch (OAuth2AuthenticationException e) {
            // 处理新版OAuth2认证异常
            OAuth2Error error = e.getError();
            String errorCode = error != null ? error.getErrorCode() : "invalid_request";
            String errorMessage = error != null ? error.getDescription() : e.getMessage();

            // 根据错误码进行分类处理
            if (OAuth2ErrorCodes.INVALID_REQUEST.equals(errorCode)) {
                return Result.failure(errorCode, errorMessage);
            } else if (OAuth2ErrorCodes.ACCESS_DENIED.equals(errorCode)) {
                return Result.failure(errorCode, errorMessage);
            } else {
                log.warn("授权过程中发生OAuth2异常", e);
                return Result.failure(errorCode, errorMessage);
            }
        } catch (Exception e) {
            // 如果授权过程中发生未知异常，记录异常信息并返回错误结果
            log.warn("授权过程中发生未知异常", e);
            return Result.failure("授权失败");
        }
        return Result.failure("授权失败");
    }
}
