package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.YeelightSocialUserService;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.utils.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * Jwt社交用户接口
 * 通过jwt-auth-token作为认证凭证
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/jwt/social")
public class JwtSocialController extends BaseController {
    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    /**
     * 获取三方用户信息
     * 获取指定来源的三方用户信息。
     * <p>
     * 该接口用于根据用户来源类型（如微信、QQ等）获取相应的三方用户信息。
     * 首先会将传入的来源类型附加厂商信息，然后通过当前登录用户的ID和处理后的来源类型，
     * 从数据库或第三方API中获取并返回三方用户的信息。
     * </p>
     *
     * @param source 用户来源类型，如"weixin"表示微信。
     * @return Result<SocialUserDto> 包含三方用户信息的Result对象。成功时，data字段为SocialUserDto类型的三方用户信息；失败时，data字段为空。
     */
    @GetMapping("/r/{source}")
    public Result<SocialUserDto> socialUser(@PathVariable String source) {
        // 为来源类型附加厂商信息
        source = UserVendorHolder.attachVendor(source);

        // 获取当前登录的用户信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();

        // 通过用户ID和来源类型获取三方用户信息
        return Result.success(justAuthUserDetailsService.getSocialUser(yeelightUser.getId(), source));
    }


    /**
     * 解绑三方用户
     * 该接口用于解绑当前用户的三方平台账户。
     * @param source 三方类型标识，表示需要解绑的第三方平台类型。
     * @return Result 返回操作结果，成功则返回Result.success()。
     */
    @DeleteMapping("/w/unbind/{source}")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.三方集成, bizSubType = "解绑三方", bizBody = "source:{#source}")
    public Result<?> unBindSocialUser(@PathVariable String source) {
        // 调用UserVendorHolder的attachVendor方法，处理source参数，可能是添加前缀或后缀等
        source = UserVendorHolder.attachVendor(source);
        // 从上下文中获取当前登录的用户信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 调用yeelightUserService服务，执行解绑操作
        yeelightSocialUserService.unBindSocialUser(yeelightUser.getId(), source);
        // 返回成功结果
        return Result.success();
    }


    /**
     * 获取三方用户列表。
     * 该接口不需要参数，通过当前登录用户的标识符来获取与特定供应商相关的三方用户列表。
     *
     * @return Result<List<SocialUserDto>> 包含三方用户列表的Result对象。如果操作成功，Result的success字段为true，data字段为三方用户列表；如果操作失败，Result的success字段为false，可能包含错误信息。
     */
    @GetMapping("/r/list")
    public Result<List<SocialUserDto>> socialUsers() {
        // 从上下文中获取当前登录的用户，确保用户不为空
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 根据当前用户和用户所属的供应商，查询并返回三方用户列表
        return Result.success(yeelightSocialUserService.findSocialUsersByVendor(UserVendorHolder.getVendor(), yeelightUser.getId()));
    }

}
