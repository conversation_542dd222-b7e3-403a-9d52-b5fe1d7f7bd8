package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.request.JwtCreateUserRequest;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.request.ResetPasswordByEmailRequest;
import com.yeelight.service.user.server.request.ResetPasswordByPhoneNumberRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;

/**
 * Jwt未登录接口
 * 通过jwt-auth-token作为认证凭证
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/jwt/un-auth")
public class JwtUnAuthController extends BaseController {
    @Resource
    private PublicController publicController;

    @Resource
    private UserController userController;

    /**
     * 验证码方式注册用户
     * 该接口用于通过验证码来注册新用户。
     * @param request 注册用户请求体，包含必要的注册信息，如手机号和验证码等。
     * @return Result<YeelightUserDto> 注册结果，包含注册是否成功的信息及可能的错误详情。
     */
    @PostMapping("/w/register")
    @ResponseBody
    @BizOperateLog(bizId = "{#request.phoneNumber}", opType = OpTypeEnums.新增, bizType = BizTypeEnums.用户, bizSubType = "验证码方式注册用户", bizBody = "{#request}")
    public Result<?> register(@NotNull @Valid @RequestBody JwtCreateUserRequest request) {
        // 调用用户控制器中的注册方法处理注册请求
        userController.register(request, request.getCaptcha(), request.getCaptchaKey(), request.getPassword());
        return Result.success();
    }

    /**
     * 使用验证码方式重置密码。
     * 该接口接收一个重置密码请求，通过验证手机号和对应的验证码来重设用户密码。
     *
     * @param request 包含重置密码所需信息的请求对象，如手机号和新密码等。
     * @return Result 返回一个结果对象，包含操作是否成功的信息，以及可能的错误码。
     */
    @PostMapping("/w/reset")
    @ResponseBody
    @BizOperateLog(bizId = "{#request.phoneNumber}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "验证码方式重置密码", bizBody = "{#request}")
    public Result<?> reset(@NotNull @Valid @RequestBody ResetPasswordByPhoneNumberRequest request) {
        // 调用公共控制器中的reset方法来处理重置密码的逻辑
        return publicController.reset(request);
    }

    /**
     * 通过邮箱重置密码
     * 该方法接收一个重置密码的请求，验证请求的合法性后，调用业务逻辑完成密码的重置，并返回操作结果。
     *
     * @param request 包含重置密码所需信息的请求对象，如用户的邮箱和新密码等。
     *                需要通过验证才能进行密码重置。
     * @return Result 返回一个结果对象，包含操作是否成功的信息和可能的错误码。
     */
    @PostMapping("/w/resetByEmail")
    @ResponseBody
    @BizOperateLog(bizId = "{#request.email}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "验证码方式通过邮箱重置密码", bizBody = "{#request}")
    public Result<?> resetByEmail(@NotNull @Valid @RequestBody ResetPasswordByEmailRequest request) {
        // 调用公共控制器中的resetByEmail方法处理重置密码的请求
        return publicController.resetByEmail(request);
    }

    /**
     * 获取环境变量
     * 该方法用于获取当前服务的环境变量，如服务的供应商等。
     *
     * @return Result<HashMap<String, Object>> 返回一个结果对象，包含环境变量的信息。
     */
    @GetMapping("/r/envs")
    @ResponseBody
    public Result<HashMap<String, Object>> envs() {
        HashMap<String, Object> map = new HashMap<>(4);
        map.put(UserVendorHolder.USER_VENDOR_KEY, UserVendorHolder.getSimpleVendor());
        return Result.success(map);
    }
}
