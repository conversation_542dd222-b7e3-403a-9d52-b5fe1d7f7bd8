package com.yeelight.service.user.server.controller;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.dto.FileSystemConfig;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.basic.platform.rpc.utils.FileSystemUtils;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.YeelightUserExtendExample;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.dto.YeelightUserExtendDto;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.service.YeelightUsersExtendService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.request.*;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * Jwt用户接口
 * 通过jwt-auth-token作为认证凭证
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/jwt/user")
public class JwtUserController extends BaseController {
    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageHelper;

    @Resource
    private JwtAuthService jwtAuthService;

    @Resource
    private YeelightUsersExtendService yeelightUsersExtendService;

    @Resource
    private UserSessionManagementService userSessionManagementService;

    /**
     * 获取当前登录用户的信息。
     * 该接口不需要任何参数，通过ContextHolder获取当前登录用户的信息，
     * 并将用户的密码字段置为空字符串返回，以保护用户敏感信息。
     *
     * @return Result<YeelightUserDto> 包含当前登录用户信息的Result对象，其中密码字段已被清空。
     */
    @GetMapping("/r/info")
    public Result<YeelightUserDto> info() {
        // 从ContextHolder中获取当前登录用户对象，并设置密码为空
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserById(yeelightUser.getId());
        // 确保密码不被泄露
        yeelightUserDto.setPassword(Strings.EMPTY);
        // 如果头像不是http开头的，设置为空
        if (Objects.nonNull(yeelightUserDto.getAvatar()) && !yeelightUserDto.getAvatar().contains(Constants.HTTP_KEY)) {
            yeelightUserDto.setAvatar(null);
        }
        // 返回包含用户信息但不含密码的结果对象
        return Result.success(yeelightUserDto);
    }


    /**
     * 获取用户扩展信息
     * 该接口不需要接收任何参数，通过当前登录用户的上下文信息来获取该用户的扩展信息。
     * @return Result<YeelightUserExtendDto> 返回一个包含用户扩展信息的结果对象。如果查询成功，结果对象的状态为成功（success），内容为用户扩展信息的实例；如果查询失败，结果对象的状态为失败（fail），内容可能包含错误信息。
     */
    @GetMapping(value = "/r/extend-info")
    public Result<YeelightUserExtendDto> userExtend() {
        // 从上下文中获取当前登录的用户信息
        YeelightUserDto yeelightUserDto = ContextHolder.getNotNullUser();
        // 根据用户ID查询其扩展信息
        YeelightUserExtendDto yeelightUserExtend = yeelightUsersExtendService.selectOneByExample(YeelightUserExtendExample.builder().yeelightUserId(yeelightUserDto.getId()).build());
        // 返回查询结果
        return Result.success(yeelightUserExtend);
    }


    /**
     * 修改用户名
     * 该接口用于接收客户端提交的请求，修改当前用户的用户名。
     *
     * @param request 请求参数，包含要修改的用户名信息。
     * @return Result 返回操作结果，成功则返回一个成功的Result对象。
     */
    @PutMapping(value = "/w/username")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "修改用户名", bizBody = "{#request}")
    public Result<?> updateUsername(@RequestBody @Valid UpdateUserNameRequest request) {
        // 获取当前登录的用户信息
        YeelightUserDto yeelightUserDto = ContextHolder.getNotNullUser();
        // 设置请求中的用户ID为当前登录用户ID
        request.setId(yeelightUserDto.getId());
        // 调用服务层方法，执行用户名修改操作
        yeelightUserWriteService.updateUsername(yeelightUserDto.getId(), request.getUsername());
        // 返回操作成功的结果
        return Result.success();
    }


    /**
     * 修改昵称
     * 该接口用于用户修改自己的昵称。
     * @param request 请求参数，包含要修改的昵称信息。
     * @return Result 返回操作结果，成功或失败。
     */
    @PutMapping(value = "/w/name")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "修改用户昵称", bizBody = "{#request}")
    public Result<?> updateName(@RequestBody @NotNull @Valid UpdateNameRequest request) {
        // 获取当前登录的用户信息
        YeelightUserDto yeelightUserDto = ContextHolder.getNotNullUser();
        // 调用服务层方法，修改用户的昵称
        yeelightUserWriteService.updateName(yeelightUserDto.getId(), request.getName());
        return Result.success();
    }


    /**
     * 检查新密码是否匹配
     * 该接口用于校验用户输入的旧密码是否正确，以确定是否可以更换新密码。
     * @param request 请求参数，包含旧密码。
     * @return Result<Boolean> 返回一个结果对象，其中包含一个布尔值：true表示旧密码匹配，false表示不匹配。
     */
    @GetMapping(value = "/r/check/password")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.读取, bizType = BizTypeEnums.用户, bizSubType = "检查新密码是否匹配", bizBody = "{#request}")
    public Result<Boolean> checkOldPasswordIsCorrect(@RequestBody @Valid CheckUserPasswordRequest request) {
        // 获取当前登录的用户信息
        YeelightUserDto yeelightUserDto = ContextHolder.getNotNullUser();
        // 校验旧密码是否正确
        return Result.success(UserUtils.passwordEncoder().matches(request.getOldPassword(), yeelightUserDto.getPassword()));
    }


    /**
     * 修改密码的接口
     * 该接口用于用户修改自己的密码。
     *
     * @param updatePasswordRequest 包含新密码和相关验证信息的请求参数对象。
     *                              需要验证用户身份，并提供新的密码。
     * @return Result 返回操作结果，成功则返回成功信息，失败则返回失败原因。
     */
    @PostMapping(value = "/w/password")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "修改密码", bizBody = "{#updatePasswordRequest}")
    public Result<?> changePassword(@RequestBody @Valid UpdatePasswordRequest updatePasswordRequest) {
        // 获取当前登录的用户信息
        YeelightUserDto yeelightUserDto = ContextHolder.getNotNullUser();
        // 调用服务层方法，执行密码修改逻辑
        yeelightUserWriteService.updatePassword(yeelightUserDto.getId(), updatePasswordRequest.getOldPassword(), updatePasswordRequest.getNewPassword());
        // 返回成功结果
        return Result.success();
    }


    /**
     * 上传用户头像到服务器。
     *
     * @param file 用户上传的头像文件。
     * @return Result<String> 包含上传成功后的头像地址。
     */
    @PostMapping(value = "/w/avatar")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "上传修改头像")
    public Result<String> uploadAvatar(@RequestParam(value = "file") MultipartFile file) throws IOException {
        // 获取当前登录用户信息
        YeelightUserDto yeelightUserDto = ContextHolder.getNotNullUser();

        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyy-MM-dd"));
        String fileName = "avatars/" + today + "/" + DateUtils.getCurrentSecond() + file.getOriginalFilename();

        // 处理头像上传，并返回头像地址
        FileSystemConfig config = FileSystemUtils.getConfig(FileSystemConfig.ConfigType.OSS_USER);
        String url = FileSystemUtils.upload(config, fileName, IOUtils.toByteArray(file.getInputStream()));
        yeelightUserWriteService.updateAvatar(yeelightUserDto.getId(), url);
        return Result.success(url);
    }


    /**
     * 登出
     * 本方法用于处理用户登出请求，主要逻辑包括：
     * 1. 验证用户是否存在；
     * 2. 根据用户会话信息，选择合适的登出逻辑（无token或有token）；
     * 3. 若存在token，则通过JWT服务执行登出操作；
     * 4. 若无token，则直接过期当前会话。
     * @param request 用户的HTTP请求，用于获取会话信息和token。
     * @return Result 结果对象，登出成功返回成功状态。
     */
    @PostMapping("/w/logout")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "登出当前用户")
    public Result<?> logout(HttpServletRequest request) {
        // 验证并获取当前用户，确保用户已登录
        ContextHolder.getNotNullUser();
        // 尝试从请求中获取token
        String token = jwtAuthService.getToken(request);
        if (StringUtils.isBlank(token)) {
            // 如果未获取到token，认为是直接登出，过期当前会话
            userSessionManagementService.expireSession(request.getSession(false).getId());
            return Result.success();
        } else {
            // 如果获取到token，执行基于token的登出逻辑
            jwtAuthService.logout(UserVendorHolder.getVendor(), token);
            return Result.success();
        }
    }


    /**
     * 登出用户
     * 删除当前用户下所有的 token&会话
     * @return Result 操作结果，成功返回success
     */
    @PostMapping("/w/logout-user")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "删除当前用户下所有的 token并登出当前用户所有会话")
    public Result<?> logoutCurrentUser() {
        // 获取当前登录用户
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 执行用户登出操作，清除该用户的所有token和会话
        yeelightUserWriteService.logoutByUserName(yeelightUser.getUsername());
        // 返回操作成功结果
        return Result.success();
    }


    /**
     * 注销用户
     * 该接口用于处理用户注销请求。首先会验证用户提交的验证码是否正确，验证通过后，
     * 根据当前登录用户的账号信息注销该用户。
     *
     * @param captchaRequest 包含验证码信息、账号信息的请求对象。
     * @return 返回操作结果，成功则返回操作成功的Result对象，失败则返回包含错误信息的Result对象。
     */
    @PostMapping("/w/remove-user")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "注销用户", bizBody = "{#captchaRequest}")
    public Result<?> removeUser(RemoveUserRequest captchaRequest) {
        // 验证验证码
        CaptchaResult captchaResult;
        captchaResult = captchaMessageHelper.checkCaptcha(captchaRequest.getCaptchaKey(), captchaRequest.getCaptcha(), captchaRequest.getAccount(),
                SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
        // 验证码错误，返回失败结果
        Assert.isNotTrue(captchaResult.isSuccess(), ResultCodeEnum.验证码错误.getCode(), captchaResult.getMessage());

        // 获取当前登录用户
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 校验账号是否存在
        Assert.isTrue((StringUtils.equals(yeelightUser.getPhoneNumber(), captchaRequest.getAccount()) || StringUtils.equals(yeelightUser.getEmail(), captchaRequest.getAccount())), I18nUtil.getMessage("ResultCode.手机号或邮箱不存在"));
        // 执行用户注销
        yeelightUserWriteService.removeUserByUserName(yeelightUser.getUsername());
        // 记录日志
        log.info("注销用户[{}], captchaRequest:{}", yeelightUser, captchaRequest);
        // 返回成功结果
        return Result.success();
    }
}
