package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.LoginSessionInfo;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.utils.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Jwt登录会话接口
 * 通过jwt-auth-token作为认证凭证
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/jwt/session")
public class JwtUserSessionController extends BaseController {
    @Resource
    private UserSessionManagementService userSessionManagementService;

    /**
     * 查看当前用户所有会话
     * 该接口不需要接收任何参数，通过当前登录用户的上下文信息来获取该用户的所有会话信息。
     *
     * @return Result<List<LoginSessionInfo>> 会话列表封装在Result对象中，其中List<LoginSessionInfo>是具体的会话信息列表。
     */
    @GetMapping("/r/list")
    @ResponseBody
    public Result<List<LoginSessionInfo>> loginSessions() {
        // 从上下文获取当前登录用户
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 调用服务层方法，获取当前用户基于用户名和供应商的会话信息
        return Result.success(userSessionManagementService.getSessionInfos(yeelightUser.getUsername(), UserVendorHolder.getVendor()));
    }


    /**
     * 强制登出当前用户的所有会话
     * <p>
     * 该接口不需要接收任何参数，调用后会终止当前用户的所有会话。
     * 主要用于管理员或者系统需要强制让用户登出时使用。
     * </p>
     * @return Result 返回操作结果，成功则为Success，失败则为相应的错误信息。
     */
    @GetMapping("/w/logout-all")
    @ResponseBody
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.会话, bizSubType = "强制登出当前用户的所有会话", bizBody = "强制登出当前用户的所有会话")
    public Result<?> logoutSessions() {
        // 获取当前登录的用户信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 终止当前用户的所有会话
        userSessionManagementService.expireUserSessions(yeelightUser.getUsername(), UserVendorHolder.getVendor());
        return Result.success();
    }


    /**
     * 强制登出指定会话
     * <p>
     * 该接口用于强制结束一个指定的会话。需要提供会话的ID（sessionId），
     * 系统会校验请求者是否有权限结束该会话。如果请求者有权且会话有效，
     * 则会强制结束该会话，并返回成功结果；否则，返回未授权错误信息。
     * </p>
     * @param sessionId 会话ID，需要强制登出的会话的标识符。
     * @return Result 返回操作结果，成功则包含成功信息，失败则包含错误信息。
     */
    @GetMapping("/w/logout")
    @ResponseBody
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.会话, bizSubType = "强制登出指定会话", bizBody = "{#sessionId}")
    public Result<?> logoutSession(String sessionId) {
        // 获取当前登录用户信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 根据提供的sessionId获取会话信息
        LoginSessionInfo loginSessionInfo = userSessionManagementService.getSessionInfo(sessionId);
        // 验证会话信息是否有效且与当前用户匹配
        if (Objects.nonNull(loginSessionInfo) && Objects.equals(loginSessionInfo.getSessionId(), sessionId) && Objects.equals(loginSessionInfo.getUsername(), yeelightUser.getUsername())) {
            // 如果会话有效且与当前用户匹配，则结束该会话
            userSessionManagementService.expireSession(sessionId);
            // 返回操作成功的结果
            return Result.success();
        }
        // 如果会话无效或与当前用户不匹配，返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }

}
