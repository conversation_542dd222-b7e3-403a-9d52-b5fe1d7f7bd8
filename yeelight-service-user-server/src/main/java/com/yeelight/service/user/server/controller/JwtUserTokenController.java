package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.LoginTokenInfo;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Jwt登录token接口
 * 通过jwt-auth-token作为认证凭证
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/jwt/token")
public class JwtUserTokenController extends BaseController {
    @Resource
    private TokenService tokenService;

    /**
     * 查看当前用户已有的所有授权
     * 该接口不需要接收任何参数，通过当前登录用户的上下文信息来获取该用户已有的授权列表。
     *
     * @return Result<List<LoginTokenInfo>> 类型的授权列表，其中 Result 表示操作结果，
     *         List<LoginTokenInfo> 则是授权信息的集合。
     */
    @GetMapping("/r/list")
    @ResponseBody
    public Result<List<LoginTokenInfo>> authorizedTokens() {
        // 从上下文获取当前登录用户的信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 根据用户用户名和厂商信息获取授权token列表
        return Result.success(tokenService.getTokenInfos(yeelightUser.getUsername(), UserVendorHolder.getVendor()));
    }


    /**
     * 强制登出当前用户的所有授权
     * 该接口不接受任何参数，通过当前登录用户的上下文信息自动获取用户凭证，并废除该用户所有授权令牌。
     * 主要用于安全需求，比如用户密码重置后，需要强制登出所有设备以保护用户账号安全。
     *
     * @return Result 操作结果，成功则返回操作成功的标识，失败则返回失败的详细信息。
     */
    @GetMapping("/w/remove-all")
    @ResponseBody
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.OAUTH, bizSubType = "强制登出当前用户的所有授权", bizBody = "强制登出当前用户的所有授权")
    public Result<?> removeTokens() {
        // 获取当前登录的用户信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 通过用户名废除所有授权令牌
        tokenService.revokeTokenByUserName(yeelightUser.getUsername());
        return Result.success();
    }


    /**
     * 强制登出指定授权
     * 该接口用于强制登出指定的授权用户。通过传入授权token，验证并注销该token对应的用户授权。
     *
     * @param token 授权token，用于标识需要登出的用户。
     * @return Result 返回操作结果，成功则返回Result.success()，失败则返回Result.failure()并附带错误信息。
     */
    @GetMapping("/w/remove")
    @ResponseBody
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.OAUTH, bizSubType = "强制登出指定授权", bizBody = "{#token}")
    public Result<?> removeToken(String token) {
        // token无效或不一致，返回未授权错误信息
        Assert.notBlank(token, I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
        // 获取当前登录用户的信息
        YeelightUserDto yeelightUser = ContextHolder.getNotNullUser();
        // 通过token获取用户名
        String userName = tokenService.getUserNameByToken(token);
        // 比较token对应的用户名与当前登录用户用户名是否一致
        if (Objects.equals(userName, yeelightUser.getUsername())) {
            // 一致则注销该token
            tokenService.logoutCurrentToken(token);
            // 登出成功，返回成功结果
            return Result.success();
        }
        // 不一致则返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }

}
