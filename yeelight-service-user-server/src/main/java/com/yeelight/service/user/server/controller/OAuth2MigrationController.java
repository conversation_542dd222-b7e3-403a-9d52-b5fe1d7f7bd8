package com.yeelight.service.user.server.controller;

import com.yeelight.service.user.server.migration.OAuth2CompleteMigrationService;
import com.yeelight.service.user.server.migration.OAuth2ClientMigrationService;
import com.yeelight.service.user.server.migration.OAuth2AuthorizationMigrationService;
import com.yeelight.service.user.server.migration.OAuth2ConsentMigrationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2数据库迁移控制器
 * 提供手动触发OAuth2数据库迁移的REST接口
 * 
 * <AUTHOR>
 * @since 2024-07-31
 */
@Slf4j
@RestController
@RequestMapping("/public/migration")
public class OAuth2MigrationController {

    @Resource
    private OAuth2CompleteMigrationService completeMigrationService;
    
    @Resource
    private OAuth2ClientMigrationService clientMigrationService;
    
    @Resource
    private OAuth2AuthorizationMigrationService authorizationMigrationService;
    
    @Resource
    private OAuth2ConsentMigrationService consentMigrationService;

    /**
     * 执行完整的OAuth2数据库迁移
     */
    @PostMapping("/complete")
    public ResponseEntity<Map<String, Object>> performCompleteMigration() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            log.info("收到完整OAuth2数据库迁移请求");
            
            // 检查是否需要迁移
            if (!completeMigrationService.needsCompleteMigration()) {
                result.put("success", false);
                result.put("message", "无需迁移：新版表已有数据或旧版表为空");
                result.put("status", completeMigrationService.getMigrationStatusReport());
                return ResponseEntity.ok(result);
            }
            
            // 执行完整迁移
            completeMigrationService.performCompleteMigration();
            
            result.put("success", true);
            result.put("message", "OAuth2完整数据库迁移成功完成");
            result.put("status", completeMigrationService.getMigrationStatusReport());
            
            log.info("OAuth2完整数据库迁移成功完成");
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("OAuth2完整数据库迁移失败", ex);
            result.put("success", false);
            result.put("message", "迁移失败: " + ex.getMessage());
            result.put("error", ex.getClass().getSimpleName());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 仅迁移客户端配置
     */
    @PostMapping("/clients")
    public ResponseEntity<Map<String, Object>> migrateClients() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            log.info("收到客户端配置迁移请求");
            
            if (!clientMigrationService.needsMigration()) {
                result.put("success", false);
                result.put("message", "无需迁移：客户端配置已迁移");
                return ResponseEntity.ok(result);
            }
            
            clientMigrationService.migrateClients();
            
            result.put("success", true);
            result.put("message", "客户端配置迁移成功完成");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("客户端配置迁移失败", ex);
            result.put("success", false);
            result.put("message", "迁移失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 仅迁移授权数据
     */
    @PostMapping("/authorizations")
    public ResponseEntity<Map<String, Object>> migrateAuthorizations() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            log.info("收到授权数据迁移请求");
            
            if (!authorizationMigrationService.hasOldAuthorizationData()) {
                result.put("success", false);
                result.put("message", "无需迁移：没有旧版授权数据");
                return ResponseEntity.ok(result);
            }
            
            authorizationMigrationService.migrateAuthorizations();
            
            result.put("success", true);
            result.put("message", "授权数据迁移成功完成");
            result.put("stats", authorizationMigrationService.getMigrationStats());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("授权数据迁移失败", ex);
            result.put("success", false);
            result.put("message", "迁移失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 仅迁移授权同意数据
     */
    @PostMapping("/consents")
    public ResponseEntity<Map<String, Object>> migrateConsents() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            log.info("收到授权同意数据迁移请求");
            
            if (!consentMigrationService.hasOldConsentData()) {
                result.put("success", false);
                result.put("message", "无需迁移：没有旧版授权同意数据");
                return ResponseEntity.ok(result);
            }
            
            consentMigrationService.migrateConsents();
            
            result.put("success", true);
            result.put("message", "授权同意数据迁移成功完成");
            result.put("stats", consentMigrationService.getMigrationStats());
            result.put("report", consentMigrationService.getMigrationReport());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("授权同意数据迁移失败", ex);
            result.put("success", false);
            result.put("message", "迁移失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 获取迁移状态报告
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getMigrationStatus() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            result.put("needsCompleteMigration", completeMigrationService.needsCompleteMigration());
            result.put("needsClientMigration", clientMigrationService.needsMigration());
            result.put("hasOldAuthorizationData", authorizationMigrationService.hasOldAuthorizationData());
            result.put("hasOldConsentData", consentMigrationService.hasOldConsentData());
            
            result.put("statusReport", completeMigrationService.getMigrationStatusReport());
            result.put("authorizationStats", authorizationMigrationService.getMigrationStats());
            result.put("consentStats", consentMigrationService.getMigrationStats());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("获取迁移状态失败", ex);
            result.put("error", "获取状态失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 清理过期的授权同意数据
     */
    @PostMapping("/cleanup/consents")
    public ResponseEntity<Map<String, Object>> cleanupExpiredConsents() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            int deletedCount = consentMigrationService.cleanupExpiredApprovals();
            
            result.put("success", true);
            result.put("message", "过期授权同意数据清理完成");
            result.put("deletedCount", deletedCount);
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("清理过期授权同意数据失败", ex);
            result.put("success", false);
            result.put("message", "清理失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 验证迁移结果
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateMigration() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            boolean consentValid = consentMigrationService.validateMigration();
            
            result.put("consentMigrationValid", consentValid);
            result.put("overallValid", consentValid);
            
            if (consentValid) {
                result.put("message", "迁移验证通过");
            } else {
                result.put("message", "迁移验证失败，请检查数据完整性");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("验证迁移结果失败", ex);
            result.put("error", "验证失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 获取详细的迁移报告
     */
    @GetMapping("/report")
    public ResponseEntity<Map<String, Object>> getMigrationReport() {
        Map<String, Object> result = new HashMap<>(8);
        
        try {
            result.put("statusReport", completeMigrationService.getMigrationStatusReport());
            result.put("consentReport", consentMigrationService.getMigrationReport());
            result.put("authorizationStats", authorizationMigrationService.getMigrationStats());
            result.put("consentStats", consentMigrationService.getMigrationStats());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception ex) {
            log.error("获取迁移报告失败", ex);
            result.put("error", "获取报告失败: " + ex.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
}
