package com.yeelight.service.user.server.controller;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.LoginSessionInfo;
import com.yeelight.service.user.client.domain.LoginTokenInfo;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.*;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.config.DynamicStaticResourceConfig;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.handler.CustomAuthenticationFailureHandler;
import com.yeelight.service.user.server.handler.CustomAuthenticationSuccessHandler;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.URLBuilder;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;

import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * 用户登录、注册、找回密码、授权等
 * <AUTHOR>
 */
@Slf4j
@Controller
@SessionAttributes({"authorizationRequest"})
@RequestMapping("/oauth")
@Deprecated
public class OauthController extends BaseController {
    private static final String FIELD_ERROR_MSG = "errorMsg";
    private static final String FIELD_ENABLE_CAPTCHA = "enableCaptcha";

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    @DubboReference(version = "0.0.1", timeout = 5000)
    private CaptchaMessageDubboService captchaMessageHelper;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Resource
    private CustomAuthenticationSuccessHandler successHandler;

    @Resource
    @Lazy
    private CustomAuthenticationFailureHandler authenticationFailureHandler;

    @Resource
    private TokenService tokenService;

    @Resource
    private UserSessionManagementService userSessionManagementService;

    /**
     * 首页
     * 该方法用于处理用户请求首页的逻辑。根据系统配置和用户认证状态，决定是重定向到外部首页链接还是显示个人首页。
     *
     * @param model Spring MVC的Model对象，用于在视图和控制器间传递数据。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求信息。
     * @return 返回一个字符串，表示视图的名称或重定向的URL。
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping(value = {"/index", "/"})
    public String index(Model model, HttpServletRequest httpServletRequest) {
        // 检查系统是否配置为前后端分离模式，是则重定向到配置的首页URL
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getIndexPageUrl(httpServletRequest);
        }

        // 添加动态静态资源的视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 获取当前认证的用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        // 如果用户未认证或认证信息中没有主体对象，重定向到登录页
        if (Objects.isNull(auth) || Objects.isNull(auth.getPrincipal()) || !(auth.getPrincipal() instanceof YeelightUser yeelightUser)) {
            return UserVendorHolder.attachVendor("login").toLowerCase();
        }

        // 处理已认证的用户逻辑
        // 根据用户ID查询用户详细信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserById(yeelightUser.getId());
        // 如果用户信息存在，将用户信息添加到Model中，准备显示个人首页
        if (Objects.nonNull(yeelightUserDto)) {
            model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
            model.addAttribute(SecurityConstants.USERNAME_KEY, yeelightUserDto.getName());
            model.addAttribute("yeelightUser", yeelightUserDto);
            return "personal";
        }
        // 如果查不到用户信息，重定向到登录页
        return UserVendorHolder.attachVendor("login").toLowerCase();
    }


    /**
     * 会话管理页面
     * 该方法首先判断是否前后端分离，如果是，则重定向到指定的前端页面；
     * 如果不是，则根据用户认证状态， either 渲染会话管理页面或重定向到登录页。
     *
     * @param model 用于在视图和控制器之间传递数据的模型对象，自动获取，无需显式传入。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求相关信息。
     * @return 返回一个字符串，表示视图的名称或重定向的URL。
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping(value = {"/sessions", "/"})
    public String sessions(Model model, HttpServletRequest httpServletRequest) {
        // 判断是否前后端分离，如果是，则重定向到前端首页
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getIndexPageUrl(httpServletRequest);
        }

        // 添加动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 获取当前认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        // 判断用户是否已认证，且认证信息有效
        if (Objects.isNull(auth) || Objects.isNull(auth.getPrincipal()) || !(auth.getPrincipal() instanceof YeelightUser yeelightUser)) {
            // 如果未认证或认证信息无效，重定向到登录页
            return UserVendorHolder.attachVendor("login").toLowerCase();
        }

        // 用户已认证且认证信息有效，进一步处理

        // 根据用户ID查询用户详细信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserById(yeelightUser.getId());

        // 如果查询到用户信息，准备数据并渲染页面
        if (Objects.nonNull(yeelightUserDto)) {
            model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
            model.addAttribute(SecurityConstants.USERNAME_KEY, yeelightUserDto.getName());
            model.addAttribute("yeelightUser", yeelightUserDto);

            // 获取当前会话ID并添加到模型中
            if (Objects.nonNull(httpServletRequest.getSession(false))) {
                model.addAttribute("currentSessionId", httpServletRequest.getSession(false).getId());
            }

            // 渲染会话管理页面
            return "sessions";
        }
        // 如果未查询到用户信息，重定向到登录页
        return UserVendorHolder.attachVendor("login").toLowerCase();
    }


    /**
     * 授权管理页面
     * 该方法首先判断是否前后端分离，如果是，则重定向到前端首页；
     * 如果不是，则根据用户认证状态和类型，来决定是显示授权页面还是登录页面。
     *
     * @param model 用于在视图和控制器之间传递数据的模型对象，自动获取，无需显式传入。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求信息。
     * @return 返回字符串指示视图的名称或重定向的URL。
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping(value = "/tokens")
    public String tokens(Model model, HttpServletRequest httpServletRequest) {
        // 判断是否前后端分离，如果是则重定向到前端首页
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getIndexPageUrl(httpServletRequest);
        }

        // 添加动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 获取当前认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        // 判断认证信息是否有效，若无效则返回登录页面
        if (Objects.isNull(auth) || Objects.isNull(auth.getPrincipal()) || !(auth.getPrincipal() instanceof YeelightUser yeelightUser)) {
            return UserVendorHolder.attachVendor("login").toLowerCase();
        }

        // 验证用户信息并准备展示授权页面所需数据
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserById(yeelightUser.getId());
        if (Objects.nonNull(yeelightUserDto)) {
            // 向模型中添加数据，用于在授权页面中显示
            model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
            model.addAttribute(SecurityConstants.USERNAME_KEY, yeelightUserDto.getName());
            model.addAttribute("yeelightUser", yeelightUserDto);
            // 返回授权页面
            return "tokens";
        }
        // 如果用户数据无效，同样返回登录页面
        return UserVendorHolder.attachVendor("login").toLowerCase();
    }


    /**
     * 已登录用户直接登录
     * 处理已登录用户直接访问页面的请求。
     * 该方法首先检查系统配置是否为前后端分离模式，如果是，则重定向到登录页面。
     * 若非前后端分离模式，则检查用户是否已经通过Spring OAuth认证，
     * 如果已认证，则重定向到登录成功页面，否则重定向到登录失败页面。
     *
     * @param model 用于在视图和控制器之间传递数据的模型对象，自动获取。
     * @param request 用户的请求对象，自动获取。
     * @param response 用户的响应对象，自动获取。
     * @throws ServletException 如果发生与Servlet相关的异常。
     * @throws IOException 如果发生输入/输出异常。
     */
    @PreAuthorize("isAuthenticated()")
    @GetMapping(value = {"/existingUser"})
    public void existingUser(Model model, HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        // 检查是否为前后端分离模式，如果是则重定向到登录页面
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            response.sendRedirect(gatewayOauthConfig.getLoginPageUrl(request));
        }
        // 添加动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // 检查用户是否已登录且认证通过，并且用户主体是YeelightUser实例
        if (authentication != null && authentication.isAuthenticated()  && authentication.getPrincipal() instanceof YeelightUser) {
            // 已登录成功，重定向到成功页面
            successHandler.onAuthenticationSuccess(request, response, authentication);
        } else {
            // 未登录或登录失败，重定向到失败页面
            authenticationFailureHandler.onAuthenticationFailure(request, response, new AccountExpiredException("登录失败"));
        }
    }



    /**
     * 登录页面
     * 该页面根据不同的登录类型和是否强制登录来展示不同的逻辑，包括处理验证码、错误信息的展示以及用户登录状态的判断。
     *
     * @param httpServletRequest 自动获取，用于获取请求相关信息。
     * @param model 自动获取，用于在视图和控制器之间传递数据。
     * @param type 用户指定的登录类型， 默认为"account"。
     * @param forceLogin 是否强制用户登录，如果为true，则不检查用户是否已经登录。
     * @return 返回登录页面的视图名称或重定向的URL。
     * @throws ServletException 如果发生Servlet相关的异常。
     */
    @GetMapping("/login")
    public String login(HttpServletRequest httpServletRequest, Model model, String type, Boolean forceLogin) throws ServletException {
        // 判断前后端是否分离，如果是则重定向到登录页面
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getLoginPageUrl(httpServletRequest);
        }

        // 初始化动态静态资源配置，并向模型添加属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 获取并处理错误信息
        String errorMsg;
        if (httpServletRequest.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION) instanceof Exception) {
            errorMsg = ((Exception) httpServletRequest.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION)).getMessage();
        } else {
            errorMsg = (String) httpServletRequest.getSession().getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
        }
        httpServletRequest.getSession().removeAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
        if (StringUtils.isNotBlank(errorMsg)) {
            model.addAttribute(FIELD_ERROR_MSG, errorMsg);
        }

        // 向模型添加验证码相关属性
        model.addAttribute(FIELD_ENABLE_CAPTCHA, SecurityConstants.IS_ENABLE_CAPTCHA);

        // 设置默认登录类型为"account"
        type = StringUtils.defaultIfBlank(type, "account");
        model.addAttribute("type", type);

        // 获取并添加当前会话的用户名
        String sessionUsername = (String) httpServletRequest.getSession().getAttribute(SecurityConstants.USERNAME_KEY);
        model.addAttribute("sessionUsername", sessionUsername);

        // 添加登录处理URL和本地化信息到模型
        model.addAttribute("loginProcessUrl", SecurityConstants.LOGIN_PROCESSING_URL);
        model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());

        // 检查是否存在之前保存的请求，并根据条件返回特定视图
        SavedRequest savedRequest = (SavedRequest) httpServletRequest.getSession().getAttribute(SecurityConstants.DEFAULT_SAVED_REQUEST_ATTR);
        if (Objects.nonNull(savedRequest)) {
            String[] externalVendors = savedRequest.getParameterValues("externalVendor");
            String wecomKey = "wecom";
            if (Objects.nonNull(externalVendors) && externalVendors.length != 0 && StringUtils.equals(externalVendors[0], wecomKey)) {
                return "login_" + wecomKey;
            }
        }

        // 判断是否强制登录，如果不是则检查用户是否已经登录
        String forceLoginName = forceLogin(model, forceLogin);
        if (forceLoginName != null) {
            return forceLoginName;
        }

        // 根据供应商信息返回相应的登录界面
        return UserVendorHolder.attachVendor("login").toLowerCase();
    }

    private String forceLogin(Model model, Boolean forceLogin) {
        forceLogin = !Objects.isNull(forceLogin) && forceLogin;
        if (!forceLogin) {
            // 已登录用户可选择直接登录
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            model.addAttribute("userSigned", false);
            if (authentication != null && authentication.isAuthenticated() && authentication.getPrincipal() instanceof YeelightUser yeelightUser) {
                YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserById(yeelightUser.getId());
                if (Objects.nonNull(yeelightUserDto)) {
                    model.addAttribute(SecurityConstants.USERNAME_KEY, authentication.getName());
                    model.addAttribute("userSigned", true);
                    model.addAttribute("yeelightUser", yeelightUserDto);
                    return "user-signed-in";
                }
            }
        }
        return null;
    }


    /**
     * 找回密码页面
     * 该方法首先会检查系统是否开启了前后端分离模式，如果是，则重定向到前端重置密码的页面。
     * 如果未开启前后端分离模式，则准备并返回找回密码的页面。
     *
     * @param model 用于在视图和控制器之间传递数据的对象，自动获取，无需显式传入。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求相关信息。
     * @return 返回字符串指示视图的名称或重定向的URL。
     */
    @GetMapping("/getpass")
    public String getpass(Model model, HttpServletRequest httpServletRequest) {
        // 检查是否开启了前后端分离模式，如果是，则重定向到重置密码的前端页面
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getResetPasswordUrl(httpServletRequest);
        }

        // 在模型中添加本地化信息和动态视图属性，准备显示找回密码页面
        model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 返回对应的视图名称，通过VendorHolder附加供应商信息并转换为小写
        return UserVendorHolder.attachVendor("retpwd").toLowerCase();
    }


    /**
     * 注册页面
     * 该方法首先检查系统是否开启了前后端分离模式，如果是，则重定向到配置的注册页面URL；
     * 如果未开启，则准备注册页面所需的模型数据，并返回注册页面的视图名称。
     *
     * @param model 用于在视图和控制器之间传递数据的模型对象，自动获取，无需手动传入。
     * @param httpServletRequest 当前HTTP请求对象，用于获取请求相关信息。
     * @return 返回注册页面的视图名称或重定向的URL。
     */
    @GetMapping("/register")
    public String register(Model model, HttpServletRequest httpServletRequest) {
        // 检查是否开启了前后端分离模式，如果是，则重定向到指定的注册页面
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getRegisterPageUrl(httpServletRequest);
        }

        // 为注册页面准备模型数据
        DynamicStaticResourceConfig.dynamicViewAttributes(model);
        model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());

        // 返回经过厂商绑定的注册页面视图名称
        return UserVendorHolder.attachVendor("register").toLowerCase();
    }


    /**
     * 处理用户注册请求
     * 首先验证用户提交的验证码、密码等信息的正确性，然后检查用户是否已存在，
     * 如果不存在则注册新用户并跳转到成功页面，否则返回错误信息。
     *
     * @param createUserRequest 包含用户注册信息的对象，如手机号、密码等。
     * @param model 用于在视图和控制器之间传递数据的模型对象，自动获取。
     * @param request 用户的请求对象，自动获取，用于获取用户提交的数据。
     * @return 返回跳转的页面路径或视图名。
     */
    @PostMapping("/register")
    @BizOperateLog(bizId = "{#createUserRequest.phoneNumber}", opType = OpTypeEnums.新增, bizType = BizTypeEnums.用户, bizSubType = "注册用户", bizBody = "{#createUserRequest}")
    public String register(CreateUserRequest createUserRequest, Model model, HttpServletRequest request) {
        // 初始化动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);
        String phoneNumber = createUserRequest.getPhoneNumber();
        Assert.notNull(phoneNumber, ResultCodeEnum.手机号或邮箱不存在.getCode(), I18nUtil.getMessage("ResultCode.手机号或邮箱不存在"));
        // 验证码验证逻辑
        String inputCaptcha = request.getParameter("captcha");
        String captchaKey = request.getParameter("captchaKey");
        try {
            // 检查验证码输入是否为空
            if (StringUtils.isAnyEmpty(inputCaptcha, captchaKey)) {
                throw new CaptchaException(I18nUtil.getMessage("ResultCode.请输入手机验证码"));
            }
            // 验证密码是否一致
            if (!createUserRequest.getPassword().equals(request.getParameter(Constants.PASSWORD2_KEY))) {
                throw new BizException(I18nUtil.getMessage("ResultCode.密码不一致"));
            }
            // 检查验证码是否有效
            CaptchaResult captchaResult;
            if (SecurityConstants.CHECK_LAST_RESULT.equals(inputCaptcha)) {
                captchaResult = captchaMessageHelper.checkLastResult(captchaKey, SecurityConstants.CAPTCHA_CACHE_PREFIX);
            } else {
                captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, inputCaptcha, createUserRequest.getPhoneNumber(),
                        SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
            }

            if (!captchaResult.isSuccess()) {
                throw new CaptchaException(captchaResult.getMessage());
            }

            // 检查用户是否已存在
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
            if (yeelightUserDto == null) {
                // 用户不存在，进行注册
                yeelightUserWriteService.create(createUserRequest);
            } else {
                // 用户已存在，抛出异常
                throw new BizException(I18nUtil.getMessage("ResultCode.用户已存在"));
            }
        } catch (Exception e) {
            // 处理异常，将错误信息添加到模型中
            model.addAttribute(FIELD_ERROR_MSG, e.getMessage());
            return UserVendorHolder.attachVendor("register").toLowerCase();
        }

        // TODO: 验证用户是否同意协议

        // 注册成功，跳转到首页并显示成功消息
        model.addAttribute("msg", I18nUtil.getMessage("User.Action.Register.Success"));
        model.addAttribute("url", gatewayOauthConfig.getLoginPageUrl(request));
        model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
        return "success";
    }


    /**
     * 找回密码
     *
     * @param user 包含需要重置密码的用户信息，如手机号等。
     * @param model 用于在视图和控制器间传递数据的模型对象，自动获取。
     * @param request 用户的请求对象，自动获取，用于获取用户提交的数据。
     * @return 返回视图名或转发路径。
     */
    @PostMapping("/reset")
    @BizOperateLog(bizId = "{#user.phoneNumber}", opType = OpTypeEnums.新增, bizType = BizTypeEnums.用户, bizSubType = "重置密码", bizBody = "{#user}")
    public String reset(YeelightUserDto user, Model model, HttpServletRequest request) {
        // 初始化视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);
        String phoneNumber = user.getPhoneNumber();

        // 验证验证码
        String inputCaptcha = request.getParameter("captcha");
        String captchaKey = request.getParameter("captchaKey");
        try {
            // 检查验证码输入是否为空
            if (StringUtils.isAnyEmpty(inputCaptcha, captchaKey)) {
                throw new CaptchaException(I18nUtil.getMessage("ResultCode.请输入手机验证码"));
            }

            // 验证验证码的正确性
            CaptchaResult captchaResult;
            if (SecurityConstants.CHECK_LAST_RESULT.equals(inputCaptcha)) {
                captchaResult = captchaMessageHelper.checkLastResult(captchaKey, SecurityConstants.CAPTCHA_CACHE_PREFIX);
            } else {
                captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, inputCaptcha, phoneNumber, SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
            }

            // 验证码验证失败处理
            if (!captchaResult.isSuccess()) {
                throw new CaptchaException(captchaResult.getMessage());
            }

            // 根据手机号查找用户
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
            // 用户不存在处理
            if (Objects.nonNull(yeelightUserDto)) {
                yeelightUserWriteService.updatePassword(yeelightUserDto.getId(), user.getPassword());
            } else {
                throw new BizException(I18nUtil.getMessage("ResultCode.手机号或邮箱不存在"));
            }
        } catch (Exception e) {
            // 错误处理，将错误信息添加到模型中
            model.addAttribute(FIELD_ERROR_MSG, e.getMessage());
            return UserVendorHolder.attachVendor("retpwd").toLowerCase();
        }

        // 成功重置密码后的处理
        model.addAttribute("msg", I18nUtil.getMessage("User.Action.Reset.Success"));
        model.addAttribute("url", gatewayOauthConfig.getLoginPageUrl(request));
        model.addAttribute(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());
        return "success";
    }

    /**
     * 自定义授权页面
     * 该方法用于处理用户访问授权页面的请求，并根据请求信息构建相应的授权页面。
     * 注意：该方法需要在类上添加@SessionAttributes({"authorizationRequest"})注解，以保持授权请求信息的会话状态。
     *
     * @param model 用于在视图和控制器之间传递数据的Map对象，自动获取，无需传入。
     * @param request 用户的请求对象，自动获取，无需传入。
     * @return 返回一个字符串，表示视图的名称或重定向的URL。
     */
    @GetMapping("/confirm_access")
    public String getAccessConfirmation(Map<String, Object> model, HttpServletRequest request) {
        // 根据请求获取重定向URL
        String redirectUrl = gatewayOauthConfig.getGrantPageUrl(request);
        // 构建完整的授权请求URL
        URLBuilder urlBuilder = URLBuilder.from(URL.valueOf(redirectUrl));

        // 从请求参数中获取OAuth2相关参数并添加到URL中
        Map<String, String> parameters = new HashMap<>(8);
        String clientId = request.getParameter(OAuth2Utils.CLIENT_ID);
        String responseType = request.getParameter(OAuth2Utils.RESPONSE_TYPE);
        String scope = request.getParameter(OAuth2Utils.SCOPE);
        String redirectUriParam = request.getParameter(OAuth2Utils.REDIRECT_URI);
        String state = request.getParameter(OAuth2Utils.STATE);

        if (clientId != null) {
            parameters.put(OAuth2Utils.CLIENT_ID, clientId);
        }
        if (responseType != null) {
            parameters.put(OAuth2Utils.RESPONSE_TYPE, responseType);
        }
        if (scope != null) {
            parameters.put(OAuth2Utils.SCOPE, scope);
        }
        if (redirectUriParam != null) {
            parameters.put(OAuth2Utils.REDIRECT_URI, redirectUriParam);
        }
        if (state != null) {
            parameters.put(OAuth2Utils.STATE, state);
        }

        urlBuilder.addParameters(parameters);
        String redirectUri = urlBuilder.build().toJavaURL().toString();

        // 判断前端和后端是否分离，根据结果进行重定向或渲染页面
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + redirectUri;
        }

        // 在模型中添加动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 处理并传递scopes信息到模型
        @SuppressWarnings("unchecked")
        Map<String, String> scopes = (Map<String, String>) (model.containsKey(Constants.WARNING_KEY) ? model.get("scopes") : request.getAttribute("scopes"));
        List<String> scopeList = new ArrayList<>();
        if (scopes != null) {
            scopeList.addAll(scopes.keySet());
        }
        model.put("scopeList", scopeList);

        // 处理并传递警告信息到模型
        // 从请求参数或属性中获取警告信息
        String warning = request.getParameter(Constants.WARNING_KEY);
        if (warning == null) {
            warning = (String) request.getAttribute(Constants.WARNING_KEY);
        }
        model.put("warning", warning != null ? warning : "");

        // 添加请求URL和本地化信息到模型
        model.put("requestUrl", redirectUri);
        model.put(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());

        // 返回视图名称
        return "grant";
    }


    /**
     * 查看指定三方的绑定状态
     * 该接口用于查看当前用户与指定第三方源的绑定状态。
     * 需要用户已认证才能访问。
     *
     * @param source 三方类型，指定要查询绑定状态的第三方平台类型。
     * @return Result<SocialUserDto> 返回三方绑定详情，如果用户未绑定则返回失败结果。
     */
    @GetMapping("/socialUser/{source}")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    public Result<SocialUserDto> socialUser(@PathVariable String source) {
        // 从安全上下文中获取当前认证的用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();

        // 检查用户是否存在
        if (Objects.nonNull(yeelightUser)) {
            // 查询并返回指定三方的绑定详情
            return Result.success(justAuthUserDetailsService.getSocialUser(yeelightUser.getId(), UserVendorHolder.attachVendor(source)));
        }
        // 如果用户不存在，返回认证失效的错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
    }


    /**
     * 解绑指定的三方绑定
     * 从当前已认证的用户中解除与指定三方平台的绑定关系。
     *
     * @param source 三方类型，表示需要解绑的第三方平台标识。
     * @return Result 操作结果，成功返回success，失败返回相应的错误信息。
     */
    @DeleteMapping("/unBindSocialUser/{source}")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.三方集成, bizSubType = "解绑三方账号", bizBody = "{#source}")
    public Result<?> unBindSocialUser(@PathVariable String source) {
        // 通过SecurityContextHolder获取当前认证的用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();
        // 判断用户信息是否存在，若存在则进行解绑操作
        if (Objects.nonNull(yeelightUser)) {
            yeelightSocialUserService.unBindSocialUser(yeelightUser.getId(), source);
            return Result.success();
        }
        // 当用户信息不存在时，返回令牌不存在或已过期的错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
    }


    /**
     * 查看当前用户已绑定的所有三方信息
     * 该接口不需要接收任何参数，通过用户认证信息获取当前登录用户的身份，
     * 并查询该用户已绑定的所有第三方平台用户信息。
     *
     * @return Result<List<SocialUserDto>> 返回当前用户已绑定的三方信息列表，
     *         如果用户未登录或认证信息无效，则返回错误信息。
     */
    @GetMapping("/socialUsers")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    public Result<List<SocialUserDto>> socialUsers() {
        // 从安全上下文中获取当前用户的认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        // 将认证信息转换为自定义的用户信息对象
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();
        if (Objects.nonNull(yeelightUser)) {
            // 如果用户信息存在，则查询并返回该用户已绑定的三方平台信息
            return Result.success(yeelightSocialUserService.findSocialUsers(yeelightUser.getId()));
        }
        // 如果用户信息不存在，返回认证失效或过期的错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
    }


    /**
     * 查看当前用户所有会话
     * 该接口不需要参数，仅对已认证的用户开放。会返回当前用户的所有会话信息。
     *
     * @return Result<List<LoginSessionInfo>> 会话列表的结果对象，其中包含会话信息的列表。
     *         如果用户未认证，将返回一个包含错误信息的Result对象。
     */
    @GetMapping("/loginSessions")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    public Result<List<LoginSessionInfo>> loginSessions() {
        // 从安全上下文中获取当前认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        // 将认证信息转换为YeelightUser对象
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();
        if (Objects.nonNull(yeelightUser)) {
            // 如果用户存在，查询并返回该用户的所有会话信息
            return Result.success(userSessionManagementService.getSessionInfos(yeelightUser.getUsername(), UserVendorHolder.getVendor()));
        }
        // 如果用户不存在，返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }


    /**
     * 强制登出当前用户的所有会话
     * <p>
     * 该接口用于强制登出当前用户的所有会话。当调用成功时，会终止当前用户的所有会话，使其需要重新登录。
     * </p>
     * @return Result 返回操作结果，成功则返回一个成功的Result对象，失败则返回包含错误信息的Result对象。
     */
    @GetMapping("/logoutSessions")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.会话, bizSubType = "强制登出当前用户的所有会话", bizBody = "强制登出当前用户的所有会话")
    public Result<?> logoutSessions() {
        // 获取当前用户的认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        // 将认证信息转换为自定义的用户信息对象
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();
        if (Objects.nonNull(yeelightUser)) {
            // 如果用户信息不为空，则强制终止该用户的所有会话
            userSessionManagementService.expireUserSessions(yeelightUser.getUsername(), UserVendorHolder.getVendor());
            // 成功终止会话，返回成功结果
            return Result.success();
        }
        // 如果无法获取到用户信息，返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }


    /**
     * 强制登出指定会话
     * <p>
     * 该接口用于管理员或其他有权限的用户强制登出指定的会话ID对应的用户会话。
     * 不需要用户本人操作，可直接通过会话ID实现强制退出。
     * </p>
     * @param sessionId 会话ID，用于标识要强制登出的会话。
     * @return Result 返回操作结果，成功则返回success，失败则返回相应的错误信息。
     */
    @GetMapping("/logoutSession")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.会话, bizSubType = "强制登出指定会话", bizBody = "{#sessionId}")
    public Result<?> logoutSession(String sessionId) {
        // 获取当前认证的用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();

        // 校验用户和会话ID是否存在
        if (Objects.nonNull(yeelightUser) && Objects.nonNull(sessionId)) {
            // 根据会话ID获取会话信息
            LoginSessionInfo loginSessionInfo = userSessionManagementService.getSessionInfo(sessionId);

            // 验证会话信息是否有效且匹配
            if (Objects.nonNull(loginSessionInfo) && Objects.equals(loginSessionInfo.getSessionId(), sessionId)) {
                // 若会话有效则强制过期该会话
                userSessionManagementService.expireSession(sessionId);
                return Result.success();
            }
        }
        // 若校验不通过，返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }


    /**
     * 查看当前用户已有的所有授权
     * 该接口不需要参数，仅返回当前已认证用户的所有授权信息列表。
     *
     * @return Result<List<LoginTokenInfo>> 授权列表，包含当前用户所有有效的登录令牌信息。
     *         如果用户未认证（未登录），则返回授权失败的结果。
     */
    @GetMapping("/authorizedTokens")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    public Result<List<LoginTokenInfo>> authorizedTokens() {
        // 从安全上下文中获取当前用户的认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        // 将认证信息转换为自定义的用户对象，以便进一步处理
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();

        if (Objects.nonNull(yeelightUser)) {
            // 如果用户对象非空，查询并返回该用户的所有令牌信息
            return Result.success(tokenService.getTokenInfos(yeelightUser.getUsername(), UserVendorHolder.getVendor()));
        }

        // 如果用户对象为空，表示用户未认证，返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }


    /**
     * 强制登出当前用户的所有授权
     * 该接口不需要参数，通过认证的用户调用该接口会强制登出其所有设备上的授权，实现安全退出。
     * @return Result 返回操作结果，成功则返回Result.success()，失败则返回Result.failure()，并附带错误信息。
     */
    @GetMapping("/removeTokens")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.OAUTH, bizSubType = "强制登出当前用户的所有授权", bizBody = "强制登出当前用户的所有授权")
    public Result<?> removeTokens() {
        // 获取当前认证的用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();
        // 判断用户信息是否非空，非空则执行强制登出操作
        if (Objects.nonNull(yeelightUser)) {
            // 调用服务，撤销当前用户的所有令牌
            tokenService.revokeTokenByUserName(yeelightUser.getUsername());
            return Result.success();
        }
        // 用户信息为空，返回未授权错误信息
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }


    /**
     * 强制登出指定授权
     * 该接口用于注销指定的授权token，如果该token对应的用户与当前登录用户一致，则注销成功。
     * @param token 授权token，用于标识需要被注销的用户会话。
     * @return Result 返回操作结果，成功则返回success，失败则返回UNAUTHORIZED。
     */
    @GetMapping("/removeToken")
    @ResponseBody
    @PreAuthorize("isAuthenticated()")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.OAUTH, bizSubType = "强制登出指定授权", bizBody = "{#token}")
    public Result<?> removeToken(String token) {
        // 获取当前认证的用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();

        // 校验用户和token是否存在，并判断是否为同一用户
        if (Objects.nonNull(yeelightUser) && Objects.nonNull(token)) {
            // 通过token获取用户名
            String userName = tokenService.getUserNameByToken(token);
            // 比较用户名是否一致
            if (Objects.equals(userName, yeelightUser.getUsername())) {
                // 如果一致，则注销该token
                tokenService.logoutCurrentToken(token);
                return Result.success();
            }
        }
        // 如果不一致或用户不存在，返回未授权错误
        return Result.failure(I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
    }


    /**
     * 自定义异常页面处理方法。
     * 该方法负责处理访问错误页面的请求，并根据不同的错误情况渲染不同的错误信息。
     *
     * @param model 用于在视图和控制器之间传递数据的Map对象，自动获取，无需传入。
     * @param request 用户的请求对象，自动获取，无需传入。
     * @return 返回错误页面的视图名称。
     */
    @GetMapping("/error")
    public String handleError(Map<String, Object> model, HttpServletRequest request) {
        // 判断是否前后端分离架构，如果是则重定向到错误页面
        if (gatewayOauthConfig.isFrontendBackendSeparated()) {
            return "redirect:" + gatewayOauthConfig.getErrorUrl(request);
        }

        // 初始化动态静态资源配置，并将视图属性添加到model中
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 从请求中获取错误信息
        Object error = request.getAttribute(DefaultLoginPageGeneratingFilter.ERROR_PARAMETER_NAME);
        String errorDescription = request.getParameter(DefaultLoginPageGeneratingFilter.ERROR_PARAMETER_NAME);

        // 对错误描述进行XSS防护处理
        String errorSummary;
        String errorCode;

        // 尝试从会话中获取错误信息
        HttpSession session = request.getSession(false);
        // 处理OAuth2相关异常
        if (error instanceof OAuth2AuthenticationException oauth2Exception) {
            // 处理新版OAuth2认证异常
            OAuth2Error oauth2Error = oauth2Exception.getError();
            errorSummary = oauth2Error.getDescription() != null ? oauth2Error.getDescription() : oauth2Exception.getMessage();
            errorCode = oauth2Error.getErrorCode();
        } else if (error instanceof Exception) {
            // 处理一般异常
            errorSummary = error.toString();
            errorCode = "ERROR";
        } else if(Objects.nonNull(session) && Objects.nonNull(session.getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION))){
            // 处理认证异常
            Exception exception = (Exception) session.getAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
            if (StringUtils.isBlank(exception.getMessage())) {
                log.info("OAuth errorDescription: {}", errorDescription);
                errorSummary = errorDescription;
                errorCode = errorDescription;
            } else {
                log.info("OAuth error message: {}", exception.getMessage());
                errorSummary = exception.getMessage();
                errorCode = "AUTHENTICATION ERROR";
            }
        } else {
            // 处理其他异常
            if (StringUtils.isNotBlank(errorDescription)) {
                log.info("OAuth error errorDescription: {}", errorDescription);
                errorSummary = errorDescription;
                errorCode = errorDescription;
            } else {
                log.info("OAuth error: {}", error);
                errorSummary = "Unknown error";
                errorCode = "Unknown error";
            }
        }

        // 将错误信息添加到model中，用于在错误页面显示
        model.put("errorSummary", errorSummary);
        model.put("errorCode", errorCode);
        // 添加本地化信息到model
        model.put(SecurityConstants.LOCALE_KEY, LocaleContextHolder.getLocale());

        // 返回错误页面的视图名称
        return "error";
    }


    /**
     * 根据用户名删除token
     * <p>
     * 本接口用于根据提供的用户名注销用户的token。当调用成功时，将返回一个表示操作成功的响应体。
     *
     * @param authentication 用户认证信息，包含用户名等认证相关数据。
     * @return 返回一个结果对象，如果token删除成功，则返回成功结果；否则返回失败结果，并提供错误信息。
     */
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @GetMapping(value = "/revoke")
    @ResponseBody
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "根据用户名删除token", bizBody = "{#authentication.name}")
    private Result<?> revokeToken(Authentication authentication) {
        // 获取用户名
        String username = authentication.getName();
        // 根据用户名尝试注销token
        boolean isRevoked = tokenService.revokeTokenByUserName(username);
        // 根据操作结果返回相应的响应
        return isRevoked ? Result.success() : Result.failure(I18nUtil.getMessage("ResultCode.Common.INTERNAL_SERVER_ERROR"));
    }

}
