package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.PartnerRegisterUserDto;
import com.yeelight.service.user.client.request.PartnerCreateUserRequest;
import com.yeelight.service.user.client.service.ClientDetailService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.utils.IdTokenEncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * Desc: 可信合作伙伴
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-12-09 14:21:14:21
 */
@Slf4j
@RestController
@RequestMapping("/partner")
public class PartnerController extends BaseController {

    @Resource
    private ClientDetailService clientDetailService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    /**
     * 可信合作伙伴批量注册用户
     * @param partnerCreateUserRequest 请求参数
     * @return 注册用户信息
     */
    @PostMapping("/register")
    @BizOperateLog(bizId = "{#partnerCreateUserRequest.clientId}", 
                   opType = OpTypeEnums.变更, 
                   bizType = BizTypeEnums.用户, 
                   bizSubType = "可信合作伙伴批量注册用户", 
                   bizBody = "{#partnerCreateUserRequest}")
    public Result<List<PartnerRegisterUserDto>> register(@NotNull @RequestBody PartnerCreateUserRequest partnerCreateUserRequest) {
        log.info("partner register user: {}", partnerCreateUserRequest);
        
        // 校验clientId和clientSecret
        if (clientDetailService.validateClientSecret(partnerCreateUserRequest.getClientId(), partnerCreateUserRequest.getClientSecret())) {
            return Result.failure("clientId或clientSecret错误");
        }
        // 去重
        List<String> userNames = partnerCreateUserRequest.getUsernames().stream().distinct().toList();
        // 检查用户名是否是手机号或邮箱
        for (String username : userNames) {
            if (!UserUtils.isEmail(username) && !UserUtils.isPhoneNumber(username)) {
                return Result.failure("用户名"+username+"必须是手机号或邮箱");
            }
        }
        
        // 批量注册用户
        List<PartnerRegisterUserDto> partnerRegisterUsers = new ArrayList<>();
        for (String username : userNames) {
            PartnerRegisterUserDto partnerRegisterUserDto = new PartnerRegisterUserDto();
            partnerRegisterUserDto.setUsername(username);
            // 创建用户, 如果用户已存在则返回已存在用户的ID, 如果用户不存在则创建新用户并返回新用户的ID
            Long yeelightId = yeelightUserWriteService.getUserIdOrCreateByPhoneOrEmail(username, UserUtils.randomStr(16));
            partnerRegisterUserDto.setYeelightId(yeelightId);
            partnerRegisterUserDto.setYeelightIdSign(IdTokenEncryptUtils.encryptYeelightId(yeelightId));

            partnerRegisterUsers.add(partnerRegisterUserDto);
        }

        return Result.success(partnerRegisterUsers);
    }
}
