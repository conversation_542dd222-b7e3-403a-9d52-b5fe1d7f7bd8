package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.dto.YeelightUserExtendDto;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.request.CheckUserPasswordRequest;
import com.yeelight.service.user.server.request.UpdateNameRequest;
import com.yeelight.service.user.server.request.UpdatePasswordRequest;
import com.yeelight.service.user.server.request.UpdateUserNameRequest;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;

/**
 * 个人信息管理
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/user/info")
public class PersonalInfoManageController extends BaseController {
    @Resource
    private JwtUserController jwtUserController;

    /**
     * 修改用户名
     * 该接口用于接收客户端提交的请求，修改当前用户的用户名。
     *
     * @param request 请求参数，包含要修改的用户名信息。
     * @return Result 返回操作结果，成功则返回一个成功的Result对象。
     */
    @PutMapping(value = "/w/username")
    public Result<?> updateUsername(@RequestBody @Valid UpdateUserNameRequest request) {
        return jwtUserController.updateUsername(request);
    }


    /**
     * 修改昵称
     * 该接口用于用户修改自己的昵称。
     * @param request 请求参数，包含要修改的昵称信息。
     * @return Result 返回操作结果，成功或失败。
     */
    @PutMapping(value = "/w/name")
    public Result<?> updateName(@RequestBody @NotNull @Valid UpdateNameRequest request) {
        return jwtUserController.updateName(request);
    }


    /**
     * 检查新密码是否匹配
     * <p>
     * 该接口用于校验用户输入的新密码是否与当前用户旧密码匹配。
     * </p>
     * @param oldPassword 旧密码, 用户输入的需要验证的旧密码。
     * @return Result<Boolean> 返回一个结果对象，其中包含一个布尔值：
     *         如果旧密码匹配，则为true；否则为false。
     */
    @GetMapping(value = "/r/check/password")
    public Result<Boolean> check(@RequestParam(value = SecurityConstants.PASSWORD_KEY)
                        @NotBlank(message = "密码不能为空")
                        @Length(min = 1, max = 50, message = "密码长度需要在{min}~{max}之间") String oldPassword) {
        CheckUserPasswordRequest checkUserPasswordRequest = new CheckUserPasswordRequest();
        checkUserPasswordRequest.setOldPassword(oldPassword);
        return jwtUserController.checkOldPasswordIsCorrect(checkUserPasswordRequest);
    }


    /**
     * 上传用户头像
     *
     * @param file 用户上传的头像文件。
     * @return Result<String> 包含上传成功后的头像地址。
     */
    @PostMapping(value = "/w/avatar")
    public Result<String> uploadAvatar(@RequestParam(value = "file") MultipartFile file) throws IOException {
        return jwtUserController.uploadAvatar(file);
    }


    /**
     * 修改密码
     * @param updatePasswordRequest 请求参数
     * @return Result
     */
    @PostMapping(value = "/w/password")
    public Result<?> changePassword(@RequestBody @Valid UpdatePasswordRequest updatePasswordRequest) {
        return jwtUserController.changePassword(updatePasswordRequest);
    }

    /**
     * 获取用户详情
     * @param id 用户ID
     * @return Result<YeelightUserDto> 用户详情
     */
    @GetMapping(value = "/r/detail/{id}")
    public Result<YeelightUserDto> userDetail(@PathVariable(name = "id", required = false) Long id) {
        return jwtUserController.info();
    }

    /**
     * 获取用户扩展信息
     * @return Result<YeelightUserExtendDto> 用户扩展信息
     */
    @GetMapping(value = "/r/userExtend")
    public Result<YeelightUserExtendDto> userExtend() {
        return jwtUserController.userExtend();
    }
}
