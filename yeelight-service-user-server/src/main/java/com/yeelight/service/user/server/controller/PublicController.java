package com.yeelight.service.user.server.controller;

import cn.hutool.core.util.RandomUtil;
import com.google.common.collect.Lists;
import com.yeelight.basic.platform.rpc.dto.CaptchaImageDto;
import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.dto.FileObject;
import com.yeelight.basic.platform.rpc.dto.FileSystemConfig;
import com.yeelight.basic.platform.rpc.service.CaptchaImageDubboService;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.basic.platform.rpc.utils.CaptchaUtils;
import com.yeelight.basic.platform.rpc.utils.FileSystemUtils;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.IPUtils;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.config.DynamicStaticResourceConfig;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.config.limiter.CountLimit;
import com.yeelight.service.user.server.config.limiter.LimitType;
import com.yeelight.service.user.server.config.limiter.RateLimit;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.AccountTypeEnum;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.request.*;
import com.yeelight.service.user.server.support.UserJudge;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.IdTokenEncryptUtils;
import com.yeelight.service.user.server.utils.RedisKeyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共接口
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequestMapping("/public")
public class PublicController extends BaseController {
    @DubboReference(version = "0.0.1", timeout = 2000)
    private CaptchaImageDubboService captchaImageHelper;

    @DubboReference(version = "0.0.1", timeout = 5000)
    private CaptchaMessageDubboService captchaMessageHelper;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private RedisManager redisManager;

    /**
     * 检查账号是否存在
     * @param account 账号，可以是邮箱、手机号或用户名，根据账号类型参数来确定检查的类型。
     * @param accountType 账号类型，如果未指定，则默认为用户名。可选值包括EMAIL、PHONE_NUMBER和USERNAME。
     * @return Result 返回一个结果对象，如果账号存在，则返回成功结果；否则，返回失败结果，并附带相应的错误信息。
     */
    @GetMapping("/account/exist")
    @ResponseBody
    public Result<?> accountExist(@RequestParam String account, @RequestParam(required = false) AccountTypeEnum accountType) {
        // 如果未指定账号类型，则默认为用户名类型
        accountType = Optional.ofNullable(accountType).orElse(AccountTypeEnum.USERNAME);

        YeelightUserDto yeelightUserDto;

        // 根据账号类型查找用户，包括邮箱、手机号和用户名
        switch (accountType) {
            case EMAIL:
                yeelightUserDto = yeelightUserReadService.findUserByEmail(account);
                Assert.notNull(yeelightUserDto, ResultCodeEnum.邮箱未注册.code, I18nUtil.getMessage("ResultCode.邮箱未注册"));
                break;
            case PHONE_NUMBER:
                yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(account);
                Assert.notNull(yeelightUserDto, ResultCodeEnum.手机号未注册.code, I18nUtil.getMessage("ResultCode.手机号未注册"));
                break;
            default: // 默认为用户名查找
                yeelightUserDto = yeelightUserReadService.findUserByUsername(account);
                Assert.notNull(yeelightUserDto, ResultCodeEnum.用户名不存在.code, I18nUtil.getMessage("ResultCode.用户名不存在"));
        }
        // 如果查找到相应的用户，则返回成功结果
        return Result.success();
    }


    /**
     * 获取验证码图片
     * 该方法用于生成一个验证码，并将该验证码以图片的形式返回给客户端。
     * 同时，将验证码的键值对以cookie的形式存储在客户端。
     *
     * @param response 图片响应，用于将验证码图片响应给客户端。
     * @throws IOException 如果发生IO异常，则抛出。
     */
    @GetMapping("/captcha.jpg")
    public void captcha(HttpServletResponse response) throws IOException {
        // 生成验证码并写入缓存，返回验证码信息
        CaptchaImageDto captchaImageDto = captchaImageHelper.generateAndWriteCaptchaImage(SecurityConstants.CAPTCHA_CACHE_PREFIX);
        // 根据验证码生成对应的cookie
        Cookie cookie = CaptchaUtils.getCaptchaCookie(captchaImageDto.getCaptchaKey());

        // 设置cookie到客户端浏览器
        response.addCookie(cookie);

        // 将验证码图片以Base64编码的形式输出到客户端
        CaptchaUtils.base64ToImage(response, captchaImageDto.getCaptchaBase64());
    }


    /**
     * 获取手机验证码(已废弃)
     * 该方法用于获取手机验证码，但已被标记为废弃，建议不再使用。
     * 请求频率限制：同一手机号每天最多获取5次验证码，同一IP每天最多获取5次验证码。
     * @param request 无需传入，该参数未被使用。
     * @param phoneNumber 手机号，用于生成验证码。
     * @param exist 是否检查手机号存在。如果为true，检查手机号是否存在，如果已存在返回错误；如果为false，检查手机号是否不存在，如果不存在返回错误。
     * @return Result<CaptchaResult> 验证码结果，包含验证码信息或错误信息。
     */
    @GetMapping("/phone_number/captcha")
    @ResponseBody
    @CountLimit(key = "#{phoneNumber}", prefix = "user:countLimit:phoneNumberCaptchaCountLimitCustom:", limit = 5, period = 86400, limitType = LimitType.CUSTOM)
    @CountLimit(prefix = "user:countLimit:phoneNumberCaptchaCountLimitCustomIp:", limit = 5, period = 86400, limitType = LimitType.IP)
    @Deprecated
    public Result<CaptchaResult> phoneNumberCaptcha(HttpServletRequest request, @RequestParam String phoneNumber, @RequestParam(required = false) Boolean exist) {
        // 检查请求头中的sourceId是否在指定范围内，若在范围内则根据exist参数校验用户存在性
        List<String> sourceIds = Arrays.asList("0", "1", "2", "3", "4", "5");
        // 获取请求头中的sourceId
        String sourceId = request.getHeader("sourceId");
        // 如果sourceId不为空且在指定范围内，则根据exist参数校验用户存在性
        if (com.yeelight.service.framework.util.StringUtils.isNotBlank(sourceId) && sourceIds.contains(sourceId)) {
            // 根据exist参数执行相应的用户存在性检查
            if (Objects.nonNull(exist)) {
                YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
                // 如果exist为true，则检查用户是否存在，如果存在则返回错误
                if (exist) {
                    Assert.isNull(yeelightUserDto, ResultCodeEnum.用户已存在.code, I18nUtil.getMessage("ResultCode.用户已存在"));
                } else {
                    // 如果exist为false，则检查用户是否不存在，如果不存在则返回错误
                    Assert.notNull(yeelightUserDto, ResultCodeEnum.手机号未注册.code, I18nUtil.getMessage("ResultCode.手机号未注册"));
                }
            }
        } else {
            // 旧接口需要保留以兼容以前的app版本，但我们认为始终只愿意停留在老的app版本的用户必然是已经注册为用户的手机号，对于这部分用户，我们强制校验手机号是否存在，不存在则不发送验证码
            // 对于不包含合法sourceId的请求，检查手机号是否存在，不存在则返回虚假的验证码信息
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
            if (Objects.isNull(yeelightUserDto)) {
                // 欺骗
                CaptchaResult jokeResult = new CaptchaResult();
                jokeResult.setCaptcha(RandomUtil.randomNumbers(6));
                jokeResult.setCaptchaKey(RandomUtil.randomString(32));
                jokeResult.setMessage("行为已记录并上报, 请不要非法请求!");
                return Result.success(jokeResult);
            }
        }

        // 生成手机验证码
        CaptchaResult captchaResult = captchaMessageHelper.generatePhoneNumberCaptcha(phoneNumber, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        if (captchaResult.isSuccess()) {
            captchaResult.clearCaptcha(); // 清除敏感信息
            return Result.success(captchaResult);
        }

        // 验证码生成失败，返回失败结果
        return Result.failure(ResultCodeEnum.获取手机验证码失败.code, captchaResult.getMessage());
    }


    /**
     * 获取手机验证码安全动态参数
     * 该接口用于生成用于获取手机验证码的安全动态参数。这些参数包括动态密钥和时间戳等，
     * 用于后续验证码的生成和验证过程中的安全性增强。
     * <p>
     * 请求频率限制：同一IP每分钟最多获取2次安全动态参数。
     * 请求计数限制：同一IP每天最多获取100次安全动态参数。
     *
     * @param request 无需传入，该参数在当前方法中未使用，但保留以符合接口规范。
     * @return Result<Map<String, Object>> 返回包含安全动态参数的结果对象。
     *         其中，Map中包含"key"和"timestamp"两个字段：
     *         - "key" 是生成的动态密钥，用于验证码的生成和验证；
     *         - "timestamp" 是当前请求的时间戳，用于防止请求重放。
     */
    @PostMapping("/mobile/captcha/key")
    @ResponseBody
    @RateLimit(prefix = "user:rateLimit:mobileCaptchaKeyRateLimit:", rate = 2.0/60, burst = 120.0, expire = 60, timeout = 3, limitType = LimitType.IP)
    @CountLimit(prefix = "user:countLimit:mobileCaptchaKeyRateLimit:", limit = 100, period = 86400, limitType = LimitType.IP)
    public Result<Map<String, Object>> mobileCaptchaKey(HttpServletRequest request) {
        long currentTimeMillis = System.currentTimeMillis();

        // 准备基础参数并生成相关密钥
        Map<String, Object> map = new HashMap<>(2);
        // 获取请求IP
        map.put("ip", IPUtils.getRealIp(request));
        // 记录当前时间戳
        map.put("timestamp", currentTimeMillis);

        // 生成基础密钥
        String key = RedisKeyUtils.generateKey(map);
        // 生成动态密钥
        String dynamicKey = RedisKeyUtils.generateKey(map);
        // 将基础密钥加入到参数Map中
        map.put("key", key);
        // 将包含密钥和时间戳的Map存储到Redis中，用于验证验证码时使用
        redisManager.redisTemplate().opsForValue().set(RedisKeyUtils.getMobileCaptchaKey(key), map, Duration.ofMinutes(1L));
        Map<String, Object> result = new HashMap<>(4);
        // 将动态密钥放入结果Map中
        result.put("key", dynamicKey);
        // 将时间戳放入结果Map中
        result.put("timestamp", currentTimeMillis);

        // 此处是生成验证码票据(ticket)的示例代码，但当前注释掉了
        //result.put("ticket", IdTokenEncryptUtils.encrypt("clientType=App&phoneNumber=15194270213&requestId=11c1-256p-xxxx-xxxx&timestamp="+currentTimeMillis,dynamicKey));

        // 返回包含动态密钥和时间戳的结果
        return Result.success(result);
    }


    /**
     * 获取手机验证码
     * 请求频率限制：同一手机号每分钟最多获取2次验证码，同一IP每分钟最多获取4次验证码。
     * 请求计数限制：同一手机号每天最多获取30次验证码，同一IP每天最多获取100次验证码。
     *
     * @param mobileCaptchaRequest 手机验证码请求对象，包含手机号码和时间戳等信息。
     * @param request 无需传入，用于获取客户端IP地址。
     * @return Result<CaptchaResult> 验证码结果，包含验证码是否发送成功及其相关信息。
     */
    @PostMapping("/mobile/captcha")
    @ResponseBody
    @RateLimit(key = "#{mobileCaptchaRequest.phoneNumber}",prefix = "user:rateLimit:mobileCaptchaRateLimit:", rate = 2.0/60, burst = 120, expire = 60, timeout = 3, limitType = LimitType.CUSTOM_AND_IP)
    @RateLimit(key = "#{mobileCaptchaRequest.phoneNumber}",prefix = "user:rateLimit:mobileCaptchaRateLimitCustom:", rate = 2.0/60, burst = 120, expire = 60, timeout = 3, limitType = LimitType.CUSTOM)
    @RateLimit(prefix = "user:rateLimit:mobileCaptchaRateLimitIp:", rate = 4.0/60, burst = 120.0, expire = 60, timeout = 3, limitType = LimitType.IP)
    @CountLimit(key = "#{mobileCaptchaRequest.phoneNumber}", prefix = "user:countLimit:mobileCaptchaCountLimit:", limit = 30, period = 86400, limitType = LimitType.CUSTOM_AND_IP)
    @CountLimit(key = "#{mobileCaptchaRequest.phoneNumber}", prefix = "user:countLimit:mobileCaptchaCountLimitCustom:", limit = 30, period = 86400, limitType = LimitType.CUSTOM)
    @CountLimit(prefix = "user:countLimit:mobileCaptchaCountLimitCustomIp:", limit = 100, period = 86400, limitType = LimitType.IP)
    @BizOperateLog(bizId = "{#mobileCaptchaRequest.phoneNumber}", opType = OpTypeEnums.读取, bizType = BizTypeEnums.公共, bizSubType = "发送手机号验证码", bizBody = "{#mobileCaptchaRequest}")
    public Result<CaptchaResult> mobileCaptcha(@RequestBody @Valid MobileCaptchaRequest mobileCaptchaRequest, HttpServletRequest request) {
        // 根据请求信息生成Redis中验证码的唯一标识key，并从Redis中获取对应的验证码信息
        Map<String, Object> map = new HashMap<>(2);
        // 获取客户端IP地址
        map.put("ip", IPUtils.getRealIp(request));
        // 获取请求时间戳
        map.put("timestamp", mobileCaptchaRequest.getTimestamp());
        String key = RedisKeyUtils.generateKey(map);
        Map<String, Object> storedMap = (Map<String, Object>) redisManager.redisTemplate().opsForValue().get(RedisKeyUtils.getMobileCaptchaKey(key));

        Assert.notNull(storedMap, ResultCodeEnum.获取手机验证码失败.code, "key not exist or expired");
        // 对接收到的ticket进行解密，验证其合法性
        String decryptedText = IdTokenEncryptUtils.decrypt(mobileCaptchaRequest.getTicket(), key);
        Assert.notBlank(decryptedText, ResultCodeEnum.获取手机验证码失败.code, "wrong ticket");

        // 解析解密后的参数，验证手机号码是否一致
        Map<String, String> paramMap = Arrays.stream(decryptedText.split("&"))
                .map(s -> s.split("="))
                .collect(Collectors.toMap(paramArr -> paramArr[0], strings -> strings.length == 2 ? strings[1] : ""));
        Assert.notNull(paramMap, ResultCodeEnum.获取手机验证码失败.code, "wrong ticket");
        Assert.isNotTrue(mobileCaptchaRequest.getPhoneNumber().equals(paramMap.get("phoneNumber")), ResultCodeEnum.获取手机验证码失败.code, "wrong phone number for current ticket");

        // 根据请求中的exist参数判断是校验用户存在还是不存在
        if (Objects.nonNull(mobileCaptchaRequest.getExist())) {
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(mobileCaptchaRequest.getPhoneNumber());
            if (Boolean.TRUE.equals(mobileCaptchaRequest.getExist())) {
                // 校验用户是否存在
                Assert.isNull(yeelightUserDto, ResultCodeEnum.用户已存在.code, I18nUtil.getMessage("ResultCode.用户已存在"));
            } else {
                // 校验用户是否不存在
                Assert.notNull(yeelightUserDto, ResultCodeEnum.手机号未注册.code, I18nUtil.getMessage("ResultCode.手机号未注册"));
            }
        }

        // 生成并发送手机验证码
        CaptchaResult captchaResult = captchaMessageHelper.generatePhoneNumberCaptcha(mobileCaptchaRequest.getPhoneNumber(), SecurityConstants.CAPTCHA_CACHE_PREFIX);
        if (captchaResult.isSuccess()) {
            captchaResult.clearCaptcha(); // 清除相关的验证码信息
            return Result.success(captchaResult);
        }

        // 发送失败，返回失败结果
        return Result.failure(ResultCodeEnum.获取手机验证码失败.code, captchaResult.getMessage());
    }


    /**
     * 获取手机验证码for安心加(已废弃)
     * 为安心加做的临时解决方案， 后面需要废弃掉
     * @param phoneNumber 手机号
     * @param exist 是否检查手机号存在
     * @return Result<CaptchaResult> 验证码结果
     */
    @GetMapping("/phone_number/aciga_captcha")
    @ResponseBody
    @BizOperateLog(bizId = "{#phoneNumber}", opType = OpTypeEnums.读取, bizType = BizTypeEnums.公共, bizSubType = "发送安心加手机验证码", bizBody = "手机号：{#phoneNumber},exist：{#exist}")
    @Deprecated
    public Result<CaptchaResult> phoneNumberCaptchaForAciga(@RequestParam String phoneNumber, @RequestParam(required = false) Boolean exist, HttpServletRequest request) {
        if (Objects.nonNull(exist)) {
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
            if (exist) {
                Assert.isNull(yeelightUserDto, ResultCodeEnum.用户已存在.code, I18nUtil.getMessage("ResultCode.用户已存在"));
            } else {
                Assert.notNull(yeelightUserDto, ResultCodeEnum.手机号未注册.code, I18nUtil.getMessage("ResultCode.手机号未注册"));
            }
        }
        CaptchaResult captchaResult = captchaMessageHelper.generatePhoneNumberCaptchaForAciga(phoneNumber, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        if (captchaResult.isSuccess()) {
            captchaResult.clearCaptcha();
            return Result.success(captchaResult);
        }

        return Result.failure(ResultCodeEnum.获取手机验证码失败.code, captchaResult.getMessage());
    }

    /**
     * 获取邮箱验证码
     * 该接口用于生成并发送邮箱验证码。如果请求时指定了邮箱存在性检查（exist参数），则会先进行邮箱存在性验证。
     * @param email 邮箱地址，用于发送验证码。
     * @param exist 是否检查该邮箱是否已注册。如果为true，表示检查邮箱是否存在；如果为false，表示检查邮箱是否不存在。该参数为可选参数。
     * @return Result<CaptchaResult> 验证码结果，包含验证码是否生成成功及其相关信息。
     */
    @GetMapping("/email/captcha")
    @ResponseBody
    @BizOperateLog(bizId = "{#email}", opType = OpTypeEnums.读取, bizType = BizTypeEnums.公共, bizSubType = "发送邮箱验证码", bizBody = "邮箱：{#email},exist：{#exist}")
    public Result<CaptchaResult> emailCaptcha(@RequestParam String email, @RequestParam(required = false) Boolean exist) {
        // 根据exist参数进行邮箱存在性检查
        if (Objects.nonNull(exist)) {
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByEmail(email);
            // 如果要求邮箱存在，但邮箱不存在
            if (exist) {
                Assert.isNull(yeelightUserDto, ResultCodeEnum.用户已存在.code, I18nUtil.getMessage("ResultCode.用户已存在"));
            // 如果要求邮箱不存在，但邮箱已存在
            } else {
                Assert.notNull(yeelightUserDto, ResultCodeEnum.邮箱未注册.code, I18nUtil.getMessage("ResultCode.邮箱未注册"));
            }
        }
        // 生成邮箱验证码
        CaptchaResult captchaResult = captchaMessageHelper.generateEmailCaptcha(email, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        // 验证码生成结果处理
        if (captchaResult.isSuccess()) {
            // 清除验证码相关信息，防止泄露
            captchaResult.clearCaptcha();
            return Result.success(captchaResult);
        }

        return Result.failure(ResultCodeEnum.获取验证码失败.code, captchaResult.getMessage());
    }


    /**
     * 验证码前置校验
     * 这个方法用于校验提供的验证码是否有效。它首先检查验证码、验证码键和验证码缓存前缀是否为空，
     * 如果不为空，则调用captchaMessageHelper来检查验证码的有效性。
     *
     * @param captcha 用户输入的验证码
     * @param captchaKey 用于标识验证码的键
     * @param number 需要验证的手机号或邮箱
     * @param captchaCachePrefix 验证码在缓存中的前缀，用于区分不同类型的验证码
     *
     * @return Result<CaptchaResult> 校验结果，包含校验是否成功及其相关信息
     */
    @PostMapping("/captcha/pre_check")
    @ResponseBody
    public Result<CaptchaResult> preCheckCaptcha(@RequestParam String captcha, @RequestParam String captchaKey, @RequestParam String number, @RequestParam String captchaCachePrefix) {
        // 检查输入参数是否为空
        Assert.notBlank(captcha, ResultCodeEnum.验证码错误.code, I18nUtil.getMessage("ResultCode.验证码错误"));
        Assert.notBlank(captchaKey, ResultCodeEnum.验证码错误.code, I18nUtil.getMessage("ResultCode.验证码错误"));
        Assert.notBlank(captchaCachePrefix, ResultCodeEnum.验证码错误.code, I18nUtil.getMessage("ResultCode.验证码错误"));

        // 调用helper类检查验证码的有效性
        CaptchaResult captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, captcha, number, captchaCachePrefix, true);
        if (captchaResult.isSuccess()) {
            return Result.success(captchaResult);
        } else {
            return Result.failure(ResultCodeEnum.验证码错误.code, captchaResult.getMessage());
        }
    }

    /**
     * 隐私协议页面
     * <p>
     * 根据用户请求的主题（如果提供）来展示对应主题的隐私协议页面。如果用户没有指定主题，
     * 则默认展示白色主题的隐私协议页面。
     * <p>
     * 根据用户的语言设置，返回对应语言版本的隐私协议页面。
     *
     * @param model 用于在视图和控制器之间传递数据的模型对象。
     * @param themes 用户请求的主题，可选参数。如果用户指定了主题，则该页面将展示所指定的主题。
     * @return 返回隐私协议页面的名称，根据用户的语言选择返回不同语言版本的页面。
     */
    @Deprecated
    @GetMapping("/privacy")
    public String privacy(Model model, @RequestParam(required = false) String themes) {
        // 为模型添加动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 如果用户指定了主题，则使用指定的主题，否则使用默认主题"white"
        if (Objects.nonNull(themes)) {
            model.addAttribute("themes", themes);
        } else {
            model.addAttribute("themes", "white");
        }

        // 获取当前用户语言设置
        String lang = LocaleContextHolder.getLocale().getLanguage();

        // 根据用户的语言选择返回相应的隐私协议页面
        if (Constants.DEFAULT_LANG.equalsIgnoreCase(lang)) {
            return UserVendorHolder.attachVendor("privacy").toLowerCase();
        } else {
            return UserVendorHolder.attachVendor("privacy-en").toLowerCase();
        }
    }


    /**
     * 用户协议页面
     * 该方法用于展示用户协议页面，根据用户提供的主题、应用、语言和国家信息来定制页面内容。
     * @param model 用于在视图和控制器之间传递数据的Model对象
     * @param themes 用户选择的主题，如果未选择则默认为"white"
     * @param app 用户应用的标识，如果未提供则不添加到模型中
     * @param language 用户选择的语言，如果未选择则默认为系统当前语言
     * @param country 用户选择的国家，如果未选择则默认为"CN"
     * @return 返回协议页面的视图名称
     */
    @Deprecated
    @GetMapping("/license")
    public String license(Model model, @RequestParam(required = false) String themes,@RequestParam(required = false) String app,@RequestParam(required = false) String language,@RequestParam(required = false) String country) {
        // 向模型中添加动态视图属性
        DynamicStaticResourceConfig.dynamicViewAttributes(model);

        // 设置主题，默认为"white"
        model.addAttribute("themes", StringUtils.defaultIfBlank(themes, "white"));
        // 设置国家，默认为"CN"
        model.addAttribute("country", StringUtils.defaultIfBlank(country, "CN"));
        // 如果提供了应用标识，则添加到模型中
        Optional.ofNullable(app).ifPresent(a -> model.addAttribute("app", a));

        // 如果提供了语言，则添加到模型中
        Optional.ofNullable(language).ifPresent(l -> model.addAttribute("language", l));

        // 根据当前语言环境决定返回的页面版本（中文或英文）
        String lang = LocaleContextHolder.getLocale().getLanguage();
        if (Constants.DEFAULT_LANG.equalsIgnoreCase(lang)) {
            // 如果是中文，则返回对应的中文协议页面
            return UserVendorHolder.attachVendor("license").toLowerCase();
        } else {
            // 否则返回对应的英文协议页面
            return UserVendorHolder.attachVendor("license-en").toLowerCase();
        }
    }


    /**
     * 修改用户信息
     * 该接口允许通过传入更新请求来修改用户的信息。如果提供了电话号码或电子邮件，会分别对提供的验证码进行验证。
     *
     * @param updateUserRequest 请求参数，包含要更新的用户信息和可能的验证码信息。
     * @return Result 返回操作结果，成功则返回一个成功的Result对象，失败则返回包含错误信息的Result对象。
     */
    @PutMapping("/user")
    @ResponseBody
    @BizOperateLog(bizId = "{#updateUserRequest.id}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "验证码方式修改用户信息(可抛异常)", bizBody = "{#updateUserRequest}")
    public Result<?> updateUser(@NotNull @Valid @RequestBody UpdateUserRequest updateUserRequest) {
        try {
            // 验证电话号码和电子邮件，如果提供，必须包含有效的验证码。
            if (StringUtils.isNotBlank(updateUserRequest.getPhoneNumber())) {
                Assert.notBlank(updateUserRequest.getCaptcha(), ResultCodeEnum.请输入验证码.code, I18nUtil.getMessage("ResultCode.请输入验证码"));
                Assert.notBlank(updateUserRequest.getCaptchaKey(), ResultCodeEnum.请输入验证码.code, I18nUtil.getMessage("ResultCode.请输入验证码"));
                checkCaptcha(updateUserRequest.getCaptcha(), updateUserRequest.getCaptchaKey(), updateUserRequest.getPhoneNumber());
            }

            if (StringUtils.isNotBlank(updateUserRequest.getEmail())) {
                Assert.notBlank(updateUserRequest.getCaptcha(), ResultCodeEnum.请输入验证码.code, I18nUtil.getMessage("ResultCode.请输入验证码"));
                Assert.notBlank(updateUserRequest.getCaptchaKey(), ResultCodeEnum.请输入验证码.code, I18nUtil.getMessage("ResultCode.请输入验证码"));
                checkCaptcha(updateUserRequest.getCaptcha(), updateUserRequest.getCaptchaKey(), updateUserRequest.getEmail());
            }

            YeelightUserDto user = yeelightUserReadService.findUserById(updateUserRequest.getId());
            UserJudge.checkUserExist(user);
            if (StringUtils.isNotBlank(updateUserRequest.getPhoneNumber()) && !StringUtils.equals(updateUserRequest.getPhoneNumber(), user.getPhoneNumber()) ) {
                yeelightUserWriteService.updatePhoneNumber(updateUserRequest.getId(), updateUserRequest.getPhoneNumber());
            }
            if (StringUtils.isNotBlank(updateUserRequest.getEmail()) && !StringUtils.equals(updateUserRequest.getEmail(), user.getEmail())) {
                yeelightUserWriteService.updateEmail(updateUserRequest.getId(), updateUserRequest.getEmail());
            }
            if (StringUtils.isNotBlank(updateUserRequest.getName()) && !StringUtils.equals(updateUserRequest.getName(), user.getName()) ) {
                yeelightUserWriteService.updateName(updateUserRequest.getId(), updateUserRequest.getName());
            }
            if (StringUtils.isNotBlank(updateUserRequest.getAvatar()) && !StringUtils.equals(updateUserRequest.getAvatar(), user.getAvatar()) ) {
                yeelightUserWriteService.updateAvatar(updateUserRequest.getId(), updateUserRequest.getAvatar());
            }

            return Result.success();
        } catch (Exception e) {
            // 捕获异常，返回失败结果，包含异常信息。
            return Result.failure(e.getMessage());
        }
    }


    /**
     * 校验验证码。
     * 该方法用于验证用户输入的验证码是否正确。根据输入的验证码类型，调用不同的验证码校验逻辑。
     * 如果验证码校验失败，会抛出CaptchaException异常。
     *
     * @param inputCaptcha 用户输入的验证码。
     * @param captchaKey  验证码的键，用于从缓存中获取验证码信息。
     * @param number      验证码关联的数字，用于一些特定类型的验证码校验。
     * @throws CaptchaException 如果验证码校验失败，抛出此异常。
     */
    private void checkCaptcha(String inputCaptcha, String captchaKey, String number) {
        CaptchaResult captchaResult;
        // 根据输入的验证码类型，选择不同的校验逻辑
        if (SecurityConstants.CHECK_LAST_RESULT.equals(inputCaptcha)) {
            // 如果是检查最后一次结果，则只校验验证码键对应的最后一次结果
            captchaResult = captchaMessageHelper.checkLastResult(captchaKey, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        } else {
            // 否则，进行常规的验证码校验，包括输入的验证码、验证码键和关联的数字
            captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, inputCaptcha, number,
                    SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
        }

        // 验证码校验结果处理，如果失败则抛出异常
        if (!captchaResult.isSuccess()) {
            throw new CaptchaException(captchaResult.getMessage());
        }
    }


    /**
     * 验证码方式注册用户。
     * 对用户提交的注册请求进行处理，包括验证码验证、密码一致性检查以及用户信息的插入。
     *
     * @param registerUserRequest 包含注册用户所需信息的请求对象，如手机号、验证码、密码等。
     * @return Result<YeelightUserDto> 注册结果，成功返回结果对象，失败返回错误信息。
     */
    @PostMapping("/register")
    @ResponseBody
    @BizOperateLog(bizId = "{#registerUserRequest.phoneNumber}", opType = OpTypeEnums.新增, bizType = BizTypeEnums.用户, bizSubType = "验证码方式注册用户", bizBody = "{#registerUserRequest}")
    public Result<YeelightUserDto> register(@NotNull @Valid @RequestBody RegisterUserRequest registerUserRequest) {
        // 验证验证码和密码
        String inputCaptcha = registerUserRequest.getCaptcha();
        String captchaKey = registerUserRequest.getCaptchaKey();

        // 检查验证码输入是否为空
        Assert.notBlank(inputCaptcha, ResultCodeEnum.请输入手机验证码.code, I18nUtil.getMessage("ResultCode.请输入手机验证码"));
        Assert.notBlank(captchaKey, ResultCodeEnum.请输入手机验证码.code, I18nUtil.getMessage("ResultCode.请输入手机验证码"));

        // 验证密码是否一致
        Assert.isNotTrue(registerUserRequest.getPassword().equals(registerUserRequest.getPassword2()), I18nUtil.getMessage("ResultCode.密码不一致"));

        // 验证验证码的有效性
        checkCaptcha(inputCaptcha, captchaKey, registerUserRequest.getPhoneNumber());

        // 检查用户是否已存在
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(registerUserRequest.getPhoneNumber());
        if (yeelightUserDto == null) {
            // 用户不存在，插入新用户
            Long yeelightId = yeelightUserWriteService.create(CreateUserRequest.builder()
                    .phoneNumber(registerUserRequest.getPhoneNumber())
                    .password(registerUserRequest.getPassword())
                    .build());
            return Result.success(yeelightUserReadService.findUserById(yeelightId));
        } else {
            // 用户已存在，抛出异常
            throw new BizException(I18nUtil.getMessage("ResultCode.用户已存在"));
        }
    }

    /**
     * 通过手机号验证码方式重置密码
     * <p>
     * 接收一个包含手机号、验证码和新密码的请求，验证验证码的有效性，并且如果手机号存在，重置该手机号对应的密码。
     * </p>
     * @param request 重置密码的请求对象，包含手机号、验证码和新密码。
     * @return Result 返回一个结果对象，包含操作是否成功的信息和可能的错误消息。
     */
    @PostMapping("/reset")
    @ResponseBody
    @BizOperateLog(bizId = "{#resetPasswordByPhoneNumberRequest.phoneNumber}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "验证码方式重置密码", bizBody = "{#request}")
    public Result<?> reset(@NotNull @Valid @RequestBody ResetPasswordByPhoneNumberRequest request) {
        String phoneNumber = request.getPhoneNumber();
        // 验证验证码
        String inputCaptcha = request.getCaptcha();
        String captchaKey = request.getCaptchaKey();

        // 验证码校验逻辑，确保验证码有效且未过期
        checkCaptcha(inputCaptcha, captchaKey, phoneNumber);

        // 根据手机号查找用户，如果存在，则重置密码
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
        Assert.notNull(yeelightUserDto, I18nUtil.getMessage("ResultCode.手机号未注册"));

        // 更新用户密码
        yeelightUserWriteService.updatePassword(yeelightUserDto.getId(), request.getPassword());

        // 密码重置成功，返回成功结果
        return Result.success(I18nUtil.getMessage("User.Action.Reset.Success"));
    }


    /**
     * 通过邮箱重置密码的接口。
     * 接收一个邮箱地址和相关的验证码信息，验证通过后重置用户的密码。
     *
     * @param request 包含重置密码所需信息的请求对象，包括邮箱、验证码等。
     * @return Result 包含操作结果的状态码和消息，成功则消息提示密码重置成功，失败则提供错误详情。
     */
    @PostMapping("/resetByEmail")
    @ResponseBody
    @BizOperateLog(bizId = "{#resetPassword.email}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "验证码方式通过邮箱重置密码", bizBody = "{#request}")
    public Result<?> resetByEmail(@NotNull @Valid @RequestBody ResetPasswordByEmailRequest request) {
        String email = request.getEmail();
        // 验证码校验逻辑
        String inputCaptcha = request.getCaptcha();
        String captchaKey = request.getCaptchaKey();
        // 执行验证码验证逻辑
        checkCaptcha(inputCaptcha, captchaKey, email);

        // 根据邮箱查找用户，如果存在则更新密码
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByEmail(email);
        Assert.notNull(yeelightUserDto, I18nUtil.getMessage("ResultCode.邮箱未注册"));

        yeelightUserWriteService.updatePassword(yeelightUserDto.getId(), request.getPassword());

        // 密码重置成功，返回成功结果
        return Result.success(I18nUtil.getMessage("User.Action.Reset.Success"));
    }


    /**
     * 上传头像到服务器。
     *
     * @param file 用户上传的头像文件，通过multipart/form-data类型提交。
     * @return Result<String> 类型的结果对象，包含上传成功后的头像地址。
     */
    @PostMapping("/avatar/upload")
    @ResponseBody
    public Result<String> uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        // 检查上传的文件是否为空
        Assert.notNull(file, I18nUtil.getMessage("ResultCode.Common.FILE_EMPTY"));

        // 获取文件类型
        String fileType = getExtension(file);
        // 生成文件存储路径，按日期归档
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyy-MM-dd"));
        String fileName = "avatars/" + today + "/" + UUID.randomUUID().toString() + "." + fileType;

        // 上传文件到指定的文件系统，并获取文件的访问URL
        String fileUrl = FileSystemUtils.upload(FileSystemUtils.getConfig(FileSystemConfig.ConfigType.YOS_USER), new FileObject(fileName, file.getBytes()));

        // 返回上传成功的文件URL
        return Result.success(fileUrl);
    }


    private String getExtension(MultipartFile file) {
        Assert.notNull(file.getOriginalFilename(), "无法获取文件名称");
        String fileType;
        if (Objects.isNull(file.getOriginalFilename())) {
            fileType = "jpg";
        } else {
            fileType = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        }

        Assert.isTrue(!Lists.newArrayList("png","jpg","jpeg").contains(fileType), "不支持该文件类型!");
        Assert.isTrue(file.getSize() > 2097152L, "文件最大为2M");
        return fileType.toUpperCase();
    }
}
