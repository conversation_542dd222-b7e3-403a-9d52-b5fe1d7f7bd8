package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.enums.ScanLoginStatusEnum;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.RedisKeyUtils;
import com.yeelight.service.user.server.utils.TokenUtils;
import com.yeelight.service.user.server.vo.ScanLoginQrCodeStatusVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotBlank;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 扫码登录
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/user/scan-login")
public class ScanLoginController extends BaseController {
    @Resource
    private TokenService tokenService;

    @Resource
    private OauthKnifeService oauthKnifeService;

    @Resource
    private RedisManager redisManager;

    private static final Long EXPIRE_IN = 5 * 60 * 1000L;

    /**
     * 获取二维码
     * <p>
     * 生成一个用于扫码登录的二维码，该二维码包含设备号信息，并通过Redis进行缓存管理。
     *
     * @param device 设备号，用于标识生成二维码的设备。
     * @return 返回包含扫码登录状态信息的对象ScanLoginQrCodeStatusVo。
     */
    @PostMapping(value = "/query/qrcode/{device}")
    @BizOperateLog(bizId = "{#device}", opType = OpTypeEnums.新增, bizType = BizTypeEnums.扫码登陆, bizSubType = "获取二维码uuid", bizBody = "{#device}")
    public Result<ScanLoginQrCodeStatusVo> queryQrCode(@NotBlank @PathVariable("device") String device) {
        // 生成二维码ID
        String qrCodeId = UUID.randomUUID().toString();
        // 创建扫码登录的二维码状态对象
        ScanLoginQrCodeStatusVo scanLoginQrCodeStatusVo = new ScanLoginQrCodeStatusVo(device, qrCodeId);
        // 设置二维码相关属性
        scanLoginQrCodeStatusVo.setDevice(device);
        scanLoginQrCodeStatusVo.setQrCodeId(qrCodeId);
        scanLoginQrCodeStatusVo.setExpireIn(EXPIRE_IN);
        scanLoginQrCodeStatusVo.setCreateAt(Instant.now().toEpochMilli());
        scanLoginQrCodeStatusVo.setExpireAt(Instant.now().plusMillis(EXPIRE_IN).toEpochMilli());
        scanLoginQrCodeStatusVo.setStatus(ScanLoginStatusEnum.CREATED);
        // 将二维码状态信息存储到Redis中，设置过期时间
        redisManager.redisTemplate().opsForValue().set(RedisKeyUtils.getScanLoginQrCodeStatusKey(qrCodeId), scanLoginQrCodeStatusVo, Duration.ofMillis(EXPIRE_IN));
        // 返回生成的二维码状态信息
        return Result.success(scanLoginQrCodeStatusVo);
    }


    /**
     * 获取二维码uuid的相关信息
     * 主要用于查询并校验二维码的状态，提供扫码登录时的二维码信息验证。
     *
     * @param qrCodeId 二维码的唯一标识符（UUID），不能为空。
     * @return Result<ScanLoginQrCodeStatusVo> 扫码登录二维码状态信息的对象，包含二维码的状态等信息。
     */
    @PostMapping(value = "/check/qrcode/{qrCodeId}")
    @BizOperateLog(bizId = "{#qrCodeId}", opType = OpTypeEnums.读取, bizType = BizTypeEnums.扫码登陆, bizSubType = "获取二维码信息", bizBody = "{#qrCodeId}")
    public Result<ScanLoginQrCodeStatusVo> checkQrCode(@NotBlank @PathVariable("qrCodeId") String qrCodeId) {
        // 查询并校验二维码状态
        ScanLoginQrCodeStatusVo scanLoginQrCodeStatusVo = queryAndCheckQrCodeStatus(qrCodeId);
        return Result.success(scanLoginQrCodeStatusVo);
    }


    /**
     * 扫码成功
     * 当用户扫描登录二维码成功时，更新二维码状态为已扫描，并验证token的有效性。
     *
     * @param qrCodeId 二维码的唯一标识符（UUID）。
     * @param request HttpServletRequest对象，用于提取token（如果请求参数中未提供token）。
     * @param token 授权设备的token，如果为空，则从请求中提取。
     * @return Result对象，表示操作的结果，成功则返回success。
     */
    @PostMapping(value = "/qrcode/scanned/{qrCodeId}")
    @BizOperateLog(bizId = "{#qrCodeId}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.扫码登陆, bizSubType = "扫码成功", bizBody = "{#token}")
    public Result<?> qrCodeScanned(@NotBlank @PathVariable("qrCodeId") String qrCodeId, HttpServletRequest request, String token) {
        // 如果token为空，则从请求中提取token
        token = StringUtils.isBlank(token) ? TokenUtils.extractToken(request) : token;
        // 验证token的有效性
        tokenService.checkValidToken(token);

        // 查询并检查二维码状态，设置状态为已扫描
        ScanLoginQrCodeStatusVo scanLoginQrCodeStatusVo = queryAndCheckQrCodeStatus(qrCodeId);
        scanLoginQrCodeStatusVo.setStatus(ScanLoginStatusEnum.SCANNED);
        // 更新Redis中二维码的状态为已扫描
        redisManager.redisTemplate().opsForValue().setIfPresent(RedisKeyUtils.getScanLoginQrCodeStatusKey(qrCodeId), scanLoginQrCodeStatusVo);

        return Result.success();
    }


    /**
     * 确认登录
     * 该方法用于处理用户确认登录的请求，通过验证二维码、来源和设备授权token来完成登录确认。
     *
     * @param qrCodeId 二维码的唯一标识符（UUID），用于确认对应的登录二维码。
     * @param source 扫码的来源信息，可选参数，用于记录登录场景。
     * @param request HttpServletRequest对象，用于获取请求相关信息，例如token。
     * @param token 授权设备的token，可选参数，若未提供则从请求中提取。
     * @return Result 对象，表示操作的结果，成功则返回success。
     */
    @PostMapping(value = "/qrcode/confirm/{qrCodeId}")
    @BizOperateLog(bizId = "{#qrCodeId}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.扫码登陆, bizSubType = "确认登录", bizBody = "source:{#source},token：{#token}")
    public Result<?> qrCodeConfirm(@NotBlank @PathVariable("qrCodeId") String qrCodeId, String source, HttpServletRequest request, String token) {
        // 若未提供token，则从请求中提取
        token = StringUtils.isBlank(token) ? TokenUtils.extractToken(request) : token;
        // 验证token的有效性
        tokenService.checkValidToken(token);

        // 查询并校验二维码状态，准备更新为确认登录状态
        ScanLoginQrCodeStatusVo scanLoginQrCodeStatusVo = queryAndCheckQrCodeStatus(qrCodeId);
        // 更新二维码状态为确认登录，并记录来源
        scanLoginQrCodeStatusVo.setStatus(ScanLoginStatusEnum.CONFIRM);
        scanLoginQrCodeStatusVo.setSource(source);
        // 将更新后的状态保存回Redis
        redisManager.redisTemplate().opsForValue().setIfPresent(RedisKeyUtils.getScanLoginQrCodeStatusKey(qrCodeId), scanLoginQrCodeStatusVo);
        // 返回操作成功的结果
        return Result.success();
    }


    /**
     * 登录
     * 通过二维码进行登录的操作。
     * 使用给定的二维码ID和设备授权的token完成登录流程，
     * 如果提供的token无效或不存在，会返回错误信息。
     *
     * @param qrCodeId 二维码的唯一标识符（UUID）。
     * @param request HttpServletRequest对象，用于提取token（如果未直接提供）。
     * @param token 授权设备的token，如果为空，则会尝试从请求中提取。
     * @return Result<ScanLoginQrCodeStatusVo> 登录结果，包含登录状态和相关token信息。
     */
    @PostMapping(value = "/qrcode/login/{qrCodeId}")
    @BizOperateLog(bizId = "{#qrCodeId}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.扫码登陆, bizSubType = "登录", bizBody = "{#token}")
    public Result<ScanLoginQrCodeStatusVo> qrCodeLogin(@NotBlank @PathVariable("qrCodeId") String qrCodeId, HttpServletRequest request, String token) {
        // 尝试从请求中提取token，如果提供的token为空。
        token = extractToken(token, request);
        // 根据token获取认证信息。
        OAuth2AuthenticationDto authentication = tokenService.getAuthenticationByToken(token);

        // 检查认证信息是否存在和有效。
        Assert.notNull(authentication, I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
        Assert.notNull(authentication.getUserAuthentication(), I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));

        // 查询并校验二维码状态。
        ScanLoginQrCodeStatusVo scanLoginQrCodeStatusVo = queryAndCheckQrCodeStatus(qrCodeId);

        // 准备额外参数，用于token增强。
        Map<String, String> extendParameters = new HashMap<>(16);
        extendParameters.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, scanLoginQrCodeStatusVo.getDevice());
        extendParameters.put("qrCodeId", scanLoginQrCodeStatusVo.getQrCodeId());
        // 将额外参数附加到token请求中。
        TokenUtils.attachAdditionalInformationForTokenRequest(extendParameters, authentication.getUserAuthentication(), authentication.getStoredRequest(), request);
        // 创建访问token。，直接使用当前token的client id， 也可以指定client id
        YeelightUser user = (YeelightUser) authentication.getUserAuthentication().getPrincipal();
        OAuth2Token accessToken = oauthKnifeService.fastCreateAccessToken(authentication.getStoredRequest().getClientId(), user, extendParameters);
        ScanLoginQrCodeStatusVo.Token qrcodeToken = new ScanLoginQrCodeStatusVo.Token(accessToken);
        scanLoginQrCodeStatusVo.setToken(qrcodeToken);
        // 更新登录状态为已登录。
        scanLoginQrCodeStatusVo.setStatus(ScanLoginStatusEnum.LOGIN);

        // 将最新的登录状态保存到Redis中。
        redisManager.redisTemplate().opsForValue().setIfPresent(RedisKeyUtils.getScanLoginQrCodeStatusKey(qrCodeId), scanLoginQrCodeStatusVo);
        // 返回登录成功信息，包含登录状态和token。
        return Result.success(scanLoginQrCodeStatusVo);
    }


    /**
     * 查询并检查二维码登录状态。
     *
     * @param qrCodeId 二维码ID，用于查询二维码的状态。
     * @return ScanLoginQrCodeStatusVo 二维码登录状态对象，包含登录状态和过期时间等信息。
     * @throws BizException 如果二维码状态为过期，则抛出业务异常。
     */
    private ScanLoginQrCodeStatusVo queryAndCheckQrCodeStatus(String qrCodeId) throws BizException {
        // 从Redis中根据二维码ID查询登录状态信息
        ScanLoginQrCodeStatusVo scanLoginQrCodeStatusVo = (ScanLoginQrCodeStatusVo) redisManager.redisTemplate().opsForValue().get(RedisKeyUtils.getScanLoginQrCodeStatusKey(qrCodeId));
        // 确保查询到的状态对象不为null，否则抛出过期异常
        Assert.notNull(scanLoginQrCodeStatusVo, ScanLoginStatusEnum.EXPIRED.getMsg());
        // 检查二维码是否已经过期，如果过期，则更新状态为过期
        if (scanLoginQrCodeStatusVo != null && scanLoginQrCodeStatusVo.getExpireAt() < System.currentTimeMillis()) {
            scanLoginQrCodeStatusVo.setStatus(ScanLoginStatusEnum.EXPIRED);
        }
        //Assert.isTrue(ScanLoginStatusEnum.EXPIRED.equals(scanLoginQrCodeStatusVo.getStatus()), ScanLoginStatusEnum.EXPIRED.getMsg());
        //Assert.isNotTrue(scanLoginQrCodeStatusVo.getExpireAt() > System.currentTimeMillis(), ScanLoginStatusEnum.EXPIRED.getMsg());
        //Assert.isTrue(ScanLoginStatusEnum.LOGIN.equals(scanLoginQrCodeStatusVo.getStatus()), ScanLoginStatusEnum.LOGIN.getMsg());
        // 返回更新后的二维码状态对象
        return scanLoginQrCodeStatusVo;
    }
}