package com.yeelight.service.user.server.controller;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.SpringBeanUtil;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.filter.JustAuthAuthenticationFilter;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.AuthUtils;
import com.yeelight.service.user.server.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 三方授权登录
 *
 * <AUTHOR> Yu
 * @date 8/16/21 2:02 PM
 */
@Slf4j
@RestController
@RequestMapping("/third-party-auth")
public class ThirdPartyAuthController extends BaseController {
    @Resource
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;

    @Resource
    private AuthStateCache authStateCache;

    @Resource
    private TokenService tokenService;

    @Resource
    private OauthKnifeService oauthKnifeService;

    @Resource
    private JwtAuthService jwtAuthService;

    /**
     * 获取授权链接
     * 该方法用于根据传入的三方类型（source），生成并返回对应的授权链接。
     * @param source 代表三方类型的参数，用于确定要获取授权链接的服务。
     * @param request HttpServletRequest对象，用于获取请求相关信息（在此方法中未直接使用，可根据实际需要添加使用）。
     * @param response HttpServletResponse对象，用于向客户端发送响应，此处用于重定向到授权页面。
     * @throws IOException 当响应进行重定向时，可能抛出的IO异常。
     */
    @RequestMapping("/render/{source}")
    @ResponseBody
    public Result<?> renderAuth(@PathVariable("source") String source, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 从请求中提取登录参数
        String redirect = request.getParameter("redirect");

        // 根据source获取对应的AuthRequest对象
        AuthRequest authRequest = yeelightAuthRequestFactory.get(source);
        // 生成授权状态码
        String state = AuthStateUtils.createState();
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            state = Constants.FRONTEND_BACKEND_SEPARATED_REQUEST_THIRD_PARTY_STATE_PREFIX + state;
        }
        // 将状态码存入缓存
        authStateCache.cache(state, state);
        // 为第三方服务缓存token
        jwtAuthService.cacheTokenForThirdParty(state, request);
        // 生成授权页面的URL
        String authorizeUrl = authRequest.authorize(state);
        log.info(authorizeUrl);
        // 重定向到授权页面
        if (AuthUtils.isFrontendBackendSeparatedRequest(request) || Boolean.FALSE.toString().equalsIgnoreCase(redirect)) {
            return Result.success(authorizeUrl);
        } else {
            response.sendRedirect(authorizeUrl);
            return Result.success();
        }
    }


    /**
     * 获取state
     * 根据提供的三方类型生成并返回一个state值。
     * <p>
     * 该方法首先根据传入的source参数获取对应的AuthRequest对象，然后生成一个随机的state值，
     * 将该state值存储到缓存中，并且基于该state值和请求信息在另一缓存系统中存储相关认证信息。
     * 最后，返回生成的state值。
     * </p>
     *
     * @param source 代表三方类型的标识符，用于获取对应的AuthRequest配置。
     * @return Result<String> 包含生成的state值的成功结果对象。
     */
    @RequestMapping("/generateState/{source}")
    @ResponseBody
    public Result<String> generateState(@PathVariable("source") String source, HttpServletRequest request) {
        // 根据source参数获取对应的AuthRequest
        AuthRequest authRequest = yeelightAuthRequestFactory.get(source);
        // 生成state值
        String state = AuthStateUtils.createState();
        // 将state值存储到缓存中
        authStateCache.cache(state, state);
        // 在JWT认证服务中缓存token信息
        jwtAuthService.cacheTokenForThirdParty(state, request);
        // 返回生成的state值
        return Result.success(state);
    }


    /**
     * 登录
     * 处理用户登录请求，支持第三方登录。
     * 该方法会验证用户通过第三方平台发送的登录代码（code），并据此获取和创建访问令牌（token）。
     *
     * @param request 用户登录请求，包含第三方平台的认证代码等信息。
     * @return Result<OAuth2Token> 包含访问令牌信息的结果对象。
     * @throws Exception 处理过程中可能抛出的异常。
     */
    @RequestMapping("/login")
    @ResponseBody
    public Result<OAuth2Token> login(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 从Spring上下文中获取Web安全配置和HttpSecurity实例
        ApplicationContext ctx = SpringBeanUtil.getCtx();
        HttpSecurity httpSecurity = ctx.getBean(HttpSecurity.class);

        // 从请求中提取登录参数
        String code = request.getParameter("code");
        String yeelightClientId = request.getParameter("yeelightClientId");
        String state = request.getParameter(OAuth2Utils.STATE);
        String source = request.getParameter("source");

        // 验证必要参数的完整性
        Assert.notBlank(source, "三方类型不能为空");
        Assert.notBlank(yeelightClientId, "应用ID不能为空");
        Assert.notBlank(code, "三方code不能为空");
        Assert.notBlank(state, "state不能为空");

        // 并入并执行三方登录认证流程（存储三方信息，映射当前用户）
        JustAuthAuthenticationFilter filter = new JustAuthAuthenticationFilter();
        filter.setAuthenticationManager(httpSecurity.getSharedObject(AuthenticationManager.class));
        filter.setYeelightAuthRequestFactory(yeelightAuthRequestFactory);
        Authentication authentication = filter.attemptAuthentication(request, response);

        // 准备额外的参数信息，附加到token请求中
        Map<String, String> extendParameters = new HashMap<>(8);
        extendParameters.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, source + ":" + code);
        TokenUtils.attachAdditionalInformationForTokenRequest(extendParameters, authentication, null, request);

        // 获取登录用户信息，用于创建访问令牌。获取访问令牌，直接使用当前token的client id， 也可以指定client id
        YeelightUser user = (YeelightUser) authentication.getPrincipal();

        // 记录登录操作日志
        BizOperateLogUtils.sendSimpleBizOperateLog(user.getId(), BizTypeEnums.三方集成.getCode(), source + "登陆", extendParameters);

        // 创建并返回访问令牌
        return Result.success(oauthKnifeService.fastCreateAccessToken(yeelightClientId, user, extendParameters));
    }


    /**
     * 绑定
     * 绑定第三方账号到当前用户
     * 该方法处理第三方登录平台的回调，验证令牌，设置用户认证状态，并绑定用户信息。
     *
     * @param request HttpServletRequest对象，用于接收来自第三方平台的回调请求参数
     * @param response HttpServletResponse对象，用于响应请求
     * @return Result对象，表示操作结果，成功返回success，失败返回相应的错误信息
     * @throws Exception 可能抛出的异常，例如令牌验证失败或数据库操作异常
     */
    @RequestMapping("/bind")
    @ResponseBody
    public Result<?> bind(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 从请求中获取第三方授权码、状态码和来源标识
        String code = request.getParameter("code");
        String state = request.getParameter(OAuth2Utils.STATE);
        String source = request.getParameter("source");

        // 参数校验，确保必要参数不为空
        Assert.notBlank(source, "三方类型不能为空");
        Assert.notBlank(code, "三方code不能为空");
        Assert.notBlank(state, "state不能为空");

        // 获取ApplicationContext和WebSecurityConfiguration以便后续操作
        ApplicationContext ctx = SpringBeanUtil.getCtx();
        HttpSecurity httpSecurity = ctx.getBean(HttpSecurity.class);

        // 从请求中提取token
        String yeelightToken = TokenUtils.extractToken(request);
        // 根据token获取用户认证信息
        OAuth2AuthenticationDto authentication = tokenService.getAuthenticationByToken(yeelightToken);
        // 如果认证信息不存在或过期，则返回失败结果
        if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
            return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
        }
        // 设置用户登录状态
        SecurityContext context = SecurityContextHolder.getContext();
        context.setAuthentication(TokenUtils.dtoToOauth2Authentication(authentication));
        request.getSession().setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, context);

        // 并入并执行正常的三方登录流程，包括存储三方信息和映射当前用户
        JustAuthAuthenticationFilter filter = new JustAuthAuthenticationFilter();
        filter.setAuthenticationManager(httpSecurity.getSharedObject(AuthenticationManager.class));
        filter.setYeelightAuthRequestFactory(yeelightAuthRequestFactory);
        filter.attemptAuthentication(request, response);

        // 记录用户操作日志
        YeelightUser user = (YeelightUser) authentication.getPrincipal();
        BizOperateLogUtils.sendSimpleBizOperateLog(user.getId(), BizTypeEnums.三方集成.getCode(), source + "绑定", source);

        return Result.success();
    }

}
