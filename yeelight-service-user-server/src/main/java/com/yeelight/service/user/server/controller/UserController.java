package com.yeelight.service.user.server.controller;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.exception.NotLoginException;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.*;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLog;
import com.yeelight.service.user.server.bizlog.aop.BizOperateLogAspect;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.enums.OpTypeEnums;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 用户restful接口
 * 通过access token作为认证凭证
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {
    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageHelper;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private TokenService tokenService;

    @Resource
    private UserSessionManagementService userSessionManagementService;

    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;


    /**
     * 测试接口（忽略）
     */
    @GetMapping("/login")
    public Result<?> login(String username, String type, HttpServletRequest request) {
        return Result.success();
    }

    /**
     * 注册
     * 注册用户到系统。
     *
     * @param createUserRequest 包含用户注册信息的对象，如电话号码、电子邮件、用户名和密码等。
     * @param captcha 用户输入的验证码。
     * @param captchaKey 验证码的键，用于验证验证码的正确性。
     * @param password2 确认新密码，用于确认用户输入的密码一致。
     * @return Result 包含操作结果的对象，成功返回success，失败返回相应的错误信息。
     */
    @PostMapping("/register")
    @BizOperateLog(bizId = "{#createUserRequest.phoneNumber}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "注册用户", bizBody = "{#createUserRequest}")
    public Result<?> register(CreateUserRequest createUserRequest, @NotBlank String captcha, @NotBlank String captchaKey, @NotBlank String password2) {
        // 密码一致性检查
        Assert.isNotTrue(password2.equals(createUserRequest.getPassword()), ResultCodeEnum.密码不一致.getCode(), I18nUtil.getMessage("ResultCode.密码不一致"));

        String phoneNumber = createUserRequest.getPhoneNumber();
        String email = createUserRequest.getEmail();
        // 获取用户的电话号码或电子邮件作为验证对象
        String number = StringUtils.isNotBlank(phoneNumber) ? phoneNumber : email;
        YeelightUserDto yeelightUserDto;

        // 验证码校验
        checkCaptcha(number, captcha, captchaKey);

        // 根据电话号码或电子邮件查找用户
        yeelightUserDto = StringUtils.isNotBlank(phoneNumber) ? yeelightUserReadService.findUserByPhoneNumber(phoneNumber) : yeelightUserReadService.findUserByEmail(email);

        // 用户存在性检查
        Assert.isNull(yeelightUserDto, ResultCodeEnum.用户已存在.getCode(), I18nUtil.getMessage("ResultCode.用户已存在"));
        // 用户注册
        yeelightUserWriteService.create(createUserRequest);
        return Result.success();
    }


    /**
     * 找回密码
     *
     * @param user 包含用户信息的DTO，可能包括电话号码和电子邮件
     * @param captcha 用户输入的验证码
     * @param captchaKey 验证码的键，用于验证验证码的正确性
     * @param newPassword 用户设定的新密码
     * @return Result 包含操作结果的状态码，成功或失败的信息
     */
    @PostMapping("/forgetPassword")
    @BizOperateLog(bizId = "{#user.phoneNumber}", opType = OpTypeEnums.变更, bizType = BizTypeEnums.用户, bizSubType = "找回密码", bizBody = "用户：{#user}, 新密码：{#newPassword}")
    public Result<?> reset(YeelightUserDto user, String captcha, String captchaKey, String newPassword) {
        String phoneNumber = user.getPhoneNumber();
        String email = user.getEmail();
        YeelightUserDto yeelightUserDto;

        String number = Optional.ofNullable(phoneNumber).orElse(email);
        // 验证验证码的正确性
        checkCaptcha(number, captcha, captchaKey);

        // 根据提供的电话号码或电子邮件查找用户
        yeelightUserDto = StringUtils.isNotBlank(phoneNumber) ? yeelightUserReadService.findUserByPhoneNumber(phoneNumber) : yeelightUserReadService.findUserByEmail(email);

        // 用户存在性检查
        Assert.notNull(yeelightUserDto, ResultCodeEnum.用户名不存在.getCode(), I18nUtil.getMessage("ResultCode.用户名不存在"));

        // 更新用户密码
        yeelightUserWriteService.updatePassword(yeelightUserDto.getId(), newPassword);
        return Result.success();
    }


    /**
     * 登出Token
     * 删除用户当前的token，实现用户登出功能。首先会尝试从请求中提取token，如果未提供，则从请求头中提取。
     * @param request 请求对象，用于提取token或获取请求的session ID。
     * @param token 用户提供的token，如果为空，则从请求头中自动提取。
     * @return Result 返回操作结果，成功则返回一个成功的Result对象。
     */
    @PostMapping("/logout-token")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "删除用户当前token并登出当前用户会话", bizBody = "{#token}")
    public Result<?> logoutCurrentToken(HttpServletRequest request, HttpServletResponse response, String token) {
        // 如果未提供token，则从请求中提取token
        token = extractToken(token, request);
        // 删除当前token，实现登出功能
        tokenService.logoutCurrentToken(token);
        // 到期当前会话
        userSessionManagementService.expireSession(request.getRequestedSessionId());

        // 清理 cookie
        AuthUtils.clearCookie(request, response);
        return Result.success();
    }


    /**
     * 登出应用
     * 删除当前应用下用户所有的 token
     * @param request 请求，用于获取用户会话信息和提取token
     * @param token 用户的token，用于登录验证和删除操作
     * @return Result 操作结果，成功或失败
     */
    @PostMapping("/logout-app")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "删除当前应用下用户所有的 token并登出当前用户会话", bizBody = "{#token}")
    public Result<?> logoutCurrentApplication(HttpServletRequest request, String token) {
        // 如果传入的token为空，则从请求中提取token
        token = extractToken(token, request);
        // 调用服务，删除当前应用下用户的token
        tokenService.logoutCurrentApplication(token);
        // 清理session，结束当前用户会话
        userSessionManagementService.expireSession(request.getRequestedSessionId());
        // 返回成功结果
        return Result.success();
    }


    /**
     * 登出用户
     * 删除当前用户下所有的 token
     * @param token 用户的token，用于标识当前请求的用户
     * @return Result 返回操作结果，成功或失败
     */
    @PostMapping("/logout-user")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "删除当前用户下所有的 token并登出当前用户所有会话", bizBody = "{#token}")
    public Result<?> logoutCurrentUser(String token) {
        // 通过token获取用户名
        String userName = tokenService.getUserNameByToken(token);
        // 检查用户名是否存在，不存在则返回失败
        Assert.notBlank(userName, I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));

        // 执行用户登出操作，删除该用户所有的token
        yeelightUserWriteService.logoutByUserName(userName);

        // 记录登出日志
        log.info("登出用户:{}", token);
        // 登出成功，返回成功结果
        return Result.success();
    }


    /**
     * 设置token 过期
     * 该方法用于标记指定的访问令牌为过期，以强制用户重新登录。
     * @param request HttpServletRequest对象，用于获取请求信息。
     * @param token 用户的访问令牌。如果请求中未携带该参数，则从请求头中提取。
     * @return Result 返回操作结果，成功则返回一个成功的Result对象。
     */
    @PostMapping("/expire/access-token")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "设置token 过期", bizBody = "{#token}")
    public Result<?> expireAccessToken(HttpServletRequest request, String token) {
        // 如果传入的token为空，则从请求中提取token
        token = extractToken(token, request);
        // 调用服务层方法，将token标记为过期
        tokenService.expireAccessTokenTime(token, 1);
        // 返回成功结果
        return Result.success();
    }


    /**
     * 设置token过期时间
     * 设置后，AccessToken和refreshToken 都会按过期时间过期
     * @param request 请求，用于提取token，如果请求中未提供token
     * @param token 用户的token，如果请求中已提供，则直接使用，否则从请求中提取
     * @param seconds 设置的过期时间，单位为秒
     * @return Result 操作结果，成功则返回success
     */
    @PostMapping("/expire/access-token-time")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "设置token过期时间", bizBody = "token:{#token}, seconds:{#seconds}")
    public Result<?> expireAccessTokenTime(HttpServletRequest request, String token, int seconds) {
        // 如果调用时没有提供token，则从请求中提取
        token = extractToken(token, request);
        // 调用服务，设置token的过期时间
        tokenService.expireAccessTokenTime(token, seconds);
        // 返回成功结果
        return Result.success();
    }


    /**
     * 根据token获取用户信息
     * 该接口用于通过提供的token或从请求中提取的token来获取用户的基本信息。
     * @param request 请求对象，用于从请求头中提取token，如果请求参数中未提供token。
     * @param token 用户的token，如果在请求参数中提供，则优先使用该token。
     * @return Result<YeelightUserDto> 包含用户信息的Result对象，如果成功找到用户信息，则返回成功状态和用户信息Dto，否则返回失败状态和错误消息。
     */
    @GetMapping("/info")
    public Result<YeelightUserDto> info(HttpServletRequest request, String token) {
        // 尝试从请求参数中获取token，如果为空，则从请求头中提取token
        token = extractToken(token, request);

        // 通过token获取用户名
        String userName = tokenService.getUserNameByToken(token);
        // 如果用户名为空，表示token无效或过期，返回错误信息
        Assert.notNull(userName, I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));

        // 根据用户名查询用户信息
        YeelightUserDto userDto = yeelightUserReadService.findUserByUsername(userName);
        if (null != userDto) {
            // 针对返回的用户信息做处理：隐藏密码，检查头像URL是否有效
            userDto.setPassword(Strings.EMPTY);
            if (Objects.nonNull(userDto.getAvatar()) && !userDto.getAvatar().contains(Constants.HTTP_KEY)) {
                // 如果头像URL不是http开头，则认为无效，设置为null
                userDto.setAvatar(null);
            }
        }
        // 返回处理后的用户信息
        return Result.success(userDto);
    }

    /**
     * 根据token获取用户信息
     * @param request 请求
     * @param token token
     * @return Result<YeelightUserDto> 用户信息
     */
    @GetMapping("/information")
    public Result<YeelightUserDto> information(HttpServletRequest request, String token) {
        token = extractToken(token, request);
        String userName = tokenService.getUserNameByToken(token);
        if (StringUtils.isBlank(userName)) {
            throw new NotLoginException();
        }
        YeelightUserDto userDto = yeelightUserReadService.findUserByUsername(userName);
        if (null != userDto) {
            userDto.setPassword(Strings.EMPTY);
            if (Objects.nonNull(userDto.getAvatar()) && !userDto.getAvatar().contains(Constants.HTTP_KEY)) {
                userDto.setAvatar(null);
            }
        }
        return Result.success(userDto);
    }


    /**
     * 校验验证码的正确性。
     *
     * @param number  用户的电话号码或电子邮件。
     * @param captcha 用户输入的验证码。
     * @param captchaKey 验证码的键，用于后端验证验证码的正确性。
     */
    private void checkCaptcha(String number, String captcha, String captchaKey) {
        // 校验 number是否为空
        Assert.notBlank(number, ResultCodeEnum.手机号或邮箱不存在.getCode(), I18nUtil.getMessage("ResultCode.手机号或邮箱不存在"));
        // 验证验证码和验证码键是否为空
        Assert.notBlank(captchaKey, ResultCodeEnum.验证码错误.getCode(), I18nUtil.getMessage("ResultCode.验证码错误"));
        Assert.notBlank(captcha, ResultCodeEnum.请输入验证码.getCode(), I18nUtil.getMessage("ResultCode.请输入验证码"));
        // 验证码已经前置验证的情况，验证码校验逻辑
        CaptchaResult captchaResult;
        // 判断是否检查最后一次验证码结果
        if (SecurityConstants.CHECK_LAST_RESULT.equals(captcha)) {
            captchaResult = captchaMessageHelper.checkLastResult(captchaKey, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        } else {
            captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, captcha, number,
                    SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
        }
        // 验证码校验失败处理
        Assert.isNotTrue(captchaResult.isSuccess(), captchaResult.getMessage());
    }

    /**
     * 处理个人页面的请求，基于token进行身份验证和页面重定向。
     * 如果token有效，则重定向到个人主页；否则，重定向到登录页面。
     *
     * @param request 用户的请求对象，用于获取请求信息。
     * @param token 用户提供的访问token，用于身份验证。
     * @return ModelAndView 重定向后的页面模型视图。
     */
    @GetMapping("/personal")
    public ModelAndView personal(HttpServletRequest request, String token) {
        // 如果用户没有提供token，则从请求中提取
        token = extractToken(token, request);
        ModelAndView model = new ModelAndView();
        try {
            // 使用token服务进行登录验证，如果失败则重定向到登录页面
            if (!tokenService.loginByToken(token, request)) {
                model.setViewName("redirect:" + gatewayOauthConfig.getLoginPageUrl(request));
                return model;
            }
            // 验证成功，重定向到个人主页
            model.setViewName("redirect:" + gatewayOauthConfig.getIndexPageUrl(request));
        } catch (Exception e) {
            // 任何异常情况下，都重定向到登录页面
            model.setViewName("redirect:" + gatewayOauthConfig.getLoginPageUrl(request));
        }
        return model;
    }


    /**
     * 注销用户
     * 该接口用于注销用户账号，需要提供验证码和验证码key以进行身份验证。
     * 如果提供了token，则直接使用；若未提供，则从请求头中获取token。
     * 成功注销后，会返回操作成功的提示。
     *
     * @param request HttpServletRequest对象，用于获取请求头中的token（如果参数中未提供）。
     * @param token 用户的token，用于身份验证。如果为空，则尝试从请求头中获取。
     * @param captcha 用户输入的验证码。
     * @param captchaKey 验证码的key，用于后端验证验证码的正确性。
     * @return Result对象，包含操作结果的状态码和信息。成功时返回success状态，失败时返回相应的错误信息。
     */
    @PostMapping("/remove-user")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.删除, bizType = BizTypeEnums.用户, bizSubType = "注销用户", bizBody = "{#token}")
    public Result<?> removeUser(HttpServletRequest request, String token, String captcha, String captchaKey) {
        // 尝试从请求头提取token，如果参数中未提供
        token = extractToken(token, request);
        // 通过token获取用户名
        String userName = tokenService.getUserNameByToken(token);
        // 检查token是否有效，无效则返回错误信息
        if (StringUtils.isBlank(userName)) {
            return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
        }
        // 根据用户名查找用户信息
        YeelightUserDto user = yeelightUserReadService.findUserByUsername(userName);
        // 用户不存在则返回错误信息
        Assert.notNull(user, I18nUtil.getMessage("ResultCode.用户名不存在"));
        // 验证验证码
        String number = Optional.ofNullable(user.getPhoneNumber()).orElse(user.getEmail());
        checkCaptcha(number, captcha, captchaKey);

        // 注销用户
        yeelightUserWriteService.removeUserByUserName(user.getUsername());
        // 记录日志
        log.info("注销用户[{}], token:{}", user, token);
        return Result.success();
    }


    /**
     * 获取三方用户信息
     *
     * @param request HttpServletRequest对象，用于获取请求头信息
     * @param source 三方类型，指定用户来源
     * @param token 用户的token，用于身份验证。若token参数为空，则会尝试从请求头中获取token。
     * @return Result<SocialUserDto> 包含三方用户信息的结果对象。成功时，result的data字段为SocialUserDto类型；失败时，result的message字段说明错误原因。
     */
    @GetMapping("/socialUser/{source}")
    public Result<SocialUserDto> socialUser(HttpServletRequest request, @PathVariable String source, String token) {
        // 如果token为空，则尝试从请求头中提取token
        token = extractToken(token, request);
        // 附加供应商信息到source
        source = UserVendorHolder.attachVendor(source);
        // 根据token和source获取三方用户信息
        return Result.success(justAuthUserDetailsService.getSocialUser(token, source));
    }


    /**
     * 解绑三方用户
     * 该接口用于解除用户与特定第三方平台的绑定关系。
     * @param source 三方类型，表示需要解绑的第三方平台标识。
     * @param token token，用于验证用户身份的令牌。
     *              1. 如果token为空，则从请求头中获取token。
     * @return Result，操作结果，成功则返回成功信息，失败则返回失败原因。
     */
    @DeleteMapping("/unBindSocialUser/{source}")
    @PreAuthorize("isAuthenticated()")
    @BizOperateLog(bizId = BizOperateLogAspect.CURRENT_USER_ID, opType = OpTypeEnums.变更, bizType = BizTypeEnums.三方集成, bizSubType = "解绑三方", bizBody = "source:{#source},token:{#token}")
    public Result<?> unBindSocialUser(HttpServletRequest request, @PathVariable String source, String token) {
        // 如果传入的token为空，则从请求头中提取token
        token = extractToken(token, request);
        // 附加厂商信息，确保来源的准确性
        source = UserVendorHolder.attachVendor(source);
        // 调用服务，执行解绑操作
        yeelightSocialUserService.unBindSocialUser(token, source);
        // 返回成功结果
        return Result.success();
    }


    /**
     * 获取三方用户列表。
     * 该接口需要用户认证，用于获取特定供应商的三方用户列表。
     *
     * @param token 用户的认证token。如果token参数为空，会尝试从请求头中获取token。
     * @return List<SocialUserDto> 返回特定供应商的三方用户列表。
     * @throws IOException 如果发生IO异常。
     */
    @GetMapping("/socialUsers")
    @PreAuthorize("isAuthenticated()")
    public Result<List<SocialUserDto>> socialUsers(HttpServletRequest request, String token) throws IOException {
        // 如果提供的token为空，则从请求头中提取token
        token = extractToken(token, request);
        // 根据当前供应商和token查找三方用户列表
        return Result.success(yeelightSocialUserService.findSocialUsersByVendor(UserVendorHolder.getVendor(), token));
    }

}
