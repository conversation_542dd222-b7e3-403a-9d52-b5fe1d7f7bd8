package com.yeelight.service.user.server.controller.thirdparty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.SpringBeanUtil;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.AuthToken;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.enums.AuthUserGender;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightSocialUserService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.controller.BaseController;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import com.yeelight.service.user.server.filter.JustAuthAuthenticationFilter;
import com.yeelight.service.user.server.request.AlexaSkillEventRequest;
import com.yeelight.service.user.server.request.AlexaSkillTokenUpdateRequest;
import com.yeelight.service.user.server.request.AuthCodeRequest;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import com.yeelight.service.user.server.service.AmazonAlexaService;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.RedisKeyUtils;
import com.yeelight.service.user.server.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * amazon a2a 授权登录
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/third-party/alexa")
public class AlexaController extends BaseController {
    @Resource
    private OauthKnifeService oauthKnifeService;

    @Resource
    private TokenService tokenService;

    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private AuthStateCache authStateCache;

    @Resource
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;

    @Resource
    private AmazonAlexaService amazonAlexaService;

    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Resource
    private RedisManager redisManager;

    @Resource
    private JwtAuthService jwtAuthService;

    /**
     * 获取Amazon A2A（应用到应用）配置信息。
     * 该接口用于从服务器获取与Amazon Alexa平台相关的配置数据，包括LWA（Login With Amazon）的回调URL和Alexa应用的URL。
     *
     * @param request HttpServletRequest对象，用于获取请求信息。
     * @return Result<Map<String, String>>对象，包含请求结果的状态和配置信息。其中配置信息以Map形式存储，键值对包括lwaFallbackUrl和alexaAppUrl。
     */
    @GetMapping("/apptoapp/config/get")
    public Result<Map<String, String>> appToAppConfigGet(HttpServletRequest request) {
        // 初始化配置信息的Map
        Map<String, String> config = new HashMap<>(8);
        // 填充LWA回调URL配置
        config.put("lwaFallbackUrl", amazonAlexaService.getLwaFallbackUrl(sourceName(), request));
        // 填充Alexa应用URL配置
        config.put("alexaAppUrl", amazonAlexaService.getAlexaAppUrl(sourceName(), request));
        // 返回成功结果，包含配置信息
        return Result.success(config);
    }


    /**
     * 刷新用户的alexa 三方信息
     * 根据alexa发给skill的skill event消息，刷新用户的alexa 三方信息
     * @param request 请求，包含alexa技能事件信息
     * @return Result 结果，包含操作是否成功的信息
     */
    @PostMapping("/apptoapp/skillEvent/handle")
    public Result<?> appToAppHandleSkillEvent(@RequestBody @Valid AlexaSkillEventRequest request) {
        // 记录接收到的skill event消息
        log.info("skill event消息：{}", JSON.toJSONString(request));

        // 提取请求中的事件类型、访问令牌、事件发布时间、用户ID
        String eventType = request.getRequest().getType();
        String accessToken = request.getContext().getSystem().getUser().getAccessToken();
        Long eventPublishingTime = Objects.nonNull(request.getRequest().getEventPublishingTime()) ? request.getRequest().getEventPublishingTime().getTime() : System.currentTimeMillis();
        String userId = request.getContext().getSystem().getUser().getUserId();

        // 确保事件类型不为空
        Assert.notNull(eventType, "skill event 消息类型错误");

        // 从Redis中获取之前存储的技能事件发布时间
        Long storedEventPublishingTime = null;
        Object storedEventPublishingTimeObject = redisManager.redisTemplate().opsForValue().get(RedisKeyUtils.getAmazonAlexaSkillEventPublishKey(userId));
        log.info("storedEventPublishingTimeObject：{}", storedEventPublishingTimeObject);

        // 发送业务操作日志
        BizOperateLogUtils.sendSimpleBizOperateLog(userId, BizTypeEnums.三方集成.getCode(), "根据alexa发给skill的skill event消息，刷新用户的alexa 三方信息", request);

        try {
            if (Objects.nonNull(storedEventPublishingTimeObject)) {
                storedEventPublishingTime = (Long) storedEventPublishingTimeObject;
            }
        } catch (Exception e) {
            // 记录转换异常
            log.warn("根据alexa发给skill的skill event消息，刷新用户的alexa 三方信息, 转换异常:{}", ExceptionUtils.getStackTrace(e));
        }

        // 检查当前处理的技能事件发布时间是否晚于已存储的发布时间
        if (Objects.nonNull(storedEventPublishingTime) && storedEventPublishingTime > eventPublishingTime) {
            log.info("当前skill event 发布时间[{}]晚于前置已处理的event发布时间[{}]", eventPublishingTime, storedEventPublishingTime);
            return Result.failure("当前skill event 发布时间晚于前置已处理的event发布时间");
        }

        // 根据事件类型处理相应的逻辑
        switch (eventType) {
            case "AlexaSkillEvent.SkillEnabled":
                // 启用技能的事件处理逻辑
                break;
            case "AlexaSkillEvent.SkillAccountLinked":
                // 账户关联事件处理逻辑，创建alexa三方用户关系
                OAuth2AuthenticationDto authentication = tokenService.getAuthenticationByToken(accessToken);
                // 如果未找到当前用户的登录信息，返回错误
                if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
                    return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
                }
                // 如果当前用户已登录，创建alexa三方用户关系
                if (authentication.isAuthenticated() && authentication.getPrincipal() instanceof YeelightUser yeelightUser) {
                    Long currentLoginUserId = yeelightUser.getId();
                    AuthUser authUser = AuthUser.builder()
                            .uuid(userId)
                            .source(AuthCustomSource.AMAZON_ALEXA.getName())
                            .gender(AuthUserGender.UNKNOWN)
                            .token(AuthToken.builder()
                                    .idToken(request.getContext().getSystem().getApiAccessToken())
                                    .expireIn(3600)
                                    .tokenType("bearer")
                                    .build())
                            .rawUserInfo(JSONObject.parseObject(JSON.toJSONString(request)))
                            .build();
                    justAuthUserDetailsService.autoRegisterSocialUser(authUser, currentLoginUserId);
                    return Result.success();
                }
                break;
            case "AlexaSkillEvent.SkillPermissionAccepted":
                // 技能权限接受事件处理逻辑
                break;
            case "AlexaSkillEvent.SkillPermissionChanged":
                // 技能权限已更改事件处理逻辑
                break;
            case "AlexaSkillEvent.SkillDisabled":
                // 技能禁用事件处理逻辑，删除alexa三方用户关系
                SocialUserDto alexaUser = yeelightSocialUserService.findSocialUserBySourceAndUuid(userId, AuthCustomSource.AMAZON_ALEXA.getName());
                // 如果未找到alexa三方用户，尝试根据访问令牌查找
                if(Objects.isNull(alexaUser) && StringUtils.isNotBlank(accessToken)) {
                    // 根据访问令牌查找alexa三方用户
                    alexaUser = yeelightSocialUserService.findSocialUserBySource(accessToken, AuthCustomSource.AMAZON_ALEXA.getName());
                }
                // 如果找到alexa三方用户，删除alexa三方用户关系
                if (Objects.nonNull(alexaUser)) {
                    log.info("技能禁用事件， 删除alexa三方用户：{}", alexaUser);
                    yeelightSocialUserService.unBindSocialUser(alexaUser.getYeelightId(), AuthCustomSource.AMAZON_ALEXA.getName());
                }
                break;
            default:
        }
        // 更新Redis中技能事件发布时间
        redisManager.redisTemplate().opsForValue().set(RedisKeyUtils.getAmazonAlexaSkillEventPublishKey(userId), eventPublishingTime);
        return Result.success();
    }


    /**
     * 刷新用户的alexa 三方信息(弃用)
     * 根据alexa发给skill的消息，刷新用户的alexa 三方信息。
     * 该方法已弃用，不应再使用。
     * @param request 请求对象，包含alexa技能令牌更新请求的详细信息。
     * @return Result 结果对象，通常表示操作是否成功。
     */
    @PostMapping("/apptoapp/token/update")
    @Deprecated
    public Result<?> appToAppTokenUpdate(@RequestBody @Valid AlexaSkillTokenUpdateRequest request) {
        // 执行刷新用户alexa三方信息的操作，此处省略具体实现。
        return Result.success();
    }


    /**
     * 获取授权码
     * 该接口用于应用程序之间安全地获取授权码，以便进行进一步的授权和数据交换。
     *
     * @param request 请求对象，包含获取授权码所需的全部信息。
     *                必须是一个经过验证的AuthCodeRequest实例，以确保请求数据的完整性和正确性。
     * @return Result<String> 返回一个结果对象，其中包含授权码或错误信息。
     *         如果请求成功，授权码将以字符串形式返回；如果请求失败，将返回相应的错误信息。
     */
    @PostMapping("/apptoapp/code/get")
    public Result<String> appToAppCodeGet(@RequestBody @Valid AuthCodeRequest request) {
        // 通过OAuthKnifeService生成授权码，并返回成功结果
        return Result.success(oauthKnifeService.generateAuthCode(request));
    }

    /**
     * 绑定
     * 处理应用程序到应用程序的链接状态请求。
     * 绑定用户与第三方应用的链接状态，并进行身份验证和授权流程。
     *
     * @param request  HttpServletRequest对象，用于接收客户端请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     * @return Result对象，表示操作的结果，成功或失败，并包含相应的信息。
     */
    @GetMapping("/apptoapp/linkingStatus/link")
    public Result<?> appToAppLinkingStatusLink(HttpServletRequest request, HttpServletResponse response) {
        // 获取SpringApplicationContext上下文
        ApplicationContext ctx = SpringBeanUtil.getCtx();
        // 从上下文中获取取HttpSecurity实例
        HttpSecurity httpSecurity = ctx.getBean(HttpSecurity.class);

        // 从请求中提取token，并验证token的有效性
        String yeelightToken = TokenUtils.extractToken(request);
        String state = request.getParameter(OAuth2Utils.STATE);
        OAuth2AuthenticationDto authentication = tokenService.getAuthenticationByToken(yeelightToken);
        if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
            // 如果token无效，返回失败结果
            return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
        }

        // 生成并获取当前用户的yeelight auth code
        String yeelightAuthCode = amazonAlexaService.generateYeelightAuthCode(sourceName(), yeelightToken, state);

        // 设置安全上下文，并将用户信息存储于session中
        SecurityContext context = SecurityContextHolder.getContext();
        // 设置当前用户的认证信息
        context.setAuthentication(TokenUtils.dtoToOauth2Authentication(authentication));
        // 将当前用户的安全上下文存储于session中
        request.getSession().setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, context);
        // 将yeelight auth code存储于request中
        request.setAttribute("yeelightAuthCode", yeelightAuthCode);

        // 执行正常的第三方登录流程
        authStateCache.cache(state, state);
        // 缓存token
        jwtAuthService.cacheTokenForThirdParty(state, request);
        // 创建JustAuthAuthenticationFilter实例，并执行认证
        JustAuthAuthenticationFilter filter = new JustAuthAuthenticationFilter();
        filter.setAuthenticationManager(httpSecurity.getSharedObject(AuthenticationManager.class));
        filter.setYeelightAuthRequestFactory(yeelightAuthRequestFactory);
        filter.attemptAuthentication(request, response);

        // 记录操作日志
        YeelightUser user = (YeelightUser) authentication.getPrincipal();
        BizOperateLogUtils.sendSimpleBizOperateLog(user.getId(), BizTypeEnums.三方集成.getCode(), "alexa a2a link", user);

        // 返回成功结果
        return Result.success();
    }


    /**
     * 获取绑定状态
     * 检查当前用户是否与外部应用（如Alexa）进行了绑定。
     *
     * @param request HttpServletRequest对象，用于获取请求信息。
     * @return Result<Boolean> 结果对象，包含操作是否成功的状态（true/false）。
     * @throws Exception 抛出异常，处理token提取或验证过程中出现的错误。
     */
    @GetMapping("/apptoapp/linkingStatus/get")
    public Result<Boolean> appToAppLinkingStatusGet(HttpServletRequest request) throws Exception {
        // 提取请求中的token
        String yeelightToken = TokenUtils.extractToken(request);
        log.info("获取alex a2a状态：{}", yeelightToken);
        // 根据token查询登录用户名
        String userName = tokenService.getUserNameByToken(yeelightToken);
        if (StringUtils.isBlank(userName)) {
            // 如果用户名为空，表示token无效，返回失败结果
            return Result.failure(I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
        }
        // 根据token查询社交用户信息，以验证是否已绑定
        SocialUserDto alexaUser = justAuthUserDetailsService.getSocialUser(yeelightToken, sourceName());
        if (Objects.isNull(alexaUser)) {
            // 如果未查询到社交用户信息，表示未绑定，返回成功但状态为false
            return Result.success(Boolean.FALSE);
        } else {
            // 如果查询到社交用户信息，表示已绑定，返回成功且状态为true
            return Result.success(Boolean.TRUE);
        }
    }


    private String sourceName() {
        return UserVendorHolder.attachVendor(AuthCustomSource.AMAZON_ALEXA.getName());
    }
}