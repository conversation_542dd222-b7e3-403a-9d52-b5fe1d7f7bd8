package com.yeelight.service.user.server.controller.thirdparty;

import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.*;
import com.yeelight.service.user.client.enums.AuthUserGender;
import com.yeelight.service.user.client.enums.UserSocialSource;
import com.yeelight.service.user.client.service.YeelightSocialUserService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.request.AppleCheckBindStatusRequest;
import com.yeelight.service.user.server.request.AppleVerifyRequest;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.AppleLoginUtils;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.*;

/**
 * apple原生登录授权登录
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/third-party/apple")
public class AppleController {
    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Resource
    private OauthKnifeService oauthKnifeService;

    /**
     * 苹果登录
     * 处理苹果登录验证
     * @param request 包含苹果登录验证所需信息的请求体
     * @param httpServletRequest HTTP请求对象，用于获取额外信息（本例中未直接使用）
     * @return 返回登录验证成功的token信息
     */
    @PostMapping(value = "/login/verify")
    public Result<OAuth2Token> loginVerify(@RequestBody @Valid AppleVerifyRequest request, HttpServletRequest httpServletRequest) {
        // 验证苹果颁发的令牌，并获取用户信息
        Map<String, Object> appleUser = AppleLoginUtils.verifyToken(request.getIdentityToken());
        Assert.notNull(appleUser, "apple登录失败");

        // 记录登录请求的日志
        log.info("loginVerify user info : {}", appleUser);
        // 将苹果用户信息转换为平台的用户信息
        AuthUser authUser = appleUserToAuthUser(appleUser, request.getIdentityToken());
        Assert.notNull(authUser, "apple用户三方信息为空");

        // 自动注册或绑定苹果社交用户
        YeelightUserDto yeelightUserDto = justAuthUserDetailsService.autoRegisterSocialUser(authUser, request.getYeelightId());
        Assert.notNull(yeelightUserDto, "apple登录失败");

        // 准备扩展参数，用于token增强
        Map<String, String> extendParameters = new HashMap<>(4);
        if (authUser != null) {
            extendParameters.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, request.getYeelightClientId() + ":" + authUser.getUuid());
        }

        // 设置额外信息到token请求中
        TokenUtils.attachAdditionalInformationForTokenRequest(extendParameters, null, null, httpServletRequest);

        // 准备用户权限
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new CustomGrantedAuthority("ROLE_USER"));

        // 复制属性到最终的用户对象中
        YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
        yeelightUser.setGrantedAuthorities(authorities);

        // 发送业务操作日志
        BizOperateLogUtils.sendSimpleBizOperateLog(yeelightUser.getId(), BizTypeEnums.三方集成.getCode(), "APPLE登陆请求", request);
        BizOperateLogUtils.sendSimpleBizOperateLog(yeelightUser.getId(), BizTypeEnums.三方集成.getCode(), "APPLE登陆", extendParameters);

        // 创建并返回访问token
        return Result.success(oauthKnifeService.fastCreateAccessToken(request.getYeelightClientId(), yeelightUser, extendParameters));
    }


    /**
     * 验证绑定状态
     * 该方法用于验证用户是否已经与我们的平台绑定了。它通过验证苹果用户的identity token来实现。
     * @param request 请求参数，包含用户的identity token。
     * @return Result<Boolean> 返回一个结果对象，其中包含了是否绑定的状态。如果绑定了，返回true；否则返回false。
     */
    @PostMapping(value = "/login/verifyBindStatus")
    public Result<Boolean> verifyBindStatus(@RequestBody @Valid AppleCheckBindStatusRequest request) {
        // 验证Apple的identity token，并获取用户信息
        Map<String, Object> appleUser = AppleLoginUtils.verifyToken(request.getIdentityToken());
        // 如果token验证失败，返回错误信息
        Assert.notNull(appleUser, "apple IdentityToken verify error");

        // 记录日志，用于调试和监控
        log.info("verifyBindStatus user info : {}", appleUser);
        // 将苹果用户信息转换为我们的平台用户信息
        AuthUser authUser = appleUserToAuthUser(appleUser, request.getIdentityToken());
        // 确保转换后的用户信息不为空
        Assert.notNull(authUser, "apple用户三方信息为空");
        // 根据用户UUID和来源查找我们的社交用户信息
        SocialUserDto socialUserDto = null;
        if (authUser != null) {
            socialUserDto = yeelightSocialUserService.findSocialUserBySourceAndUuid(authUser.getUuid(), authUser.getSource());
        }
        // 根据是否找到社交用户信息，来判断用户是否已绑定
        return Result.success(Objects.nonNull(socialUserDto));
    }


    /**
     * 将Apple用户信息转换为平台认证用户信息。
     *
     * @param appleUser 包含Apple用户信息的Map，如sub（用户ID）、email（电子邮箱）等。
     * @param identityToken Apple身份认证令牌。
     * @return 转换后的认证用户信息，如果输入的appleUser为null，则返回null。
     */
    private AuthUser appleUserToAuthUser(Map<String, Object> appleUser, String identityToken) {
        // 如果传入的Apple用户信息为null，则直接返回null
        if (Objects.isNull(appleUser)) {
            return null;
        }
        // 初始化AuthUser对象，设置基本属性
        AuthUser authUser = AuthUser.builder()
                .uuid(appleUser.get("sub").toString())
                // 默认设置性别为未知
                .gender(AuthUserGender.UNKNOWN)
                // 设置身份令牌
                .token(AuthToken.builder().idToken(identityToken).build())
                // 设置用户来源为Apple
                .source(UserSocialSource.APPLE.getCode())
                .build();
        // 检查Apple用户信息中是否包含邮箱，并处理私有邮箱标志
        if (Objects.nonNull(appleUser.get(Constants.EMAIL_KEY))) {
            if (Objects.nonNull(appleUser.get(Constants.IS_PRIVATE_EMAIL_KEY)) && appleUser.get(Constants.IS_PRIVATE_EMAIL_KEY).equals(Boolean.FALSE)) {
                // 如果邮箱非私有，则设置邮箱地址
                authUser.setEmail(appleUser.get(Constants.EMAIL_KEY).toString());
            }
        }
        return authUser;
    }
}