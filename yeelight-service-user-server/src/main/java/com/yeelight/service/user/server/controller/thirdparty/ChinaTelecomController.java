package com.yeelight.service.user.server.controller.thirdparty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.Assert;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.AuthToken;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.enums.AuthUserGender;
import com.yeelight.service.user.client.enums.UserSocialSource;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.request.ChinaTelecomTokenNotifyRequest;
import com.yeelight.service.user.server.request.ChinaTelecomVerifyRequest;
import com.yeelight.service.user.server.service.ChinaTelecomService;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.TokenUtils;
import com.yeelight.service.user.server.utils.chinatelecom.Constants;
import com.yeelight.service.user.server.utils.chinatelecom.ReturnUtil;
import com.yeelight.service.user.server.vo.ChinaTelecomToken;
import com.yeelight.service.user.server.vo.ChinaTelecomUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 中国电信一键授权登录
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/third-party/china-telecom")
public class ChinaTelecomController {
    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;
    @Resource
    private OauthKnifeService oauthKnifeService;

    @Resource
    private ChinaTelecomService chinaTelecomService;

    /**
     * 中国电信登陆
     * @param request 包含登录认证信息的请求对象
     * @param httpServletRequest HTTP请求对象，用于获取请求相关信息
     * @return 返回一个包含登录结果的JSONObject对象，如访问令牌、刷新令牌等信息
     */
    @PostMapping(value = "/login/verify")
    public JSONObject loginVerify(@RequestBody ChinaTelecomTokenNotifyRequest request, HttpServletRequest httpServletRequest) {
        // 用于存储登录成功后的重定向参数
        Map<String, Object> redirectMap = new HashMap<>(2);

        try {
            // 从请求中提取认证信息
            ChinaTelecomVerifyRequest verifyRequest = chinaTelecomService.extractTokenCode(request);
            log.info("中国电信 loginVerify token info : {}", verifyRequest);

            // 存储状态参数
            redirectMap.put(OAuth2Utils.STATE, verifyRequest.getState());

            // 提取并校验yeelight客户端ID
            String yeelightClientId = verifyRequest.getExtraParam().getString(Constants.YEELIGHT_CLIENT_ID_KEY);
            Assert.notBlank(yeelightClientId, "中国电信用户-客户端ID信息为空");

            // 通过认证码获取访问令牌
            ChinaTelecomToken telecomToken = chinaTelecomService.getTokenByTokenCode(verifyRequest.getTokenCode());

            // 使用访问令牌获取用户信息
            ChinaTelecomUserInfo telecomUserInfo = chinaTelecomService.getUserInfo(telecomToken.getAccessToken());

            // 将电信用户信息转换为平台用户信息
            AuthUser authUser = telecomUserToAuthUser(telecomToken, telecomUserInfo);
            Assert.notNull(authUser, "中国电信用户三方信息为空");

            // 自动注册并登录社交用户
            YeelightUserDto yeelightUserDto = justAuthUserDetailsService.autoRegisterSocialUser(authUser, null);
            Assert.notNull(yeelightUserDto, "中国电信自动登录失败");

            // 准备扩展参数
            Map<String, String> extendParameters = new HashMap<>(1);
            if (authUser != null) {
                extendParameters.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, UserSocialSource.CHINA_TELECOM.getCode() + ":" + yeelightClientId + ":"  + authUser.getUuid());
            }
            TokenUtils.attachAdditionalInformationForTokenRequest(extendParameters, null, null, httpServletRequest);

            // 准备权限信息
            Collection<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new CustomGrantedAuthority("ROLE_USER"));

            // 复制属性到YeelightUser对象，准备token颁发所需信息
            YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
            yeelightUser.setGrantedAuthorities(authorities);

            // 记录业务操作日志
            BizOperateLogUtils.sendSimpleBizOperateLog(yeelightUser.getId(), BizTypeEnums.三方集成.getCode(), "中国电信登陆请求", request);
            BizOperateLogUtils.sendSimpleBizOperateLog(yeelightUser.getId(), BizTypeEnums.三方集成.getCode(), "中国电信登陆", extendParameters);

            // 生成并校验OAuth2令牌
            OAuth2Token auth2Token = oauthKnifeService.fastCreateAccessToken(yeelightClientId, yeelightUser, extendParameters);
            Assert.notNull(auth2Token, "中国电信获取token失败");

            // 准备返回给客户端的登录信息
            redirectMap.put("accessToken", auth2Token.getAccessToken());
            redirectMap.put("atExpireTime", auth2Token.getExpiresIn());
            redirectMap.put("refreshToken", auth2Token.getRefreshToken());
            redirectMap.put("rtExpireTime", auth2Token.getExpiresIn() * 3);
            redirectMap.put("openUserId", auth2Token.getId());
            // 返回登录成功信息
            return ReturnUtil.success(redirectMap);
        } catch (Exception e) {
            log.warn("中国电信登陆失败", e);
            // 根据异常类型返回不同的错误信息
            if (e instanceof BizException) {
                return ReturnUtil.fail(((BizException) e).getCode(), e.getMessage(), redirectMap);
            }
            return ReturnUtil.fail(e.getMessage(), redirectMap);
        }
    }


    /**
     * 将电信用户信息转换为认证用户信息。
     *
     * @param telecomToken 电信令牌对象，包含访问令牌、刷新令牌等信息。
     * @param telecomUserInfo 电信用户信息对象，包含用户的基本信息如昵称、手机号等。
     * @return 转换后的认证用户信息对象，如果输入参数为null，则返回null。
     */
    private AuthUser telecomUserToAuthUser(ChinaTelecomToken telecomToken, ChinaTelecomUserInfo telecomUserInfo) {
        // 检查输入参数是否为null
        if (Objects.isNull(telecomToken) || Objects.isNull(telecomUserInfo)) {
            return null;
        }
        // 构建认证用户信息
        return AuthUser.builder()
                // 将电信用户信息转换为原始JSON对象
                .rawUserInfo(JSONObject.parseObject(JSON.toJSONString(telecomUserInfo)))
                // 用户唯一标识
                .uuid(telecomUserInfo.getOpenId())
                // 用户名
                .username(telecomUserInfo.getNickName())
                // 昵称
                .nickname(telecomUserInfo.getNickName())
                // 公司或运营商
                .company(telecomUserInfo.getOperator())
                // 头像URL
                .avatar(telecomUserInfo.getUserIconUrl())
                // 电子邮件
                .email(telecomUserInfo.getEmail())
                // 电话号码
                .phone(telecomUserInfo.getMobile())
                // 性别，此处默认为未知
                .gender(AuthUserGender.UNKNOWN)
                // 构建认证令牌信息
                .token(AuthToken.builder()
                        // 访问令牌
                        .accessToken(telecomToken.getAccessToken())
                        // 访问令牌过期时间
                        .expireIn(Integer.parseInt(telecomToken.getExpiresTime()))
                        // 刷新令牌
                        .refreshToken(telecomToken.getRefreshToken())
                        // 刷新令牌过期时间
                        .refreshTokenExpireIn(Integer.parseInt(telecomToken.getExpiresReTime()))
                        // 用户唯一标识
                        .uid(telecomUserInfo.getOpenId())
                        // 用户ID
                        .userId(telecomUserInfo.getOpenId())
                        // 开放ID
                        .openId(telecomUserInfo.getOpenId())
                        // 联合ID
                        .unionId(telecomUserInfo.getOpenId())
                        .build())
                // 用户来源标识为电信
                .source(UserSocialSource.CHINA_TELECOM.getCode())
                .build();
    }
}