package com.yeelight.service.user.server.controller.thirdparty;

import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.*;
import com.yeelight.service.user.client.enums.AuthUserGender;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.controller.BaseController;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.request.FaceBookLoginRequest;
import com.yeelight.service.user.server.request.FacebookDeletionRequest;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.config.AuthDefaultSource;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.utils.UrlBuilder;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;

/**
 * facebook原生登录授权登录
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/third-party/facebook")
public class FacebookController extends BaseController {
    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private OauthKnifeService oauthKnifeService;

    @Resource
    private TokenService tokenService;

    @Value("${justauth.type.FACEBOOK.client-secret}")
    private String clientSecret;

    @Value("${justauth.http-config.proxy.FACEBOOK.hostname:}")
    private String proxyHost;

    @Value("${justauth.http-config.proxy.FACEBOOK.port:}")
    private Integer proxyPort;

    private final static String EXCEPTION_ID_IO_EXCEPTION = "IO_EXCEPTION";
    private final static String PICTURE = "picture";
    private final static String ALGORITHM = "algorithm";
    private final static String HMAC_SHA_256 = "HMAC-SHA256";

    /**
     * 登录
     * 处理Facebook登录请求。
     * 首先验证并获取当前请求中的用户token，如果存在，则验证token的有效性并获取用户信息。
     * 其次，校验并获取Facebook的登录token，通过这个token获取Facebook用户的信息。
     * 如果用户已存在，将Facebook用户信息与现有用户绑定；若不存在，则自动注册一个新用户。
     * 最后，生成并返回一个OAuth2访问令牌。
     *
     * @param request 包含Facebook登录信息的请求体。
     * @param httpServletRequest 当前HTTP请求对象，用于提取token等信息。
     * @return Result<OAuth2Token> 包含登录成功后返回的OAuth2令牌信息。
     */
    @PostMapping(value = "/login")
    public Result<OAuth2Token> login(@RequestBody @Valid FaceBookLoginRequest request, HttpServletRequest httpServletRequest) {
        log.info("facebook login user info : {}", request);

        // 提取并验证当前请求中的yeelight Token
        String yeelightToken = TokenUtils.extractToken(httpServletRequest);
        OAuth2AuthenticationDto authentication = null;
        Long yeelightUserId = null;
        // 如果存在yeelightToken，则获取用户信息
        if(StringUtils.isNotBlank(yeelightToken)) {
            authentication = tokenService.getAuthenticationByToken(yeelightToken);
            // 验证token的有效性
            Assert.isTrue(Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication()), I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
            // 根据token获取用户信息
            YeelightUserDto userDto = yeelightUserReadService.findUserByUsername(authentication.getUserAuthentication().getName());
            Assert.notNull(userDto, ResultCodeEnum.用户名不存在.getCode(), I18nUtil.getMessage("ResultCode.用户名不存在"));

            yeelightUserId = userDto.getId();
        }

        // 验证Facebook登录token
        Assert.notBlank(request.getFacebookToken(), "facebook用户token信息为空");
        AuthUser authUser;
        try {
            // 获取Facebook用户信息
            authUser = requestToAuthUser(request);
        } catch (IOException e) {
            log.error("获取facebook用户信息失败：{}",e. getMessage());
            return Result.failure(e.getMessage());
        }
        // 确保获取到Facebook的用户信息
        Assert.notNull(authUser, "facebook用户三方信息为空");

        // 自动注册或绑定Facebook账号
        YeelightUserDto yeelightUserDto = justAuthUserDetailsService.autoRegisterSocialUser(authUser, yeelightUserId);
        // 确保登录或注册成功
        Assert.notNull(yeelightUserDto, "facebook登录失败");

        // 准备额外参数，准备生成访问令牌
        Map<String, String> extendParameters = new HashMap<>(8);
        if (authUser != null) {
            extendParameters.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, request.getYeelightClientId() + ":" + authUser.getUuid());
        }        if (Objects.nonNull(authentication)) {
            // 如果当前用户已登录，则附加当前用户信息
            TokenUtils.attachAdditionalInformationForTokenRequest(extendParameters, authentication.getUserAuthentication(), authentication.getStoredRequest(), httpServletRequest);
        } else {
            // 如果未登录，则不附加用户信息
            TokenUtils.attachAdditionalInformationForTokenRequest(extendParameters, null, null, httpServletRequest);
        }

        // 准备权限信息
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new CustomGrantedAuthority("ROLE_USER"));

        // 复制用户信息，设置权限
        YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
        yeelightUser.setGrantedAuthorities(authorities);

        // 记录操作日志
        BizOperateLogUtils.sendSimpleBizOperateLog(yeelightUser.getId(), BizTypeEnums.三方集成.getCode(), "FACEBOOK登陆请求", request);
        BizOperateLogUtils.sendSimpleBizOperateLog(yeelightUser.getId(), BizTypeEnums.三方集成.getCode(), "FACEBOOK登陆", extendParameters);

        // 生成并返回访问令牌
        return Result.success(oauthKnifeService.fastCreateAccessToken(request.getYeelightClientId(), yeelightUser, extendParameters));
    }


    /**
     * 将Facebook登录请求转换为认证用户信息。
     * @param request Facebook登录请求对象，不可为null。
     * @return AuthUser 认证用户信息对象，如果请求为null，则返回null。
     * @throws IOException 当网络请求失败时抛出。
     */
    private AuthUser requestToAuthUser(FaceBookLoginRequest request) throws IOException {
        // 检查请求对象是否为null
        if (Objects.isNull(request)) {
            return null;
        }

        // 创建代理对象，如果代理主机和端口不为空
        Proxy proxy = null;
        if (StringUtils.isNotBlank(proxyHost) && Objects.nonNull(proxyPort)) {
            proxy = new Proxy(Proxy.Type.SOCKS, InetSocketAddress.createUnresolved(
                    proxyHost, proxyPort));
        }

        // 使用OkHttp构建客户端实例，并设置代理
        OkHttpClient client = new OkHttpClient.Builder()
                .proxy(proxy).build();

        // 根据供应商类型动态设置需要获取的用户信息字段
        String fields = "id,name,email";
        if (UserVendorEnum.DEFAULT.getCode().equals(UserVendorHolder.getVendor())) {
            fields = fields.concat(",birthday,gender,hometown,devices,picture.width(400),link");
        }

        // 构建获取用户信息的URL，并携带访问令牌和所需字段
        String infoUrl = UrlBuilder.fromBaseUrl(AuthDefaultSource.FACEBOOK.userInfo())
                .queryParam(YeelightOAuth2AccessToken.ACCESS_TOKEN, request.getFacebookToken())
                .queryParam("fields", fields)
                .build();

        // 发起HTTP GET请求获取用户信息
        Request httpRequest = new Request.Builder().url(infoUrl).get().build();
        try (Response response = client.newCall(httpRequest).execute()){
            // 检查HTTP响应是否成功
            int code = response.code();
            if (!response.isSuccessful()) {
                throw new BizException(String.valueOf(code), response.body().string());
            }

            // 处理响应中的错误信息
            JSONObject object = JSONObject.parseObject(response.body().string());
            if (object.containsKey(Constants.ERROR_KEY)) {
                throw new AuthException(object.getString(Constants.ERROR_KEY).concat(" ") + object.getString(Constants.ERROR_DESCRIPTION_KEY));
            }
            if (object.containsKey(Constants.MESSAGE_KEY)) {
                throw new AuthException(object.getString(Constants.MESSAGE_KEY));
            }

            // 解析并构建AuthUser对象
            return AuthUser.builder()
                    .rawUserInfo(object)
                    .uuid(object.getString("id"))
                    .username(object.getString("name"))
                    .nickname(object.getString("name"))
                    .blog(object.getString("link"))
                    .avatar(getUserPicture(object))
                    .location(object.getString("locale"))
                    .email(object.getString("email"))
                    .gender(AuthUserGender.UNKNOWN)
                    .token(AuthToken.builder()
                            .accessToken(request.getFacebookToken())
                            .expireIn(request.getFacebookTokenExpireIn())
                            .uid(object.getString("id"))
                            .userId(object.getString("id"))
                            .openId(object.getString("id"))
                            .unionId(object.getString("id"))
                            .build())
                    .source(request.getSource())
                    .build();
        } catch (IOException e) {
            // 记录IO异常日志，并抛出业务异常
            log.warn("get request-{} result failed because of ioException-{}", httpRequest.url(), e.getMessage());
            throw new BizException(EXCEPTION_ID_IO_EXCEPTION, e.getMessage());
        }
    }


    /**
     * 从JSONObject中获取用户图片的URL。
     * @param object 包含用户图片信息的JSONObject对象。
     * @return 返回用户图片的URL字符串，如果不存在则返回null。
     */
    private String getUserPicture(JSONObject object) {
        String picture = null;
        // 检查对象中是否包含"picture"键
        if (object.containsKey(PICTURE)) {
            JSONObject pictureObj = object.getJSONObject("picture");
            // 获取"picture"键对应的JSONObject对象中的"data"键
            pictureObj = pictureObj.getJSONObject("data");
            if (null != pictureObj) {
                // 从"data"对象中获取并返回"url"键对应的字符串
                picture = pictureObj.getString("url");
            }
        }
        return picture;
    }

    /**
     * 处理facebook删除请求
     * @param request facebook删除请求对象
     * @param httpServletRequest HTTP请求对象
     * @return JSONObject 返回删除请求的结果
     * @throws Exception 抛出异常的处理。
     */
    @PostMapping(value = "/deletion")
    public JSONObject deletion(@RequestBody @Valid FacebookDeletionRequest request, HttpServletRequest httpServletRequest) throws Exception {
        JSONObject requestJson = parseFacebookSignedRequest(request.getSignedRequest(), clientSecret);
        // todo https://developers.facebook.com/docs/development/create-an-app/app-dashboard/data-deletion-callback
        JSONObject result = new JSONObject();
        result.put("url", httpServletRequest.getRequestURL() + "/" + requestJson.get("user_id"));
        result.put("confirmation_code", requestJson.get("user_id"));
        BizOperateLogUtils.sendRemoveBizOperateLog(requestJson.get("user_id"), BizTypeEnums.三方集成.getCode(), "FACEBOOK侧请求解绑", requestJson);

        return result;
    }

    /**
     * 查询facebook删除结果
     * 该接口通过facebook用户ID来查询删除请求的状态。
     *
     * @param facebookId 用户的facebook用户ID，用于查询指定用户的删除请求状态。
     * @return JSONObject 返回一个包含删除请求结果的JSONObject对象。
     *         包含的字段有：
     *         - algorithm: 使用的加密算法，此处为"HMAC-SHA256"。
     *         - expires: 结果的有效期，以秒为单位的UNIX时间戳。
     *         - issued_at: 结果的发放时间，以秒为单位的UNIX时间戳。
     *         - user_id: 查询的facebook用户ID。
     */
    @GetMapping(value = "/deletion/{userId}")
    public JSONObject deletionStatus(@PathVariable("userId") String facebookId) {
        JSONObject result = new JSONObject();
        // 初始化结果对象，设置算法、有效期、发放时间和用户ID
        result.put(ALGORITHM, HMAC_SHA_256);
        result.put("expires", Instant.now().getEpochSecond());
        result.put("issued_at", Instant.now().getEpochSecond());
        result.put("user_id", facebookId);
        return result;
    }


    /**
     * 解析Facebook的签名请求。
     *
     * @param signedRequest 待解析的签名请求字符串，格式为"signature.data"。
     * @param secret 用于验证签名的密钥。
     * @return 一个JSONObject，包含解析出的数据。如果签名验证失败或算法不匹配，则返回null。
     * @throws Exception 如果解析过程中出现异常。
     */
    private JSONObject parseFacebookSignedRequest(String signedRequest, String secret) throws Exception {
        // 将请求字符串拆分为签名和数据两部分
        String[] signedRequests = signedRequest.split("\\.", 2);
        // 解析签名
        String sig = signedRequests[0];

        // 解析数据并转换为JSONObject
        String data = signedRequests[1];
        // 假设编码为UTF-8
        JSONObject jsonData = JSONObject.parseObject(new String(Base64.decodeBase64(data), StandardCharsets.UTF_8));
        // 检查签名算法是否为HMAC-SHA256
        String algorithm = jsonData.getString("algorithm");
        if(!HMAC_SHA_256.equals(algorithm)) {
            // 使用了未知的算法
            return null;
        }

        // 检查数据是否正确签名
        if(!hmacSha256(signedRequests[1], secret).equals(sig)) {
            // 签名不匹配，可能数据被篡改
            return null;
        }
        return jsonData;
    }

    /**
     * 使用HMAC-SHA256算法生成签名
     * 此方法用于数据完整性校验，通过给定的数据和密钥生成一个HMAC-SHA256签名
     * 选择HMAC-SHA256算法是因为其安全性较高，能够提供较强的完整性校验
     *
     * @param data 要生成签名的数据，可以是任意长度的字符串
     * @param key 用于签名的密钥，也是任意长度的字符串密钥，用于保证数据的私密性和安全性
     * @return 返回Base64编码的URL安全的HMAC-SHA256签名
     * @throws Exception 如果密钥或数据格式不正确，或者算法实现不可用，则抛出异常
     */
    private String hmacSha256(String data, String key) throws Exception {
        // 创建一个秘密密钥，使用UTF-8编码的密钥字节数组和"HmacSHA256"算法
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");

        // 获取"HmacSHA256"算法的消息认证码对象
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(secretKey);

        // 对数据进行HMAC签名，使用UTF-8编码的数据字节数组
        byte[] hmacData = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));

        // 返回Base64编码的URL安全的HMAC数据签名，使用UTF-8字符集进行字符串编码
        return new String(Base64.encodeBase64URLSafe(hmacData), StandardCharsets.UTF_8);
    }

}