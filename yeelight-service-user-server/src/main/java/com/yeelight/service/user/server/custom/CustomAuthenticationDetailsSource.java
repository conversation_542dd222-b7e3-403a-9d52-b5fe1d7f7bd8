package com.yeelight.service.user.server.custom;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaImageDubboService;
import com.yeelight.basic.platform.rpc.utils.CaptchaUtils;
import com.yeelight.service.user.server.constant.SecurityConstants;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 自定义获取AuthenticationDetails 用于封装传进来的验证码
 * <AUTHOR>
 */
@Component
public class CustomAuthenticationDetailsSource implements AuthenticationDetailsSource<HttpServletRequest, WebAuthenticationDetails> {

    @DubboReference(version = "0.0.1",timeout = 2000)
    private CaptchaImageDubboService captchaImageHelper;

    /**
     * 构建Web认证详情对象。
     * 该方法会从请求中读取验证码相关的Cookie，并将验证码内容存入请求属性中。
     *
     * @param request HttpServletRequest对象，用于获取客户端请求信息。
     * @return CustomWebAuthenticationDetails 认证详情对象，包含了请求的认证信息。
     */
    @Override
    public WebAuthenticationDetails buildDetails(HttpServletRequest request) {
        // 尝试从请求的Cookie中获取验证码键值
        Cookie captchaKeyCookie = CaptchaUtils.getCookie(request.getCookies(), CaptchaResult.FIELD_CAPTCHA_KEY);
        if (captchaKeyCookie != null) {
            // 如果存在验证码键值Cookie，则尝试从缓存中获取对应的验证码内容
            String cacheCaptcha = captchaImageHelper.getCaptcha(SecurityConstants.CAPTCHA_CACHE_PREFIX, captchaKeyCookie.getValue());
            // 将获取到的验证码内容存入请求属性中，以便后续使用
            request.setAttribute(CustomWebAuthenticationDetails.FIELD_CACHE_CAPTCHA, cacheCaptcha);
        }
        // 创建并返回自定义的Web认证详情对象
        return new CustomWebAuthenticationDetails(request);
    }
}
