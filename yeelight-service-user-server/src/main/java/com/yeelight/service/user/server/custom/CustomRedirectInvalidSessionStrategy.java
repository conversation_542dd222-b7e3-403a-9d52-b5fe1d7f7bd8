/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.custom
 * Description: 自定义
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-11-21 09:58:09:58
 */
package com.yeelight.service.user.server.custom;

import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.session.InvalidSessionStrategy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 当检测到无效请求的会话时，执行重定向到固定 URL 的策略类，替代默认的SimpleRedirectInvalidSessionStrategy
 * 用户请求携带无效的 JSESSIONID 访问时的处理策略，即对应的 Session 会话失效
 * {@code SessionManagementFilter}.
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-11-21 09:58:09:58
 */
@Slf4j
@Component
public class CustomRedirectInvalidSessionStrategy implements InvalidSessionStrategy {
    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();
    private boolean createNewSession = true;

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    /**
     * 当检测到无效会话时的处理逻辑。
     * 这个方法会清除浏览器中的会话Cookie，如果需要，创建一个新的会话，
     * 并根据请求的类型，要么响应JSON数据，要么重定向到登录页面。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求
     * @param response HttpServletResponse对象，用于向客户端发送响应
     * @throws IOException 如果在处理过程中发生IO错误
     */
    @Override
    public void onInvalidSessionDetected(HttpServletRequest request,
                                         HttpServletResponse response) throws IOException {
        // 获取登录页面的URL
        String destinationUrl = gatewayOauthConfig.getLoginPageUrl(request);

        // 清除浏览器中的无效的 JSESSIONID
        AuthUtils.clearCookie(request, response);

        if (createNewSession) {
            request.getSession();
        }
        String msg = "session is invalid, please log in again!";
        log.info("InvalidSessionDetected: Starting new session (if required) and redirecting to '"
                + destinationUrl + "'");

        // 判断请求是否来自前后端分离的架构
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            // 对于前后端分离的请求，返回JSON格式的错误信息
            Map<String, Object> data = new HashMap<>(2);
            data.put(SecurityConstants.REDIRECT_URL_KEY, destinationUrl);
            AuthUtils.authFail(response, ResultCodeEnum.会话失效.getIntegerCode(), msg, data);
        } else {
            // 对于非前后端分离的请求，保存异常信息到会话，并重定向到登录页面
            AuthenticationException e = new AuthenticationServiceException(msg);
            request.getSession().setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, e);
            log.info("InvalidSessionDetected: Starting new session (if required) and redirecting to '{}'", destinationUrl);
            // 重定向到登录页面
            redirectStrategy.sendRedirect(request, response, destinationUrl);
        }
    }

    /**
     * Determines whether a new session should be created before redirecting (to avoid
     * possible looping issues where the same session ID is sent with the redirected
     * request). Alternatively, ensure that the configured URL does not pass through the
     * {@code SessionManagementFilter}.
     *
     * @param createNewSession defaults to {@code true}.
     */
    public void setCreateNewSession(boolean createNewSession) {
        this.createNewSession = createNewSession;
    }
}