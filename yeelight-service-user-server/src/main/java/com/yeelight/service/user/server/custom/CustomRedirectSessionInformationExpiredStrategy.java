/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.custom
 * Description: 自定义session过期策略
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-11-21 10:13:10:13
 */
package com.yeelight.service.user.server.custom;

import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Desc: 自定义session过期策略
 * 当检测到过期会话时，执行到固定 URL 的重定向策略类，替代默认的SimpleRedirectSessionInformationExpiredStrategy
 * 前提：Session 并发处理的配置为 maxSessionsPreventsLogin(false)
 * 用户的并发 Session 会话数量达到上限，新会话登录后，最老会话会在下一次请求中失效，并执行此策略
 * <AUTHOR> [<EMAIL>]
 * @since 2023-11-21 10:13:10:13
 */
@Slf4j
@Component
public class CustomRedirectSessionInformationExpiredStrategy implements SessionInformationExpiredStrategy {
    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    /**
     * 当会话信息过期事件发生时的处理逻辑。
     * 该方法重写了SessionInformationExpiredEvent的监听方法，用于处理用户会话过期的情况。
     * @param event 会话信息过期事件，包含请求、响应和会话信息等数据。
     * @throws IOException 当进行响应重定向时可能抛出的IO异常。
     */
    @Override
    public void onExpiredSessionDetected(SessionInformationExpiredEvent event) throws IOException {
        HttpServletRequest request = event.getRequest();
        HttpServletResponse response = event.getResponse();
        // 获取登录页面的URL
        String destinationUrl = gatewayOauthConfig.getLoginPageUrl(event.getRequest());
        // 获取被踢下线的用户信息
        UserDetails userDetails = (UserDetails) event.getSessionInformation().getPrincipal();
        // 编辑会话过期时的提示信息
        String msg = String.format("User [%s] logged in on another machine and you are offline!", userDetails.getUsername());
        log.info("SessionInformationExpired: Redirecting to '{}'", destinationUrl);

        // 判断请求是否来自前后端分离的架构
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            // 如果是前后端分离的请求，则返回JSON格式的错误信息和重定向URL
            Map<String, Object> data = new HashMap<>(1);
            data.put(SecurityConstants.REDIRECT_URL_KEY, destinationUrl);
            AuthUtils.authFail(response, ResultCodeEnum.会话过期.getIntegerCode(), msg, data);
        } else {
            // 如果不是，则将错误信息保存到会话中，并重定向到登录页面
            AuthenticationException e = new AuthenticationServiceException(msg);
            request.getSession().setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, e);
            log.info("SessionInformationExpired: Redirecting to '{}'", destinationUrl);
            // 重定向到登录页面
            redirectStrategy.sendRedirect(request, response, destinationUrl);
        }
    }
}