package com.yeelight.service.user.server.custom;

import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.token.UsernameLimit;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.custom.token.CustomAuthenticationKeyGenerator;
import com.yeelight.service.user.server.custom.token.FastjsonRedisTokenStoreSerializationStrategy;
import com.yeelight.service.user.server.utils.JwtUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.security.oauth2.common.*;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.AuthenticationKeyGenerator;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStoreSerializationStrategy;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @program: yeelight-oauth-api
 * @description: 自定义RedisTokenStore，增加用户登录端数量限制，超出则自动清除最先登录的token
 * @author: Sheldon
 * @create: 2019-07-10 10:31
 **/
@Slf4j
public class CustomRedisTokenStore implements TokenStore {

    /**
     * 保存到redis的key前缀, 用于标识访问权限的前缀
     */
    private static final String ACCESS = "access:";

    /**
     * Redis key前缀，用于存储访问令牌信息
     */
    private static final String AUTH = "auth:";

    /**
     * Redis key前缀，用于存储从授权码到访问令牌的映射
     */
    private static final String AUTH_TO_ACCESS = "auth_to_access:";

    /**
     * Redis key前缀，用于存储从访问令牌到刷新令牌的映射
     */
    private static final String ACCESS_TO_REFRESH = "access_to_refresh:";

    /**
     * Redis key前缀，用于存储刷新令牌信息
     */
    private static final String REFRESH_AUTH = "refresh_auth:";

    /**
     * Redis key前缀，用于存储刷新令牌信息
     */
    private static final String REFRESH = "refresh:";

    /**
     * Redis key前缀，用于存储从刷新令牌到访问令牌的映射
     */
    private static final String REFRESH_TO_ACCESS = "refresh_to_access:";

    /**
     * Redis key前缀，用于存储从客户端ID到访问令牌的映射
     */
    private static final String CLIENT_ID_TO_ACCESS = "client_id_to_access:";

    /**
     * Redis key前缀，用于存储从各应用的用户名到访问令牌的映射, 用于分应用查询用户登录信息
     */
    private static final String APPROVAL_UNAME_TO_ACCESS = "uname_to_access:";

    /**
     * Redis key前缀，用于存储从用户名到访问令牌的映射
     */
    private static final String USERNAME_TO_ACCESS = "username_to_access:";

    /**
     * Redis key前缀，用于存储用户名限制列表
     */
    private static final String USERNAME_LIMIT_LIST = "username_limit_list:";

    /**
     * Redis key前缀，用于标记延迟删除的刷新令牌
     */
    private static final String DELAY_REMOVE_REFRESH_TOKEN_KEY = "delay_remove:refresh_token:";

    /**
     * Redis key前缀，用于标记当用户名访问限额超过限制时，移除最旧的访问令牌时的分布式锁
     */
    private static final String REMOVE_TOKEN_WHEN_USERNAME_OVER_LIMIT_LOCK = "lock:remove_token_when_username_over_limit_lock:";

    /**
     * Redis key前缀，用于标记清理无效token引用的分布式锁
     */
    private static final String CLEANUP_INVALID_TOKEN_REFERENCES_LOCK = "lock:cleanup_invalid_token_references_lock";

    /**
     * Redis key前缀，用于标记当前是否正在清理
     */
    private static final String TOKEN_CLEANUP_LOCK = "scheduled:token_cleanup_lock";

    /**
     * 每个用户允许登录的端数量，超出则自动清除最先登录的
     */
    private static final Long USERNAME_ACCESS_LIMIT = 20L;
    /**
     * 刷新token后，上一个accessToken的过期时间,单位：秒
     */
    private static final int REFRESH_LAST_ACCESS_EXPIRE_TIME = 600;

    /**
     * 定期清理过期token, 每批处理的token数量
     */
    private static final int BATCH_SIZE = 500;

    /**
     * 最大处理时间(秒)
     */
    private static final int MAX_PROCESS_TIME = 300;

    /**
     * 检查是否使用 Spring Data Redis 2.0 或更高版本。
     */
    private static final boolean SPRING_DATA_REDIS_2_0 = ClassUtils.isPresent(
            "org.springframework.data.redis.connection.RedisStandaloneConfiguration",
            org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore.class.getClassLoader());

    /**
     * Redis 管理器，用于与 Redis 进行交互。
     */
    private final RedisManager redisManager;

    /**
     * 认证密钥生成器，默认使用自定义认证密钥生成器。
     */
    @Setter
    private AuthenticationKeyGenerator authenticationKeyGenerator = new CustomAuthenticationKeyGenerator();

    /**
     * Redis Token 存储序列化策略，默认使用 Fastjson 进行序列化。
     */
    @Setter
    private RedisTokenStoreSerializationStrategy serializationStrategy = new FastjsonRedisTokenStoreSerializationStrategy();

    /**
     * Token 键前缀，默认为空字符串。
     */
    @Setter
    private String prefix = "";

    /**
     * 缓存的方法对象，用于设置 Spring Data Redis 2.0 或更高版本中的 Redis 连接。
     */
    private Method redisConnectionSet20;

    /**
     * 构造方法
     *
     * @param redisManager Redis 管理器
     */
    public CustomRedisTokenStore(RedisManager redisManager) {
        this.redisManager = redisManager;
        if (SPRING_DATA_REDIS_2_0) {
            this.loadRedisConnectionMethods20();
        }
    }

    /**
     * 加载 Spring Data Redis 2.0 或更高版本中的 Redis 连接方法。
     */
    private void loadRedisConnectionMethods20() {
        this.redisConnectionSet20 = ReflectionUtils.findMethod(
                RedisConnection.class, "set", byte[].class, byte[].class);
    }

    /**
     * 获取 Redis 连接
     *
     * @return Redis 连接
     */
    private RedisConnection getConnection() {
        return redisManager.getConnectionFactory().getConnection();
    }

    /**
     * 序列化对象为字节码
     *
     * @param object 需要被序列化的对象
     * @return 序列化后的字节码数组
     */
    private byte[] serialize(Object object) {
        return serializationStrategy.serialize(object);
    }

    /**
     * 特定字符串对象序列化为字节码
     *
     * @param object 需要被序列化的字符串对象
     * @return 序列化后的字节码数组
     */
    private byte[] serializeKey(String object) {
        return serialize(prefix + object);
    }

    private String serializeKeyStr(String object) {
        return prefix + object;
    }

    /**
     * 将字节码反序列化为OAuth2访问令牌对象
     *
     * @param bytes 需要被反序列化的字节码数组
     * @return 反序列化后的OAuth2访问令牌对象
     */
    private OAuth2AccessToken deserializeAccessToken(byte[] bytes) {
        return serializationStrategy.deserialize(bytes, OAuth2AccessToken.class);
    }

    /**
     * 将字节码反序列化为OAuth2认证对象
     *
     * @param bytes 需要被反序列化的字节码数组
     * @return 反序列化后的OAuth2认证对象
     */
    private OAuth2Authentication deserializeAuthentication(byte[] bytes) {
        return serializationStrategy.deserialize(bytes, OAuth2Authentication.class);
    }

    /**
     * 将字节码反序列化为OAuth2刷新令牌对象
     *
     * @param bytes 需要被反序列化的字节码数组
     * @return 反序列化后的OAuth2刷新令牌对象
     */
    private OAuth2RefreshToken deserializeRefreshToken(byte[] bytes) {
        return serializationStrategy.deserialize(bytes, OAuth2RefreshToken.class);
    }

    /**
     * 将字节码反序列化为用户名限制对象
     *
     * @param bytes 需要被反序列化的字节码数组
     * @return 反序列化后的用户名限制对象
     */
    private UsernameLimit deserializeUsernameLimit(byte[] bytes) {
        return serializationStrategy.deserialize(bytes, UsernameLimit.class);
    }

    /**
     * 字符串对象序列化为字节码
     *
     * @param string 需要被序列化的字符串
     * @return 序列化后的字节码数组
     */
    private byte[] serialize(String string) {
        return serializationStrategy.serialize(string);
    }

    /**
     * 将字节码反序列化为字符串
     *
     * @param bytes 需要被反序列化的字节码数组
     * @return 反序列化后的字符串
     */
    private String deserializeString(byte[] bytes) {
        return serializationStrategy.deserializeString(bytes);
    }

    /**
     * 通过认证信息获取访问令牌
     *
     * @param authentication 认证信息，包含用户的认证数据
     * @return OAuth2AccessToken 返回提取的访问令牌，如果存在的话
     */
    @Override
    public OAuth2AccessToken getAccessToken(OAuth2Authentication authentication) {
        if (authentication == null) {
            return null;
        }

        // 生成认证信息的唯一键
        String authKey = authenticationKeyGenerator.extractKey(authentication);
        // 将认证信息键序列化，用于在Redis中存储
        byte[] serializedKey = serializeKey(AUTH_TO_ACCESS + authKey);
        OAuth2AccessToken accessToken;
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            conn.openPipeline();
            
            // 获取访问令牌
            conn.get(serializedKey);
            
            List<Object> results = conn.closePipeline();
            byte[] bytes = (byte[]) results.get(0);
            
            // 反序列化获取到的字节数据，转换为访问令牌对象
            try {
                accessToken = deserializeAccessToken(bytes);
            } catch (Exception e) {
                log.warn("无法解析授权密钥的访问令牌: {}", authKey, e);
                return null;
            }

            // 如果找不到访问令牌,直接返回null
            if (accessToken == null) {
                log.info("找不到认证密钥的访问令牌: {}", authKey);
                return null;
            }

            // 检查token是否过期
            if (accessToken.isExpired()) {
                log.info("授权密钥的访问令牌已过期: {}", authKey);
                // 移除过期的访问令牌
                removeAccessToken(accessToken);
                return null;
            }

            // 通过访问令牌读取认证信息
            OAuth2Authentication storedAuthentication = readAuthentication(accessToken.getValue());
            
            // 检查存储的认证信息是否存在
            if (storedAuthentication == null) {
                log.warn("未找到存储的访问令牌的认证: {}", accessToken.getValue());
                // 不存在则移除访问令牌
                removeAccessToken(accessToken);
                return null;
            }

            // 检查存储的认证信息是否一致
            String storedKey = authenticationKeyGenerator.extractKey(storedAuthentication);
            if (!authKey.equals(storedKey)) {
                // Keep the stores consistent (maybe the same user is
                // represented by this authentication but the details have
                // changed)
                log.warn("存储的认证信息不一致, 更新存储的访问令牌, 存储的 key: {}, 当前 key: {}", storedKey, authKey);
                // 认证信息不一致,更新存储
                storeAccessToken(accessToken, authentication);
            }

            return accessToken;

        } catch (Exception e) {
            log.warn("获取认证密钥的访问令牌时出错: {}", authKey, e);
            return null;
        } finally {
            conn.close();
        }
    }

    /**
     * 根据OAuth2AccessToken的值读取认证信息
     *
     * @param token OAuth2AccessToken对象
     * @return OAuth2Authentication认证信息
     */
    @Override
    public OAuth2Authentication readAuthentication(OAuth2AccessToken token) {
        return readAuthentication(token.getValue());
    }

    /**
     * 根据令牌字符串读取认证信息
     *
     * @param token 令牌字符串
     * @return OAuth2Authentication认证信息
     */
    @Override
    public OAuth2Authentication readAuthentication(String token) {
        byte[] bytes;
        RedisConnection conn = getConnection();
        try {
            bytes = conn.get(serializeKey(AUTH + token));
        } finally {
            conn.close();
        }
        return deserializeAuthentication(bytes);
    }

    /**
     * 根据OAuth2RefreshToken的值读取刷新令牌的认证信息
     *
     * @param refreshToken OAuth2RefreshToken对象
     * @return OAuth2Authentication认证信息
     */
    @Override
    public OAuth2Authentication readAuthenticationForRefreshToken(OAuth2RefreshToken refreshToken) {
        return readAuthenticationForRefreshToken(refreshToken.getValue());
    }

    /**
     * 根据刷新令牌字符串读取认证信息
     *
     * @param refreshToken 刷新令牌字符串
     * @return OAuth2Authentication认证信息
     */
    public OAuth2Authentication readAuthenticationForRefreshToken(String refreshToken) {
        RedisConnection conn = getConnection();
        try {
            byte[] bytes = conn.get(serializeKey(REFRESH_AUTH + refreshToken));
            return deserializeAuthentication(bytes);
        } finally {
            conn.close();
        }
    }

    /**
     * 存储访问令牌及关联认证信息
     * <p>
     * 本方法负责将OAuth2访问令牌（AccessToken）及其相关的认证信息存储到Redis中
     * 它序列化访问令牌和认证信息，并根据一定的键命名规则存储到Redis，
     * 同时处理访问令牌的过期时间、客户端限额及刷新令牌等逻辑
     *
     * @param token 访问令牌对象，包含令牌及其过期时间等信息
     * @param authentication 认证对象，包含关于认证的所有信息，如客户端ID、用户名等
     */
    @Override
    public void storeAccessToken(OAuth2AccessToken token, OAuth2Authentication authentication) {
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            try {
                conn.multi();
                // 序列化访问令牌和认证信息
                byte[] serializedAccessToken = serialize(token);
                byte[] serializedAuth = serialize(authentication);

                // 生成Redis键
                byte[] accessKey = serializeKey(ACCESS + token.getValue());
                byte[] authKey = serializeKey(AUTH + token.getValue());
                byte[] authToAccessKey = serializeKey(AUTH_TO_ACCESS + authenticationKeyGenerator.extractKey(authentication));
                byte[] approvalKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authentication));
                byte[] clientId = serializeKey(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
                String userNameLimitKeyStr = USERNAME_LIMIT_LIST + getApprovalKey(authentication);
                byte[] usernameLimitKey = serializeKey(userNameLimitKeyStr);

                // 初始化用户名限额对象，记录相关键信息
                UsernameLimit usernameLimit = new UsernameLimit();
                usernameLimit.setAccessKey(ACCESS + token.getValue());
                usernameLimit.setAuthKey(AUTH + token.getValue());
                usernameLimit.setAuthToAccessKey(AUTH_TO_ACCESS + authenticationKeyGenerator.extractKey(authentication));
                usernameLimit.setApprovalKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authentication));
                usernameLimit.setClientId(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());

                // 检查用户名访问限额，如果超过限额则移除旧的访问令牌
                Long usernameLimitLen = conn.lLen(usernameLimitKey);
                
                // 在事务中执行所有写操作
                if (Objects.nonNull(usernameLimitLen) && usernameLimitLen >= USERNAME_ACCESS_LIMIT) {
                    log.warn("用户名访问限额超过限制，移除最旧的访问令牌, usernameLimitKey: {}", userNameLimitKeyStr);
                    removeTokenWhenOverUsernameLimit(usernameLimitKey);
                }
                conn.get(accessKey);
                conn.set(accessKey, serializedAccessToken);
                conn.set(authKey, serializedAuth);
                conn.set(authToAccessKey, serializedAccessToken);
                List<Object> results = conn.exec();
                
                // 检查事务结果
                if (results == null) {
                    conn.discard(); // 显式回滚事务
                    // 事务失败,清理部分写入的数据
                    cleanupPartialTokenData(token, authentication);
                    throw new RuntimeException("Failed to store access token");
                }
                
                byte[] oldSerializedAccessToken = (byte[]) results.get(1);

                // 开始另一组管道操作，处理认证信息非仅客户端的情况
                conn.openPipeline();
                // 处理用户名和访问令牌关系
                handleUserNameAndAccessRel(authentication, oldSerializedAccessToken, conn, approvalKey, serializedAccessToken, usernameLimit);

                // 如果存在旧的访问令牌，则从客户端列表中移除
                if (Objects.nonNull(oldSerializedAccessToken)) {
                    // 从客户端列表中移除旧的访问令牌
                    log.warn("旧的访问令牌存在，从客户端列表中移除, clientId: {}", authentication.getOAuth2Request().getClientId());
                    conn.lRem(clientId, 0, oldSerializedAccessToken);
                }
                // 将新的访问令牌添加到客户端列表
                conn.rPush(clientId, serializedAccessToken);

                // 处理访问令牌过期逻辑
                handleAccessTokenExpiration(token, authentication, conn, Arrays.asList(accessKey, authKey, authToAccessKey, clientId, approvalKey));

                // 在存储访问令牌时设置刷新令牌
                setRefreshTokenWhenStoreAccessToken(token, usernameLimit, conn);

                // 更新用户名限额列表
                log.info("更新用户名限额列表, 删除头部旧的用户名限额, 尾部写入新的用户名限额, usernameLimitKey: {}", userNameLimitKeyStr);

                // 1. 直接删除可能存在的旧记录
                conn.lRem(usernameLimitKey, 0, serialize(usernameLimit));
                
                // 2. 添加新的记录到列表尾部, 从右侧(尾部)添加新token
                // 列表的结构：[oldest token] <- [token2] <- [token3] <- [newest token]  // 从左到右表示时间顺序
                //   (head/left)                              (tail/right)
                conn.rPush(usernameLimitKey, serialize(usernameLimit));
                
                // 3. 设置过期时间: 如果访问令牌有设置过期时间，则为用户名限额键设置过期时间
                if (token.getExpiration() != null) {
                    int seconds = token.getExpiresIn();
                    conn.expire(usernameLimitKey, seconds);
                }
                
                // 4. 提交事务
                conn.exec();
            } catch (Exception e) {
                conn.discard(); // 确保异常时也回滚事务
                cleanupPartialTokenData(token, authentication);
                log.warn("更新用户名限额列表失败: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to store access token", e);
            }
        } finally {
            conn.close();
        }
    }

    /**
     * 清理部分令牌数据, 用于事务失败时清理部分写入的数据
     * @param token 访问令牌
     * @param authentication 认证信息
     */
    private void cleanupPartialTokenData(OAuth2AccessToken token, OAuth2Authentication authentication) {
        // 清理可能部分写入的数据
        RedisConnection conn = getConnection();
        try {
            conn.openPipeline();
            
            // 清理访问令牌相关的键
            byte[] accessKey = serializeKey(ACCESS + token.getValue());
            byte[] authKey = serializeKey(AUTH + token.getValue());
            byte[] authToAccessKey = serializeKey(AUTH_TO_ACCESS + authenticationKeyGenerator.extractKey(authentication));
            byte[] approvalKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authentication));
            byte[] clientId = serializeKey(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
            byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + token.getValue());
            
            // 删除所有相关的键
            conn.del(accessKey);
            conn.del(authKey); 
            conn.del(authToAccessKey);
            conn.del(approvalKey);
            conn.del(clientId);
            conn.del(accessToRefreshKey);

            // 如果存在刷新令牌,也清理相关的键
            OAuth2RefreshToken refreshToken = token.getRefreshToken();
            if (refreshToken != null) {
                byte[] refreshKey = serializeKey(REFRESH + refreshToken.getValue());
                byte[] refreshAuthKey = serializeKey(REFRESH_AUTH + refreshToken.getValue());
                byte[] refreshToAccessKey = serializeKey(REFRESH_TO_ACCESS + refreshToken.getValue());
                
                conn.del(refreshKey);
                conn.del(refreshAuthKey);
                conn.del(refreshToAccessKey);
            }

            // 如果认证信息包含用户名,清理用户名相关的键
            if (!StringUtils.isEmpty(authentication.getName())) {
                byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + authentication.getName());
                byte[] usernameLimitKey = serializeKey(USERNAME_LIMIT_LIST + getApprovalKey(authentication));
                
                conn.del(usernameKey);
                conn.del(usernameLimitKey);
            }

            conn.closePipeline();
            
        } catch (Exception e) {
            log.warn("清理部分令牌数据失败", e);
            throw new RuntimeException("Failed to cleanup partial token data", e);
        } finally {
            conn.close();
        }
    }


    /**
     * 处理用户名和访问令牌关系
     * 当存储访问令牌时，确保用户名和访问令牌之间的关系正确
     * 如果认证信息中包含用户名，则将访问令牌与用户名关联
     * 如果认证信息中不包含用户名，则不进行任何操作
     *
     * @param authentication 认证信息，包含客户端和用户信息
     * @param oldSerializedAccessToken 旧的序列化访问令牌，可能为null
     * @param conn Redis连接，用于操作Redis数据
     * @param approvalKey 批准键，用于在Redis中标识批准列表
     * @param serializedAccessToken 新的序列化访问令牌
     * @param usernameLimit 用户名限制器，用于跟踪用户名相关的访问令牌
     */
    private void handleUserNameAndAccessRel(OAuth2Authentication authentication, byte[] oldSerializedAccessToken, RedisConnection conn, byte[] approvalKey, byte[] serializedAccessToken, UsernameLimit usernameLimit) {
        // 确保认证主体不是仅客户端
        if (authentication != null && !authentication.isClientOnly()) {
            // 如果旧的访问令牌存在，则从批准列表中移除
            if (Objects.nonNull(oldSerializedAccessToken)) {
                log.warn("旧的访问令牌存在，从批准列表中移除, approvalKey: {}", APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authentication));
                conn.lRem(approvalKey, 0, oldSerializedAccessToken);
            }
            
            // 验证新的访问令牌是否有效
            if (serializedAccessToken == null) {
                log.warn("新的访问令牌序列化结果为空");
                throw new IllegalStateException("New access token serialization is null");
            }
            
            // 将新的访问令牌添加到批准列表
            conn.rPush(approvalKey, serializedAccessToken);
            
            // 如果认证名称非空
            if (!StringUtils.isEmpty(authentication.getName())) {
                // 生成用户名键，用于在Redis中标识用户名相关的访问令牌
                byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + authentication.getName());
                // 设置用户名键到用户名限制器，以便后续操作
                usernameLimit.setUsernameKey(USERNAME_TO_ACCESS + authentication.getName());
                
                try {
                    // 如果旧的访问令牌存在，则从用户名相关的列表中移除
                    if (Objects.nonNull(oldSerializedAccessToken)) {
                        log.warn("旧的访问令牌存在，从用户名相关的列表中移除, usernameKey: {}", USERNAME_TO_ACCESS + authentication.getName());
                        conn.lRem(usernameKey, 0, oldSerializedAccessToken);
                    }
                    
                    // 将新的访问令牌添加到用户名相关的列表
                    conn.rPush(usernameKey, serializedAccessToken);
                } catch (Exception e) {
                    log.warn("处理用户名访问令牌关系失败: {}", authentication.getName(), e);
                    // 回滚已执行的操作
                    conn.lRem(approvalKey, 0, serializedAccessToken);
                    throw new RuntimeException("Failed to handle username access token relationship", e);
                }
            }
        }
    }

    /**
     * 处理访问令牌过期逻辑
     * 当OAuth2访问令牌过期时，确保相关缓存数据同步过期
     *
     * @param token            OAuth2访问令牌
     * @param authentication   OAuth2认证信息
     * @param conn             Redis连接
     * @param tokenKeys        accessToken键列表
     */
    private void handleAccessTokenExpiration(OAuth2AccessToken token, OAuth2Authentication authentication, RedisConnection conn, List<byte[]> tokenKeys) {
        // 检查令牌是否有过期时间
        if (token.getExpiration() != null) {
            // 获取令牌过期时间（秒）
            int seconds = token.getExpiresIn();
            // 如果token已过期,则立即删除
            if (seconds <= 0) {
                for (byte[] tokenKey : tokenKeys) {
                    conn.del(tokenKey);
                }
            } else {
                // 设置相关Redis键的过期时间与令牌一致
                for (byte[] tokenKey : tokenKeys) {
                    conn.expire(tokenKey, seconds);
                }

                // 如果认证信息中的用户名不为空，也为用户名相关键设置过期时间
                if (!StringUtils.isEmpty(authentication.getName())) {
                    // 构造用户名到访问令牌的映射键
                    byte[] usernameExpireKey = serializeKey(USERNAME_TO_ACCESS + authentication.getName());
                    // 设置用户名映射键的过期时间
                    conn.expire(usernameExpireKey, seconds);
                }
            }
        }
    }

    /**
     * 当存储访问令牌时，设置刷新令牌
     * 仅当访问令牌中包含刷新令牌时，才会设置刷新令牌
     * <p>
     * 此方法的目的是在Redis中存储刷新令牌与访问令牌之间的映射关系
     * 它还设置了刷新令牌的过期时间
     *
     * @param token OAuth2访问令牌，可能包含刷新令牌
     * @param usernameLimit 用户名限制对象，用于存储Redis中的键
     * @param conn Redis连接，用于执行设置操作
     */
    private void setRefreshTokenWhenStoreAccessToken(OAuth2AccessToken token, UsernameLimit usernameLimit, RedisConnection conn) {
        // 获取刷新令牌
        OAuth2RefreshToken refreshToken = token.getRefreshToken();
        // 检查刷新令牌是否非空, 如果为空则不进行任何操作
        if (refreshToken != null && refreshToken.getValue() != null) {
            // 序列化刷新令牌值
            byte[] refresh = serialize(token.getRefreshToken().getValue());
            // 序列化访问令牌值
            byte[] accessToken = serialize(token.getValue());
            // 生成刷新令牌到访问令牌的映射键
            byte[] refreshToAccessKey = serializeKey(REFRESH_TO_ACCESS + token.getRefreshToken().getValue());
            // 生成访问令牌到刷新令牌的映射键
            byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + token.getValue());

            // 设置用户名限制对象中的键值
            usernameLimit.setAccessToRefreshKey(ACCESS_TO_REFRESH + token.getValue());
            usernameLimit.setRefreshToAccessKey(REFRESH_TO_ACCESS + token.getRefreshToken().getValue());
            usernameLimit.setRefreshKey(REFRESH + token.getRefreshToken().getValue());
            usernameLimit.setRefreshAuthKey(REFRESH_AUTH + refreshToken.getValue());

            // 根据Redis版本选择合适的方法设置映射关系
            if (SPRING_DATA_REDIS_2_0) {
                try {
                    // 使用Redis 2.0及以上版本的方法设置映射关系
                    this.redisConnectionSet20.invoke(conn, refreshToAccessKey, accessToken);
                    this.redisConnectionSet20.invoke(conn, accessToRefreshKey, refresh);
                } catch (Exception ex) {
                    // 抛出运行时异常，处理异常情况
                    throw new RuntimeException(ex);
                }
            } else {
                // 使用旧版本Redis的方法设置映射关系
                conn.set(refreshToAccessKey, accessToken);
                conn.set(accessToRefreshKey, refresh);
            }
            // 设置刷新令牌的过期时间
            log.info("访问令牌对应的旧刷新令牌存在, 重新写入刷新令牌并重新设置刷新令牌的过期时间, refresh: {}", refreshToken.getValue());
            setRefreshTokenExpiration(refreshToken, conn, Arrays.asList(refreshToAccessKey, accessToRefreshKey));
        }
    }


    /**
     * 当用户名超过访问限制时，移除对应的令牌
     * <p>
     * 本方法主要功能是当某个客户端对于特定用户名的访问请求数量超过了设定的限制后，
     * 移除最旧的访问令牌，以确保请求数量不会无限增长同时保持最近的访问记录
     *
     * @param usernameLimitKey 字节型数组，表示用户名限制的键
     *                         该键用于在Redis中定位存储访问限制信息的列表
     */
    private void removeTokenWhenOverUsernameLimit(byte[] usernameLimitKey) {
        String lockKey = REMOVE_TOKEN_WHEN_USERNAME_OVER_LIMIT_LOCK + deserializeString(usernameLimitKey);
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            // 1. 增加锁的重试机制
            int retryCount = 3;
            while (retryCount > 0) {
                // 获取分布式锁
                String lockValue = UUID.randomUUID().toString();
                Boolean locked = conn.setNX(serializeKey(lockKey), serialize(lockValue));
                if (Boolean.TRUE.equals(locked)) {
                    // 2. 增加锁的过期时间到10秒，避免死锁
                    conn.expire(serializeKey(lockKey), 10);
                    try {
                        conn.multi();
                        // 3. 直接弹出最早的token, 使用 LPOP 移除最早的 token
                        byte[] bytes = conn.lPop(usernameLimitKey);
                        if (bytes != null) {
                            // 反序列化移除的字节数据，得到用户名限制对象
                            UsernameLimit limit = deserializeUsernameLimit(bytes);
                            // 4. 移除与被限制的用户名相关的所有键
                            if (shouldRemoveToken(limit, conn)) {
                                removeRelatedKeys(limit, conn);
                            } else {
                                // 5. 如果不应该移除，放回列表
                                conn.rPush(usernameLimitKey, bytes);
                            }
                        }
                        // 6. 提交事务
                        conn.exec();
                        break;
                    } catch (Exception e) {
                        conn.discard(); // 发生异常时回滚事务
                        log.warn("当用户名数量超过限制时，未能移除令牌", e);
                    } finally {
                        // 7. 释放锁
                        byte[] currentValue = conn.get(serializeKey(lockKey));
                        if (currentValue != null && lockValue.equals(deserializeString(currentValue))) {
                            conn.del(serializeKey(lockKey));
                        }
                    }
                }
                retryCount--;
                if (retryCount > 0) {
                    Thread.sleep(100);
                }
            }
        } catch (Exception e) {
            log.warn("处理token移除失败", e);
        } finally {
            // 确保关闭Redis连接
            conn.close();
        }
    }

    /**
     * 移除与被限制的用户名相关的所有键
     *
     * @param limit UsernameLimit 对象
     * @param conn Redis连接
     */
    private void removeRelatedKeys(UsernameLimit limit, RedisConnection conn) {
        // 移除与被限制的用户名相关的所有键
        conn.del(serializeKey(limit.getAccessKey()));
        conn.del(serializeKey(limit.getAuthKey()));
        conn.del(serializeKey(limit.getAuthToAccessKey()));
        conn.del(serializeKey(limit.getRefreshToAccessKey()));
        conn.del(serializeKey(limit.getAccessToRefreshKey()));
        conn.del(serializeKey(limit.getRefreshKey()));
        conn.del(serializeKey(limit.getRefreshAuthKey()));

        // 从各个列表中移除token
        byte[] access = conn.get(serializeKey(limit.getAccessKey()));
        if (access != null) {
            // 移除客户端ID列表中的特定访问记录
            conn.lRem(serializeKey(limit.getClientId()), 0, access);
            // 移除用户名列表中的特定访问记录
            conn.lRem(serializeKey(limit.getUsernameKey()), 0, access);
            // 移除审批列表中的特定访问记录
            conn.lRem(serializeKey(limit.getApprovalKey()), 0, access);
        }
    }

    /**
     * 根据OAuth2Authentication对象生成审批键
     * 审批键的生成通常用于标识客户端和用户
     *
     * @param authentication OAuth2Authentication对象，包含客户端和用户信息
     * @return 审批键字符串
     */
    private static String getApprovalKey(OAuth2Authentication authentication) {
        // 获取用户名称，如果用户认证信息为空，则使用空字符串
        String userName = authentication.getUserAuthentication() == null ? ""
                : authentication.getUserAuthentication().getName();
        // 调用另一个方法生成审批键
        return getApprovalKey(authentication.getOAuth2Request().getClientId(), userName);
    }

    /**
     * 根据客户端ID和用户名称生成审批键
     * 审批键格式为客户端ID加上用户名称，用户名称前加冒号分隔
     * 如果用户名称为空，则仅返回客户端ID
     *
     * @param clientId 客户端ID
     * @param userName 用户名称
     * @return 审批键字符串
     */
    private static String getApprovalKey(String clientId, String userName) {
        // 生成并返回审批键
        return clientId + (userName == null ? "" : ":" + userName);
    }

    /**
     * 从Redis中读取访问令牌
     * 通过序列化和反序列化操作，从给定的令牌值中读取对应的OAuth2AccessToken对象
     *
     * @param tokenValue 访问令牌的值
     * @return 从Redis中读取并反序列化的OAuth2AccessToken对象
     */
    @Override
    public OAuth2AccessToken readAccessToken(String tokenValue) {
        // 序列化键，用于在Redis中定位访问令牌
        byte[] key = serializeKey(ACCESS + tokenValue);
        byte[] bytes;
        RedisConnection conn = getConnection();
        try {
            // 尝试从Redis获取令牌数据
            bytes = conn.get(key);
        } catch (Exception ex) {
            log.warn("Error reading access token", ex);
            return null;
        } finally {
            // 确保关闭Redis连接
            conn.close();
        }
        // 反序列化获取的字节数组为OAuth2AccessToken对象并返回
        return deserializeAccessToken(bytes);
    }

    /**
     * 重写删除访问令牌的方法
     * 该方法负责从Redis存储中删除与给定访问令牌相关的所有信息
     * 包括直接与访问令牌相关的钥匙，以及通过访问令牌间接关联的其他信息
     *
     * @param accessToken 访问令牌，用于标识需要删除的会话信息
     */
    @Override
    public void removeAccessToken(OAuth2AccessToken accessToken) {
        if (accessToken == null || accessToken.getValue() == null) {
            return;
        }
        log.warn("删除访问令牌, accessToken: {}", accessToken.getValue());
        
        RedisConnection conn = getConnection();
        try {
            // 开启事务
            conn.multi();
            
            // 1. 获取所有基本的token相关key
            byte[] accessKey = serializeKey(ACCESS + accessToken.getValue());
            byte[] authKey = serializeKey(AUTH + accessToken.getValue());
            byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + accessToken.getValue());
            
            // 2. 获取认证信息（用于后续清理）
            byte[] auth = conn.get(authKey);

            // 3. 删除基本的token相关key
            // 删除访问钥匙
            conn.del(accessKey);
            conn.del(authKey);
            // 删除访问到刷新钥匙
            conn.del(accessToRefreshKey);
            
            // 4. 提交事务并获取结果
            List<Object> results = conn.exec();
            if (results == null) {
                log.warn("删除访问令牌事务执行失败");
                return;
            }

            // 5. 处理认证信息相关的清理
            OAuth2Authentication authentication;
            if (auth != null) {
                // 反序列化认证信息
                authentication = deserializeAuthentication((byte[]) results.get(0));
                if (authentication != null) {
                    // 复用 cleanupTokenFromLists 方法清理所有相关列表
                    cleanupTokenFromLists(authentication, accessToken, conn);
                }
            }

            // 6. 处理刷新令牌相关的清理
            // Don't remove the refresh token - it's up to the caller to do that
            // 不删除刷新令牌，由调用者决定是否删除
        } catch (Exception e) {
            log.warn("删除访问令牌失败: {}", accessToken.getValue(), e);
            throw new RuntimeException("Failed to remove access token", e);
        } finally {
            // 确保连接关闭
            conn.close();
        }
    }

    /**
     * 将刷新令牌存储到Redis中
     *
     * @param refreshToken 刷新令牌对象，包含刷新令牌及其相关的信息
     * @param authentication OAuth2认证对象，包含用于刷新令牌认证的数据
     */
    @Override
    public void storeRefreshToken(OAuth2RefreshToken refreshToken, OAuth2Authentication authentication) {
        if (refreshToken == null || authentication == null) {
            return;
        }
        log.info("存储刷新令牌, refreshToken: {}", refreshToken.getValue());
        // 生成刷新令牌和刷新认证的键
        byte[] refreshKey = serializeKey(REFRESH + refreshToken.getValue());
        byte[] refreshAuthKey = serializeKey(REFRESH_AUTH + refreshToken.getValue());

        // 序列化刷新令牌
        byte[] serializedRefreshToken = serialize(refreshToken);

        // 获取到Redis连接
        RedisConnection conn = getConnection();
        try {
            // 开启管道操作，减少网络往返延迟
            conn.openPipeline();

            // 根据Spring Data Redis版本选择不同的操作方式
            if (SPRING_DATA_REDIS_2_0) {
                try {
                    // 使用Spring Data Redis 2.0及以上版本的调用方式存储刷新令牌和认证信息
                    this.redisConnectionSet20.invoke(conn, refreshKey, serializedRefreshToken);
                    this.redisConnectionSet20.invoke(conn, refreshAuthKey, serialize(authentication));
                } catch (Exception ex) {
                    // 内部错误处理
                    throw new RuntimeException(ex);
                }
            } else {
                // 对于旧版本的Spring Data Redis，直接设置键值对
                conn.set(refreshKey, serializedRefreshToken);
                conn.set(refreshAuthKey, serialize(authentication));
            }

            // 如果刷新令牌是过期类型的，则设置过期时间
            setRefreshTokenExpiration(refreshToken, conn, Arrays.asList(refreshKey, refreshAuthKey));

            // 关闭管道操作
            conn.closePipeline();
        } finally {
            // 关闭Redis连接
            conn.close();
        }
    }

    /**
     * 设置刷新令牌的过期时间
     *
     * @param refreshToken 刷新令牌对象
     * @param conn Redis连接
     * @param keys Redis键列表
     */
    private static void setRefreshTokenExpiration(OAuth2RefreshToken refreshToken, RedisConnection conn,  List<byte[]> keys) {
        if (refreshToken instanceof ExpiringOAuth2RefreshToken) {
            ExpiringOAuth2RefreshToken expiringRefreshToken = (ExpiringOAuth2RefreshToken) refreshToken;
            Date expiration = expiringRefreshToken.getExpiration();
            if (expiration != null) {
                // 计算距离过期的时间（秒）
                int seconds = Long.valueOf((expiration.getTime() - System.currentTimeMillis()) / 1000L)
                        .intValue();
                // 设置过期时间
                for (byte[] key : keys) {
                    conn.expire(key, seconds);
                }
            }
        }
    }

    /**
     * 从Redis中读取刷新令牌
     *
     * @param refreshTokenValue 刷新令牌的值
     * @return 返回读取到的OAuth2RefreshToken对象
     */
    @Override
    public OAuth2RefreshToken readRefreshToken(String refreshTokenValue) {
        if (refreshTokenValue == null) {
            return null;
        }
        // 根据刷新令牌的值生成Redis中的键
        byte[] refreshKey = serializeKey(REFRESH + refreshTokenValue);
        byte[] refreshToAccessKey = serializeKey(REFRESH_TO_ACCESS + refreshTokenValue);

        byte[] delayKey = serializeKey(DELAY_REMOVE_REFRESH_TOKEN_KEY + refreshTokenValue);
        RedisConnection conn = getConnection();
        try {
            // 开始一个管道操作
            conn.openPipeline();
            // 从Redis中获取刷新令牌对应的字节数组
            conn.get(refreshKey);
            conn.get(refreshToAccessKey);
            // 提交并获取结果
            List<Object> results = conn.closePipeline();
            // 处理获取的访问数据
            byte[] refresh = (byte[]) results.get(0);
            byte[] refreshToAccess = (byte[]) results.get(1);
            // 将字节数组反序列化为刷新令牌对象
            OAuth2RefreshToken oAuth2RefreshToken = deserializeRefreshToken(refresh);
            if (oAuth2RefreshToken == null) {
                String accessToken = deserializeString(refreshToAccess);
                if (accessToken != null) {
                    log.warn("刷新令牌不存在, 通过访问令牌重新生成刷新令牌, refreshTokenValue: {}, accessToken:{}", refreshTokenValue, accessToken);
                    // 如果刷新令牌为空，则通过取访问令牌重新生成刷新令牌
                    oAuth2RefreshToken = restoreRefreshTokenByAccessToken(accessToken);
                }
            }

            // 检查是否处于延迟删除状态
            if (Boolean.TRUE.equals(conn.exists(delayKey))) {
                // 取消延迟删除
                conn.del(delayKey);
                // 读取刷新令牌的时候，设置10 秒后过期, 这样就能保证延迟删除的刷新令牌能够被正常使用一次(10秒内), 读取后立即过期
                setRefreshRelatedKeysExpire(refreshTokenValue, conn, 10);
            }
            return oAuth2RefreshToken;
        } finally {
            // 关闭Redis连接
            conn.close();
        }
    }

    /**
     * 移除刷新令牌根据客户端ID判断是否需要延迟移除
     *
     * @param refreshToken 要移除的刷新令牌
     */
    @Override
    public void removeRefreshToken(OAuth2RefreshToken refreshToken) {
        // 从刷新令牌值中解析出客户端ID
        String clientId = JwtUtil.parseClientIdFromTokenWithoutVerify(refreshToken.getValue());
        // 检查客户端ID是否需要延迟移除
        if (com.yeelight.service.framework.util.StringUtils.isNotBlank(clientId) && Constants.DELAY_REMOVE_REFRESH_TOKEN_CLIENT_IDS.contains(clientId)) {
            // 对需要延迟移除的刷新令牌执行延迟移除操作
            delayRemoveRefreshToken(refreshToken.getValue());
        } else {
            // 对不需要延迟移除的刷新令牌直接执行移除操作
            removeRefreshToken(refreshToken.getValue());
        }
    }

    /**
     * 直接移除刷新令牌及其相关联的键
     *
     * @param refreshToken 刷新令牌的值
     */
    public void removeRefreshToken(String refreshToken) {
        if (refreshToken == null) {
            return;
        }
        log.warn("移除刷新令牌, refreshToken: {}", refreshToken);
        // 生成刷新令牌相关联的键
        byte[] refreshKey = serializeKey(REFRESH + refreshToken);
        byte[] refreshAuthKey = serializeKey(REFRESH_AUTH + refreshToken);
        byte[] refresh2AccessKey = serializeKey(REFRESH_TO_ACCESS + refreshToken);
        byte[] access2RefreshKey = serializeKey(ACCESS_TO_REFRESH + refreshToken);
        RedisConnection conn = getConnection();
        try {
            // 开始管道操作，提高Redis操作效率
            conn.openPipeline();
            // 执行删除操作，移除刷新令牌及其相关联的键
            conn.del(refreshKey);
            conn.del(refreshAuthKey);
            conn.del(refresh2AccessKey);
            conn.del(access2RefreshKey);
            // 完成管道操作
            conn.closePipeline();
        } finally {
            // 关闭Redis连接
            conn.close();
        }
    }

    /**
     * 延迟删除刷新令牌
     * 在Redis中设置相关键的过期时间，确保10分钟后这些键失效
     *
     * @param refreshToken 刷新令牌，用于标识Redis中的相关键
     */
    public void delayRemoveRefreshToken(String refreshToken) {
        if (refreshToken == null) {
            return;
        }
        log.warn("延迟删除刷新令牌, refreshToken: {}", refreshToken);
        RedisConnection conn = getConnection();

        // 设置键在10分钟后过期
        int delayRemoveSeconds = 60 * 10;
        try {
            // 开启Redis管道, 批量设置过期，减少网络往返次数
            conn.openPipeline();
            
            // 记录延迟删除状态
            byte[] delayKey = serializeKey(DELAY_REMOVE_REFRESH_TOKEN_KEY + refreshToken);
            conn.setEx(delayKey, delayRemoveSeconds, serialize(true));
            
            // 设置相关key过期
            setRefreshRelatedKeysExpire(refreshToken, conn, delayRemoveSeconds);

            // 关闭Redis管道，执行所有命令
            conn.closePipeline();
        } finally {
            // 关闭Redis连接
            conn.close();
        }
    }

    /**
     * 使用刷新令牌移除对应的访问令牌
     *
     * @param refreshToken 刷新令牌，用于查找对应的访问令牌
     */
    @Override
    public void removeAccessTokenUsingRefreshToken(OAuth2RefreshToken refreshToken) {
        removeAccessTokenUsingRefreshToken(refreshToken.getValue());
    }

    /**
     * 私有方法，通过刷新令牌字符串移除对应的访问令牌
     *
     * @param refreshToken 刷新令牌字符串，用作查找访问令牌的键
     */
    private void removeAccessTokenUsingRefreshToken(String refreshToken) {
        if (refreshToken == null) {
            return;
        }
        log.warn("使用刷新令牌移除对应的访问令牌, refreshToken: {}", refreshToken);
        // 将刷新令牌对应的键进行序列化
        byte[] refreshToAccessKey = serializeKey(REFRESH_TO_ACCESS + refreshToken);
        List<Object> results;
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            // 开启Redis管道，提高执行效率
            conn.openPipeline();
            // 获取访问令牌
            conn.get(refreshToAccessKey);
            // 删除刷新令牌对应的访问令牌映射
            conn.del(refreshToAccessKey);
            // 关闭管道并获取执行结果
            results = conn.closePipeline();
        } finally {
            // 关闭Redis连接
            conn.close();
        }
        // 从结果中获取访问令牌的字节数组
        byte[] bytes = (byte[]) results.get(0);
        // 将字节数组反序列化为访问令牌字符串
        String accessToken = deserializeString(bytes);
        // 如果访问令牌存在，则使其过期
        if (accessToken != null) {
            expireAccessToken(accessToken);
        }
    }

    /**
     * 根据访问令牌的值恢复刷新令牌对象
     * <p>
     * 此方法通过给定的访问令牌值从Redis存储中检索相关信息，
     * 并重建OAuth2RefreshToken对象如果恢复成功，则将刷新令牌存储并返回
     *
     * @param accessToken 访问令牌的值
     * @return 恢复的OAuth2RefreshToken对象，如果恢复失败则返回null
     */
    private OAuth2RefreshToken restoreRefreshTokenByAccessToken(String accessToken) {
        if (accessToken == null) {
            return null;
        }
        log.warn("通过访问令牌重新生成刷新令牌, accessToken: {}", accessToken);
        // 获取 Redis 连接
        RedisConnection conn = getConnection();
        try {
            // 生成其他需要的键
            byte[] accessKey = serializeKey(ACCESS + accessToken);
            byte[] authKey = serializeKey(AUTH + accessToken);
            byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + accessToken);

            // 开始另一个管道操作获取其他数据
            conn.openPipeline();
            conn.get(accessKey);
            conn.get(authKey);
            conn.get(accessToRefreshKey);

            // 提交并获取结果
            List<Object> accessResults = conn.closePipeline();

            // 处理获取的访问数据
            byte[] access = (byte[]) accessResults.get(0);
            byte[] auth = (byte[]) accessResults.get(1);
            byte[] accessToRefresh = (byte[]) accessResults.get(2);

            // 检查数据是否为空
            if (access == null) {
                log.warn("通过访问令牌重新生成刷新令牌, access信息为空, accessToken: {}", ACCESS + accessToken);
                return null;
            }
            if (auth == null) {
                log.warn("通过访问令牌重新生成刷新令牌, auth信息为空, accessToken: {}", AUTH + accessToken);
                return null;
            }
            if (accessToRefresh == null) {
                log.warn("通过访问令牌重新生成刷新令牌, accessToRefresh信息为空, accessToken: {}", ACCESS_TO_REFRESH + accessToken);
                return null;
            }

            // 反序列化数据
            OAuth2AccessToken oAuth2AccessToken = deserializeAccessToken(access);
            OAuth2Authentication authentication = deserializeAuthentication(auth);
            String refreshTokenStr = deserializeString(accessToRefresh);
            log.warn("通过访问令牌重新生成刷新令牌, 访问令牌: {}, 认证信息: {}, 刷新令牌: {}", oAuth2AccessToken, authentication, refreshTokenStr);
            if (oAuth2AccessToken == null || authentication == null || refreshTokenStr == null) {
                return null;
            }
            // 生成刷新令牌对象, 这里默认使用 DefaultOAuth2RefreshToken, 不过期. ToDo 实际上应该处理过期的情况
            OAuth2RefreshToken refreshToken  = new DefaultOAuth2RefreshToken(refreshTokenStr);

            // 存储恢复的刷新令牌
            storeRefreshToken(refreshToken, authentication);
            return refreshToken;

        } finally {
            // 关闭 Redis 连接
            conn.close();
        }
    }


    /**
     * 设置令牌相关键的过期时间
     *
     * @param token 访问令牌
     * @param authentication 认证信息
     * @param expiresSeconds 过期时间(秒)
     * @param conn Redis连接
     */
    private void setTokenKeysExpiration(OAuth2AccessToken token, OAuth2Authentication authentication, 
            int expiresSeconds, RedisConnection conn) {
        try {
            // 1. 准备所有相关的key
            byte[] accessKey = serializeKey(ACCESS + token.getValue());
            byte[] authKey = serializeKey(AUTH + token.getValue());
            byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + token.getValue());
            
            // 2. 获取认证相关的key
            List<byte[]> keysToExpire = new ArrayList<>();
            keysToExpire.add(accessKey);
            keysToExpire.add(authKey);
            keysToExpire.add(accessToRefreshKey);
            
            // 3. 如果有刷新令牌，添加刷新令牌相关的key
            OAuth2RefreshToken refreshToken = token.getRefreshToken();
            // 如果刷新令牌不为空，则为刷新令牌设置过期时间
            if (refreshToken != null) {
                byte[] refreshKey = serializeKey(REFRESH + refreshToken.getValue());
                byte[] refreshAuthKey = serializeKey(REFRESH_AUTH + refreshToken.getValue());
                byte[] refresh2AccessKey = serializeKey(REFRESH_TO_ACCESS + refreshToken.getValue());
                keysToExpire.add(refreshKey);
                keysToExpire.add(refreshAuthKey);
                keysToExpire.add(refresh2AccessKey);
            }
            
            // 4. 如果有认证信息，添加认证相关的key
            if (authentication != null) {
                String key = authenticationKeyGenerator.extractKey(authentication);
                byte[] authToAccessKey = serializeKey(AUTH_TO_ACCESS + key);
                byte[] approvalUnameToAccessKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authentication));
                byte[] clientId = serializeKey(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
                keysToExpire.add(authToAccessKey);
                keysToExpire.add(approvalUnameToAccessKey);
                keysToExpire.add(clientId);
                keysToExpire.add(serialize(ACCESS + key));
                
                if (!StringUtils.isEmpty(authentication.getName())) {
                    byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + authentication.getName());
                    keysToExpire.add(usernameKey);
                }
            }
            
            // 5. 批量设置过期时间
            for (byte[] key : keysToExpire) {
                conn.expire(key, expiresSeconds);
            }
        } catch (Exception e) {
            log.warn("设置令牌过期时间失败: token={}, expiresSeconds={}", token.getValue(), expiresSeconds, e);
            throw new RuntimeException("Failed to set token expiration", e);
        }
    }

    /**
     * 使访问令牌过期。该方法主要负责在Redis中更新相关键的过期时间，并且清理一些关联数据
     * 它不直接删除刷新令牌，而是通过重新设置过期时间来管理访问令牌和相关联的刷新令牌
     *
     * @param tokenValue 访问令牌的值，用于标识相关的Redis键
     */
    public void expireAccessToken(String tokenValue) {
        if (tokenValue == null) {
            return;
        }
        log.warn("使访问令牌过期, tokenValue: {}", tokenValue);
        
        RedisConnection conn = getConnection();
        try {
            conn.multi();
            
            // 1. 获取访问令牌信息
            byte[] accessKey = serializeKey(ACCESS + tokenValue);
            byte[] authKey = serializeKey(AUTH + tokenValue);
            byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + tokenValue);
            
            // 2. 获取认证信息
            byte[] auth = conn.get(authKey);
            OAuth2Authentication authentication = null;
            if (auth != null) {
                authentication = deserializeAuthentication(auth);
            }
            
            // 3. 设置所有相关key的过期时间
            OAuth2AccessToken accessToken = readAccessToken(tokenValue);
            if (accessToken != null) {
                setTokenKeysExpiration(accessToken, authentication, REFRESH_LAST_ACCESS_EXPIRE_TIME, conn);
            }
            
            // 4. 如果有认证信息，清理关联的列表数据
            if (authentication != null) {
                cleanupTokenFromLists(authentication, accessToken, conn);
            }
            
            conn.exec();
        } catch (Exception e) {
            conn.discard();
            log.warn("令牌过期处理失败: {}", tokenValue, e);
            throw new RuntimeException("Failed to expire access token", e);
        } finally {
            conn.close();
        }
    }

    /**
     * 过期OAuth2访问令牌
     *
     * @param token OAuth2访问令牌
     * @param authentication OAuth2认证信息
     * @param expiresSeconds 过期时间（秒）
     */
    public void expireToken(OAuth2AccessToken token, OAuth2Authentication authentication, int expiresSeconds) {
        if (token == null) {
            return;
        }
        log.warn("过期OAuth2访问令牌, token: {}, expiresSeconds:{}", token.getValue(), expiresSeconds);
        
        RedisConnection conn = getConnection();
        try {
            conn.multi();
            
            // 1. 设置所有相关key的过期时间
            setTokenKeysExpiration(token, authentication, expiresSeconds, conn);
            
            // 2. 如果有认证信息，清理关联的列表数据
            if (authentication != null) {
                cleanupTokenFromLists(authentication, token, conn);
            }
            
            conn.exec();
        } catch (Exception e) {
            conn.discard();
            log.warn("令牌过期处理失败: {}", token.getValue(), e);
            throw new RuntimeException("Failed to expire token", e);
        } finally {
            conn.close();
        }
    }

    /**
     * 清理令牌在各个列表中的数据
     * <p>
     *     应用列表, 用户名列表, 审批列表, 用户名限制列表
     *
     * @param authentication OAuth2认证信息
     * @param token OAuth2访问令牌
     * @param conn Redis连接
     */
    private void cleanupTokenFromLists(OAuth2Authentication authentication, OAuth2AccessToken token, 
            RedisConnection conn) {
        if (authentication == null || token == null) {
            return;
        }
        
        byte[] serializedToken = serialize(token);
        
        // 1. 从审批列表中移除
        byte[] approvalKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authentication));
        conn.lRem(approvalKey, 0, serializedToken);
        
        // 2. 从用户名列表中移除
        if (!StringUtils.isEmpty(authentication.getName())) {
            byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + authentication.getName());
            conn.lRem(usernameKey, 0, serializedToken);
        }
        
        // 3. 从客户端列表中移除
        byte[] clientId = serializeKey(CLIENT_ID_TO_ACCESS + authentication.getOAuth2Request().getClientId());
        conn.lRem(clientId, 0, serializedToken);

        // 4. 从用户名限制列表中移除
        if (!StringUtils.isEmpty(authentication.getName())) {
            // 构建用户名限制列表的key
            String userLimitKey = USERNAME_LIMIT_LIST + getApprovalKey(
                authentication.getOAuth2Request().getClientId(), 
                authentication.getName()
            );
            byte[] limitKey = serializeKey(userLimitKey);
            
            // 构建需要移除的 UsernameLimit 对象
            UsernameLimit usernameLimit = getUsernameLimitByAccessToken(token, authentication);
            
            // 从列表中移除对应的记录
            conn.lRem(limitKey, 0, serialize(usernameLimit));
            
            log.debug("从用户名限制列表中移除令牌: userLimitKey={}, token={}", userLimitKey, token.getValue());
        }
    }

    /**
     * 根据客户端ID和用户名查找OAuth2访问令牌
     *
     * @param clientId 客户端ID
     * @param userName 用户名
     * @return 返回一个不可修改的OAuth2AccessToken集合，如果找不到则返回空集合
     */
    @Override
    public Collection<OAuth2AccessToken> findTokensByClientIdAndUserName(String clientId, String userName) {
        // 构建基于客户端ID和用户名的序列化键
        byte[] approvalUnameToAccessKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(clientId, userName));
        List<byte[]> byteList;
        RedisConnection conn = getConnection();
        try {
            // 尝试从Redis中获取所有访问令牌
            byteList = getByteLists(approvalUnameToAccessKey, conn);
        } finally {
            // 确保关闭Redis连接
            conn.close();
        }
        // 如果获取的令牌列表为空，则返回一个空的不可修改集合
        if (byteList == null || byteList.isEmpty()) {
            return Collections.emptySet();
        }
        // 准备存储反序列化后的访问令牌
        List<OAuth2AccessToken> accessTokens = new ArrayList<>(byteList.size());
        for (byte[] bytes : byteList) {
            // 反序列化字节数据为OAuth2AccessToken对象
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            // 添加反序列化后的访问令牌到列表中
            accessTokens.add(accessToken);
        }
        // 返回不可修改的访问令牌集合
        return Collections.unmodifiableCollection(accessTokens);
    }


    /**
     * 根据客户端ID和用户名撤销令牌
     */
    public boolean revokeTokenByClientIdAndUserName(String clientId, String userName) {
        log.info("开始撤销客户端用户令牌并清理数据, clientId: {}, userName: {}", clientId, userName);
        
        // 1. 查找指定客户端ID和用户名的所有令牌
        Collection<OAuth2AccessToken> tokens = findTokensByClientIdAndUserName(clientId, userName);
        if(tokens.isEmpty()) {
            return true;
        }

        RedisConnection conn = getConnection();
        try {
            conn.multi(); // 开启事务
            
            // 2. 清理approval相关的key
            byte[] approvalKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(clientId, userName));
            conn.del(approvalKey);
            
            // 3. 清理tokens和相关数据
            boolean success = revokeTokens(tokens, conn);
            
            // 4. 清理username limit相关
            String userLimitKey = USERNAME_LIMIT_LIST + getApprovalKey(clientId, userName);
            byte[] limitKey = serializeKey(userLimitKey);
            conn.del(limitKey);
            
            // 5. 提交事务
            conn.exec();
            
            // 6. 验证清理结果
            if(success) {
                success = verifyClientUserCleanup(clientId, userName, conn);
            }
            
            log.info("完成撤销客户端用户令牌和清理数据, clientId: {}, userName: {}, success: {}", 
                clientId, userName, success);
            return success;
            
        } catch(Exception e) {
            conn.discard(); // 回滚事务
            log.warn("撤销客户端用户令牌失败: clientId: {}, userName: {}", clientId, userName, e);
            return false;
        } finally {
            conn.close();
        }
    }

    /**
     * 根据用户名撤销所有令牌
     *
     * @param userName 用户名
     * @return 如果成功撤销所有令牌，则返回true；如果发现令牌为null，则返回false
     */
    public boolean revokeTokenByUserName(String userName) {
        log.warn("开始撤销用户令牌并清理数据, userName: {}", userName);
        
        // 1. 查找指定用户名的所有令牌
        Collection<OAuth2AccessToken> tokens = findTokensByUserName(userName);
        if(tokens.isEmpty()) {
            return true;
        }

        RedisConnection conn = getConnection();
        try {
            conn.multi(); // 开启事务
            
            // 2. 清理username相关的key
            byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + userName);
            conn.del(usernameKey);
            
            // 3. 清理tokens和相关数据
            boolean success = revokeTokens(tokens, conn);
            
            // 4. 清理username limit相关
            String userLimitKey = USERNAME_LIMIT_LIST + userName;
            byte[] limitKey = serializeKey(userLimitKey);
            conn.del(limitKey);
            
            // 5. 提交事务
            conn.exec();
            
            // 6. 验证清理结果
            if(success) {
                success = verifyCleanup(userName, conn);
            }
            
            log.info("完成撤销用户令牌和清理数据, userName: {}, success: {}", userName, success);
            return success;
            
        } catch(Exception e) {
            // 回滚事务
            conn.discard();
            log.warn("撤销用户令牌失败: {}", userName, e);
            return false;
        } finally {
            conn.close();
        }
    }

    /**
     * 撤销令牌集合并清理相关数据
     * 
     * @param tokens 要撤销的令牌集合
     * @param conn Redis连接
     * @return 如果所有令牌都成功撤销返回true,否则返回false
     */
    private boolean revokeTokens(Collection<OAuth2AccessToken> tokens, RedisConnection conn) {
        boolean success = true;
        
        for(OAuth2AccessToken token : tokens) {
            try {
                // 清理access token相关
                byte[] accessKey = serializeKey(ACCESS + token.getValue());
                byte[] authKey = serializeKey(AUTH + token.getValue());
                byte[] accessToRefreshKey = serializeKey(ACCESS_TO_REFRESH + token.getValue());
                
                conn.del(accessKey);
                conn.del(authKey);
                conn.del(accessToRefreshKey);
                
                // 清理refresh token相关
                OAuth2RefreshToken refreshToken = token.getRefreshToken();
                if(refreshToken != null) {
                    byte[] refreshKey = serializeKey(REFRESH + refreshToken.getValue());
                    byte[] refreshAuthKey = serializeKey(REFRESH_AUTH + refreshToken.getValue());
                    byte[] refreshToAccessKey = serializeKey(REFRESH_TO_ACCESS + refreshToken.getValue());
                    
                    conn.del(refreshKey);
                    conn.del(refreshAuthKey); 
                    conn.del(refreshToAccessKey);
                }
                
                // 清理client相关
                String clientId = JwtUtil.parseClientIdFromTokenWithoutVerify(token.getValue());
                if(clientId != null) {
                    byte[] clientIdKey = serializeKey(CLIENT_ID_TO_ACCESS + clientId);
                    conn.lRem(clientIdKey, 0, serialize(token));
                }
                
            } catch(Exception e) {
                log.warn("清理token失败: {}", token.getValue(), e);
                success = false;
            }
        }
        
        return success;
    }

    /**
     * 验证客户端用户数据清理结果
     *
     * @param clientId 客户端ID
     * @param userName 用户名
     * @param conn Redis连接
     * @return 如果清理成功返回true,否则返回false
     */
    private boolean verifyClientUserCleanup(String clientId, String userName, RedisConnection conn) {
        try {
            // 验证approval相关的key是否已清理
            byte[] approvalKey = serializeKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(clientId, userName));
            Long size = conn.lLen(approvalKey);
            if(size != null && size > 0) {
                log.warn("客户端用户token列表未完全清理: clientId: {}, userName: {}, 剩余数量: {}", 
                    clientId, userName, size);
                return false;
            }
            
            // 验证limit key是否已清理
            byte[] limitKey = serializeKey(USERNAME_LIMIT_LIST + getApprovalKey(clientId, userName));
            size = conn.lLen(limitKey);
            if(size != null && size > 0) {
                log.warn("客户端用户limit列表未完全清理: clientId: {}, userName: {}, 剩余数量: {}", 
                    clientId, userName, size);
                return false;
            }
            
            return true;
        } catch(Exception e) {
            log.warn("验证清理结果失败: clientId: {}, userName: {}", clientId, userName, e);
            return false;
        }
    }

    /**
     * 验证数据清理结果
     * @param userName 用户名
     * @param conn Redis连接
     * @return 如果清理成功返回true,否则返回false
     */
    private boolean verifyCleanup(String userName, RedisConnection conn) {
        try {
            // 验证username相关的key是否已清理
            byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + userName);
            Long size = conn.lLen(usernameKey);
            if(size != null && size > 0) {
                log.warn("用户token列表未完全清理: {}, 剩余数量: {}", userName, size);
                return false;
            }
            
            // 验证limit key是否已清理
            byte[] limitKey = serializeKey(USERNAME_LIMIT_LIST + userName);
            size = conn.lLen(limitKey);
            if(size != null && size > 0) {
                log.warn("用户limit列表未完全清理: {}, 剩余数量: {}", userName, size);
                return false;
            }
            
            return true;
        } catch(Exception e) {
            log.warn("验证清理结果失败: {}", userName, e);
            return false;
        }
    }

    /**
     * 根据用户名查找相关的OAuth2访问令牌
     *
     * @param userName 用户名
     * @return 返回一个不可修改的OAuth2AccessToken集合
     */
    public Collection<OAuth2AccessToken> findTokensByUserName(String userName) {
        // 生成用户名对应的键
        byte[] usernameKey = serializeKey(USERNAME_TO_ACCESS + userName);
        List<byte[]> byteList;
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            // 使用Redis的LIST类型存储令牌，这里获取所有存储的令牌信息
            byteList = getByteLists(usernameKey, conn);
        } finally {
            // 关闭Redis连接
            conn.close();
        }
        // 如果令牌信息列表为空，则返回一个空的不可修改集合
        if (byteList == null || byteList.isEmpty()) {
            return Collections.emptySet();
        }
        // 创建一个与byte列表大小相同的AccessToken列表
        List<OAuth2AccessToken> accessTokens = new ArrayList<>(byteList.size());
        // 遍历byte列表，反序列化为AccessToken对象
        for (byte[] bytes : byteList) {
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            // 将反序列化的AccessToken添加到列表中
            accessTokens.add(accessToken);
        }
        // 返回一个不可修改的AccessToken集合
        return Collections.unmodifiableCollection(accessTokens);
    }

    /**
     * 根据客户端ID查找相关的访问令牌
     *
     * @param clientId 客户端ID
     * @return 一个不可修改的集合，包含与clientId相关的OAuth2AccessToken实例
     */
    @Override
    public Collection<OAuth2AccessToken> findTokensByClientId(String clientId) {
        // 生成表示客户端ID关联的访问令牌键的字节数组
        byte[] key = serializeKey(CLIENT_ID_TO_ACCESS + clientId);
        List<byte[]> byteList;
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            // 获取与键关联的所有字节数组
            byteList = getByteLists(key, conn);
        } finally {
            // 确保关闭连接
            conn.close();
        }
        // 如果获取的字节数组列表为空，则返回一个空的不可修改集合
        if (byteList == null || byteList.isEmpty()) {
            return Collections.emptySet();
        }
        // 创建一个与获取的字节数组列表大小相同的AccessToken列表
        List<OAuth2AccessToken> accessTokens = new ArrayList<>(byteList.size());
        // 遍历字节数组列表，反序列化为AccessToken对象并添加到集合中
        for (byte[] bytes : byteList) {
            OAuth2AccessToken accessToken = deserializeAccessToken(bytes);
            accessTokens.add(accessToken);
        }
        // 返回不可修改的AccessToken集合
        return Collections.unmodifiableCollection(accessTokens);
    }

    /**
     * 根据访问令牌获取用户名限制信息
     * 该方法主要用于生成与OAuth2访问令牌相关的用户名限制对象
     * 它会根据访问令牌和刷新令牌的值构建相应的唯一键，用于后续的令牌管理和验证
     *
     * @param accessToken 访问令牌对象，包含了访问令牌和刷新令牌的信息
     * @return UsernameLimit对象，包含了基于访问令牌和刷新令牌生成的键
     */
    private UsernameLimit getUsernameLimitByAccessToken(OAuth2AccessToken accessToken) {
        // 获取访问令牌的值
        String tokenValue = accessToken.getValue();

        // 创建一个新的用户名限制对象来存储令牌相关的键
        UsernameLimit usernameLimit = new UsernameLimit();
        // 设置访问键，用于标识访问令牌
        usernameLimit.setAccessKey(ACCESS + tokenValue);
        // 设置访问到刷新键，用于从访问令牌追溯到刷新令牌
        usernameLimit.setAccessToRefreshKey(ACCESS_TO_REFRESH + tokenValue);
        // 设置认证键，用于访问令牌的认证
        usernameLimit.setAuthKey(AUTH + tokenValue);

        // 获取刷新令牌的值
        if (accessToken.getRefreshToken() != null) {
            String refreshTokenValue = accessToken.getRefreshToken().getValue();
            // 设置刷新键，用于标识刷新令牌
            usernameLimit.setRefreshKey(REFRESH + refreshTokenValue);
            // 设置刷新到访问键，用于从刷新令牌生成新的访问令牌
            usernameLimit.setRefreshToAccessKey(REFRESH_TO_ACCESS + refreshTokenValue);
            // 设置刷新认证键，用于刷新令牌的认证
            usernameLimit.setRefreshAuthKey(REFRESH_AUTH + refreshTokenValue);
        }
        
        return usernameLimit;
    }

    /**
     * 根据访问令牌获取用户名限制信息
     * 该方法主要用于生成与OAuth2访问令牌相关的用户名限制对象
     * 它会根据访问令牌和刷新令牌的值构建相应的唯一键，用于后续的令牌管理和验证
     *
     * @param accessToken 访问令牌对象，包含了访问令牌和刷新令牌的信息
     * @param authentication 认证信息
     * @return UsernameLimit对象，包含了基于访问令牌和刷新令牌生成的键
     */
    private UsernameLimit getUsernameLimitByAccessToken(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        UsernameLimit usernameLimit = getUsernameLimitByAccessToken(accessToken);
        
        // 设置额外的关联信息
        if (authentication != null) {
            String clientId = authentication.getOAuth2Request().getClientId();
            String userName = authentication.getName();
            
            usernameLimit.setClientId(CLIENT_ID_TO_ACCESS + clientId);
            usernameLimit.setUsernameKey(USERNAME_TO_ACCESS + userName);
            usernameLimit.setApprovalKey(APPROVAL_UNAME_TO_ACCESS + getApprovalKey(clientId, userName));
        }
        
        return usernameLimit;
    }

    /**
     * 清理过期token
     * 每天凌晨3点执行一次
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanupExpiredTokens() {
        String lockKey = TOKEN_CLEANUP_LOCK;
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            // 获取分布式锁,确保同时只有一个实例在清理token, 过期时间为清理间隔的一半
            Boolean locked = conn.setNX(serializeKey(lockKey), serialize(System.currentTimeMillis()));
            if (Boolean.FALSE.equals(locked)) {
                // 如果未能获取锁，则直接返回，避免并发执行清理操作
                return;
            }
            // 设置锁的过期时间，防止死锁
            conn.expire(serializeKey(lockKey), 1800);

            try {
                // 执行实际的清理操作
                doCleanupExpiredTokens();
            } finally {
                // 释放锁，确保锁被正确释放
                conn.del(serializeKey(lockKey));
            }
        }  catch (RedisSystemException e) {
            log.warn("Redis操作异常", e);
            // 可以在这里添加重试逻辑或者其他错误处理
        } catch (Exception e) {
            log.warn("未预期的异常", e);
        } finally {
            // 关闭Redis连接
            conn.close();
        }
    }

    /**
     * 执行过期token的清理操作
     */
    private void doCleanupExpiredTokens() {
        // 记录日志，开始清理过期token
        log.info("开始执行过期token的清理操作");
        // 记录开始时间，用于计算处理时间
        long startTime = System.currentTimeMillis();
        // 初始化处理的key计数器
        int processedCount = 0;
        // 初始化移除的token计数器
        int removedCount = 0;

        // 获取Redis连接
        RedisConnection conn = getConnection();
        // 初始化游标，用于遍历Redis中的key
        Cursor<byte[]> cursor = null;
        try {
            // 配置扫描选项，匹配特定前缀的key，并设置每次扫描返回的元素数量
            ScanOptions options = ScanOptions.scanOptions()
                .match(serializeKeyStr(USERNAME_LIMIT_LIST) + "*")
                .count(BATCH_SIZE)
                .build();

            // 开始扫描Redis中的key
            cursor = conn.scan(options);

            // 遍历扫描结果，直到扫描完成或超过时间限制
            while(cursor.hasNext() && !isTimeExceeded(startTime)) {
                // 获取下一个key
                byte[] key = cursor.next();
                try {
                    // 清理指定用户的过期token，并获取移除的数量
                    int removed = cleanupExpiredTokensForUser(deserializeString(key));
                    // 更新处理的key数量
                    processedCount++;
                    // 更新移除的token数量
                    removedCount += removed;

                    // 每处理BATCH_SIZE个后暂停一下,避免对Redis造成太大压力
                    if(processedCount % BATCH_SIZE == 0) {
                        // 记录日志，处理了BATCH_SIZE个key
                        log.info("Processed {} keys, removed {} tokens",
                            processedCount, removedCount);
                        // 暂停100毫秒
                        TimeUnit.MILLISECONDS.sleep(100);
                    }
                } catch(Exception e) {
                    // 如果清理指定的token时出错，记录警告日志
                    log.warn("清理指定的令牌时出错: {}", deserializeString(key), e);
                }
            }
        } catch(Exception e) {
            // 如果清理过程中出现错误，记录警告日志
            log.warn("令牌清理过程中出现错误", e);
        } finally {
            // 关闭游标
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (IOException e) {
                    log.warn("关闭cursor时出错", e);
                } finally {
                    try {
                        cursor.close();
                    } catch (IOException ignored) {}
                }
            }
            // 关闭Redis连接
            conn.close();
        }

        // 记录日志，清理操作完成，记录处理的key数量、移除的token数量和耗时
        log.info("完成令牌清理: processed={}, removed={}, time={}ms",
            processedCount, removedCount, System.currentTimeMillis() - startTime);
    }

    /**
     * 检查处理时间是否超过最大处理时间
     *
     * @param startTime 开始时间
     * @return 如果处理时间超过最大处理时间返回true，否则返回false
     */
    private boolean isTimeExceeded(long startTime) {
        return System.currentTimeMillis() - startTime > TimeUnit.SECONDS.toMillis(MAX_PROCESS_TIME);
    }

    /**
     * 清理指定用户的过期token
     *
     * @param userLimitKey 用户限制key
     * @return 返回移除的token数量
     */
    private int cleanupExpiredTokensForUser(String userLimitKey) {
        RedisConnection conn = getConnection();
        int removedCount = 0;
        try {
            // 每次只取一定数量的token
            List<byte[]> tokens = conn.lRange(serializeKey(userLimitKey), 0, BATCH_SIZE);

            if(tokens == null || tokens.isEmpty()) {
                return 0;
            }

            // 遍历token并检查是否过期
            for (byte[] tokenBytes : tokens) {
                try {
                    UsernameLimit limit = deserializeUsernameLimit(tokenBytes);
                    if (isTokenExpired(limit)) {
                        // 如果token过期，移除token
                        log.warn("令牌过期, 移除用户名访问限额限制，里的相关访问令牌, usernameLimitKey: {}", userLimitKey);
                        removeTokenWhenOverUsernameLimit(serializeKey(userLimitKey));
                        removedCount++;
                    }
                } catch(Exception e) {
                    log.warn("移除过期令牌时发生异常: {}", userLimitKey, e);
                }
            }
            
            if(removedCount > 0) {
                log.info("为用户 {} 移除了 {} 个过期的令牌", userLimitKey, removedCount);
            }
        } finally {
            conn.close();
        }
        return removedCount;
    }

    /**
     * 检查 UsernameLimit 对象中的 token 是否过期
     * 
     * @param limit UsernameLimit 对象
     * @return 如果 token 过期返回 true，否则返回 false
     */
    private boolean isTokenExpired(UsernameLimit limit) {
        if (limit == null) {
            return true;
        }
        
        RedisConnection conn = getConnection();
        try {
            // 检查访问令牌是否存在
            byte[] accessKey = serializeKey(limit.getAccessKey());
            byte[] accessValue = conn.get(accessKey);
            
            // 如果访问令牌不存在，说明已过期
            if (accessValue == null) {
                return true;
            }
            
            // 反序列化访问令牌
            OAuth2AccessToken accessToken = deserializeAccessToken(accessValue);
            if (accessToken == null) {
                return true;
            }
            
            // 检查令牌是否过期
            return accessToken.isExpired();
        } finally {
            conn.close();
        }
    }

    /**
     * 获取字节数组列表
     * 通过Redis连接和批准键来获取存储在Redis集合中的所有字节数组
     *
     * @param approvalKey 字节数组，作为Redis集合的键
     * @param conn Redis连接对象，用于与Redis进行交互
     * @return 返回一个字节数组列表，如果集合为空或不存在，则返回空列表
     */
    private List<byte[]> getByteLists(byte[] approvalKey, RedisConnection conn) {
        List<byte[]> byteList = new ArrayList<>();
        
        // 获取列表长度
        Long size = conn.lLen(approvalKey);
        if(size == null || size <= 0) {
            return Collections.emptyList();
        }

        // 分批获取,每次获取100个
        int batchSize = 100;
        long start = 0;
        
        while(start < size) {
            // 计算本批次结束位置
            long end = Math.min(start + batchSize - 1, size - 1);
            
            // 获取一批数据
            List<byte[]> batch = conn.lRange(approvalKey, start, end);
            if(batch != null && !batch.isEmpty()) {
                byteList.addAll(batch);
            }
            
            // 移动start位置
            start = end + 1;
            
            // 每批处理后暂停一下,避免对Redis造成压力
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        // 返回包含所有字节数组的列表
        return byteList;
    }

    /**
     * 设置刷新令牌相关key的过期时间
     * 
     * @param refreshToken 刷新令牌
     * @param conn Redis连接
     * @param expireSeconds 过期时间(秒),-1表示永不过期
     */
    private void setRefreshRelatedKeysExpire(String refreshToken, RedisConnection conn, int expireSeconds) {
        byte[] refreshKey = serializeKey(REFRESH + refreshToken);
        byte[] refreshAuthKey = serializeKey(REFRESH_AUTH + refreshToken);
        byte[] refresh2AccessKey = serializeKey(REFRESH_TO_ACCESS + refreshToken);
        byte[] access2RefreshKey = serializeKey(ACCESS_TO_REFRESH + refreshToken);
        
        if (expireSeconds < 0) {
            // 移除过期时间, 永不过期
            conn.persist(refreshKey);
            conn.persist(refreshAuthKey);
            conn.persist(refresh2AccessKey);
            conn.persist(access2RefreshKey);
        } else {
            // 设置过期时间
            conn.expire(refreshKey, expireSeconds);
            conn.expire(refreshAuthKey, expireSeconds);
            conn.expire(refresh2AccessKey, expireSeconds);
            conn.expire(access2RefreshKey, expireSeconds);
        }
    }

    /**
     * 检查token是否应该被移除
     *
     * @param limit 用户名限制对象
     * @param conn Redis连接
     * @return 如果应该移除返回true,否则返回false
     */
    private boolean shouldRemoveToken(UsernameLimit limit, RedisConnection conn) {
        try {
            // 1. 检查访问令牌是否存在
            byte[] accessKey = serializeKey(limit.getAccessKey());
            byte[] accessValue = conn.get(accessKey);
            if (accessValue == null) {
                // token已不存在，可以移除
                return true;
            }
            
            // 2. 检查token是否过期
            OAuth2AccessToken token = deserializeAccessToken(accessValue);
            if (token == null || token.isExpired()) {
                return true;
            }
            
            // 3. 检查认证信息是否存在
            byte[] authKey = serializeKey(limit.getAuthKey());
            byte[] authValue = conn.get(authKey);
            // 如果认证信息不存在，可以移除
            if (authValue == null) {
                return true;
            }
            // token仍然有效
            return false;
        } catch (Exception e) {
            log.warn("验证token失败: {}", limit, e);
            // 发生异常时应该移除token以避免潜在的问题
            return true;
        }
    }

    /**
     * 清理过期或无效的token引用
     * 每周一凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 ? * MON")
    public void cleanupInvalidTokenReferences() {
        String lockKey  = CLEANUP_INVALID_TOKEN_REFERENCES_LOCK;
        // 获取Redis连接
        RedisConnection conn = getConnection();
        try {
            // 获取分布式锁
            Boolean locked = conn.setNX(serializeKey(lockKey), serialize(System.currentTimeMillis()));
            if (Boolean.FALSE.equals(locked)) {
                return;
            }
            // 设置锁过期时间为2小时
            conn.expire(serializeKey(lockKey), 7200);

            try {
                doCleanupInvalidTokenReferences();
            } finally {
                conn.del(serializeKey(lockKey));
            }
        } finally {
            conn.close();
        }
    }

    /**
     * 执行清理无效token引用的具体操作
     */
    private void doCleanupInvalidTokenReferences() {
        log.info("开始清理无效token引用");
        long startTime = System.currentTimeMillis();
        int totalProcessed = 0;
        int totalRemoved = 0;

        // 清理各种类型的token引用列表
        String[] patterns = new String[] {
            CLIENT_ID_TO_ACCESS + "*",
            APPROVAL_UNAME_TO_ACCESS + "*", 
            USERNAME_TO_ACCESS + "*",
            USERNAME_LIMIT_LIST + "*"
        };

        for (String pattern : patterns) {
            try {
                int[] result = cleanupTokenReferencesByPattern(pattern);
                totalProcessed += result[0];
                totalRemoved += result[1];
            } catch (Exception e) {
                log.warn("清理pattern: {} 的token引用时发生错误", pattern, e);
            }
        }

        log.info("完成无效token引用清理: 处理数={}, 移除数={}, 耗时={}ms",
            totalProcessed, totalRemoved, System.currentTimeMillis() - startTime);
    }

    /**
     * 清理指定pattern的token引用
     *
     * @param pattern 匹配的pattern
     * @return int[]{processed count, removed count}
     */
    private int[] cleanupTokenReferencesByPattern(String pattern) {
        int processedCount = 0;
        int removedCount = 0;
        boolean isUsernameLimit = pattern.contains(USERNAME_LIMIT_LIST);
        RedisConnection conn = getConnection();
        Cursor<byte[]> cursor = null;
        // 每批次处理的最大token数
        final int maxTokensPerBatch = 1000;
        // 单次执行处理的最大总token数
        final int maxTotalTokens = 20000;
        
        try {
            // 扫描匹配pattern的key
            ScanOptions options = ScanOptions.scanOptions()
                .match(serializeKeyStr(pattern))
                .count(BATCH_SIZE)
                .build();
            cursor = conn.scan(options);

            int currentBatchSize = 0;

            while (cursor.hasNext()  && processedCount < maxTotalTokens) {
                byte[] key = cursor.next();
                try {
                    // 获取列表中的部分token,每次最多处理MAX_TOKENS_PER_BATCH个
                    List<byte[]> tokens = conn.lRange(key, 0, maxTokensPerBatch - 1);
                    if (tokens == null || tokens.isEmpty()) {
                        continue;
                    }

                    for (byte[] tokenBytes : tokens) {
                        processedCount++;
                        currentBatchSize++;
                        
                        // 检查token是否有效
                        if (isUsernameLimit ? isUsernameLimitTokenInvalid(tokenBytes, conn) : isOAuth2AccessTokenInvalid(tokenBytes, conn)) {
                            // 从列表中移除无效token
                            conn.lRem(key, 0, tokenBytes);
                            removedCount++;
                        }

                        // 达到批次上限,记录日志
                        if (currentBatchSize >= maxTokensPerBatch) {
                            log.info("当前批次处理完成 - 已处理 {} 个token, 移除 {} 个无效token",
                                    processedCount, removedCount);
                            currentBatchSize = 0;
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理key: {} 时发生错误", deserializeString(key), e);
                }
            }
            // 最后一批次的处理结果
            if (currentBatchSize > 0) {
                log.info("最后批次处理完成 - 共处理 {} 个token, 移除 {} 个无效token",
                        processedCount, removedCount);
            }
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (IOException e) {
                    log.warn("关闭cursor时出错", e);
                }
            }
            conn.close();
        }
        
        return new int[]{processedCount, removedCount};
    }

    /**
     * 检查token是否无效
     */
    private boolean isOAuth2AccessTokenInvalid(byte[] tokenBytes, RedisConnection conn) {
        // 尝试作为OAuth2AccessToken处理
        try {
            OAuth2AccessToken token = deserializeAccessToken(tokenBytes);
            if (token != null) {
                // 检查token是否存在
                byte[] accessKey = serializeKey(ACCESS + token.getValue());
                if (!Boolean.TRUE.equals(conn.exists(accessKey))) {
                    return true;
                }
                // 检查token是否过期
                return token.isExpired();
            }
        } catch (Exception e) {
            log.debug("反序列化OAuth2AccessToken失败", e);
        }

        // 无法反序列化为有效的token
        return false;
    }

    /**
     * 检查token是否无效
     */
    private boolean isUsernameLimitTokenInvalid(byte[] tokenBytes, RedisConnection conn) {
        // 尝试作为UsernameLimit处理
        try {
            UsernameLimit limit = deserializeUsernameLimit(tokenBytes);
            if (limit != null) {
                return isTokenExpired(limit);
            }
        } catch (Exception e) {
            // UsernameLimit反序列化失败,继续尝试OAuth2AccessToken
            log.debug("反序列化UsernameLimit失败", e);
        }

        // 无法反序列化为有效的token
        return false;
    }
}
