package com.yeelight.service.user.server.custom;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import lombok.Getter;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 封装验证码
 * <AUTHOR>
 */
@Getter
public class CustomWebAuthenticationDetails extends WebAuthenticationDetails {

    public static final String FIELD_CACHE_CAPTCHA = "cacheCaptcha";

    private final String inputCaptcha;
    private final String cacheCaptcha;

    public CustomWebAuthenticationDetails(HttpServletRequest request) {
        super(request);
        cacheCaptcha = (String) request.getAttribute(FIELD_CACHE_CAPTCHA);
        inputCaptcha = request.getParameter(CaptchaResult.FIELD_CAPTCHA);
    }

}
