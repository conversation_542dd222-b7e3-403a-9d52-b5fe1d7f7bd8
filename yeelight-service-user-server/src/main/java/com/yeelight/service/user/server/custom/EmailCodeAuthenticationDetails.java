package com.yeelight.service.user.server.custom;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import lombok.Getter;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 封装邮箱验证码
 * <AUTHOR>
 */
@Getter
public class EmailCodeAuthenticationDetails extends WebAuthenticationDetails {

    private final String inputCaptcha;
    private final String captchaKey;

    public EmailCodeAuthenticationDetails(HttpServletRequest request) {
        super(request);
        captchaKey = request.getParameter(CaptchaResult.FIELD_CAPTCHA_KEY);
        inputCaptcha = request.getParameter(CaptchaResult.FIELD_CAPTCHA);
    }

}
