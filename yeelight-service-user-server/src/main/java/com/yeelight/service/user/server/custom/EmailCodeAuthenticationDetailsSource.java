package com.yeelight.service.user.server.custom;

import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 自定义获取AuthenticationDetails 用于封装传进来的邮箱验证码
 * <AUTHOR>
 */
public class EmailCodeAuthenticationDetailsSource implements AuthenticationDetailsSource<HttpServletRequest, WebAuthenticationDetails> {

    /**
     * 构建WebAuthenticationDetails实例。
     * 该方法覆盖了父类的buildDetails方法，用于创建并返回一个EmailCodeAuthenticationDetails实例。
     *
     * @param request HttpServletRequest对象，代表当前的HTTP请求。
     * @return 返回一个EmailCodeAuthenticationDetails实例，包含与电子邮件验证码认证相关的详细信息。
     */
    @Override
    public WebAuthenticationDetails buildDetails(HttpServletRequest request) {
        // 创建并返回EmailCodeAuthenticationDetails实例
        return new EmailCodeAuthenticationDetails(request);
    }
}
