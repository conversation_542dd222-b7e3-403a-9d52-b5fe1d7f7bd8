package com.yeelight.service.user.server.custom;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataRetrievalFailureException;
import org.springframework.jdbc.core.ArgumentPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsent;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Set;

/**
 * 新版自定义JDBC OAuth2授权同意服务
 * 基于Spring Authorization Server的OAuth2AuthorizationConsentService接口
 * 兼容旧版oauth_approvals表结构
 * 
 * <AUTHOR>
 * @description: 新版OAuth2授权同意服务，使用JDBC存储，与旧版ApprovalStore保持兼容
 */
@Slf4j
@Component("newCustomJdbcOAuth2AuthorizationConsentService")
public class NewCustomJdbcOAuth2AuthorizationConsentService implements OAuth2AuthorizationConsentService {

    /**
     * 与旧版oauth_approvals表兼容的SQL语句
     */
    private static final String COLUMN_NAMES = "registeredClientId, principalName, authorities";
    private static final String TABLE_NAME = "oauth_approvals";
    private static final String PK_FILTER = "userId = ? AND clientId = ?";
    private static final String LOAD_AUTHORIZATION_CONSENT_SQL = "SELECT " + COLUMN_NAMES
            + " FROM " + TABLE_NAME + " WHERE " + PK_FILTER + " AND status = 'APPROVED'";
    private static final String SAVE_AUTHORIZATION_CONSENT_SQL = "INSERT INTO " + TABLE_NAME
            + " (userId, clientId, scope, status, expiresAt, lastModifiedAt) VALUES (?, ?, ?, 'APPROVED', ?, ?)";
    private static final String REMOVE_AUTHORIZATION_CONSENT_SQL = "DELETE FROM " + TABLE_NAME + " WHERE " + PK_FILTER;

    private final JdbcOperations jdbcOperations;
    private RowMapper<OAuth2AuthorizationConsent> authorizationConsentRowMapper;

    public NewCustomJdbcOAuth2AuthorizationConsentService(@Qualifier("oauthDataSource") DataSource dataSource) {
        Assert.notNull(dataSource, "dataSource cannot be null");
        this.jdbcOperations = new org.springframework.jdbc.core.JdbcTemplate(dataSource);
        this.authorizationConsentRowMapper = new OAuth2AuthorizationConsentRowMapper();
    }

    @Override
    public void save(OAuth2AuthorizationConsent authorizationConsent) {
        Assert.notNull(authorizationConsent, "authorizationConsent cannot be null");
        
        try {
            // 先删除现有的授权同意记录
            remove(authorizationConsent);
            
            // 插入新的授权同意记录
            String registeredClientId = authorizationConsent.getRegisteredClientId();
            String principalName = authorizationConsent.getPrincipalName();
            Set<GrantedAuthority> authorities = authorizationConsent.getAuthorities();

            // 30天过期
            Timestamp expiresAt = Timestamp.from(Instant.now().plusSeconds(86400 * 30));
            Timestamp lastModifiedAt = Timestamp.from(Instant.now());
            
            for (GrantedAuthority authority : authorities) {
                PreparedStatementSetter pss = new ArgumentPreparedStatementSetter(new Object[]{
                        principalName, registeredClientId, authority, expiresAt, lastModifiedAt
                });
                jdbcOperations.update(SAVE_AUTHORIZATION_CONSENT_SQL, pss);
            }
            
            log.debug("Saved OAuth2AuthorizationConsent for client: {} and principal: {}", 
                    registeredClientId, principalName);
        } catch (Exception ex) {
            log.error("Failed to save OAuth2AuthorizationConsent", ex);
            throw new DataRetrievalFailureException("Failed to save OAuth2AuthorizationConsent", ex);
        }
    }

    @Override
    public void remove(OAuth2AuthorizationConsent authorizationConsent) {
        Assert.notNull(authorizationConsent, "authorizationConsent cannot be null");
        
        try {
            String registeredClientId = authorizationConsent.getRegisteredClientId();
            String principalName = authorizationConsent.getPrincipalName();
            
            PreparedStatementSetter pss = new ArgumentPreparedStatementSetter(new Object[]{
                    principalName, registeredClientId
            });
            
            int deletedRows = jdbcOperations.update(REMOVE_AUTHORIZATION_CONSENT_SQL, pss);
            log.debug("Removed {} OAuth2AuthorizationConsent records for client: {} and principal: {}", 
                    deletedRows, registeredClientId, principalName);
        } catch (Exception ex) {
            log.error("Failed to remove OAuth2AuthorizationConsent", ex);
            throw new DataRetrievalFailureException("Failed to remove OAuth2AuthorizationConsent", ex);
        }
    }

    @Override
    public OAuth2AuthorizationConsent findById(String registeredClientId, String principalName) {
        Assert.hasText(registeredClientId, "registeredClientId cannot be empty");
        Assert.hasText(principalName, "principalName cannot be empty");
        
        try {
            PreparedStatementSetter pss = new ArgumentPreparedStatementSetter(new Object[]{
                    principalName, registeredClientId
            });
            
            List<OAuth2AuthorizationConsent> result = jdbcOperations.query(
                    LOAD_AUTHORIZATION_CONSENT_SQL, pss, authorizationConsentRowMapper);
            
            if (result.isEmpty()) {
                log.debug("OAuth2AuthorizationConsent not found for client: {} and principal: {}", 
                        registeredClientId, principalName);
                return null;
            }
            
            // 合并多个scope记录为一个授权同意对象
            OAuth2AuthorizationConsent.Builder builder = OAuth2AuthorizationConsent.withId(
                    registeredClientId, principalName);
            
            for (OAuth2AuthorizationConsent consent : result) {
                builder.authorities(authorities -> authorities.addAll(consent.getAuthorities()));
            }
            
            OAuth2AuthorizationConsent authorizationConsent = builder.build();
            log.debug("Found OAuth2AuthorizationConsent for client: {} and principal: {} with {} authorities", 
                    registeredClientId, principalName, authorizationConsent.getAuthorities().size());
            
            return authorizationConsent;
        } catch (Exception ex) {
            log.error("Failed to find OAuth2AuthorizationConsent", ex);
            return null;
        }
    }

    /**
     * 设置自定义的行映射器
     */
    public void setAuthorizationConsentRowMapper(RowMapper<OAuth2AuthorizationConsent> authorizationConsentRowMapper) {
        Assert.notNull(authorizationConsentRowMapper, "authorizationConsentRowMapper cannot be null");
        this.authorizationConsentRowMapper = authorizationConsentRowMapper;
    }

    /**
     * OAuth2AuthorizationConsent行映射器
     * 将数据库记录映射为OAuth2AuthorizationConsent对象
     */
    public static class OAuth2AuthorizationConsentRowMapper implements RowMapper<OAuth2AuthorizationConsent> {

        @Override
        public OAuth2AuthorizationConsent mapRow(ResultSet rs, int rowNum) throws SQLException {
            String registeredClientId = rs.getString("clientId");
            String principalName = rs.getString("userId");
            String scope = rs.getString("scope");

            OAuth2AuthorizationConsent.Builder builder = OAuth2AuthorizationConsent.withId(
                    registeredClientId, principalName);

            if (StringUtils.hasText(scope)) {
                builder.authority(() -> scope);
            }

            return builder.build();
        }
    }

    /**
     * 创建oauth_approvals表（如果不存在）
     * 保持与旧版ApprovalStore的兼容性
     */
    public void createTableIfNotExists() {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS oauth_approvals (
                userId VARCHAR(256),
                clientId VARCHAR(256),
                scope VARCHAR(256),
                status VARCHAR(10),
                expiresAt TIMESTAMP,
                lastModifiedAt TIMESTAMP,
                PRIMARY KEY (userId, clientId, scope)
            )
            """;
        
        try {
            jdbcOperations.execute(createTableSql);
            log.info("确保oauth_approvals表存在");
        } catch (Exception ex) {
            log.warn("创建oauth_approvals表失败", ex);
        }
    }

    /**
     * 检查是否需要从旧版ApprovalStore迁移数据
     */
    public boolean needsMigration() {
        try {
            Integer count = jdbcOperations.queryForObject(
                "SELECT COUNT(*) FROM oauth_approvals WHERE status = 'APPROVED'", Integer.class);
            return count != null && count > 0;
        } catch (Exception ex) {
            log.warn("检查迁移状态失败", ex);
            return false;
        }
    }
}
