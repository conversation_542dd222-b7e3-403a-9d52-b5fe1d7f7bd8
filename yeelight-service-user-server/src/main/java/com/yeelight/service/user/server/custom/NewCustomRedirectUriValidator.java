package com.yeelight.service.user.server.custom;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationContext;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationException;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationCodeRequestAuthenticationValidator;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.function.Consumer;

/**
 * 新版自定义重定向URI验证器
 * 基于Spring Authorization Server的OAuth2AuthorizationCodeRequestAuthenticationValidator
 * 完全兼容旧版CustomRedirectResolver的所有功能
 * 
 * <AUTHOR>
 * @description: 新版重定向URI验证器，与旧版CustomRedirectResolver保持兼容
 */
@Slf4j
@Component("newCustomRedirectUriValidator")
public class NewCustomRedirectUriValidator implements Consumer<OAuth2AuthorizationCodeRequestAuthenticationContext> {

    /**
     * 与旧版CustomRedirectResolver保持一致的配置
     */
    private static final boolean MATCH_SUBDOMAINS = true;
    private static final boolean MATCH_PATH = true;
    private static final boolean MATCH_PORTS = true;
    private static final String ALL_PREFIX = "*.";

    @Override
    public void accept(OAuth2AuthorizationCodeRequestAuthenticationContext context) {
        // 获取授权请求和注册客户端
        String requestedRedirectUri = context.getAuthorizationRequest().getRedirectUri();
        RegisteredClient registeredClient = context.getRegisteredClient();

        // 如果没有请求重定向URI，使用默认验证逻辑
        if (!StringUtils.hasText(requestedRedirectUri)) {
            OAuth2AuthorizationCodeRequestAuthenticationValidator.DEFAULT_REDIRECT_URI_VALIDATOR
                    .accept(context);
            return;
        }

        // 检查请求的重定向URI是否与注册的URI匹配
        boolean isValidRedirectUri = registeredClient.getRedirectUris().stream()
                .anyMatch(registeredRedirectUri -> redirectMatches(requestedRedirectUri, registeredRedirectUri));

        if (!isValidRedirectUri) {
            OAuth2Error error = new OAuth2Error(OAuth2ErrorCodes.INVALID_REQUEST,
                    "Invalid redirect_uri: " + requestedRedirectUri, null);
            throw new OAuth2AuthorizationCodeRequestAuthenticationException(error, null);
        }

        log.debug("Redirect URI validation passed for: {}", requestedRedirectUri);
    }

    /**
     * 判断请求的重定向URI是否与指定的重定向URI"匹配"
     * 与旧版CustomRedirectResolver的redirectMatches方法完全一致
     *
     * @param requestedRedirect 请求的重定向URI
     * @param redirectUri 注册的重定向URI
     * @return 请求的重定向URI是否与指定的重定向URI"匹配"
     */
    protected boolean redirectMatches(String requestedRedirect, String redirectUri) {
        try {
            // 处理以"*."开头的重定向URI，将其替换为"//"
            if (redirectUri.startsWith(ALL_PREFIX)) {
                redirectUri = redirectUri.replace(ALL_PREFIX, "//");
            }

            // 解析请求的重定向URI的组件
            UriComponents requestedRedirectUri = UriComponentsBuilder.fromUriString(requestedRedirect).build();
            String requestedRedirectUriScheme = (requestedRedirectUri.getScheme() != null ? requestedRedirectUri.getScheme() : "");
            String requestedRedirectUriHost = (requestedRedirectUri.getHost() != null ? requestedRedirectUri.getHost() : "");
            String requestedRedirectUriPath = (requestedRedirectUri.getPath() != null ? requestedRedirectUri.getPath() : "");

            // 解析注册的重定向URI的组件
            UriComponents registeredRedirectUri = UriComponentsBuilder.fromUriString(redirectUri).build();
            String registeredRedirectUriScheme = (registeredRedirectUri.getScheme() != null ? registeredRedirectUri.getScheme() : "");
            String registeredRedirectUriHost = (registeredRedirectUri.getHost() != null ? registeredRedirectUri.getHost() : "");
            String registeredRedirectUriPath = (registeredRedirectUri.getPath() != null ? registeredRedirectUri.getPath() : "");

            // 判断端口是否匹配
            boolean portsMatch = !MATCH_PORTS || (-1 == registeredRedirectUri.getPort() || registeredRedirectUri.getPort() == requestedRedirectUri.getPort());

            // 判断URI的方案、主机、端口和路径是否匹配
            return (registeredRedirectUriScheme.isEmpty() || registeredRedirectUriScheme.equals(requestedRedirectUriScheme)) &&
                    hostMatches(registeredRedirectUriHost, requestedRedirectUriHost) &&
                    portsMatch &&
                    // 确保路径完全匹配
                    pathMatches(registeredRedirectUriPath, StringUtils.cleanPath(requestedRedirectUriPath));
        } catch (Exception ex) {
            log.error("Error matching redirect URIs: requested={}, registered={}", requestedRedirect, redirectUri, ex);
            return false;
        }
    }

    /**
     * 检查给定的路径是否与注册的值匹配
     * 与旧版CustomRedirectResolver的pathMatches方法完全一致
     *
     * @param registered 注册的路径
     * @param requested 请求的路径
     * @return 如果它们匹配，则返回true；否则返回false
     */
    protected boolean pathMatches(String registered, String requested) {
        // 如果配置为匹配子域名、不匹配路径或注册路径为空，则视为匹配
        if (MATCH_SUBDOMAINS || !MATCH_PATH || "".equals(registered)) {
            return true;
        }
        // 对注册路径和请求路径进行严格比较
        return registered.equals(requested);
    }

    /**
     * 检查主机是否匹配
     * 与旧版CustomRedirectResolver的hostMatches方法保持一致
     *
     * @param registered 注册的主机
     * @param requested 请求的主机
     * @return 如果它们匹配，则返回true；否则返回false
     */
    protected boolean hostMatches(String registered, String requested) {
        // 如果注册的主机为空，则视为匹配
        if ("".equals(registered)) {
            return true;
        }
        
        // 如果配置为匹配子域名，则进行子域名匹配检查
        if (MATCH_SUBDOMAINS) {
            // 如果注册的主机以"//"开头（通配符处理后的结果），则匹配任何主机
            String doubleSlash = "//";
            if (registered.startsWith(doubleSlash)) {
                return true;
            }
            // 检查是否为子域名匹配
            return requested.equals(registered) || requested.endsWith("." + registered);
        }
        
        // 严格匹配
        return registered.equals(requested);
    }
}
