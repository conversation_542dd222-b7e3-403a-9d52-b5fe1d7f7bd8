package com.yeelight.service.user.server.custom;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2RefreshToken;
import com.yeelight.service.user.client.token.UsernameLimit;
import com.yeelight.service.user.server.custom.token.CustomAuthenticationKeyGenerator;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.migration.LegacyOAuth2AuthorizationConverter;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.endpoint.OAuth2ParameterNames;
import org.springframework.security.oauth2.core.oidc.endpoint.OidcParameterNames;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationCode;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * 新版自定义Redis OAuth2授权服务
 * 基于Spring Authorization Server的OAuth2AuthorizationService接口
 * 
 * <AUTHOR>
 * @description: 新版OAuth2授权服务，使用Redis存储授权信息，与旧版本共存
 */
@Slf4j
@Component("newCustomRedisAuthorizationService")
public class NewCustomRedisAuthorizationService implements OAuth2AuthorizationService {

    /**
     * RedisManager实例
     */
    private final RedisManager redisManager;

    /**
     * Redis键前缀
     */
    private final String keyPrefix;
    private final ObjectMapper objectMapper;

    /**
     * 认证密钥生成器，与CustomRedisTokenStore保持一致
     */
    private final CustomAuthenticationKeyGenerator authenticationKeyGenerator;



    /**
     * 旧版OAuth2对象转换器
     */
    private final LegacyOAuth2AuthorizationConverter legacyConverter;

    /**
     * Redis键前缀常量 - 新版OAuth2 Authorization Server格式（优先使用）
     */
    private static final String AUTHORIZATION_KEY_PREFIX = "oauth2:authorization:";
    private static final String ACCESS_TOKEN_KEY_PREFIX = "oauth2:access_token:";
    private static final String REFRESH_TOKEN_KEY_PREFIX = "oauth2:refresh_token:";
    private static final String AUTHORIZATION_CODE_KEY_PREFIX = "oauth2:authorization_code:";

    /**
     * 旧版OAuth2键前缀常量 - 兼容性读取使用
     */
    private static final String LEGACY_ACCESS_TOKEN_KEY_PREFIX = "oauth2:access:";
    private static final String LEGACY_REFRESH_TOKEN_KEY_PREFIX = "oauth2:refresh:";
    private static final String LEGACY_REFRESH_AUTH_KEY_PREFIX = "oauth2:refresh_auth:";
    private static final String LEGACY_ACCESS_TO_REFRESH_KEY_PREFIX = "oauth2:access_to_refresh:";
    private static final String LEGACY_REFRESH_TO_ACCESS_KEY_PREFIX = "oauth2:refresh_to_access:";
    private static final String LEGACY_UNAME_TO_ACCESS_KEY_PREFIX = "oauth2:uname_to_access:";

    // 用户登录端数量限制相关常量 - 与CustomRedisTokenStore完全一致
    private static final String ACCESS = "access:";
    private static final String AUTH = "auth:";
    private static final String AUTH_TO_ACCESS = "auth_to_access:";
    private static final String ACCESS_TO_REFRESH = "access_to_refresh:";
    private static final String REFRESH = "refresh:";
    private static final String REFRESH_AUTH = "refresh_auth:";
    private static final String REFRESH_TO_ACCESS = "refresh_to_access:";
    private static final String CLIENT_ID_TO_ACCESS = "client_id_to_access:";
    private static final String APPROVAL_UNAME_TO_ACCESS = "uname_to_access:";
    private static final String USERNAME_TO_ACCESS = "username_to_access:";
    private static final String USERNAME_LIMIT_LIST = "username_limit_list:";
    private static final String DELAY_REMOVE_REFRESH_TOKEN_KEY = "delay_remove:refresh_token:";
    private static final String REMOVE_TOKEN_WHEN_USERNAME_OVER_LIMIT_LOCK = "lock:remove_token_when_username_over_limit_lock:";
    private static final String CLEANUP_INVALID_TOKEN_REFERENCES_LOCK = "lock:cleanup_invalid_token_references_lock";
    private static final String TOKEN_CLEANUP_LOCK = "scheduled:token_cleanup_lock";
    private static final Long USERNAME_ACCESS_LIMIT = 20L;
    private static final int REFRESH_LAST_ACCESS_EXPIRE_TIME = 600;
    private static final int DELAY_REMOVE_SECONDS = 60 * 10; // 延迟删除时间：10分钟
    private static final int BATCH_SIZE = 500;
    private static final int MAX_PROCESS_TIME = 300;
    private static final String STATE_KEY_PREFIX = "oauth2:state:";

    public NewCustomRedisAuthorizationService(RedisManager redisManager,
            @Lazy RegisteredClientRepository registeredClientRepository) {
        Assert.notNull(redisManager, "redisManager cannot be null");
        Assert.notNull(registeredClientRepository, "registeredClientRepository cannot be null");
        this.redisManager = redisManager;
        this.keyPrefix = SecurityConstants.REDIS_OAUTH_PREFIX;
        this.objectMapper = createObjectMapper();
        this.authenticationKeyGenerator = new CustomAuthenticationKeyGenerator();

        // 初始化转换器（延迟初始化）
        this.legacyConverter = new LegacyOAuth2AuthorizationConverter(registeredClientRepository);
    }

    /**
     * 创建配置好的ObjectMapper用于序列化/反序列化
     * 使用双模式序列化器的配置
     */
    private ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册Java时间模块
        mapper.registerModule(new JavaTimeModule());

        // 配置序列化选项
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 兼容性配置 - 支持类型信息以便正确反序列化复杂对象
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance,
            ObjectMapper.DefaultTyping.NON_FINAL
        );

        return mapper;
    }

    @Override
    public void save(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "authorization cannot be null");
        
        try {
            String authorizationId = authorization.getId();
            String key = buildKey(AUTHORIZATION_KEY_PREFIX, authorizationId);
            
            // 序列化并存储授权信息
            String serializedAuthorization = serializeAuthorization(authorization);
            
            // 设置过期时间（默认30天）
            long expireTime = calculateExpireTime(authorization);
            redisManager.oauthRedisTemplate().opsForValue().set(key, serializedAuthorization, Duration.ofSeconds(expireTime));
            
            // 存储各种token的映射关系
            saveTokenMappings(authorization);

            // 存储PKCE code_challenge信息 - 与旧版YeelightAuthorizationCodeServices保持一致
            storeCodeChallenge(authorization);

            // 检测是否是刷新令牌场景，如果是则设置旧token的短期过期时间
            detectAndHandleRefreshTokenScenario(authorization);

            log.debug("Saved OAuth2Authorization with id: {}", authorizationId);
        } catch (Exception ex) {
            log.error("Failed to save OAuth2Authorization", ex);
            throw new RuntimeException("Failed to save OAuth2Authorization", ex);
        }
    }

    @Override
    public void remove(OAuth2Authorization authorization) {
        Assert.notNull(authorization, "authorization cannot be null");
        
        try {
            String authorizationId = authorization.getId();
            String key = buildKey(AUTHORIZATION_KEY_PREFIX, authorizationId);
            
            // 删除主授权记录
            redisManager.oauthRedisTemplate().delete(key);
            
            // 删除token映射关系
            removeTokenMappings(authorization);

            // 删除PKCE code_challenge信息 - 与旧版YeelightAuthorizationCodeServices保持一致
            removeCodeChallenge(authorization);

            // 清理用户名限制相关数据 - 与CustomRedisTokenStore保持一致
            if (authorization.getAccessToken() != null && StringUtils.hasText(authorization.getPrincipalName())) {
                removeUsernameLimitForAccessToken(authorization);
            }

            log.debug("Removed OAuth2Authorization with id: {}", authorizationId);
        } catch (Exception ex) {
            log.error("Failed to remove OAuth2Authorization", ex);
            throw new RuntimeException("Failed to remove OAuth2Authorization", ex);
        }
    }

    @Override
    public OAuth2Authorization findById(String id) {
        Assert.hasText(id, "id cannot be empty");
        
        try {
            String key = buildKey(AUTHORIZATION_KEY_PREFIX, id);
            String serializedAuthorization = String.valueOf(redisManager.oauthRedisTemplate().opsForValue().get(key));
            
            if (StringUtils.hasText(serializedAuthorization)) {
                OAuth2Authorization authorization = deserializeAuthorization(serializedAuthorization);
                log.debug("Found OAuth2Authorization with id: {}", id);
                return authorization;
            }
            
            log.debug("OAuth2Authorization not found with id: {}", id);
            return null;
        } catch (Exception ex) {
            log.error("Failed to find OAuth2Authorization by id: {}", id, ex);
            return null;
        }
    }

    @Override
    public OAuth2Authorization findByToken(String token, OAuth2TokenType tokenType) {
        Assert.hasText(token, "token cannot be empty");

        try {
            // 策略1：优先尝试从新版结构中查找
            String authorizationId = findAuthorizationIdByToken(token, tokenType);
            if (StringUtils.hasText(authorizationId)) {
                OAuth2Authorization authorization = findById(authorizationId);
                if (authorization != null) {
                    log.debug("从新版结构找到OAuth2Authorization");
                    return authorization;
                }
            }

            // 策略2：如果新版结构中没有找到，尝试从旧版结构中查找并转换
            OAuth2Authorization legacyAuthorization = findAuthorizationByTokenFromLegacyStructure(token, tokenType);
            if (legacyAuthorization != null) {
                log.debug("从旧版结构找到并转换OAuth2Authorization，token类型: {}", tokenType.getValue());
                return legacyAuthorization;
            }

            log.debug("OAuth2Authorization not found for token type: {}", tokenType);
            return null;
        } catch (Exception ex) {
            log.error("Failed to find OAuth2Authorization by token", ex);
            return null;
        }
    }

    /**
     * 从旧版Redis结构中查找token并转换为OAuth2Authorization
     */
    private OAuth2Authorization findAuthorizationByTokenFromLegacyStructure(String token, OAuth2TokenType tokenType) {
        try {
            switch (tokenType.getValue()) {
                case OAuth2ParameterNames.ACCESS_TOKEN -> {
                    return buildAuthorizationFromLegacyAccessToken(token);
                }
                case OAuth2ParameterNames.REFRESH_TOKEN -> {
                    return buildAuthorizationFromLegacyRefreshToken(token);
                }
                default -> {
                    log.warn("不支持的token类型: {}", tokenType.getValue());
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("从旧版结构构建OAuth2Authorization失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从AccessToken构建OAuth2Authorization（使用旧版前缀）
     */
    private OAuth2Authorization buildAuthorizationFromLegacyAccessToken(String accessToken) {
        try {
            // 1. 获取AccessToken对象（使用旧版前缀）
            String accessKey = buildKey(LEGACY_ACCESS_TOKEN_KEY_PREFIX, accessToken);
            log.debug("尝试从Redis获取AccessToken，key: {}", accessKey);

            Object accessTokenObj = redisManager.oauthRedisTemplate().opsForValue().get(accessKey);

            if (accessTokenObj == null) {
                log.debug("AccessToken对象为null，key: {}", accessKey);
                return null;
            }

            log.debug("成功获取AccessToken对象，类型: {}", accessTokenObj.getClass().getSimpleName());

            // 2. 获取RefreshToken（如果存在）
            String refreshTokenKey = buildKey(LEGACY_ACCESS_TO_REFRESH_KEY_PREFIX, accessToken);
            String refreshTokenValue = (String) redisManager.oauthRedisTemplate().opsForValue().get(refreshTokenKey);

            // 3. 获取Authentication对象（如果存在）
            Object authenticationObj = null;
            if (StringUtils.hasText(refreshTokenValue)) {
                String authKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, refreshTokenValue);
                authenticationObj = redisManager.oauthRedisTemplate().opsForValue().get(authKey);
            }

            // 4. 构建OAuth2Authorization
            return buildOAuth2AuthorizationFromLegacyObjects(accessTokenObj, refreshTokenValue, authenticationObj, accessToken);

        } catch (Exception e) {
            log.error("从AccessToken构建OAuth2Authorization失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从RefreshToken构建OAuth2Authorization（使用旧版前缀）
     * 集成刷新令牌恢复功能
     */
    private OAuth2Authorization buildAuthorizationFromLegacyRefreshToken(String refreshToken) {
        try {
            // 1. 通过refresh_to_access映射找到AccessToken（使用旧版前缀）
            String accessTokenKey = buildKey(LEGACY_REFRESH_TO_ACCESS_KEY_PREFIX, refreshToken);
            String accessTokenValue = (String) redisManager.oauthRedisTemplate().opsForValue().get(accessTokenKey);

            if (StringUtils.hasText(accessTokenValue)) {
                // 如果找到了AccessToken，使用AccessToken来构建完整的Authorization
                return buildAuthorizationFromLegacyAccessToken(accessTokenValue);
            } else {
                // 如果没有找到AccessToken，尝试直接从RefreshToken构建
                String refreshKey = buildKey(LEGACY_REFRESH_TOKEN_KEY_PREFIX, refreshToken);
                Object refreshTokenObj = redisManager.oauthRedisTemplate().opsForValue().get(refreshKey);

                if (refreshTokenObj != null) {
                    // 获取Authentication对象
                    String authKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, refreshToken);
                    Object authenticationObj = redisManager.oauthRedisTemplate().opsForValue().get(authKey);

                    // 检查并处理延迟删除状态
                    if (refreshTokenObj instanceof OAuth2RefreshToken) {
                        handleDelayedRefreshToken(refreshToken, (OAuth2RefreshToken) refreshTokenObj);
                    }

                    return buildOAuth2AuthorizationFromLegacyObjects(null, refreshToken, authenticationObj, null);
                }
            }

            return null;
        } catch (Exception e) {
            log.error("从RefreshToken构建OAuth2Authorization失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据token查找授权ID（使用新版前缀）
     * 这个方法用于查找新版OAuth2 Authorization Server存储的数据
     */
    private String findAuthorizationIdByToken(String token, OAuth2TokenType tokenType) {
        String prefix;

        switch (tokenType.getValue()) {
            case OAuth2ParameterNames.ACCESS_TOKEN -> prefix = ACCESS_TOKEN_KEY_PREFIX;
            case OAuth2ParameterNames.REFRESH_TOKEN -> prefix = REFRESH_TOKEN_KEY_PREFIX;
            case OAuth2ParameterNames.CODE -> prefix = AUTHORIZATION_CODE_KEY_PREFIX;
            case OidcParameterNames.ID_TOKEN -> {
                // ID Token通常不需要单独存储映射，因为它包含在授权记录中
                return null;
            }
            case null, default -> {
                log.warn("Unsupported token type: {}", tokenType.getValue());
                return null;
            }
        }

        String key = buildKey(prefix, token);
        Object authorizationId = redisManager.oauthRedisTemplate().opsForValue().get(key);
        return authorizationId != null ? authorizationId.toString() : null;
    }


    /**
     * 从旧版对象构建OAuth2Authorization
     * 委托给专门的转换器处理
     */
    private OAuth2Authorization buildOAuth2AuthorizationFromLegacyObjects(
            Object accessTokenObj, String refreshTokenValue, Object authenticationObj, String accessTokenValue) {

        return legacyConverter.buildOAuth2AuthorizationFromLegacyObjects(
                accessTokenObj, refreshTokenValue, authenticationObj, accessTokenValue);
    }

    /**
     * 存储token映射关系
     */
    private void saveTokenMappings(OAuth2Authorization authorization) {
        String authorizationId = authorization.getId();

        // 存储访问令牌映射
        if (authorization.getAccessToken() != null) {
            String accessToken = authorization.getAccessToken().getToken().getTokenValue();
            String key = buildKey(ACCESS_TOKEN_KEY_PREFIX, accessToken);
            redisManager.oauthRedisTemplate().opsForValue().set(key, authorizationId, Duration.ofSeconds(1800)); // 30分钟
        }
        
        // 存储刷新令牌映射
        if (authorization.getRefreshToken() != null) {
            String refreshToken = authorization.getRefreshToken().getToken().getTokenValue();
            String key = buildKey(REFRESH_TOKEN_KEY_PREFIX, refreshToken);
            redisManager.oauthRedisTemplate().opsForValue().set(key, authorizationId, Duration.ofSeconds(2592000)); // 30天
        }
        
        // 存储授权码映射
        OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCode =
            authorization.getToken(OAuth2AuthorizationCode.class);
        if (authorizationCode != null) {
            String code = authorizationCode.getToken().getTokenValue();
            String key = buildKey(AUTHORIZATION_CODE_KEY_PREFIX, code);
            redisManager.oauthRedisTemplate().opsForValue().set(key, authorizationId, Duration.ofSeconds(600)); // 10分钟
        }

        // 处理用户登录端数量限制 - 与CustomRedisTokenStore保持一致的逻辑
        if (authorization.getAccessToken() != null && StringUtils.hasText(authorization.getPrincipalName())) {
            handleUsernameLimitForAccessToken(authorization);
        }
    }

    /**
     * 删除token映射关系
     */
    private void removeTokenMappings(OAuth2Authorization authorization) {
        // 删除访问令牌映射
        if (authorization.getAccessToken() != null) {
            String accessToken = authorization.getAccessToken().getToken().getTokenValue();
            String key = buildKey(ACCESS_TOKEN_KEY_PREFIX, accessToken);
            redisManager.oauthRedisTemplate().delete(key);
        }
        
        // 删除刷新令牌映射（使用延迟删除策略）
        if (authorization.getRefreshToken() != null) {
            String refreshToken = authorization.getRefreshToken().getToken().getTokenValue();
            delayRemoveRefreshToken(refreshToken);
        }
        
        // 删除授权码映射
        OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCode = 
            authorization.getToken(OAuth2AuthorizationCode.class);
        if (authorizationCode != null) {
            String code = authorizationCode.getToken().getTokenValue();
            String key = buildKey(AUTHORIZATION_CODE_KEY_PREFIX, code);
            redisManager.oauthRedisTemplate().delete(key);
        }
    }

    /**
     * 构建Redis键
     */
    private String buildKey(String prefix, String suffix) {
        return keyPrefix + prefix.replace(keyPrefix, "") + suffix;
    }

    /**
     * 计算过期时间
     */
    private long calculateExpireTime(OAuth2Authorization authorization) {
        // 默认30天
        long defaultExpire = 2592000;
        
        // 如果有访问令牌，使用访问令牌的过期时间
        if (authorization.getAccessToken() != null && authorization.getAccessToken().getToken().getExpiresAt() != null) {
            Instant expiresAt = authorization.getAccessToken().getToken().getExpiresAt();
            long expireSeconds = expiresAt.getEpochSecond() - Instant.now().getEpochSecond();
            return Math.max(expireSeconds, defaultExpire);
        }
        
        return defaultExpire;
    }

    /**
     * 序列化授权对象
     * 使用生产级的序列化方案，将OAuth2Authorization转换为可存储的Map结构
     */
    private String serializeAuthorization(OAuth2Authorization authorization) {
        try {
            // 创建可序列化的Map结构
            Map<String, Object> authorizationMap = new HashMap<>();

            // 基本信息
            authorizationMap.put("id", authorization.getId());
            authorizationMap.put("registeredClientId", authorization.getRegisteredClientId());
            authorizationMap.put("principalName", authorization.getPrincipalName());
            authorizationMap.put("authorizationGrantType", authorization.getAuthorizationGrantType().getValue());

            // 授权范围
            if (authorization.getAuthorizedScopes() != null) {
                authorizationMap.put("authorizedScopes", new ArrayList<>(authorization.getAuthorizedScopes()));
            }

            // 属性 - 只序列化基本类型
            if (authorization.getAttributes() != null) {
                Map<String, Object> serializableAttributes = new HashMap<>();
                authorization.getAttributes().forEach((key, value) -> {
                    if (isSerializable(value)) {
                        serializableAttributes.put(key, value);
                    }
                });
                authorizationMap.put("attributes", serializableAttributes);
            }

            // Token信息 - 只存储基本信息，不存储敏感的token值
            Map<String, Object> tokens = new HashMap<>();

            // 授权码token
            OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCodeToken =
                authorization.getToken(OAuth2AuthorizationCode.class);
            if (authorizationCodeToken != null) {
                Map<String, Object> codeInfo = new HashMap<>();
                codeInfo.put("issuedAt", authorizationCodeToken.getToken().getIssuedAt());
                codeInfo.put("expiresAt", authorizationCodeToken.getToken().getExpiresAt());
                tokens.put("authorizationCode", codeInfo);
            }

            // 访问令牌token
            OAuth2Authorization.Token<OAuth2AccessToken> accessTokenToken =
                authorization.getToken(OAuth2AccessToken.class);
            if (accessTokenToken != null) {
                Map<String, Object> accessTokenInfo = new HashMap<>();
                accessTokenInfo.put("issuedAt", accessTokenToken.getToken().getIssuedAt());
                accessTokenInfo.put("expiresAt", accessTokenToken.getToken().getExpiresAt());
                accessTokenInfo.put("scopes", accessTokenToken.getToken().getScopes());
                accessTokenInfo.put("tokenType", accessTokenToken.getToken().getTokenType().getValue());
                tokens.put("accessToken", accessTokenInfo);
            }

            // 刷新令牌token
            OAuth2Authorization.Token<OAuth2RefreshToken> refreshTokenToken =
                authorization.getToken(OAuth2RefreshToken.class);
            if (refreshTokenToken != null) {
                Map<String, Object> refreshTokenInfo = new HashMap<>();
                refreshTokenInfo.put("issuedAt", refreshTokenToken.getToken().getIssuedAt());
                refreshTokenInfo.put("expiresAt", refreshTokenToken.getToken().getExpiresAt());
                tokens.put("refreshToken", refreshTokenInfo);
            }

            authorizationMap.put("tokens", tokens);

            // 使用ObjectMapper序列化
            return objectMapper.writeValueAsString(authorizationMap);

        } catch (Exception ex) {
            log.error("Failed to serialize OAuth2Authorization: {}", authorization.getId(), ex);
            throw new RuntimeException("Failed to serialize OAuth2Authorization", ex);
        }
    }

    /**
     * 反序列化授权对象
     * 从序列化的JSON字符串重建OAuth2Authorization对象
     * 支持兼容性处理，避免反序列化失败
     */
    private OAuth2Authorization deserializeAuthorization(String serializedAuthorization) {
        try {
            if (!StringUtils.hasText(serializedAuthorization)) {
                return null;
            }

            // 尝试多种反序列化策略
            Map<String, Object> authorizationMap = deserializeWithCompatibility(serializedAuthorization);

            // 添加null检查，避免NullPointerException
            if (authorizationMap == null) {
                log.warn("OAuth2Authorization反序列化结果为null，可能是历史数据兼容性问题");
                // 不删除数据，只是记录错误，避免数据丢失
                return null;
            }

            // 提取基本信息
            String id = (String) authorizationMap.get("id");
            String registeredClientId = (String) authorizationMap.get("registeredClientId");
            String principalName = (String) authorizationMap.get("principalName");
            String grantType = (String) authorizationMap.get("authorizationGrantType");

            if (id == null || registeredClientId == null || principalName == null || grantType == null) {
                log.warn("Invalid serialized OAuth2Authorization data, missing required fields");
                return null;
            }

            // 创建OAuth2Authorization.Builder
            OAuth2Authorization.Builder builder = OAuth2Authorization.withRegisteredClient(
                // 注意：这里需要从RegisteredClientRepository获取完整的RegisteredClient
                // 为了简化，这里创建一个最小的RegisteredClient
                createMinimalRegisteredClient(registeredClientId)
            )
            .id(id)
            .principalName(principalName)
            .authorizationGrantType(new AuthorizationGrantType(grantType));

            // 添加授权范围
            @SuppressWarnings("unchecked")
            List<String> scopes = (List<String>) authorizationMap.get("authorizedScopes");
            if (scopes != null) {
                builder.authorizedScopes(new HashSet<>(scopes));
            }

            // 添加属性
            @SuppressWarnings("unchecked")
            Map<String, Object> attributes = (Map<String, Object>) authorizationMap.get("attributes");
            if (attributes != null) {
                attributes.forEach(builder::attribute);
            }

            // 注意：Token信息不完全反序列化，因为包含敏感信息
            // 实际的token值应该从其他地方获取或重新生成

            return builder.build();

        } catch (Exception ex) {
            log.error("Failed to deserialize OAuth2Authorization", ex);
            return null;
        }
    }

    /**
     * 兼容性反序列化方法
     * 支持多种序列化格式的自动检测和处理
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> deserializeWithCompatibility(String serializedData) {
        // 检查数据有效性
        if (serializedData == null || serializedData.trim().isEmpty()) {
            log.debug("序列化数据为空");
            return null;
        }

        // 检查特殊情况：数据为"null"字符串
        if ("null".equals(serializedData.trim())) {
            log.debug("数据为null字符串，返回null");
            return null;
        }

        // 检查数据长度，过短的数据可能已损坏
        if (serializedData.length() < 10) {
            log.warn("序列化数据过短（{}字符），可能已损坏: {}", serializedData.length(), serializedData);
            return null;
        }

        // 策略1：尝试当前的Jackson JSON反序列化
        try {
            Map<String, Object> result = objectMapper.readValue(
                serializedData, new TypeReference<>() {});
            if (result != null) {
                log.debug("Jackson JSON反序列化成功");
                return result;
            }
        } catch (Exception e) {
            log.debug("Jackson JSON反序列化失败: {}", e.getMessage());
        }

        // 策略2：尝试FastJSON反序列化（历史格式兼容）
        try {
            Object result = com.alibaba.fastjson.JSON.parse(serializedData);
            if (result instanceof Map) {
                log.warn("使用FastJSON兼容性反序列化成功");
                return (Map<String, Object>) result;
            }
        } catch (Exception e) {
            log.debug("FastJSON反序列化失败: {}", e.getMessage());
        }

        // 策略3：尝试JDK反序列化（最老格式兜底）
        try {
            byte[] bytes = serializedData.getBytes(java.nio.charset.StandardCharsets.ISO_8859_1);
            java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(bytes);
            java.io.ObjectInputStream ois = new java.io.ObjectInputStream(bis);
            Object result = ois.readObject();
            ois.close();
            bis.close();

            if (result instanceof Map) {
                log.warn("使用JDK反序列化成功");
                return (Map<String, Object>) result;
            }
        } catch (Exception e) {
            log.debug("JDK反序列化失败: {}", e.getMessage());
        }

        log.warn("所有反序列化策略都失败，数据长度: {} 字符", serializedData.length());
        return null;
    }

    /**
     * 存储PKCE code_challenge信息
     * 与旧版YeelightAuthorizationCodeServices的storeCodeChallenge方法保持一致
     */
    private void storeCodeChallenge(OAuth2Authorization authorization) {
        try {
            // 获取授权码token
            OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCodeToken = authorization.getToken(OAuth2AuthorizationCode.class);

            if (authorizationCodeToken == null) {
                return;
            }

            String code = authorizationCodeToken.getToken().getTokenValue();
            if (code == null || code.trim().isEmpty()) {
                return;
            }

            // 从授权属性中获取请求参数
            Map<String, Object> attributes = authorization.getAttributes();
            if (attributes == null) {
                return;
            }

            // 尝试获取OAuth2请求参数
            Object parametersObj = attributes.get("java.util.Map");
            if (!(parametersObj instanceof Map)) {
                return;
            }

            @SuppressWarnings("unchecked")
            Map<String, String> parameters = (Map<String, String>) parametersObj;

            // 获取code_challenge参数
            String codeChallenge = parameters.get(SecurityConstants.CODE_CHALLENGE);
            String clientId = authorization.getRegisteredClientId();

            if (codeChallenge != null && clientId != null) {
                // 使用与旧版相同的缓存键格式和过期时间
                String cacheKey = AuthUtils.generateCodeChallengeCacheKey(code);
                // 注意：这里需要序列化parameters Map，因为RedisTemplate需要字符串值
                String serializedParameters = objectMapper.writeValueAsString(parameters);
                redisManager.oauthRedisTemplate().opsForValue().set(cacheKey, serializedParameters, Duration.ofSeconds(86400)); // 1天过期，与旧版保持一致

                log.debug("Stored code_challenge for code: {} and client: {}", code, clientId);
            }
        } catch (Exception ex) {
            log.error("Failed to store code_challenge", ex);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 删除PKCE code_challenge信息
     * 与旧版YeelightAuthorizationCodeServices的remove方法中的逻辑保持一致
     */
    private void removeCodeChallenge(OAuth2Authorization authorization) {
        try {
            // 获取授权码token
            OAuth2Authorization.Token<OAuth2AuthorizationCode> authorizationCodeToken = authorization.getToken(OAuth2AuthorizationCode.class);

            if (authorizationCodeToken == null) {
                return;
            }

            String code = authorizationCodeToken.getToken().getTokenValue();
            if (code == null || code.trim().isEmpty()) {
                return;
            }

            // 删除code_challenge缓存
            String cacheKey = AuthUtils.generateCodeChallengeCacheKey(code);
            redisManager.oauthRedisTemplate().delete(cacheKey);

            log.debug("Removed code_challenge for code: {}", code);
        } catch (Exception ex) {
            log.error("Failed to remove code_challenge", ex);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 检查对象是否可序列化
     */
    private boolean isSerializable(Object value) {
        if (value == null) {
            return false;
        }

        // 允许基本类型和常见的可序列化类型
        return value instanceof String ||
               value instanceof Number ||
               value instanceof Boolean ||
               value instanceof java.util.Date ||
               value instanceof Instant ||
               value instanceof Map ||
               value instanceof List ||
               value instanceof Set;
    }

    /**
     * 创建最小的RegisteredClient用于反序列化
     * 注意：这是一个简化实现，生产环境中应该从RegisteredClientRepository获取完整信息
     */
    private org.springframework.security.oauth2.server.authorization.client.RegisteredClient createMinimalRegisteredClient(String clientId) {
        return org.springframework.security.oauth2.server.authorization.client.RegisteredClient.withId(java.util.UUID.randomUUID().toString())
                .clientId(clientId)
                .clientSecret("dummy") // 临时值，实际使用时会被覆盖
                .authorizationGrantType(org.springframework.security.oauth2.core.AuthorizationGrantType.AUTHORIZATION_CODE)
                .authorizationGrantType(org.springframework.security.oauth2.core.AuthorizationGrantType.REFRESH_TOKEN)
                .redirectUri("http://localhost:8080/callback") // 临时值
                .scope("read")
                .build();
    }

    /**
     * 根据用户主体名称查找所有访问令牌 - 与CustomRedisTokenStore完全一致的高效实现
     * 这是为了兼容旧版TokenService接口而添加的方法
     *
     * @param principalName 用户主体名称
     * @return 该用户的所有访问令牌列表
     */
    public List<YeelightOAuth2AccessToken> findTokensByPrincipalName(String principalName) {
        try {
            if (!StringUtils.hasText(principalName)) {
                return List.of();
            }

            List<YeelightOAuth2AccessToken> tokens = new ArrayList<>();

            // 使用与CustomRedisTokenStore完全一致的高效查找方式
            // 生成用户名对应的键 - 与CustomRedisTokenStore完全一致
            String usernameKey = USERNAME_TO_ACCESS + principalName;

            // 使用Redis的LIST类型存储令牌，这里获取所有存储的令牌信息 - 与CustomRedisTokenStore完全一致
            List<String> tokenValues = (List<String>) (List<?>) redisManager.oauthRedisTemplate().opsForList().range(usernameKey, 0, -1);

            // 如果令牌信息列表为空，则返回一个空列表 - 与CustomRedisTokenStore完全一致
            if (tokenValues == null || tokenValues.isEmpty()) {
                return List.of();
            }

            // 遍历token值列表，构建YeelightOAuth2AccessToken对象 - 与CustomRedisTokenStore完全一致
            findTokens(tokenValues, tokens);

            return tokens;
        } catch (Exception e) {
            log.error("Error finding tokens by principal name: {}", principalName, e);
            return List.of();
        }
    }

    private void findTokens(List<String> tokenValues, List<YeelightOAuth2AccessToken> tokens) {
        for (String tokenValue : tokenValues) {
            try {
                if (StringUtils.hasText(tokenValue)) {
                    // 通过token值查找完整的授权信息
                    OAuth2Authorization authorization = findByToken(tokenValue, OAuth2TokenType.ACCESS_TOKEN);
                    if (authorization != null && authorization.getAccessToken() != null) {
                        // 构建YeelightOAuth2AccessToken - 与CustomRedisTokenStore完全一致
                        YeelightOAuth2AccessToken token = buildYeelightOAuth2AccessToken(authorization);
                        if (token != null) {
                            tokens.add(token);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("Error processing token value: {}", tokenValue, e);
                // 继续处理其他token，不因单个token的错误而中断
            }
        }
    }

    /**
     * 构建YeelightOAuth2AccessToken对象 - 与CustomRedisTokenStore完全一致
     *
     * @param authorization OAuth2授权对象
     * @return YeelightOAuth2AccessToken对象
     */
    private YeelightOAuth2AccessToken buildYeelightOAuth2AccessToken(OAuth2Authorization authorization) {
        try {
            if (authorization == null || authorization.getAccessToken() == null) {
                return null;
            }

            String tokenValue = authorization.getAccessToken().getToken().getTokenValue();
            YeelightOAuth2AccessToken token = new YeelightOAuth2AccessToken(tokenValue);

            // 设置过期时间 - 与CustomRedisTokenStore完全一致
            if (authorization.getAccessToken().getToken().getExpiresAt() != null) {
                token.setExpiration(Date.from(authorization.getAccessToken().getToken().getExpiresAt()));
            }

            // 设置额外信息 - 与CustomRedisTokenStore完全一致
            Map<String, Object> additionalInformation = new HashMap<>();
            Map<String, Object> attributes = authorization.getAttributes();
            if (attributes != null) {
                // 从attributes中提取额外信息
                for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                    if (entry.getValue() != null) {
                        additionalInformation.put(entry.getKey(), entry.getValue());
                    }
                }
            }
            token.setAdditionalInformation(additionalInformation);

            // 设置刷新令牌 - 与CustomRedisTokenStore完全一致
            if (authorization.getRefreshToken() != null) {
                String refreshTokenValue = authorization.getRefreshToken().getToken().getTokenValue();
                // 这里需要创建一个简单的刷新令牌对象
                // 由于YeelightOAuth2AccessToken可能需要OAuth2RefreshToken，我们创建一个简单的实现
                token.setRefreshToken(new YeelightOAuth2RefreshToken(refreshTokenValue));
            }

            return token;
        } catch (Exception e) {
            log.error("Error building YeelightOAuth2AccessToken", e);
            return null;
        }
    }

    /**
     * 根据token值查找YeelightOAuth2AccessToken
     * 这是为了兼容旧版TokenService接口而添加的方法
     *
     * @param tokenValue token值
     * @return YeelightOAuth2AccessToken对象，如果不存在则返回null
     */
    public YeelightOAuth2AccessToken findAccessTokenByValue(String tokenValue) {
        try {
            if (!StringUtils.hasText(tokenValue)) {
                return null;
            }

            // 通过token值查找授权信息
            OAuth2Authorization authorization = findByToken(tokenValue, OAuth2TokenType.ACCESS_TOKEN);
            if (authorization == null) {
                return null;
            }

            // 提取访问令牌信息并构建YeelightOAuth2AccessToken
            OAuth2Authorization.Token<OAuth2AccessToken> accessToken = authorization.getAccessToken();
            if (accessToken == null) {
                return null;
            }

            return getYeelightOAuth2AccessToken(accessToken, authorization);
        } catch (Exception e) {
            log.error("Error finding access token by value: {}", tokenValue, e);
            return null;
        }
    }

    @NotNull
    private static YeelightOAuth2AccessToken getYeelightOAuth2AccessToken(OAuth2Authorization.Token<OAuth2AccessToken> accessToken, OAuth2Authorization authorization) {
        YeelightOAuth2AccessToken token = new YeelightOAuth2AccessToken(accessToken.getToken().getTokenValue());

        // 设置过期时间
        if (accessToken.getToken().getExpiresAt() != null) {
            token.setExpiration(Date.from(accessToken.getToken().getExpiresAt()));
        }

        // 设置scope
        if (accessToken.getToken().getScopes() != null) {
            token.setScope(accessToken.getToken().getScopes());
        }

        // 设置additionalInformation（从authorization的attributes中获取）
        Map<String, Object> additionalInfo = new HashMap<>();
        Map<String, Object> attributes = authorization.getAttributes();
        if (attributes != null) {
            additionalInfo.putAll(attributes);
        }
        token.setAdditionalInformation(additionalInfo);
        return token;
    }

    /**
     * 根据授权码查找OAuth2Authorization
     * 这是为了兼容旧版TokenService接口而添加的方法
     *
     * @param authorizationCode 授权码
     * @return OAuth2Authorization对象，如果不存在则返回null
     */
    public OAuth2Authorization findByAuthorizationCode(String authorizationCode) {
        try {
            if (!StringUtils.hasText(authorizationCode)) {
                return null;
            }

            // 获取所有授权相关的键
            Set<String> authorizationKeys = redisManager.redisTemplate().keys(buildKey(AUTHORIZATION_KEY_PREFIX,"*"));

            for (String key : authorizationKeys) {
                try {
                    String authorizationData = String.valueOf(redisManager.redisTemplate().opsForValue().get(key));
                    if (authorizationData != null) {
                        Map<String, Object> authorizationMap = objectMapper.readValue(authorizationData, new TypeReference<>() {
                        });

                        // 检查是否包含匹配的授权码
                        Map<String, Object> authorizationCodeMap = (Map<String, Object>) authorizationMap.get("authorizationCode");
                        if (authorizationCodeMap != null) {
                            String storedCode = (String) ((Map<String, Object>) authorizationCodeMap.get("token")).get("tokenValue");
                            if (authorizationCode.equals(storedCode)) {
                                // 找到匹配的授权码，反序列化并返回OAuth2Authorization
                                return deserializeAuthorization(authorizationData);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("Error processing authorization key: {}", key, e);
                    // 继续处理其他键，不因单个键的错误而中断
                }
            }

            return null;
        } catch (Exception e) {
            log.error("Error finding authorization by code: {}", authorizationCode, e);
            return null;
        }
    }

    /**
     * 处理用户登录端数量限制 - 与CustomRedisTokenStore完全一致的逻辑
     * 每个用户最多允许20个并发登录，超出则自动清除最旧的token
     *
     * @param authorization OAuth2授权对象
     */
    private void handleUsernameLimitForAccessToken(OAuth2Authorization authorization) {
        try {
            String principalName = authorization.getPrincipalName();
            String clientId = authorization.getRegisteredClientId();
            String accessToken = authorization.getAccessToken().getToken().getTokenValue();

            // 构建用户名限制列表的key - 与CustomRedisTokenStore完全一致
            String approvalKey = getApprovalKey(clientId, principalName);
            String usernameLimitKey = USERNAME_LIMIT_LIST + approvalKey;

            // 创建UsernameLimit对象 - 与CustomRedisTokenStore完全一致
            UsernameLimit usernameLimit = createUsernameLimit(authorization, approvalKey);

            // 检查用户名访问限额，如果超过限额则移除旧的访问令牌 - 与CustomRedisTokenStore完全一致
            Long usernameLimitLen = redisManager.oauthRedisTemplate().opsForList().size(usernameLimitKey);

            if (usernameLimitLen != null && usernameLimitLen >= USERNAME_ACCESS_LIMIT) {
                log.warn("用户名访问限额超过限制，移除最旧的访问令牌, usernameLimitKey: {}", usernameLimitKey);
                removeTokenWhenOverUsernameLimit(usernameLimitKey);
            }

            // 检查是否存在旧的访问令牌 - 与CustomRedisTokenStore完全一致
            String accessKey = ACCESS + accessToken;
            String oldAccessTokenData = (String) redisManager.oauthRedisTemplate().opsForValue().get(accessKey);

            // 处理用户名和访问令牌关系 - 与CustomRedisTokenStore完全一致
            handleUserNameAndAccessRel(authorization, oldAccessTokenData, usernameLimit);

            // 如果存在旧的访问令牌，则从客户端列表中移除 - 与CustomRedisTokenStore完全一致
            if (oldAccessTokenData != null) {
                log.warn("旧的访问令牌存在，从客户端列表中移除, clientId: {}", clientId);
                String clientKey = CLIENT_ID_TO_ACCESS + clientId;
                redisManager.oauthRedisTemplate().opsForList().remove(clientKey, 0, oldAccessTokenData);
            }

            // 将新的访问令牌添加到客户端列表 - 与CustomRedisTokenStore完全一致
            String clientKey = CLIENT_ID_TO_ACCESS + clientId;
            redisManager.oauthRedisTemplate().opsForList().rightPush(clientKey, accessToken);

            // 处理访问令牌过期逻辑 - 与CustomRedisTokenStore完全一致
            handleAccessTokenExpiration(authorization);

            // 在存储访问令牌时设置刷新令牌 - 与CustomRedisTokenStore完全一致
            setRefreshTokenWhenStoreAccessToken(authorization, usernameLimit);

            // 更新用户名限额列表 - 与CustomRedisTokenStore完全一致
            log.info("更新用户名限额列表, 删除头部旧的用户名限额, 尾部写入新的用户名限额, usernameLimitKey: {}", usernameLimitKey);

            // 1. 直接删除可能存在的旧记录
            redisManager.oauthRedisTemplate().opsForList().remove(usernameLimitKey, 0, usernameLimit);

            // 2. 添加新的记录到列表尾部, 从右侧(尾部)添加新token
            // 列表的结构：[oldest token] <- [token2] <- [token3] <- [newest token]  // 从左到右表示时间顺序
            //   (head/left)                              (tail/right)
            redisManager.oauthRedisTemplate().opsForList().rightPush(usernameLimitKey, usernameLimit);

            // 3. 设置过期时间 - 与CustomRedisTokenStore完全一致
            if (authorization.getAccessToken().getToken().getExpiresAt() != null) {
                long seconds = Duration.between(Instant.now(), authorization.getAccessToken().getToken().getExpiresAt()).getSeconds();
                if (seconds > 0) {
                    redisManager.oauthRedisTemplate().expire(usernameLimitKey, Duration.ofSeconds(seconds));
                }
            }

            log.debug("Updated username limit for user: {}, client: {}", principalName, clientId);
        } catch (Exception e) {
            log.error("Error handling username limit for access token", e);
        }
    }

    /**
     * 获取批准键 - 与CustomRedisTokenStore保持完全一致
     * 审批键格式为客户端ID加上用户名称，用户名称前加冒号分隔
     * 如果用户名称为空，则仅返回客户端ID
     */
    private String getApprovalKey(String clientId, String principalName) {
        // 生成并返回审批键 - 与CustomRedisTokenStore完全一致的逻辑
        return clientId + (principalName == null ? "" : ":" + principalName);
    }

    /**
     * 创建UsernameLimit对象 - 与CustomRedisTokenStore完全一致
     */
    private UsernameLimit createUsernameLimit(OAuth2Authorization authorization, String approvalKey) {
        String accessToken = authorization.getAccessToken().getToken().getTokenValue();
        String principalName = authorization.getPrincipalName();
        String clientId = authorization.getRegisteredClientId();

        UsernameLimit usernameLimit = new UsernameLimit();
        // 使用与CustomRedisTokenStore完全一致的常量和逻辑
        usernameLimit.setAccessKey(ACCESS + accessToken);
        usernameLimit.setAuthKey(AUTH + accessToken);
        // 使用与CustomRedisTokenStore完全一致的authenticationKeyGenerator
        String authKey = authenticationKeyGenerator.extractKey(authorization);
        usernameLimit.setAuthToAccessKey(AUTH_TO_ACCESS + authKey);
        usernameLimit.setApprovalKey(APPROVAL_UNAME_TO_ACCESS + approvalKey);
        usernameLimit.setClientId(CLIENT_ID_TO_ACCESS + clientId);

        // 只有当principalName不为空时才设置usernameKey - 与CustomRedisTokenStore逻辑一致
        if (principalName != null) {
            usernameLimit.setUsernameKey(USERNAME_TO_ACCESS + principalName);
        }

        // 如果有刷新令牌，设置相关键 - 与CustomRedisTokenStore完全一致
        if (authorization.getRefreshToken() != null) {
            String refreshToken = authorization.getRefreshToken().getToken().getTokenValue();
            usernameLimit.setRefreshKey(REFRESH + refreshToken);
            usernameLimit.setRefreshToAccessKey(REFRESH_TO_ACCESS + refreshToken);
            usernameLimit.setRefreshAuthKey(REFRESH_AUTH + refreshToken);
            usernameLimit.setAccessToRefreshKey(ACCESS_TO_REFRESH + accessToken);
        }

        return usernameLimit;
    }

    /**
     * 处理用户名和访问令牌关系 - 与CustomRedisTokenStore完全一致
     *
     * @param authorization OAuth2授权对象
     * @param oldAccessTokenData 旧的访问令牌数据
     * @param usernameLimit 用户名限制器
     */
    private void handleUserNameAndAccessRel(OAuth2Authorization authorization, String oldAccessTokenData, UsernameLimit usernameLimit) {
        try {
            String principalName = authorization.getPrincipalName();
            String clientId = authorization.getRegisteredClientId();
            String accessToken = authorization.getAccessToken().getToken().getTokenValue();

            // 确保认证主体不是仅客户端 - 与CustomRedisTokenStore完全一致
            if (principalName != null) {
                String approvalKey = APPROVAL_UNAME_TO_ACCESS + getApprovalKey(clientId, principalName);

                // 如果旧的访问令牌存在，则从批准列表中移除 - 与CustomRedisTokenStore完全一致
                if (oldAccessTokenData != null) {
                    log.warn("旧的访问令牌存在，从批准列表中移除, approvalKey: {}", approvalKey);
                    redisManager.oauthRedisTemplate().opsForList().remove(approvalKey, 0, oldAccessTokenData);
                }

                // 验证新的访问令牌是否有效 - 与CustomRedisTokenStore完全一致
                if (accessToken == null) {
                    log.warn("新的访问令牌为空");
                    throw new IllegalStateException("New access token is null");
                }

                // 将新的访问令牌添加到批准列表 - 与CustomRedisTokenStore完全一致
                redisManager.oauthRedisTemplate().opsForList().rightPush(approvalKey, accessToken);

                // 如果认证名称为空则返回 - 与CustomRedisTokenStore完全一致
                if (!StringUtils.hasText(principalName)) {
                    return;
                }

                // 生成用户名键，用于在Redis中标识用户名相关的访问令牌 - 与CustomRedisTokenStore完全一致
                String usernameKey = USERNAME_TO_ACCESS + principalName;
                // 设置用户名键到用户名限制器，以便后续操作 - 与CustomRedisTokenStore完全一致
                usernameLimit.setUsernameKey(usernameKey);

                try {
                    // 如果旧的访问令牌存在，则从用户名相关的列表中移除 - 与CustomRedisTokenStore完全一致
                    if (oldAccessTokenData != null) {
                        log.warn("旧的访问令牌存在，从用户名相关的列表中移除, usernameKey: {}", usernameKey);
                        redisManager.oauthRedisTemplate().opsForList().remove(usernameKey, 0, oldAccessTokenData);
                    }

                    // 将新的访问令牌添加到用户名相关的列表 - 与CustomRedisTokenStore完全一致
                    redisManager.oauthRedisTemplate().opsForList().rightPush(usernameKey, accessToken);
                } catch (Exception e) {
                    log.warn("处理用户名访问令牌关系失败: {}", principalName, e);
                    // 回滚已执行的操作 - 与CustomRedisTokenStore完全一致
                    redisManager.oauthRedisTemplate().opsForList().remove(approvalKey, 0, accessToken);
                    throw new RuntimeException("Failed to handle username access token relationship", e);
                }
            }
        } catch (Exception e) {
            log.error("Error handling username and access token relationship", e);
            throw e;
        }
    }

    /**
     * 处理访问令牌过期逻辑 - 与CustomRedisTokenStore完全一致
     * 当OAuth2访问令牌过期时，确保相关缓存数据同步过期
     *
     * @param authorization OAuth2授权对象
     */
    private void handleAccessTokenExpiration(OAuth2Authorization authorization) {
        try {
            // 检查令牌是否有过期时间 - 与CustomRedisTokenStore完全一致
            if (authorization.getAccessToken().getToken().getExpiresAt() != null) {
                // 获取令牌过期时间（秒） - 与CustomRedisTokenStore完全一致
                long seconds = Duration.between(Instant.now(), authorization.getAccessToken().getToken().getExpiresAt()).getSeconds();

                // 如果token已过期,则立即删除 - 与CustomRedisTokenStore完全一致
                if (seconds <= 0) {
                    log.warn("Token已过期，立即删除相关数据");
                    remove(authorization);
                    return;
                }

                String accessToken = authorization.getAccessToken().getToken().getTokenValue();
                String authKey = authenticationKeyGenerator.extractKey(authorization);

                // 为相关键设置过期时间 - 与CustomRedisTokenStore完全一致
                List<String> tokenKeys = Arrays.asList(
                    ACCESS + accessToken,
                    AUTH + accessToken,
                    AUTH_TO_ACCESS + authKey,
                    CLIENT_ID_TO_ACCESS + authorization.getRegisteredClientId(),
                    APPROVAL_UNAME_TO_ACCESS + getApprovalKey(authorization.getRegisteredClientId(), authorization.getPrincipalName())
                );

                for (String key : tokenKeys) {
                    try {
                        redisManager.oauthRedisTemplate().expire(key, Duration.ofSeconds(seconds));
                    } catch (Exception e) {
                        log.warn("设置键过期时间失败: {}", key, e);
                    }
                }

                log.debug("设置访问令牌过期时间: {} 秒", seconds);
            }
        } catch (Exception e) {
            log.error("Error handling access token expiration", e);
        }
    }

    /**
     * 在存储访问令牌时设置刷新令牌 - 与CustomRedisTokenStore完全一致
     *
     * @param authorization OAuth2授权对象
     * @param usernameLimit 用户名限制对象，用于存储Redis中的键
     */
    private void setRefreshTokenWhenStoreAccessToken(OAuth2Authorization authorization, UsernameLimit usernameLimit) {
        try {
            // 检查刷新令牌是否存在 - 与CustomRedisTokenStore完全一致
            if (authorization.getRefreshToken() != null && authorization.getRefreshToken().getToken() != null) {
                String refreshTokenValue = authorization.getRefreshToken().getToken().getTokenValue();
                String accessTokenValue = authorization.getAccessToken().getToken().getTokenValue();

                // 生成刷新令牌到访问令牌的映射键 - 与CustomRedisTokenStore完全一致
                String refreshToAccessKey = REFRESH_TO_ACCESS + refreshTokenValue;
                String accessToRefreshKey = ACCESS_TO_REFRESH + accessTokenValue;

                // 设置用户名限制对象中的键值 - 与CustomRedisTokenStore完全一致
                usernameLimit.setAccessToRefreshKey(accessToRefreshKey);
                usernameLimit.setRefreshToAccessKey(refreshToAccessKey);
                usernameLimit.setRefreshKey(REFRESH + refreshTokenValue);
                usernameLimit.setRefreshAuthKey(REFRESH_AUTH + refreshTokenValue);

                // 设置映射关系 - 与CustomRedisTokenStore完全一致
                redisManager.oauthRedisTemplate().opsForValue().set(refreshToAccessKey, accessTokenValue);
                redisManager.oauthRedisTemplate().opsForValue().set(accessToRefreshKey, refreshTokenValue);

                // 设置刷新令牌的过期时间 - 与CustomRedisTokenStore完全一致
                log.info("访问令牌对应的刷新令牌存在, 重新写入刷新令牌并重新设置刷新令牌的过期时间, refresh: {}", refreshTokenValue);
                setRefreshTokenExpiration(authorization, Arrays.asList(refreshToAccessKey, accessToRefreshKey));
            }
        } catch (Exception e) {
            log.error("Error setting refresh token when storing access token", e);
        }
    }

    /**
     * 设置刷新令牌的过期时间 - 与CustomRedisTokenStore完全一致
     *
     * @param authorization OAuth2授权对象
     * @param tokenKeys 需要设置过期时间的键列表
     */
    private void setRefreshTokenExpiration(OAuth2Authorization authorization, List<String> tokenKeys) {
        try {
            // 检查刷新令牌是否有过期时间 - 与CustomRedisTokenStore完全一致
            if (authorization.getRefreshToken() != null && authorization.getRefreshToken().getToken().getExpiresAt() != null) {
                // 获取刷新令牌过期时间（秒） - 与CustomRedisTokenStore完全一致
                long seconds = Duration.between(Instant.now(), authorization.getRefreshToken().getToken().getExpiresAt()).getSeconds();

                // 如果刷新令牌已过期,则立即删除 - 与CustomRedisTokenStore完全一致
                if (seconds <= 0) {
                    log.warn("刷新令牌已过期，立即删除相关数据");
                    for (String key : tokenKeys) {
                        redisManager.oauthRedisTemplate().delete(key);
                    }
                    return;
                }

                // 为相关键设置过期时间 - 与CustomRedisTokenStore完全一致
                for (String key : tokenKeys) {
                    try {
                        redisManager.oauthRedisTemplate().expire(key, Duration.ofSeconds(seconds));
                    } catch (Exception e) {
                        log.warn("设置刷新令牌键过期时间失败: {}", key, e);
                    }
                }

                log.debug("设置刷新令牌过期时间: {} 秒", seconds);
            }
        } catch (Exception e) {
            log.error("Error setting refresh token expiration", e);
        }
    }

    /**
     * 当用户名超过访问限制时，移除对应的令牌 - 与CustomRedisTokenStore完全一致
     */
    private void removeTokenWhenOverUsernameLimit(String usernameLimitKey) {
        String lockKey = REMOVE_TOKEN_WHEN_USERNAME_OVER_LIMIT_LOCK + usernameLimitKey;

        try {
            // 1. 增加锁的重试机制 - 与CustomRedisTokenStore完全一致
            int retryCount = 3;
            while (retryCount > 0) {
                // 获取分布式锁
                String lockValue = UUID.randomUUID().toString();
                Boolean locked = redisManager.oauthRedisTemplate().opsForValue()
                    .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(10));

                if (Boolean.TRUE.equals(locked) && removeTokenWhenOverUsernameLimit(usernameLimitKey, lockKey, lockValue)) {
                    break;
                }
                retryCount--;
                if (retryCount > 0) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理token移除失败", e);
        }
    }

    private boolean removeTokenWhenOverUsernameLimit(String usernameLimitKey, String lockKey, String lockValue) {
        try {
            // 3. 直接弹出最早的token, 使用 LPOP 移除最早的 token
            UsernameLimit limit = (UsernameLimit) redisManager.oauthRedisTemplate()
                .opsForList().leftPop(usernameLimitKey);

            // 4. 移除与被限制的用户名相关的所有键
            if (limit != null && shouldRemoveToken(limit)) {
                removeRelatedKeys(limit);
                log.info("Removed oldest token due to username limit: {}", limit.getAccessKey());
            } else if (limit != null) {
                // 5. 如果不应该移除，放回列表
                redisManager.oauthRedisTemplate().opsForList().rightPush(usernameLimitKey, limit);
            }
            return true;
        } catch (Exception e) {
            log.warn("当用户名数量超过限制时，未能移除令牌", e);
        } finally {
            // 7. 释放锁 - 检查锁值确保安全释放
            String currentValue = (String) redisManager.oauthRedisTemplate().opsForValue().get(lockKey);
            if (lockValue.equals(currentValue)) {
                redisManager.oauthRedisTemplate().delete(lockKey);
            }
        }
        return false;
    }

    /**
     * 检查是否应该移除token
     * 参考CustomRedisTokenStore的完整实现，包含过期检查
     */
    private boolean shouldRemoveToken(UsernameLimit limit) {
        if (limit == null || limit.getAccessKey() == null) {
            return true;
        }

        try {
            // 1. 检查访问令牌是否存在
            String accessKey = limit.getAccessKey();
            Object accessTokenObj = redisManager.oauthRedisTemplate().opsForValue().get(accessKey);
            if (accessTokenObj == null) {
                log.debug("访问令牌不存在，应该移除: {}", accessKey);
                return true;
            }

            // 2. 检查认证信息是否存在
            String authTokenKey = limit.getAuthKey();
            Object authValue = redisManager.oauthRedisTemplate().opsForValue().get(authTokenKey);
            if (authValue == null) {
                log.debug("认证信息不存在，应该移除: {}", authTokenKey);
                return true;
            }

            // 3. 检查令牌是否过期
            if (isTokenExpired(limit)) {
                log.debug("令牌已过期，应该移除: {}", limit.getAccessKey());
                return true;
            }

            // 4. 在新版中，检查OAuth2Authorization是否存在
            String tokenValue = extractTokenValueFromKey(accessKey);
            if (StringUtils.hasText(tokenValue)) {
                OAuth2Authorization authorization = findByToken(tokenValue, OAuth2TokenType.ACCESS_TOKEN);
                if (authorization == null) {
                    log.debug("OAuth2Authorization不存在，应该移除: {}", tokenValue);
                    return true;
                }

                // 5. 检查OAuth2Authorization中的令牌是否过期
                if (authorization.getAccessToken() != null &&
                    authorization.getAccessToken().getToken().getExpiresAt() != null &&
                    authorization.getAccessToken().getToken().getExpiresAt().isBefore(java.time.Instant.now())) {
                    log.debug("OAuth2Authorization中的令牌已过期，应该移除: {}", tokenValue);
                    return true;
                }
            }

            // token仍然有效，不应该移除
            log.debug("令牌仍然有效，不应该移除: {}", limit.getAccessKey());
            return false;

        } catch (Exception e) {
            log.warn("验证token失败: {}", limit, e);
            // 发生异常时应该移除token以避免潜在的问题
            return true;
        }
    }

    /**
     * 检查令牌是否过期
     * 参考CustomRedisTokenStore的实现
     */
    private boolean isTokenExpired(UsernameLimit limit) {
        try {
            if (limit == null || limit.getAccessKey() == null) {
                return true;
            }

            // 1. 从Redis获取访问令牌对象
            Object accessTokenObj = redisManager.oauthRedisTemplate().opsForValue().get(limit.getAccessKey());
            if (accessTokenObj == null) {
                return true;
            }

            // 2. 检查旧版YeelightOAuth2AccessToken的过期时间
            if (accessTokenObj instanceof YeelightOAuth2AccessToken token) {
                if (token.getExpiration() != null) {
                    return token.getExpiration().before(new java.util.Date());
                }
            }

            // 3. 检查Map格式的令牌过期时间
            if (accessTokenObj instanceof java.util.Map) {
                java.util.Map<String, Object> tokenMap = (java.util.Map<String, Object>) accessTokenObj;
                Object expiration = tokenMap.get("expiration");

                if (expiration instanceof Number) {
                    long expirationTime = ((Number) expiration).longValue();
                    return expirationTime < System.currentTimeMillis();
                } else if (expiration instanceof java.util.Date) {
                    return ((java.util.Date) expiration).before(new java.util.Date());
                }
            }

            // 4. 检查Redis键的TTL
            String accessKey = limit.getAccessKey();
            Long ttl = redisManager.oauthRedisTemplate().getExpire(accessKey);
            if (ttl == -2) {
                // -2表示键不存在或已过期
                return true;
            }

            // 如果无法确定过期时间，认为未过期
            return false;

        } catch (Exception e) {
            log.warn("检查令牌过期状态失败: {}", limit, e);
            // 出错时认为已过期，安全起见
            return true;
        }
    }

    /**
     * 从Redis键中提取令牌值
     */
    private String extractTokenValueFromKey(String key) {
        if (!StringUtils.hasText(key)) {
            return null;
        }

        // 移除前缀，提取令牌值
        if (key.startsWith(ACCESS)) {
            return key.substring(ACCESS.length());
        }

        return null;
    }

    /**
     * 验证令牌的完整性和有效性
     * 提供给外部调用的公共方法
     */
    public boolean isTokenValid(String tokenValue, OAuth2TokenType tokenType) {
        try {
            if (!StringUtils.hasText(tokenValue)) {
                return false;
            }

            // 1. 尝试查找OAuth2Authorization
            OAuth2Authorization authorization = findByToken(tokenValue, tokenType);
            if (authorization == null) {
                log.debug("未找到对应的OAuth2Authorization: {}", tokenValue);
                return false;
            }

            // 2. 检查令牌是否过期
            if (tokenType == OAuth2TokenType.ACCESS_TOKEN && authorization.getAccessToken() != null) {
                java.time.Instant expiresAt = authorization.getAccessToken().getToken().getExpiresAt();
                if (expiresAt != null && expiresAt.isBefore(java.time.Instant.now())) {
                    log.debug("访问令牌已过期: {}", tokenValue);
                    return false;
                }
            } else if (tokenType == OAuth2TokenType.REFRESH_TOKEN && authorization.getRefreshToken() != null) {
                java.time.Instant expiresAt = authorization.getRefreshToken().getToken().getExpiresAt();
                if (expiresAt != null && expiresAt.isBefore(java.time.Instant.now())) {
                    log.debug("刷新令牌已过期: {}", tokenValue);
                    return false;
                }
            }

            // 3. 检查Redis中的数据是否存在
            String key = buildTokenKey(tokenValue, tokenType);
            if (!redisManager.oauthRedisTemplate().hasKey(key)) {
                log.debug("Redis中不存在对应的令牌数据: {}", key);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.warn("验证令牌有效性失败: tokenValue={}, tokenType={}", tokenValue, tokenType.getValue(), e);
            return false;
        }
    }

    /**
     * 根据令牌类型构建Redis键
     */
    private String buildTokenKey(String tokenValue, OAuth2TokenType tokenType) {
        return switch (tokenType.getValue()) {
            case OAuth2ParameterNames.ACCESS_TOKEN -> buildKey(LEGACY_ACCESS_TOKEN_KEY_PREFIX, tokenValue);
            case OAuth2ParameterNames.REFRESH_TOKEN -> buildKey(LEGACY_REFRESH_TOKEN_KEY_PREFIX, tokenValue);
            case OAuth2ParameterNames.CODE -> buildKey(AUTHORIZATION_CODE_KEY_PREFIX, tokenValue);
            default -> buildKey(ACCESS_TOKEN_KEY_PREFIX, tokenValue);
        };
    }

    /**
     * 移除相关的键 - 与CustomRedisTokenStore完全一致
     */
    private void removeRelatedKeys(UsernameLimit limit) {
        try {
            // 移除与被限制的用户名相关的所有键 - 与CustomRedisTokenStore完全一致
            if (limit.getAccessKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getAccessKey());
            }
            if (limit.getAuthKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getAuthKey());
            }
            if (limit.getAuthToAccessKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getAuthToAccessKey());
            }
            if (limit.getRefreshToAccessKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getRefreshToAccessKey());
            }
            if (limit.getAccessToRefreshKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getAccessToRefreshKey());
            }
            if (limit.getRefreshKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getRefreshKey());
            }
            if (limit.getRefreshAuthKey() != null) {
                redisManager.oauthRedisTemplate().delete(limit.getRefreshAuthKey());
            }

            // 从各个列表中移除token - 与CustomRedisTokenStore完全一致
            removeRelatedListKeys(limit);
        } catch (Exception e) {
            log.error("Error removing related keys for limit: {}", limit.getAccessKey(), e);
        }
    }

    private void removeRelatedListKeys(UsernameLimit limit) {
        if (limit.getAccessKey() != null) {
            String access = (String) redisManager.oauthRedisTemplate().opsForValue().get(limit.getAccessKey());
            if (access != null) {
                // 移除客户端ID列表中的特定访问记录
                if (limit.getClientId() != null) {
                    redisManager.oauthRedisTemplate().opsForList().remove(limit.getClientId(), 0, access);
                }
                // 移除用户名列表中的特定访问记录
                if (limit.getUsernameKey() != null) {
                    redisManager.oauthRedisTemplate().opsForList().remove(limit.getUsernameKey(), 0, access);
                }
                // 移除审批列表中的特定访问记录
                if (limit.getApprovalKey() != null) {
                    redisManager.oauthRedisTemplate().opsForList().remove(limit.getApprovalKey(), 0, access);
                }
            }
        }
    }



    /**
     * 清理过期token - 与CustomRedisTokenStore完全一致
     * 每天凌晨3点执行一次
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanupExpiredTokens() {
        String lockKey = TOKEN_CLEANUP_LOCK;

        try {
            // 获取分布式锁,确保同时只有一个实例在清理token, 过期时间为清理间隔的一半 - 与CustomRedisTokenStore完全一致
            Boolean locked = redisManager.oauthRedisTemplate().opsForValue()
                .setIfAbsent(lockKey, String.valueOf(System.currentTimeMillis()), Duration.ofSeconds(1800));

            if (Boolean.FALSE.equals(locked)) {
                // 如果未能获取锁，则直接返回，避免并发执行清理操作 - 与CustomRedisTokenStore完全一致
                return;
            }

            try {
                log.info("开始清理过期的OAuth2令牌");

                // 执行清理操作 - 与CustomRedisTokenStore完全一致
                doCleanupExpiredTokens();

                log.info("清理过期OAuth2令牌完成");
            } finally {
                // 释放锁 - 与CustomRedisTokenStore完全一致
                redisManager.oauthRedisTemplate().delete(lockKey);
            }
        } catch (Exception e) {
            log.error("清理过期OAuth2令牌时发生异常", e);
        }
    }

    /**
     * 清理无效token引用 - 与CustomRedisTokenStore完全一致
     * 每周一凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 ? * MON")
    public void cleanupInvalidTokenReferences() {
        String lockKey = CLEANUP_INVALID_TOKEN_REFERENCES_LOCK;

        try {
            // 获取分布式锁 - 与CustomRedisTokenStore完全一致
            Boolean locked = redisManager.oauthRedisTemplate().opsForValue()
                .setIfAbsent(lockKey, String.valueOf(System.currentTimeMillis()), Duration.ofSeconds(7200));

            if (Boolean.FALSE.equals(locked)) {
                return;
            }

            try {
                log.info("开始清理无效的OAuth2令牌引用");

                // 执行清理操作 - 与CustomRedisTokenStore完全一致
                doCleanupInvalidTokenReferences();

                log.info("清理无效OAuth2令牌引用完成");
            } finally {
                // 释放锁 - 与CustomRedisTokenStore完全一致
                redisManager.oauthRedisTemplate().delete(lockKey);
            }
        } catch (Exception e) {
            log.error("清理无效OAuth2令牌引用时发生异常", e);
        }
    }

    /**
     * 执行过期token的清理操作 - 与CustomRedisTokenStore完全一致
     */
    private void doCleanupExpiredTokens() {
        log.info("开始执行过期token的清理操作");
        long startTime = System.currentTimeMillis();
        int processedCount = 0;
        int removedCount = 0;

        try {
            // 获取所有用户名限制列表的键 - 与CustomRedisTokenStore完全一致
            Set<String> usernameLimitKeys = redisManager.oauthRedisTemplate().keys(buildKey(USERNAME_LIMIT_LIST, "*"));

            for (String key : usernameLimitKeys) {
                try {
                    // 检查处理时间是否超过最大处理时间 - 与CustomRedisTokenStore完全一致
                    if (isTimeExceeded(startTime)) {
                        log.warn("清理操作超时，停止处理");
                        break;
                    }

                    // 清理指定用户的过期token - 与CustomRedisTokenStore完全一致
                    int removed = cleanupExpiredTokensForUser(key);
                    processedCount++;
                    removedCount += removed;

                    // 每处理BATCH_SIZE个后暂停一下,避免对Redis造成太大压力 - 与CustomRedisTokenStore完全一致
                    if (processedCount % BATCH_SIZE == 0) {
                        log.info("Processed {} keys, removed {} tokens", processedCount, removedCount);
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                } catch (Exception e) {
                    log.warn("清理指定的令牌时出错: {}", key, e);
                }
            }
        } catch (Exception e) {
            log.warn("令牌清理过程中出现错误", e);
        }

        log.info("完成令牌清理: processed={}, removed={}, time={}ms",
            processedCount, removedCount, System.currentTimeMillis() - startTime);
    }

    /**
     * 检查处理时间是否超过最大处理时间 - 与CustomRedisTokenStore完全一致
     */
    private boolean isTimeExceeded(long startTime) {
        return System.currentTimeMillis() - startTime > (MAX_PROCESS_TIME * 1000L);
    }

    /**
     * 清理指定用户的过期token - 与CustomRedisTokenStore完全一致
     */
    private int cleanupExpiredTokensForUser(String userLimitKey) {
        int removedCount = 0;

        try {
            // 每次只取一定数量的token - 与CustomRedisTokenStore完全一致
            List<UsernameLimit> tokens = (List<UsernameLimit>) (List<?>) redisManager.oauthRedisTemplate().opsForList().range(userLimitKey, 0, BATCH_SIZE);

            if (tokens == null || tokens.isEmpty()) {
                return 0;
            }

            // 遍历token并检查是否过期 - 与CustomRedisTokenStore完全一致
            for (UsernameLimit limit : tokens) {
                try {
                    if (isTokenExpired(limit)) {
                        // 如果token过期，移除token - 与CustomRedisTokenStore完全一致
                        log.warn("令牌过期, 移除用户名访问限额限制，里的相关访问令牌, usernameLimitKey: {}", userLimitKey);
                        removeTokenWhenOverUsernameLimit(userLimitKey);
                        removedCount++;
                    }
                } catch (Exception e) {
                    log.warn("移除过期令牌时发生异常: {}", userLimitKey, e);
                }
            }
        } catch (Exception e) {
            log.error("清理用户过期token时发生异常: {}", userLimitKey, e);
        }

        return removedCount;
    }

    /**
     * 执行无效token引用的清理操作 - 与CustomRedisTokenStore完全一致
     */
    private void doCleanupInvalidTokenReferences() {
        log.info("开始执行无效token引用的清理操作");
        long startTime = System.currentTimeMillis();
        int processedCount = 0;
        int removedCount = 0;

        try {
            // 清理无效的访问令牌映射 - 与CustomRedisTokenStore完全一致
            removedCount += cleanupInvalidAccessTokenMappings();

            // 清理无效的刷新令牌映射 - 与CustomRedisTokenStore完全一致
            removedCount += cleanupInvalidRefreshTokenMappings();

            // 清理无效的用户名限制列表 - 与CustomRedisTokenStore完全一致
            removedCount += cleanupInvalidUsernameLimitLists();

        } catch (Exception e) {
            log.warn("无效token引用清理过程中出现错误", e);
        }

        log.info("完成无效token引用清理: removed={}, time={}ms",
            removedCount, System.currentTimeMillis() - startTime);
    }

    /**
     * 清理无效的访问令牌映射 - 与CustomRedisTokenStore完全一致
     */
    private int cleanupInvalidAccessTokenMappings() {
        int removedCount = 0;

        try {
            Set<String> accessTokenKeys = redisManager.oauthRedisTemplate().keys(buildKey(ACCESS_TOKEN_KEY_PREFIX ,"*"));
            for (String key : accessTokenKeys) {
                try {
                    String authorizationId = (String) redisManager.oauthRedisTemplate().opsForValue().get(key);
                    if (authorizationId != null) {
                        String authKey = buildKey(AUTHORIZATION_KEY_PREFIX, authorizationId);
                        if (!redisManager.oauthRedisTemplate().hasKey(authKey)) {
                            redisManager.oauthRedisTemplate().delete(key);
                            removedCount++;
                        }
                    } else {
                        redisManager.oauthRedisTemplate().delete(key);
                        removedCount++;
                    }
                } catch (Exception e) {
                    log.warn("清理访问令牌映射时发生异常: {}", key, e);
                }
            }
        } catch (Exception e) {
            log.error("清理无效访问令牌映射时发生异常", e);
        }

        return removedCount;
    }

    /**
     * 清理无效的刷新令牌映射 - 与CustomRedisTokenStore完全一致
     */
    private int cleanupInvalidRefreshTokenMappings() {
        int removedCount = 0;

        try {
            Set<String> refreshTokenKeys = redisManager.oauthRedisTemplate().keys(buildKey(REFRESH_TOKEN_KEY_PREFIX, "*"));
            for (String key : refreshTokenKeys) {
                try {
                    String authorizationId = (String) redisManager.oauthRedisTemplate().opsForValue().get(key);
                    if (authorizationId != null) {
                        String authKey = buildKey(AUTHORIZATION_KEY_PREFIX, authorizationId);
                        if (!redisManager.oauthRedisTemplate().hasKey(authKey)) {
                            redisManager.oauthRedisTemplate().delete(key);
                            removedCount++;
                        }
                    } else {
                        redisManager.oauthRedisTemplate().delete(key);
                        removedCount++;
                    }
                } catch (Exception e) {
                    log.warn("清理刷新令牌映射时发生异常: {}", key, e);
                }
            }
        } catch (Exception e) {
            log.error("清理无效刷新令牌映射时发生异常", e);
        }

        return removedCount;
    }

    /**
     * 清理无效的用户名限制列表 - 与CustomRedisTokenStore完全一致
     */
    private int cleanupInvalidUsernameLimitLists() {
        int removedCount = 0;

        try {
            Set<String> usernameLimitKeys = redisManager.oauthRedisTemplate().keys(buildKey(USERNAME_LIMIT_LIST, "*"));
            for (String key : usernameLimitKeys) {
                try {
                    List<UsernameLimit> limits = (List<UsernameLimit>) (List<?>) redisManager.oauthRedisTemplate().opsForList().range(key, 0, -1);
                    if (limits != null) {
                        for (UsernameLimit limit : limits) {
                            if (shouldRemoveToken(limit)) {
                                redisManager.oauthRedisTemplate().opsForList().remove(key, 0, limit);
                                removedCount++;
                            }
                        }

                        // 如果列表为空，删除整个键
                        Long size = redisManager.oauthRedisTemplate().opsForList().size(key);
                        if (size != null && size == 0) {
                            redisManager.oauthRedisTemplate().delete(key);
                        }
                    }
                } catch (Exception e) {
                    log.warn("清理用户名限制列表时发生异常: {}", key, e);
                }
            }
        } catch (Exception e) {
            log.error("清理无效用户名限制列表时发生异常", e);
        }

        return removedCount;
    }

    /**
     * 清理访问令牌相关的用户名限制数据 - 与CustomRedisTokenStore保持一致
     */
    private void removeUsernameLimitForAccessToken(OAuth2Authorization authorization) {
        try {
            String principalName = authorization.getPrincipalName();
            String clientId = authorization.getRegisteredClientId();
            String accessToken = authorization.getAccessToken().getToken().getTokenValue();

            // 构建用户名限制列表的key
            String approvalKey = getApprovalKey(clientId, principalName);
            String usernameLimitKey = USERNAME_LIMIT_LIST + approvalKey;

            // 创建要删除的UsernameLimit对象
            UsernameLimit usernameLimit = createUsernameLimit(authorization, approvalKey);

            // 从用户名限制列表中移除
            redisManager.oauthRedisTemplate().opsForList().remove(usernameLimitKey, 0, usernameLimit);

            // 清理其他映射关系
            removeAccessTokenMappings(principalName, clientId, accessToken);

            log.debug("Removed username limit for user: {}, client: {}, token: {}", principalName, clientId, accessToken);
        } catch (Exception e) {
            log.error("Error removing username limit for access token", e);
        }
    }

    /**
     * 清理访问令牌的映射关系 - 与CustomRedisTokenStore保持一致
     */
    private void removeAccessTokenMappings(String principalName, String clientId, String accessToken) {
        try {
            // 从用户名到访问令牌的映射中移除
            String usernameKey = USERNAME_TO_ACCESS + principalName;
            redisManager.oauthRedisTemplate().opsForList().remove(usernameKey, 0, accessToken);

            // 从客户端到访问令牌的映射中移除
            String clientKey = CLIENT_ID_TO_ACCESS + clientId;
            redisManager.oauthRedisTemplate().opsForList().remove(clientKey, 0, accessToken);

            // 从批准键到访问令牌的映射中移除
            String approvalKey = APPROVAL_UNAME_TO_ACCESS + getApprovalKey(clientId, principalName);
            redisManager.oauthRedisTemplate().opsForList().remove(approvalKey, 0, accessToken);
        } catch (Exception e) {
            log.error("Error removing access token mappings", e);
        }
    }

    /**
     * 获取Redis管理器 - 为了兼容旧版TokenService接口而添加的方法
     *
     * @return RedisManager实例
     */
    public RedisManager getRedisManager() {
        return this.redisManager;
    }

    /**
     * 延迟删除刷新令牌
     * 参考CustomRedisTokenStore的实现，避免立即删除刷新令牌，给客户端一个缓冲时间
     */
    private void delayRemoveRefreshToken(String refreshToken) {
        try {
            // 1. 标记为延迟删除
            String delayKey = buildKey(DELAY_REMOVE_REFRESH_TOKEN_KEY, refreshToken);
            redisManager.oauthRedisTemplate().opsForValue().set(delayKey, refreshToken,
                java.time.Duration.ofSeconds(DELAY_REMOVE_SECONDS));

            // 2. 设置刷新令牌的过期时间（而不是立即删除）
            String refreshKey = buildKey(REFRESH_TOKEN_KEY_PREFIX, refreshToken);
            redisManager.oauthRedisTemplate().expire(refreshKey,
                java.time.Duration.ofSeconds(DELAY_REMOVE_SECONDS));

            // 3. 设置相关映射的过期时间
            String legacyRefreshKey = buildKey(LEGACY_REFRESH_TOKEN_KEY_PREFIX, refreshToken);
            redisManager.oauthRedisTemplate().expire(legacyRefreshKey,
                java.time.Duration.ofSeconds(DELAY_REMOVE_SECONDS));

            String refreshAuthKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, refreshToken);
            redisManager.oauthRedisTemplate().expire(refreshAuthKey,
                java.time.Duration.ofSeconds(DELAY_REMOVE_SECONDS));

            log.debug("已标记刷新令牌延迟删除: {}, 延迟时间: {}秒", refreshToken, DELAY_REMOVE_SECONDS);

        } catch (Exception e) {
            log.error("延迟删除刷新令牌失败: {}", e.getMessage());
            // 如果延迟删除失败，直接删除
            try {
                String refreshKey = buildKey(REFRESH_TOKEN_KEY_PREFIX, refreshToken);
                redisManager.oauthRedisTemplate().delete(refreshKey);

                String legacyRefreshKey = buildKey(LEGACY_REFRESH_TOKEN_KEY_PREFIX, refreshToken);
                redisManager.oauthRedisTemplate().delete(legacyRefreshKey);

                String refreshAuthKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, refreshToken);
                redisManager.oauthRedisTemplate().delete(refreshAuthKey);
            } catch (Exception e2) {
                log.error("直接删除刷新令牌也失败: {}", e2.getMessage());
            }
        }
    }

    /**
     * 检测并处理刷新令牌场景
     * 当检测到是刷新令牌生成新的访问令牌时，设置旧令牌的短期过期时间
     */
    private void detectAndHandleRefreshTokenScenario(OAuth2Authorization authorization) {
        try {
            // 检查是否同时包含访问令牌和刷新令牌（这通常表示是刷新令牌的场景）
            if (authorization.getAccessToken() != null && authorization.getRefreshToken() != null) {
                String principalName = authorization.getPrincipalName();
                String clientId = authorization.getRegisteredClientId();

                // 查找同一用户的其他访问令牌（可能是旧的访问令牌）
                String usernameKey = buildKey(LEGACY_UNAME_TO_ACCESS_KEY_PREFIX, clientId + ":" + principalName);

                // 检查是否存在旧的访问令牌
                if (redisManager.oauthRedisTemplate().hasKey(usernameKey)) {
                    log.debug("检测到可能的刷新令牌场景，为用户 {} 设置旧令牌短期过期时间", principalName);
                    setTokenKeysExpirationAfterRefresh(authorization);
                }
            }
        } catch (Exception e) {
            log.error("检测刷新令牌场景失败: {}", e.getMessage());
        }
    }

    /**
     * 设置令牌过期时间为刷新后的短期过期时间
     * 当刷新token后，上一个accessToken应该在短时间内过期
     */
    public void setTokenKeysExpirationAfterRefresh(OAuth2Authorization authorization) {
        try {
            if (authorization.getAccessToken() != null) {
                String accessToken = authorization.getAccessToken().getToken().getTokenValue();

                // 设置访问令牌的短期过期时间
                String accessKey = buildKey(ACCESS_TOKEN_KEY_PREFIX, accessToken);
                redisManager.oauthRedisTemplate().expire(accessKey,
                    java.time.Duration.ofSeconds(REFRESH_LAST_ACCESS_EXPIRE_TIME));

                // 设置旧版访问令牌的短期过期时间
                String legacyAccessKey = buildKey(LEGACY_ACCESS_TOKEN_KEY_PREFIX, accessToken);
                redisManager.oauthRedisTemplate().expire(legacyAccessKey,
                    java.time.Duration.ofSeconds(REFRESH_LAST_ACCESS_EXPIRE_TIME));

                // 设置相关映射的短期过期时间
                String accessToRefreshKey = buildKey(LEGACY_ACCESS_TO_REFRESH_KEY_PREFIX, accessToken);
                redisManager.oauthRedisTemplate().expire(accessToRefreshKey,
                    java.time.Duration.ofSeconds(REFRESH_LAST_ACCESS_EXPIRE_TIME));

                log.debug("已设置令牌短期过期时间: {}秒", REFRESH_LAST_ACCESS_EXPIRE_TIME);
            }
        } catch (Exception e) {
            log.error("设置令牌过期时间失败: {}", e.getMessage());
        }
    }

    /**
     * 设置刷新令牌相关键的过期时间
     * 参考CustomRedisTokenStore的实现
     */
    private void setRefreshRelatedKeysExpire(String refreshToken, int expireSeconds) {
        try {
            if (!StringUtils.hasText(refreshToken)) {
                return;
            }

            // 构建所有刷新令牌相关的键
            String refreshKey = buildKey(LEGACY_REFRESH_TOKEN_KEY_PREFIX, refreshToken);
            String refreshAuthKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, refreshToken);
            String refreshToAccessKey = buildKey(LEGACY_REFRESH_TO_ACCESS_KEY_PREFIX, refreshToken);
            String accessToRefreshKey = buildKey(LEGACY_ACCESS_TO_REFRESH_KEY_PREFIX, refreshToken);

            java.time.Duration expiration = java.time.Duration.ofSeconds(expireSeconds);

            // 批量设置过期时间
            redisManager.oauthRedisTemplate().expire(refreshKey, expiration);
            redisManager.oauthRedisTemplate().expire(refreshAuthKey, expiration);
            redisManager.oauthRedisTemplate().expire(refreshToAccessKey, expiration);
            redisManager.oauthRedisTemplate().expire(accessToRefreshKey, expiration);

            log.debug("设置刷新令牌相关键过期时间: refreshToken={}, expireSeconds={}", refreshToken, expireSeconds);

        } catch (Exception e) {
            log.error("设置刷新令牌相关键过期时间失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查并处理延迟删除的刷新令牌
     * 参考CustomRedisTokenStore的实现，当读取刷新令牌时检查是否处于延迟删除状态
     */
    private OAuth2RefreshToken handleDelayedRefreshToken(String refreshTokenValue, OAuth2RefreshToken refreshToken) {
        try {
            if (!StringUtils.hasText(refreshTokenValue)) {
                return refreshToken;
            }

            // 检查是否处于延迟删除状态
            String delayKey = buildKey(DELAY_REMOVE_REFRESH_TOKEN_KEY, refreshTokenValue);
            Boolean isDelayed = redisManager.oauthRedisTemplate().hasKey(delayKey);

            if (isDelayed) {
                log.info("检测到延迟删除的刷新令牌，取消延迟删除并设置短期过期: {}", refreshTokenValue);

                // 取消延迟删除标记
                redisManager.oauthRedisTemplate().delete(delayKey);

                // 设置10秒后过期，允许使用一次
                setRefreshRelatedKeysExpire(refreshTokenValue, 10);

                log.debug("已处理延迟删除的刷新令牌: {}", refreshTokenValue);
            }

            return refreshToken;

        } catch (Exception e) {
            log.error("处理延迟删除刷新令牌失败: {}", e.getMessage(), e);
            return refreshToken;
        }
    }

    /**
     * 使访问令牌过期
     * 参考CustomRedisTokenStore的实现，设置访问令牌及相关键的短期过期时间
     */
    public void expireAccessToken(String tokenValue) {
        if (!StringUtils.hasText(tokenValue)) {
            return;
        }

        log.warn("使访问令牌过期, tokenValue: {}", tokenValue);

        try {
            // 1. 获取访问令牌信息
            OAuth2Authorization authorization = findByToken(tokenValue, OAuth2TokenType.ACCESS_TOKEN);
            if (authorization == null) {
                log.warn("未找到要过期的访问令牌: {}", tokenValue);
                return;
            }

            // 2. 设置所有相关键的短期过期时间
            setTokenKeysExpiration(authorization, REFRESH_LAST_ACCESS_EXPIRE_TIME);

            // 3. 清理令牌列表中的相关数据
            cleanupTokenFromLists(authorization);

            log.info("成功设置访问令牌过期: {}", tokenValue);

        } catch (Exception e) {
            log.error("使访问令牌过期失败: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to expire access token: " + tokenValue, e);
        }
    }

    /**
     * 设置令牌相关键的过期时间
     * 参考CustomRedisTokenStore的实现，批量设置所有相关键的过期时间
     */
    private void setTokenKeysExpiration(OAuth2Authorization authorization, int expiresSeconds) {
        try {
            if (authorization == null) {
                return;
            }

            java.time.Duration expiration = java.time.Duration.ofSeconds(expiresSeconds);

            // 1. 设置访问令牌相关键的过期时间
            if (authorization.getAccessToken() != null) {
                String accessTokenValue = authorization.getAccessToken().getToken().getTokenValue();

                String accessKey = buildKey(LEGACY_ACCESS_TOKEN_KEY_PREFIX, accessTokenValue);
                String authKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, accessTokenValue);
                String accessToRefreshKey = buildKey(LEGACY_ACCESS_TO_REFRESH_KEY_PREFIX, accessTokenValue);

                redisManager.oauthRedisTemplate().expire(accessKey, expiration);
                redisManager.oauthRedisTemplate().expire(authKey, expiration);
                redisManager.oauthRedisTemplate().expire(accessToRefreshKey, expiration);
            }

            // 2. 设置刷新令牌相关键的过期时间
            if (authorization.getRefreshToken() != null) {
                String refreshTokenValue = authorization.getRefreshToken().getToken().getTokenValue();

                String refreshKey = buildKey(LEGACY_REFRESH_TOKEN_KEY_PREFIX, refreshTokenValue);
                String refreshAuthKey = buildKey(LEGACY_REFRESH_AUTH_KEY_PREFIX, refreshTokenValue);
                String refreshToAccessKey = buildKey(LEGACY_REFRESH_TO_ACCESS_KEY_PREFIX, refreshTokenValue);

                redisManager.oauthRedisTemplate().expire(refreshKey, expiration);
                redisManager.oauthRedisTemplate().expire(refreshAuthKey, expiration);
                redisManager.oauthRedisTemplate().expire(refreshToAccessKey, expiration);
            }

            // 3. 设置认证相关键的过期时间
            String principalName = authorization.getPrincipalName();
            String clientId = authorization.getRegisteredClientId();

            if (StringUtils.hasText(principalName) && StringUtils.hasText(clientId)) {
                String approvalKey = getApprovalKey(clientId, principalName);
                String authToAccessKey = buildKey(LEGACY_UNAME_TO_ACCESS_KEY_PREFIX, approvalKey);
                String clientIdKey = buildKey("oauth2:client_id_to_access:", clientId);
                String usernameKey = buildKey("oauth2:username_to_access:", principalName);

                redisManager.oauthRedisTemplate().expire(authToAccessKey, expiration);
                redisManager.oauthRedisTemplate().expire(clientIdKey, expiration);
                redisManager.oauthRedisTemplate().expire(usernameKey, expiration);
            }

            // 4. 设置新版OAuth2Authorization的过期时间
            String authorizationKey = buildKey(AUTHORIZATION_KEY_PREFIX, authorization.getId());
            redisManager.oauthRedisTemplate().expire(authorizationKey, expiration);

            log.debug("设置令牌过期时间完成: authorizationId={}, expiresSeconds={}",
                    authorization.getId(), expiresSeconds);

        } catch (Exception e) {
            log.error("设置令牌过期时间失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理令牌列表中的相关数据
     * 参考CustomRedisTokenStore的实现，从各种列表中移除过期的令牌
     */
    private void cleanupTokenFromLists(OAuth2Authorization authorization) {
        try {
            if (authorization == null || authorization.getAccessToken() == null) {
                return;
            }

            String accessTokenValue = authorization.getAccessToken().getToken().getTokenValue();
            String principalName = authorization.getPrincipalName();
            String clientId = authorization.getRegisteredClientId();

            // 1. 从用户名限制列表中移除
            if (StringUtils.hasText(principalName) && StringUtils.hasText(clientId)) {
                cleanupTokenFromLists(clientId, principalName, accessTokenValue);
            }

            // 2. 从其他相关列表中移除（如果有的话）
            // 这里可以根据需要添加更多的清理逻辑

            log.debug("清理令牌列表完成: accessToken={}", accessTokenValue);

        } catch (Exception e) {
            log.error("清理令牌列表失败: {}", e.getMessage(), e);
        }
    }

    private void cleanupTokenFromLists(String clientId, String principalName, String accessTokenValue) {
        String approvalKey = getApprovalKey(clientId, principalName);
        String usernameLimitKey = buildKey(USERNAME_LIMIT_LIST, approvalKey);

        // 获取列表中的所有元素
        List<Object> limits = redisManager.oauthRedisTemplate().opsForList().range(usernameLimitKey, 0, -1);
        if (limits != null && !limits.isEmpty()) {
            for (Object limitObj : limits) {
                if (limitObj instanceof UsernameLimit limit) {
                    if (accessTokenValue.equals(limit.getAccessKey())) {
                        // 从列表中移除匹配的元素
                        redisManager.oauthRedisTemplate().opsForList().remove(usernameLimitKey, 1, limitObj);
                        log.debug("从用户名限制列表中移除令牌: {}", accessTokenValue);
                        break;
                    }
                }
            }
        }
    }
}
