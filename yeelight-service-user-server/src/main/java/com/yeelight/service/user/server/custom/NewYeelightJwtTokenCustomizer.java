/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.custom
 * Description: 新版Yeelight JWT Token定制器，替代旧版YeelightTokenEnhancer
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-01-25
 */
package com.yeelight.service.user.server.custom;

import com.yeelight.service.framework.enums.RegionEnum;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.IPUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.server.constant.TokenConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.oidc.endpoint.OidcParameterNames;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.server.authorization.token.JwtEncodingContext;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenCustomizer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.Locale;

/**
 * 新版Yeelight JWT Token定制器
 * 替代旧版YeelightTokenEnhancer，为新版Spring Authorization Server提供JWT令牌增强功能
 * <p>
 * 此类实现了OAuth2TokenCustomizer接口，用于在JWT令牌中添加自定义声明，
 * 包括用户信息、设备信息、客户端信息、时间信息和请求信息等。
 * 
 * <AUTHOR>
 * @description: JWT令牌定制器，与旧版YeelightTokenEnhancer功能完全一致
 */
@Slf4j
@Component("newYeelightJwtTokenCustomizer")
public class NewYeelightJwtTokenCustomizer implements OAuth2TokenCustomizer<JwtEncodingContext> {

    @Override
    public void customize(JwtEncodingContext context) {
        // 只处理JWT访问令牌
        if (context.getTokenType().getValue().equals(YeelightOAuth2AccessToken.ACCESS_TOKEN) || context.getTokenType().getValue().equals(OidcParameterNames.ID_TOKEN)) {
            JwtClaimsSet.Builder claims = context.getClaims();
            
            try {
                // 添加用户信息
                addUserInformation(claims, context);
                
                // 添加客户端信息
                addClientInformation(claims, context);
                
                // 添加设备信息
                addDeviceInformation(claims);
                
                // 添加供应商和区域信息
                addVendorAndRegionInformation(claims);
                
                // 添加请求信息
                addRequestInformation(claims);
                
                // 添加时间信息
                addTimeInformation(claims);
                
                log.debug("JWT令牌增强完成，添加了自定义声明");
                
            } catch (Exception e) {
                log.error("JWT令牌增强失败", e);
                // 不抛出异常，避免影响令牌生成
            }
        }
    }

    /**
     * 添加用户信息到JWT声明中
     */
    private void addUserInformation(JwtClaimsSet.Builder claims, JwtEncodingContext context) {
        Authentication authentication = context.getPrincipal();
        if (authentication != null && authentication.getPrincipal() instanceof YeelightUser user) {

            // 添加用户ID
            if (user.getId() != null) {
                claims.claim(TokenConstants.ADDITIONAL_INFORMATION_ID, user.getId());
            }
            
            // 添加用户名
            if (user.getUsername() != null) {
                claims.claim(TokenConstants.ADDITIONAL_INFORMATION_USERNAME, user.getUsername());
            }
            
            log.debug("添加用户信息到JWT: id={}, username={}", user.getId(), user.getUsername());
        }
    }

    /**
     * 添加客户端信息到JWT声明中
     */
    private void addClientInformation(JwtClaimsSet.Builder claims, JwtEncodingContext context) {
        String clientId = context.getRegisteredClient().getClientId();
        if (clientId != null) {
            claims.claim(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_ID, clientId);
            log.debug("添加客户端信息到JWT: client_id={}", clientId);
        }
    }

    /**
     * 添加设备信息到JWT声明中
     */
    private void addDeviceInformation(JwtClaimsSet.Builder claims) {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                String device = request.getParameter(TokenConstants.ADDITIONAL_INFORMATION_DEVICE);
                if (device != null && !device.trim().isEmpty()) {
                    claims.claim(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, device);
                    log.debug("添加设备信息到JWT: device={}", device);
                }
            }
        } catch (Exception e) {
            log.warn("获取设备信息失败", e);
        }
    }

    /**
     * 添加供应商和区域信息到JWT声明中
     */
    private void addVendorAndRegionInformation(JwtClaimsSet.Builder claims) {
        try {
            // 添加供应商信息
            UserVendorEnum vendor = UserVendorHolder.getVendorEnum();
            if (vendor != null) {
                claims.claim(TokenConstants.ADDITIONAL_INFORMATION_VENDOR, vendor.name());
                log.debug("添加供应商信息到JWT: vendor={}", vendor.name());
                // 添加区域信息
                RegionEnum region = vendor.getRegion();
                if (region != null) {
                    claims.claim(TokenConstants.ADDITIONAL_INFORMATION_REGION, region.name());
                    log.debug("添加区域信息到JWT: region={}", region.name());
                }
            }
        } catch (Exception e) {
            log.warn("获取供应商和区域信息失败", e);
        }
    }

    /**
     * 添加请求信息到JWT声明中
     */
    private void addRequestInformation(JwtClaimsSet.Builder claims) {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request != null) {
                // 添加User-Agent
                String userAgent = request.getHeader("User-Agent");
                if (userAgent != null && !userAgent.trim().isEmpty()) {
                    claims.claim(TokenConstants.ADDITIONAL_INFORMATION_USER_AGENT, userAgent);
                }
                
                // 添加IP地址
                String ip = IPUtils.getRealIp(request);
                if (ip != null && !ip.trim().isEmpty()) {
                    claims.claim(TokenConstants.ADDITIONAL_INFORMATION_IP, ip);
                }
                
                // 添加语言环境
                Locale locale = request.getLocale();
                if (locale != null) {
                    claims.claim(TokenConstants.ADDITIONAL_INFORMATION_LOCALE, locale.toString());
                }
                
                log.debug("添加请求信息到JWT: ip={}, locale={}", ip, locale);
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败", e);
        }
    }

    /**
     * 添加时间信息到JWT声明中
     */
    private void addTimeInformation(JwtClaimsSet.Builder claims) {
        try {
            // 添加创建时间（当前时间戳）
            long createTime = Instant.now().getEpochSecond();
            claims.claim(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME, createTime);
            log.debug("添加时间信息到JWT: create_time={}", createTime);
        } catch (Exception e) {
            log.warn("添加时间信息失败", e);
        }
    }

    /**
     * 获取当前HTTP请求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            log.debug("无法获取当前HTTP请求", e);
            return null;
        }
    }
}
