package com.yeelight.service.user.server.custom;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import lombok.Getter;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 封装短信验证码
 * <AUTHOR>
 */
@Getter
public class SmsAuthenticationDetails extends WebAuthenticationDetails {

    private final String inputCaptcha;
    private final String captchaKey;

    public SmsAuthenticationDetails(HttpServletRequest request) {
        super(request);
        captchaKey = request.getParameter(CaptchaResult.FIELD_CAPTCHA_KEY);
        inputCaptcha = request.getParameter(CaptchaResult.FIELD_CAPTCHA);
    }

}
