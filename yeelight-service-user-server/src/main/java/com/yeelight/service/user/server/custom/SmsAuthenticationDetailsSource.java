package com.yeelight.service.user.server.custom;

import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 自定义获取AuthenticationDetails 用于封装传进来的短信验证码
 * <AUTHOR>
 */
public class SmsAuthenticationDetailsSource implements AuthenticationDetailsSource<HttpServletRequest, WebAuthenticationDetails> {

    /**
     * 构建Web认证细节对象。
     * 该方法覆盖了父类的buildDetails方法，用于创建并返回一个SmsAuthenticationDetails实例，
     * 该实例基于提供的HttpServletRequest对象构建。
     *
     * @param request HttpServletRequest对象，代表当前的HTTP请求。
     * @return SmsAuthenticationDetails实例，包含了Web认证所需的细节信息。
     */
    @Override
    public WebAuthenticationDetails buildDetails(HttpServletRequest request) {
        return new SmsAuthenticationDetails(request);
    }
}
