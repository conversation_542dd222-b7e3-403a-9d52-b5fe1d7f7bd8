package com.yeelight.service.user.server.custom;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.support.UserConverter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;

/**
 * Yeelight用户信息实现类
 * <AUTHOR>
 */
@Component
public class YeelightUserDetailsService implements UserDetailsService {
    @Resource
    private YeelightUserReadService yeelightUserReadService;

    /**
     * 根据用户名加载用户信息。
     *
     * @param username 用户的用户名。
     * @return YeelightUser 用户信息对象，包含用户详细信息及授权信息。
     * @throws UsernameNotFoundException 如果用户不存在，则抛出此异常。
     */
    @Override
    public YeelightUser loadUserByUsername(String username) throws UsernameNotFoundException {
        // 通过用户名查询用户信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByAccount(username);
        // 如果用户不存在，则抛出未找到异常
        if (yeelightUserDto == null) {
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.账号未注册"));
        }

        // 初始化用户权限集合
        Collection<GrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new CustomGrantedAuthority("ROLE_USER"));

        // 将用户DTO信息拷贝到用户实体中，并设置权限信息
        YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
        yeelightUser.setGrantedAuthorities(authorities);
        return yeelightUser;
    }

    /**
     * 通过电话号码加载用户信息。
     *
     * @param phoneNumber 用户的电话号码，作为查找用户的依据。
     * @return YeelightUser 如果找到对应的用户，返回一个包含用户信息的YeelightUser对象。
     * @throws UsernameNotFoundException 如果未找到指定电话号码的用户，抛出此异常。
     */
    public YeelightUser loadUserByPhoneNumber(String phoneNumber) throws UsernameNotFoundException {
        // 通过电话号码查找用户信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
        if (yeelightUserDto == null) {
            // 如果未找到用户，抛出异常
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.手机号未注册"));
        } else {
            // 初始化用户权限
            Collection<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new CustomGrantedAuthority("ROLE_USER"));

            // 拷贝Dto到User对象，设置用户权限

            YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
            yeelightUser.setGrantedAuthorities(authorities);
            return yeelightUser;
        }
    }

    /**
     * 通过邮箱加载用户信息。
     *
     * @param email 用户的邮箱地址，作为查找用户的依据。
     * @return YeelightUser 用户对象，包含从数据库中查找到的用户信息及角色权限。
     * @throws UsernameNotFoundException 如果根据提供的邮箱未找到用户，抛出此异常。
     */
    public YeelightUser loadUserByEmail(String email) throws UsernameNotFoundException {
        // 通过邮箱查找用户信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByEmail(email);
        if (yeelightUserDto == null) {
            // 如果未找到用户，抛出异常
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.邮箱未注册"));
        } else {
            // 初始化用户权限
            Collection<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new CustomGrantedAuthority("ROLE_USER"));

            // 拷贝Dto到User对象，设置权限信息
            YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
            yeelightUser.setGrantedAuthorities(authorities);
            return yeelightUser;
        }
    }
}
