/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.custom
 * Description: 自定义
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-11-08 14:55:14:55
 */
package com.yeelight.service.user.server.custom.endpoint;

import com.yeelight.service.framework.request.utils.OriginalRequestContextHolder;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import lombok.extern.slf4j.Slf4j;
import com.yeelight.service.user.server.constant.Constants;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.*;
import org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint;
import org.springframework.security.web.util.RedirectUrlBuilder;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Desc: 自定义基于登录页面的认证方案入口
 * /oauth/authorize 重定向至登录处理
 * <AUTHOR> [<EMAIL>]
 * @since 2023-11-08 14:55:14:55
 */
@Slf4j
public class YeelightLoginTargetAuthenticationEntryPoint extends LoginUrlAuthenticationEntryPoint {
    // ~ Static fields/initializers
    // =====================================================================================

    private static final Log logger = LogFactory
            .getLog(YeelightLoginTargetAuthenticationEntryPoint.class);

    /**
     * Instance fields
     */
    private PortMapper portMapper = new PortMapperImpl();

    private PortResolver portResolver = new PortResolverImpl();

    private final String loginFormUrl;

    private boolean forceHttps = false;

    private boolean useForward = false;

    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    private final GatewayOauthConfig gatewayOauthConfig;

    /**
     *
     * @param loginFormUrl URL where the login page can be found. Should either be
     * relative to the web-app context path (include a leading {@code /}) or an absolute
     * URL.
     */
    public YeelightLoginTargetAuthenticationEntryPoint(GatewayOauthConfig gatewayOauthConfig, String loginFormUrl) {
        super(loginFormUrl);
        Assert.notNull(loginFormUrl, "loginFormUrl cannot be null");
        this.loginFormUrl = loginFormUrl;
        this.gatewayOauthConfig = gatewayOauthConfig;
    }

    // ~ Methods
    // ========================================================================================================

    /**
     * 初始化方法，验证必要的属性是否已经设置并且合法。
     * 该方法在Bean的属性设置完成后调用，用于验证Bean的状态是否正确，以便在应用中正确使用。
     *
     */
    @Override
    public void afterPropertiesSet() {
        // 验证loginFormUrl是否指定且为有效的重定向URL
        Assert.isTrue(
                StringUtils.hasText(loginFormUrl)
                        && UrlUtils.isValidRedirectUrl(loginFormUrl),
                "loginFormUrl must be specified and must be a valid redirect URL");

        // 如果设置了使用转发且loginFormUrl是绝对URL，抛出异常
        if (useForward && UrlUtils.isAbsoluteUrl(loginFormUrl)) {
            throw new IllegalArgumentException(
                    "useForward must be false if using an absolute loginFormURL");
        }

        // 验证portMapper和portResolver是否已指定
        Assert.notNull(portMapper, "portMapper must be specified");
        Assert.notNull(portResolver, "portResolver must be specified");
    }


    /**
     * 允许子类修改适用于给定请求的登录表单URL。
     *
     * @param request  the request
     * @param response the response
     * @param exception the exception
     * @return the URL (cannot be null or empty; defaults to {@link #getLoginFormUrl()})
     */
    @Override
    protected String determineUrlToUseForThisRequest(HttpServletRequest request,
                                                     HttpServletResponse response, AuthenticationException exception) {
        // 获取基于请求的登录页URL
        String loginForm = gatewayOauthConfig.getLoginPageUrl(request);

        // 判断loginForm是否是绝对路径且不包含ip
        if (UrlUtils.isAbsoluteUrl(loginForm) && !containsIp(loginForm)) {
            return loginForm;
        }

        // 获取原始请求的域名、端口和协议信息
        String originalDomain = OriginalRequestContextHolder.getOriginalDomain();
        int serverPort = OriginalRequestContextHolder.getOriginalPort();
        String scheme = OriginalRequestContextHolder.getOriginalProtocol();

        // 使用RedirectUrlBuilder构建重定向URL
        RedirectUrlBuilder urlBuilder = new RedirectUrlBuilder();

        urlBuilder.setScheme(scheme);
        urlBuilder.setServerName(originalDomain);
        // 设置端口，如果端口为空或小于1，则根据协议默认设置为80或443
        if (serverPort < 1) {
            urlBuilder.setPort(Constants.HTTP_KEY.equals(scheme) ? 80 : 443);
        } else {
            urlBuilder.setPort(serverPort);
        }
        // 设置上下文路径和路径信息
        urlBuilder.setContextPath(request.getContextPath());
        urlBuilder.setPathInfo(loginForm);

        // 记录构建重定向URL的日志信息
        logger.info("buildRedirectUrlToLoginPage: domain: " + originalDomain +", scheme:" + scheme + ", serverPort: " + serverPort + ", contextPath: " + request.getContextPath() + ", pathInfo: " + loginForm);

        return urlBuilder.getUrl();
    }


    /**
     * 检查给定的URL中是否包含有效的非本地IP地址。
     *
     * @param url 需要检查的URL字符串。
     * @return 如果URL中包含有效的非本地IP地址，则返回true；否则返回false。
     */
    public static boolean containsIp(String url) {
        // 定义匹配IP地址的正则表达式模式
        String ipPattern = "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}";
        // 使用正则表达式匹配URL
        Pattern pattern = Pattern.compile(ipPattern);
        Matcher matcher = pattern.matcher(url);
        // 如果找到匹配的IP地址，进一步判断是否为非本地IP；否则返回false
        if (!matcher.find()) {
            return false;
        } else {
            // 检查匹配到的IP是否为非本地IP地址
            return !matcher.group().startsWith("127") && !matcher.group().startsWith("192") && !matcher.group().startsWith("10") && !matcher.group().startsWith("172");
        }
    }

    /**
     * 执行重定向（或转发）到登录表单URL。
     *
     * @param request HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送HTTP响应。
     * @param authException 认证异常，表示在认证过程中发生的异常。
     * @throws IOException 如果发生I/O错误。
     * @throws ServletException 如果发生Servlet相关错误。
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {
        String redirectUrl = null;

        // 决定用于当前请求的登录表单URL
        String loginForm = determineUrlToUseForThisRequest(request, response, authException);

        // 如果是前后端分离且登录表单URL是绝对路径，则重定向到前后端分离的登录页面
        if(gatewayOauthConfig.isFrontendBackendSeparated() && UrlUtils.isAbsoluteUrl(loginForm)){
            OriginalRequestContextHolder.getOriginalDomainByRequest(request);

            // 获取原始请求的URL，用于构建重定向URL
            StringBuilder requestUrl = new StringBuilder(gatewayOauthConfig.getOriginalUrl(request));
            // 如果请求中包含查询参数，则添加到重定向URL中
            if (!ObjectUtils.isEmpty(request.getQueryString())) {
                requestUrl.append("?").append(request.getQueryString());
            }

            // 为重定向地址添加nonce参数，其值为sessionId，绝对路径在重定向前添加target参数
            String targetParameter = URLEncoder.encode(requestUrl.toString(), StandardCharsets.UTF_8);
            redirectUrl = loginForm + "?target=" + targetParameter;
            log.debug("重定向至前后端分离的登录页面：{}", redirectUrl);
        } else {
            if (useForward) {
                // 如果设置为服务器端转发，并且强制HTTPS且当前请求为HTTP，则先重定向到HTTPS
                if (forceHttps && Constants.HTTP_KEY.equals(request.getScheme())) {
                    // First redirect the current request to HTTPS.
                    // When that request is received, the forward to the login page will be
                    // used.
                    redirectUrl = buildHttpsRedirectUrlForRequest(request);
                }
                if (redirectUrl == null) {
                    // 服务器端转发到登录页面
                    if (logger.isDebugEnabled()) {
                        logger.debug("Server side forward to: " + loginForm);
                    }
                    RequestDispatcher dispatcher = request.getRequestDispatcher(loginForm);

                    dispatcher.forward(request, response);
                    return;
                }
            } else {
                // 重定向到登录页面，如果设置了强制HTTPS，则使用HTTPS
                redirectUrl = buildRedirectUrlToLoginPage(request, response, authException);
            }
        }
        // 执行重定向
        redirectStrategy.sendRedirect(request, response, redirectUrl);
    }


    /**
     * 构建重定向到登录页的URL。
     * <p>
     * 根据提供的请求、响应及认证异常信息，构建并返回一个重定向到登录页面的URL。
     * 如果登录页面URL是绝对路径，则直接返回该URL；否则，根据请求信息及配置构建重定向的URL。
     * 如果配置了强制HTTPS，则会尝试将重定向URL的协议更改为HTTPS。
     *
     * @param request       当前的HttpServletRequest对象，用于获取请求信息。
     * @param response      当前的HttpServletResponse对象，可用于设置响应信息，本方法未使用。
     * @param authException 认证异常，用于处理认证错误，本方法未直接使用。
     * @return 返回构建好的重定向URL字符串。
     */
    @Override
    protected String buildRedirectUrlToLoginPage(HttpServletRequest request,
                                                 HttpServletResponse response, AuthenticationException authException) {

        // 确定用于当前请求的登录表单URL
        String loginForm = determineUrlToUseForThisRequest(request, response,
                authException);
        // 判断loginForm是否是绝对路径，若是，则直接返回该URL
        if (UrlUtils.isAbsoluteUrl(loginForm)) {
            return loginForm;
        }

        // 获取原始请求的域、端口和协议信息，用于构建重定向URL
        String originalDomain = OriginalRequestContextHolder.getOriginalDomain();
        int serverPort = OriginalRequestContextHolder.getOriginalPort();
        String scheme = OriginalRequestContextHolder.getOriginalProtocol();

        // 使用RedirectUrlBuilder构建重定向URL
        RedirectUrlBuilder urlBuilder = new RedirectUrlBuilder();
        // 设置协议
        urlBuilder.setScheme(scheme);
        // 设置域名
        urlBuilder.setServerName(OriginalRequestContextHolder.getOriginalDomain());
        // 设置端口，若端口无效则根据协议默认设置为80或443
        if (serverPort < 1) {
            urlBuilder.setPort(Constants.HTTP_KEY.equals(scheme) ? 80 : 443);
        } else {
            urlBuilder.setPort(serverPort);
        }
        // 设置上下文路径和路径信息
        urlBuilder.setContextPath(request.getContextPath());
        urlBuilder.setPathInfo(loginForm);
        // 记录构建的URL信息
        logger.info("buildRedirectUrlToLoginPage: domain: " + originalDomain +", scheme:" + scheme + ", serverPort: " + serverPort + ", contextPath: " + request.getContextPath() + ", pathInfo: " + loginForm);

        // 如果配置了强制HTTPS且当前协议为HTTP，则尝试重定向到HTTPS
        if (forceHttps && Constants.HTTP_KEY.equals(scheme)) {
            Integer httpsPort = portMapper.lookupHttpsPort(serverPort);

            if (httpsPort != null) {
                // 如果找到了HTTPS端口映射，则更新协议和端口
                urlBuilder.setScheme(Constants.HTTPS_KEY);
                urlBuilder.setPort(httpsPort);
            }
            else {
                // 如果没有找到HTTPS端口映射，记录警告日志
                logger.warn("Unable to redirect to HTTPS as no port mapping found for HTTP port "
                        + serverPort);
            }
        }

        // 返回构建完成的重定向URL
        return urlBuilder.getUrl();
    }


    /**
     * 构建一个重定向到HTTPS的URL。此方法用于在转发到登录页面之前，将当前请求重定向到HTTPS。
     *
     * @param request HttpServletRequest对象，代表当前的HTTP请求。
     * @return 返回一个重定向到HTTPS的URL字符串。如果无法找到HTTP端口的HTTPS映射，则返回null。
     */
    @Override
    protected String buildHttpsRedirectUrlForRequest(HttpServletRequest request) {

        // 获取当前服务器的端口号
        int serverPort = portResolver.getServerPort(request);
        // 查找与HTTP端口对应的HTTPS端口号
        Integer httpsPort = portMapper.lookupHttpsPort(serverPort);

        if (httpsPort != null) {
            // 使用RedirectUrlBuilder构建HTTPS重定向URL
            RedirectUrlBuilder urlBuilder = new RedirectUrlBuilder();
            urlBuilder.setScheme("https");
            urlBuilder.setServerName(request.getServerName());
            urlBuilder.setPort(httpsPort);
            urlBuilder.setContextPath(request.getContextPath());
            urlBuilder.setServletPath(request.getServletPath());
            urlBuilder.setPathInfo(request.getPathInfo());
            urlBuilder.setQuery(request.getQueryString());

            return urlBuilder.getUrl();
        }

        // 当找不到HTTP端口的HTTPS映射时，记录警告信息并返回null
        logger.warn("Unable to redirect to HTTPS as no port mapping found for HTTP port "
                + serverPort);

        return null;
    }


    /**
     * Set to true to force login form access to be via https. If this value is true (the
     * default is false), and the incoming request for the protected resource which
     * triggered the interceptor was not already <code>https</code>, then the client will
     * first be redirected to an https URL, even if <tt>serverSideRedirect</tt> is set to
     * <tt>true</tt>.
     */
    @Override
    public void setForceHttps(boolean forceHttps) {
        this.forceHttps = forceHttps;
    }

    @Override
    protected boolean isForceHttps() {
        return forceHttps;
    }

    @Override
    public String getLoginFormUrl() {
        return loginFormUrl;
    }

    @Override
    public void setPortMapper(PortMapper portMapper) {
        Assert.notNull(portMapper, "portMapper cannot be null");
        this.portMapper = portMapper;
    }

    @Override
    protected PortMapper getPortMapper() {
        return portMapper;
    }

    @Override
    public void setPortResolver(PortResolver portResolver) {
        Assert.notNull(portResolver, "portResolver cannot be null");
        this.portResolver = portResolver;
    }

    @Override
    protected PortResolver getPortResolver() {
        return portResolver;
    }

    /**
     * Tells if we are to do a forward to the {@code loginFormUrl} using the
     * {@code RequestDispatcher}, instead of a 302 redirect.
     *
     * @param useForward true if a forward to the login page should be used. Must be false
     * (the default) if {@code loginFormUrl} is set to an absolute value.
     */
    @Override
    public void setUseForward(boolean useForward) {
        this.useForward = useForward;
    }

    @Override
    protected boolean isUseForward() {
        return useForward;
    }
}
