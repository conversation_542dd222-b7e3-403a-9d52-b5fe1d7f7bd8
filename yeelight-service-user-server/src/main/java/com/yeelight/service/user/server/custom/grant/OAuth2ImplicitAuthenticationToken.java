/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.custom.grant
 * Description: OAuth2 Implicit Grant Authentication Token for Spring Authorization Server
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-01-25
 */
package com.yeelight.service.user.server.custom.grant;

import org.springframework.lang.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationGrantAuthenticationToken;
import org.springframework.util.Assert;

import java.util.*;

/**
 * OAuth2 Implicit Grant Authentication Token
 * 用于新版Spring Authorization Server的implicit模式认证令牌
 * <p>
 * 此类表示OAuth2 Implicit Grant的认证令牌，
 * 包含用户认证信息和请求的权限范围。
 * 
 * <AUTHOR>
 * @description: 隐式模式认证令牌，与旧版ImplicitTokenGranter兼容
 */
public class OAuth2ImplicitAuthenticationToken extends OAuth2AuthorizationGrantAuthenticationToken {

    private final Authentication userAuthentication;
    private final Set<String> scopes;

    /**
     * 构造函数
     *
     * @param userAuthentication 用户认证信息
     * @param clientPrincipal 客户端主体认证信息
     * @param scopes 请求的权限范围
     * @param additionalParameters 额外参数
     */
    public OAuth2ImplicitAuthenticationToken(Authentication userAuthentication,
                                           Authentication clientPrincipal,
                                           @Nullable Set<String> scopes,
                                           @Nullable Map<String, Object> additionalParameters) {
        super(new AuthorizationGrantType("implicit"), clientPrincipal, additionalParameters);
        Assert.notNull(userAuthentication, "userAuthentication cannot be null");
        this.userAuthentication = userAuthentication;
        this.scopes = Collections.unmodifiableSet(scopes != null ? new HashSet<>(scopes) : Collections.emptySet());
    }

    /**
     * 获取用户认证信息
     *
     * @return 用户认证信息
     */
    public Authentication getUserAuthentication() {
        return this.userAuthentication;
    }

    /**
     * 获取请求的权限范围
     *
     * @return 权限范围集合
     */
    public Set<String> getScopes() {
        return this.scopes;
    }
}
