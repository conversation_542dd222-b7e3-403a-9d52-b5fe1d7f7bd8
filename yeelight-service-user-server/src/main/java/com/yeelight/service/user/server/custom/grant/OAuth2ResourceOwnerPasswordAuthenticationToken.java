/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.custom.grant
 * Description: OAuth2 Resource Owner Password Grant Authentication Token for Spring Authorization Server
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-01-25
 */
package com.yeelight.service.user.server.custom.grant;

import org.springframework.lang.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.server.authorization.authentication.OAuth2AuthorizationGrantAuthenticationToken;
import org.springframework.util.Assert;

import java.util.*;

/**
 * OAuth2 Resource Owner Password Grant Authentication Token
 * 用于新版Spring Authorization Server的password模式认证令牌
 * <p>
 * 此类表示OAuth2 Resource Owner Password Credentials Grant的认证令牌，
 * 包含用户名、密码和请求的权限范围。
 * 
 * <AUTHOR>
 * @description: 资源所有者密码模式认证令牌，与旧版ResourceOwnerPasswordTokenGranter兼容
 */
public class OAuth2ResourceOwnerPasswordAuthenticationToken extends OAuth2AuthorizationGrantAuthenticationToken {

    private final String username;
    private final String password;
    private final Set<String> scopes;

    /**
     * 构造函数
     *
     * @param username 用户名
     * @param password 密码
     * @param clientPrincipal 客户端主体认证信息
     * @param scopes 请求的权限范围
     * @param additionalParameters 额外参数
     */
    public OAuth2ResourceOwnerPasswordAuthenticationToken(String username,
                                                          String password,
                                                          Authentication clientPrincipal,
                                                          @Nullable Set<String> scopes,
                                                          @Nullable Map<String, Object> additionalParameters) {
        super(new AuthorizationGrantType("password"), clientPrincipal, additionalParameters);
        Assert.hasText(username, "username cannot be empty");
        Assert.hasText(password, "password cannot be empty");
        this.username = username;
        this.password = password;
        this.scopes = Collections.unmodifiableSet(scopes != null ? new HashSet<>(scopes) : Collections.emptySet());
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public String getUsername() {
        return this.username;
    }

    /**
     * 获取密码
     *
     * @return 密码
     */
    public String getPassword() {
        return this.password;
    }

    /**
     * 获取请求的权限范围
     *
     * @return 权限范围集合
     */
    public Set<String> getScopes() {
        return this.scopes;
    }
}
