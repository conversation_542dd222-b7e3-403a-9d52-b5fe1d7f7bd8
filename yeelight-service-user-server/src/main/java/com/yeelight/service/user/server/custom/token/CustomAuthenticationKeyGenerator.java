package com.yeelight.service.user.server.custom.token;

import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeSet;
import java.util.UUID;

/**
 * @program: yeelight-oauth-api
 * @description:
 * @author: Sheldon
 * @create: 2019-07-11 19:57
 **/
public class CustomAuthenticationKeyGenerator {
    /**
     * 提取OAuth2认证中的关键信息作为键。
     *
     * @param authentication OAuth2认证对象，包含客户端请求的授权信息。
     * @return 生成的键，由认证中的关键信息组成。
     */
    public String extractKey(OAuth2Authorization authentication) {
        // 初始化存储关键信息的Map，并按顺序插入信息
        Map<String, String> values = new LinkedHashMap<>();

        // 如果认证不只包含客户端信息，则添加用户名
        if (authentication.getPrincipalName() != null) {
            values.put(SecurityConstants.USERNAME_KEY, authentication.getPrincipalName());
        }

        // 添加客户端ID
        values.put(OAuth2Utils.CLIENT_ID, authentication.getRegisteredClientId());

        // 如果有作用域信息，格式化并添加到values中
        if (authentication.getAuthorizedScopes() != null) {
            values.put(OAuth2Utils.SCOPE, OAuth2Utils.formatParameterList(new TreeSet<>(authentication.getAuthorizedScopes())));
        }

        // 尝试从请求参数或认证详情中获取设备额外信息，若无则生成一个唯一标识
        if (authentication.getAttributes().get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE) != null) {
            values.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, (String) authentication.getAttributes().get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE));
        } else {
            values.put(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, UUID.randomUUID().toString());
        }
        // 生成并返回键
        return generateKey(values);
    }

    /**
     * 生成给定值的MD5加密键。
     * @param values 包含需要参与加密生成键的字符串键值对。
     * @return 返回一个32位的MD5加密字符串。
     * @throws IllegalStateException 如果MD5算法不可用（理论上不应发生，因为MD5包含在JDK中）。
     */
    private String generateKey(Map<String, String> values) {
        try {
            // 获取MD5消息摘要实例
            MessageDigest digest = MessageDigest.getInstance("MD5");
            // 将值的字符串表示进行MD5加密
            byte[] bytes = digest.digest(values.toString().getBytes(StandardCharsets.UTF_8));
            // 将加密后的字节数据格式化为32位十六进制字符串
            return String.format("%032x", new BigInteger(1, bytes));
        } catch (NoSuchAlgorithmException e) {
            // 如果无法获取MD5算法实例，则抛出异常
            throw new IllegalStateException("MD5 algorithm not available.  Fatal (should be in the JDK).", e);
        }
    }
}
