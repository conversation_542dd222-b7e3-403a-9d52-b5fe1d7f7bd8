package com.yeelight.service.user.server.custom.token;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2RefreshToken;
import com.yeelight.service.user.server.constant.TokenConstants;

import java.lang.reflect.Type;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

/**
 * @program: yeelight-service-user
 * @description:
 * @author: Sheldon
 * @create: 2020-03-03 16:47
 **/
public class DefaultOauth2AccessTokenSerializer implements ObjectDeserializer {
    /**
     * 反序列化方法，用于将JSON格式的数据解析为OAuth2的访问令牌对象。
     *
     * @param parser JSON解析器，用于解析JSON数据。
     * @param type 类型标记，指示要反序列化的对象类型。此处应为{@link YeelightOAuth2AccessToken}类。
     * @param fieldName 字段名，指示当前解析的字段名称。在本场景中未直接使用，但可能用于更复杂的解析逻辑中。
     * @return 反序列化后的对象。如果类型匹配，则返回一个{@link YeelightOAuth2AccessToken}实例；否则返回null。
     * @param <T> 泛型参数，指示返回对象的类型。
     */
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        // 检查要反序列化的类型是否为YeelightOAuth2AccessToken
        if (type == YeelightOAuth2AccessToken.class) {
            // 解析JSON对象
            JSONObject jsonObject = parser.parseObject();
            // 从JSON对象中提取tokenId，并创建YeelightOAuth2AccessToken实例
            String tokenId = jsonObject.getString("value");
            YeelightOAuth2AccessToken accessToken = YeelightOAuth2AccessToken.builder()
                    .value(tokenId)
                    .build();

            // 设置刷新令牌、令牌类型、额外信息和作用域
            YeelightOAuth2RefreshToken refreshToken = jsonObject.getObject(StrUtil.toCamelCase(YeelightOAuth2AccessToken.REFRESH_TOKEN), YeelightOAuth2RefreshToken.class);
            accessToken.setRefreshToken(refreshToken);
            accessToken.setTokenType(jsonObject.getString(StrUtil.toCamelCase(YeelightOAuth2AccessToken.TOKEN_TYPE)));

            // 处理额外信息，将"id"字段转换为Long类型
            Map<String, Object> additionalInformation = jsonObject.getObject("additionalInformation", new TypeReference<LinkedHashMap<String, Object>>() {});
            if (additionalInformation != null && additionalInformation.containsKey(TokenConstants.ADDITIONAL_INFORMATION_ID)) {
                additionalInformation.replace(TokenConstants.ADDITIONAL_INFORMATION_ID, Long.valueOf(String.valueOf(additionalInformation.get(TokenConstants.ADDITIONAL_INFORMATION_ID))));
            }
            accessToken.setAdditionalInformation(additionalInformation);

            // 设置作用域
            Set<String> scope = jsonObject.getObject(OAuth2Utils.SCOPE, new TypeReference<HashSet<String>>() {});
            accessToken.setScope(scope);

            // 设置过期时间
            accessToken.setExpiration(jsonObject.getDate("expiration"));

            // 返回解析后的访问令牌对象
            return (T) accessToken;
        }
        // 如果类型不匹配，则返回null
        return null;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}
