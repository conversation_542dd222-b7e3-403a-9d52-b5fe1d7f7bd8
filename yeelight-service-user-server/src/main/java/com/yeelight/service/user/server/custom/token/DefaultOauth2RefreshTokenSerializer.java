package com.yeelight.service.user.server.custom.token;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2RefreshToken;


import java.lang.reflect.Type;

/**
 * @program: yeelight-service-user
 * @description:
 * @author: Sheldon
 * @create: 2020-03-03 16:47
 **/
public class DefaultOauth2RefreshTokenSerializer implements ObjectDeserializer {
    /**
     * 反序列化方法，用于将JSON格式的数据解析为OAuth2刷新令牌对象。
     *
     * @param parser JSON解析器，用于解析JSON数据。
     * @param type 类型标记，指示要反序列化的对象类型。
     * @param fieldName 字段名，当前未使用，传入参数即可。
     * @return 反序列化后的对象。如果类型匹配YeelightOAuth2RefreshToken，则返回解析后的刷新令牌对象；否则返回null。
     */
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        // 检查要反序列化的类型是否为YeelightOAuth2RefreshToken
        if (type == YeelightOAuth2RefreshToken.class) {
            // 解析JSON对象
            JSONObject jsonObject = parser.parseObject();
            // 从JSON对象中获取令牌值
            String tokenId = jsonObject.getString("value");
            // 创建并返回YeelightOAuth2RefreshToken对象
            YeelightOAuth2RefreshToken refreshToken = YeelightOAuth2RefreshToken.builder()
                    .value(tokenId)
                    .build();
            return (T) refreshToken;
        }
        // 如果类型不匹配，则返回null
        return null;
    }

    @Override
    public int getFastMatchToken() {
        return 0;
    }
}
