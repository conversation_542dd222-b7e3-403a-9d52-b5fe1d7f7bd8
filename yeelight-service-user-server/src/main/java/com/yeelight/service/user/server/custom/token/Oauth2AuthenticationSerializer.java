package com.yeelight.service.user.server.custom.token;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;

import java.io.Serializable;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * @program: yeelight-service-user
 * @description: Oauth2AuthenticationSerializer
 * @author: Sheldon
 * @create: 2020-03-03 16:27
 **/
@Slf4j
public class Oauth2AuthenticationSerializer implements ObjectDeserializer {
    /**
     * 反序列化方法，用于将JSON格式的数据转换为YeelightOAuth2Authentication对象。
     *
     * @param parser JSON解析器，用于解析JSON数据。
     * @param type 目标类型，指定需要反序列化的对象类型。
     * @param fieldName 字段名，指示当前解析的字段名称，本方法中未使用该参数。
     * @return 反序列化后的YeelightOAuth2Authentication对象。如果解析失败或类型不匹配，则返回null。
     */
    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        // 检查目标类型是否为YeelightOAuth2Authentication
        if (type == YeelightOAuth2Authentication.class) {
            try {
                // 解析JSON数据
                Object o = parse(parser);
                // 如果解析结果为null或已直接是YeelightOAuth2Authentication对象，则直接返回
                if (o == null) {
                    return null;
                } else if (o instanceof YeelightOAuth2Authentication) {
                    return (T) o;
                }

                // 对解析出的JSON对象进行进一步处理，构造YeelightOAuth2Authentication对象
                JSONObject jsonObject = (JSONObject) o;
                YeelightOAuth2Request request = parseOauth2Request(jsonObject);
                AbstractAuthenticationToken authentication = jsonObject
                        .getObject("userAuthentication", AbstractAuthenticationToken.class);
                return (T) new YeelightOAuth2Authentication(request, authentication);
            } catch (Exception e) {
                // 记录解析过程中的异常
                log.error("OAuth2AuthenticationSerializer parse error: {}", ExceptionUtils.getStackTrace(e));
            }
            return null;
        }
        // 如果目标类型不是OAuth2Authentication，则直接返回null
        return null;
    }

    /**
     * 解析 YeelightOAuth2Request 请求信息。
     *
     * @param jsonObject 包含 OAuth2 请求信息的 JSON 对象。
     * @return 解析后的 YeelightOAuth2Request 对象。
     */
    private YeelightOAuth2Request parseOauth2Request(JSONObject jsonObject) {
        // 从 JSON 对象中解析 OAuth2 请求的详细信息
        JSONObject json = jsonObject.getObject("oAuth2Request", JSONObject.class);
        Map<String, String> requestParameters = json.getObject("requestParameters", Map.class);
        String clientId = json.getString(StrUtil.toCamelCase(OAuth2Utils.CLIENT_ID));
        String grantType = json.getString(StrUtil.toCamelCase(OAuth2Utils.GRANT_TYPE));
        String redirectUri = json.getString(StrUtil.toCamelCase(OAuth2Utils.REDIRECT_URI));
        Boolean approved = json.getBoolean("approved");
        Set<String> responseTypes = json.getObject("responseTypes", new TypeReference<HashSet<String>>() {});
        Set<String> scope = json.getObject(OAuth2Utils.SCOPE, new TypeReference<HashSet<String>>() {});
        Set<String> authorities = json.getObject("authorities", new TypeReference<HashSet<String>>() {});
        Set<String> resourceIds = json.getObject("resourceIds", new TypeReference<HashSet<String>>() {});
        Map<String, Serializable> extensions = json.getObject("extensions", new TypeReference<HashMap<String, Serializable>>() {});

        // 创建 YeelightOAuth2Request 对象并设置相关属性
        return YeelightOAuth2Request.builder()
                .requestParameters(requestParameters)
                .clientId(clientId)
                .authorities(authorities)
                .approved(approved)
                .scope(scope)
                .resourceIds(resourceIds)
                .redirectUri(redirectUri)
                .responseTypes(responseTypes)
                .extensions(extensions)
                .grantType(grantType)
                .build();
    }



    @Override
    public int getFastMatchToken() {
        return 0;
    }

    /**
     * 解析给定的 DefaultJSONParser 对象。
     *
     * @param parse DefaultJSONParser 对象，用于解析 JSON。
     * @return 返回解析后的对象。如果解析结果是 JSONObject 或 OAuth2Authentication 类型，则直接返回；
     *         如果是其他类型，尝试将其转换为 JSONObject。
     */
    private Object parse(DefaultJSONParser parse) {
        // 获取输入的字符串
        String json = parse.getInput();
        if (StrUtil.isBlank(json)) {
            return null;
        }

        // 1. 先尝试反序列化为 OAuth2Authentication
        YeelightOAuth2Authentication oauth = JSON.parseObject(json, YeelightOAuth2Authentication.class);
        if (oauth != null) {
            return oauth;
        }

        // 2. 否则返回有序 JSONObject
        return JSON.parseObject(json, JSONObject.class, Feature.OrderedField);
    }
}
