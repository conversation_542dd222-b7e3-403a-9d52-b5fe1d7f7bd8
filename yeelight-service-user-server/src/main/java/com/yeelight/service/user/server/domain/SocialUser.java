package com.yeelight.service.user.server.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * 第三方用户信息表
 * <AUTHOR>
 */
@Table(name = "`social_user`")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SocialUser implements Serializable {
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    private Long id;

    /**
     * 第三方系统的唯一ID
     */
    @Column(name = "`uuid`")
    private String uuid;

    /**
     * 第三方系统的唯一ID GITHUB、GITEE、QQ
     */
    @Column(name = "`source`")
    private String source;

    /**
     * 用户的授权令牌
     */
    @Column(name = "`access_token`")
    private String accessToken;

    /**
     * 第三方用户的授权令牌的有效期
     */
    @Column(name = "`expire_in`")
    private Integer expireIn;

    /**
     * 刷新令牌
     */
    @Column(name = "`refresh_token`")
    private String refreshToken;

    /**
     * 第三方用户的 open id
     */
    @Column(name = "`open_id`")
    private String openId;

    /**
     * 第三方用户的 ID
     */
    @Column(name = "`uid`")
    private String uid;

    /**
     * 个别平台的授权信息
     */
    @Column(name = "`access_code`")
    private String accessCode;

    /**
     * 第三方用户的 union id
     */
    @Column(name = "`union_id`")
    private String unionId;

    /**
     * 第三方用户授予的权限
     */
    @Column(name = "`scope`")
    private String scope;

    /**
     * 个别平台的授权信息
     */
    @Column(name = "`token_type`")
    private String tokenType;

    /**
     * id token
     */
    @Column(name = "`id_token`")
    private String idToken;

    /**
     * 小米平台用户的附带属性
     */
    @Column(name = "`mac_algorithm`")
    private String macAlgorithm;

    /**
     * 小米平台用户的附带属性
     */
    @Column(name = "`mac_key`")
    private String macKey;

    /**
     * 用户的授权code
     */
    @Column(name = "`code`")
    private String code;

    /**
     * Twitter平台用户的附带属性
     */
    @Column(name = "`oauth_token`")
    private String oauthToken;

    /**
     * Twitter平台用户的附带属性
     */
    @Column(name = "`oauth_token_secret`")
    private String oauthTokenSecret;

    /**
     * 微信会话秘钥
     */
    @Column(name = "`session_key`")
    private String sessionKey;

    /**
     * 添加时间
     */
    @Column(name = "created_time")
    private Integer createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private Integer updatedTime;

    /**
     * 最新token存储时间时间
     */
    @Column(name = "last_token_time")
    private Integer lastTokenTime;

    private static final long serialVersionUID = 1L;
}
