package com.yeelight.service.user.server.domain;

import lombok.Builder;
import lombok.Data;

import jakarta.persistence.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * social_user_auth
 *
 * <AUTHOR>
 */
@Table(name = "`social_user_auth`")
@Data
@Builder
public class SocialUserAuth implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    private Long id;

    /**
     * 系统用户ID
     */
    @Column(name = "`user_id`")
    private Long userId;

    /**
     * 社会化用户ID	数据库主键（非第三方用户的ID）
     */
    @Column(name = "`social_user_id`")
    private Long socialUserId;
}
