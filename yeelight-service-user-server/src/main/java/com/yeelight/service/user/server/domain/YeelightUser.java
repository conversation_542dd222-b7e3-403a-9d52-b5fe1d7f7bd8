package com.yeelight.service.user.server.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: yeelight-service-user
 * @description:
 * @author: Sheldon
 * @create: 2019-06-20 17:15
 **/
@Data
@Table(name = "yeelight_users")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YeelightUser implements Serializable {
    private static final int PHONE_NUMBER_FILLED_SCORE = 50;
    private static final int EMAIL_FILLED_SCORE = 50;
    private static final int USERNAME_IS_PHONE_NUMBER_SCORE = 0;
    private static final int USERNAME_IS_EMAIL_SCORE = 0;

    /**
     * 用户ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 登录账号
     */
    @Size(max = 50,message = "{User.Validate.username.checkSize}")
    private String username;

    /**
     * 名称
     */
    @Size(max = 50,message = "{User.Validate.name.checkSize}")
    private String name;

    /**
     * 密码
     */
    private String password;

    /**
     * 密码盐
     */
    @JsonIgnore
    private String salt;

    /**
     * 电话号码
     */
    @Column(name = "phone_number")
    private String phoneNumber;

    /**
     * 电子邮箱
     */
    @Email(message = "{User.Validate.email.checkEmail}")
    private String email;

    /**
     * 状态:  1-可用，0-禁用，-1-锁定
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonIgnore
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonIgnore
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 删除状态: 1-已删除,0-未删除
     */
    @JsonIgnore
    private String deleted;

    private String avatar;

    /**
     * 用户地域
     */
    private String region;
}
