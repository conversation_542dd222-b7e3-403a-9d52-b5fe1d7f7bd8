package com.yeelight.service.user.server.engine;

import com.yeelight.service.user.client.domain.UserEarnings;
import com.yeelight.service.user.client.domain.YeelightUserAccount;
import com.yeelight.service.user.client.domain.YeelightUserAccountExample;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.enums.UserStatus;
import com.yeelight.service.user.client.service.YeelightUserAccountService;
import com.yeelight.service.user.server.mapper.user.UserEarningsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 用户收入规则引擎
 * 完全替代Drools的UpdateUserEarning.drl规则文件
 * 实现与原Drools规则完全相同的业务逻辑
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@Component
public class UserEarningsRuleEngine {

    @Resource
    private UserEarningsMapper userEarningsMapper;

    @Resource
    private YeelightUserAccountService yeelightUserAccountService;

    /**
     * 执行用户收入更新规则
     * 完全替代原Drools规则组"UpdateUserEarning"的所有规则
     * <p>
     * 原Drools规则优先级（salience）：
     * - UpdateUserActualEarningWhenNotExistAccount: 40 (最高优先级)
     * - UpdateUserActualEarningWhenExistAccount: 30
     * - UpdateUserEstimateEarningWhenNotExistAccount: 20
     * - UpdateUserEstimateEarningWhenExistAccount: 10 (最低优先级)
     * 
     * @param yeelightUser 用户信息
     * @param yeelightUserAccount 用户账户信息（可能为null或空对象）
     * @param userEarnings 用户收入信息
     */
    public void executeUpdateUserEarningRules(YeelightUserDto yeelightUser, 
                                            YeelightUserAccount yeelightUserAccount, 
                                            UserEarnings userEarnings) {
        
        log.debug("开始执行用户收入更新规则 - 用户ID: {}, 收入类型: {}, 收入金额: {}", 
                yeelightUser.getId(), userEarnings.getIsCredit(), userEarnings.getEarningMoney());

        // 检查用户状态 - 所有规则的前置条件
        if (!UserStatus.ENABLED.getCode().equals(yeelightUser.getStatus())) {
            log.warn("用户状态不是启用状态，跳过收入更新 - 用户ID: {}, 状态: {}", 
                    yeelightUser.getId(), yeelightUser.getStatus());
            return;
        }

        // 判断账户是否存在
        boolean accountExists = isAccountExists(yeelightUserAccount, yeelightUser.getId());
        
        // 根据收入类型和账户存在情况执行相应规则
        // 注意：这里必须严格按照Drools规则的优先级顺序执行
        // 优先级：40 > 30 > 20 > 10

        if (userEarnings.getIsCredit() == 1) {
            // 实际收入（已到账）
            if (!accountExists) {
                // 优先级40：账户不存在时创建账户
                executeUpdateUserActualEarningWhenNotExistAccount(yeelightUser, userEarnings);
            } else {
                // 优先级30：账户存在时更新
                executeUpdateUserActualEarningWhenExistAccount(yeelightUser, yeelightUserAccount, userEarnings);
            }
        } else if (userEarnings.getIsCredit() == 0) {
            // 预估收入（未到账）
            if (!accountExists) {
                // 优先级20：账户不存在时创建账户
                executeUpdateUserEstimateEarningWhenNotExistAccount(yeelightUser, userEarnings);
            } else {
                // 优先级10：账户存在时更新
                executeUpdateUserEstimateEarningWhenExistAccount(yeelightUser, yeelightUserAccount, userEarnings);
            }
        } else {
            log.warn("未知的收入类型 - 用户ID: {}, 收入类型: {}", yeelightUser.getId(), userEarnings.getIsCredit());
        }

        log.debug("用户收入更新规则执行完成 - 用户ID: {}", yeelightUser.getId());
    }

    /**
     * 规则: UpdateUserActualEarningWhenExistAccount (salience 30)
     * 当用户账户存在且收入为实际收入时更新
     */
    private void executeUpdateUserActualEarningWhenExistAccount(YeelightUserDto yeelightUser, 
                                                              YeelightUserAccount yeelightUserAccount, 
                                                              UserEarnings userEarnings) {
        
        log.debug("执行规则: UpdateUserActualEarningWhenExistAccount - 用户ID: {}", yeelightUser.getId());

        // 检查规则条件
        if (yeelightUserAccount.getYeelightUserId().equals(yeelightUser.getId()) && 
            userEarnings.getEarningMoney() != null) {
            
            // 更新实际可提现金额
            if (yeelightUserAccount.getActualWithdrawableAmount() == null) {
                yeelightUserAccount.setActualWithdrawableAmount(userEarnings.getEarningMoney());
            } else {
                yeelightUserAccount.setActualWithdrawableAmount(
                    userEarnings.getEarningMoney().add(yeelightUserAccount.getActualWithdrawableAmount()));
            }

            // 插入收入记录
            userEarningsMapper.insertSelective(userEarnings);
            
            // 更新用户账户
            yeelightUserAccountService.updateByExampleSelective(
                yeelightUserAccount, 
                YeelightUserAccountExample.builder().yeelightUserId(userEarnings.getYeelightUserId()).build());

            log.info("实际收入更新完成 - 用户ID: {}, 新增金额: {}, 总实际可提现金额: {}", 
                    yeelightUser.getId(), userEarnings.getEarningMoney(), 
                    yeelightUserAccount.getActualWithdrawableAmount());
        }
    }

    /**
     * 规则: UpdateUserActualEarningWhenNotExistAccount (salience 40)
     * 当用户账户不存在且收入为实际收入时创建账户并更新
     */
    private void executeUpdateUserActualEarningWhenNotExistAccount(YeelightUserDto yeelightUser, 
                                                                 UserEarnings userEarnings) {
        
        log.debug("执行规则: UpdateUserActualEarningWhenNotExistAccount - 用户ID: {}", yeelightUser.getId());

        // 创建新的用户账户
        YeelightUserAccount newAccount = new YeelightUserAccount();
        newAccount.setActualWithdrawableAmount(userEarnings.getEarningMoney());
        newAccount.setYeelightUserId(yeelightUser.getId());

        // 插入收入记录
        userEarningsMapper.insertSelective(userEarnings);
        
        // 插入新账户
        yeelightUserAccountService.insertSelective(newAccount);

        log.info("创建新账户并更新实际收入 - 用户ID: {}, 实际可提现金额: {}", 
                yeelightUser.getId(), userEarnings.getEarningMoney());
    }

    /**
     * 规则: UpdateUserEstimateEarningWhenExistAccount (salience 10)
     * 当用户账户存在且收入为预估收入时更新
     */
    private void executeUpdateUserEstimateEarningWhenExistAccount(YeelightUserDto yeelightUser, 
                                                                YeelightUserAccount yeelightUserAccount, 
                                                                UserEarnings userEarnings) {
        
        log.debug("执行规则: UpdateUserEstimateEarningWhenExistAccount - 用户ID: {}", yeelightUser.getId());

        // 检查规则条件
        if (yeelightUserAccount.getYeelightUserId().equals(yeelightUser.getId()) && 
            yeelightUserAccount.getEstimateWithdrawableAmount() != null) {
            
            // 更新预估可提现金额
            yeelightUserAccount.setEstimateWithdrawableAmount(
                yeelightUserAccount.getEstimateWithdrawableAmount().add(userEarnings.getEarningMoney()));

            // 插入收入记录
            userEarningsMapper.insertSelective(userEarnings);
            
            // 更新用户账户
            yeelightUserAccountService.updateByExampleSelective(
                yeelightUserAccount, 
                YeelightUserAccountExample.builder().yeelightUserId(userEarnings.getYeelightUserId()).build());

            log.info("预估收入更新完成 - 用户ID: {}, 新增金额: {}, 总预估可提现金额: {}", 
                    yeelightUser.getId(), userEarnings.getEarningMoney(), 
                    yeelightUserAccount.getEstimateWithdrawableAmount());
        }
    }

    /**
     * 规则: UpdateUserEstimateEarningWhenNotExistAccount (salience 20)
     * 当用户账户不存在且收入为预估收入时创建账户并更新
     */
    private void executeUpdateUserEstimateEarningWhenNotExistAccount(YeelightUserDto yeelightUser, 
                                                                   UserEarnings userEarnings) {
        
        log.debug("执行规则: UpdateUserEstimateEarningWhenNotExistAccount - 用户ID: {}", yeelightUser.getId());

        // 创建新的用户账户
        YeelightUserAccount newAccount = new YeelightUserAccount();
        newAccount.setEstimateWithdrawableAmount(userEarnings.getEarningMoney());
        newAccount.setYeelightUserId(yeelightUser.getId());

        // 插入收入记录
        userEarningsMapper.insertSelective(userEarnings);
        
        // 插入新账户
        yeelightUserAccountService.insertSelective(newAccount);

        log.info("创建新账户并更新预估收入 - 用户ID: {}, 预估可提现金额: {}", 
                yeelightUser.getId(), userEarnings.getEarningMoney());
    }

    /**
     * 判断用户账户是否存在
     * 对应Drools规则中的条件判断逻辑
     */
    private boolean isAccountExists(YeelightUserAccount yeelightUserAccount, Long yeelightUserId) {
        return yeelightUserAccount != null && 
               yeelightUserAccount.getId() != null && 
               yeelightUserAccount.getYeelightUserId() != null &&
               yeelightUserAccount.getYeelightUserId().equals(yeelightUserId);
    }
}
