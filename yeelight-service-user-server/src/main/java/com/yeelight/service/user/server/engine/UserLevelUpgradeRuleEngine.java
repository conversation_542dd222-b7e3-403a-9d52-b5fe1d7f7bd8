package com.yeelight.service.user.server.engine;

import com.yeelight.service.user.client.domain.UserLevel;
import com.yeelight.service.user.client.domain.UserLevelLog;
import com.yeelight.service.user.client.domain.UserLevelRel;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import com.yeelight.service.user.client.request.UserLevelUpgradeRule;
import com.yeelight.service.user.client.service.UserLevelLogService;
import com.yeelight.service.user.client.service.UserLevelRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 用户等级升级规则引擎
 * 完全替代Drools的YeelightUserLevelUpgrade.drl规则文件
 * 实现与原Drools规则完全相同的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@Component
public class UserLevelUpgradeRuleEngine {

    @Resource
    private UserLevelRelService userLevelRelService;

    @Resource
    private UserLevelLogService userLevelLogService;

    /**
     * 执行用户等级升级规则
     * 完全替代原Drools规则组"UserLevelUpgrade"的所有规则
     * <p>
     * 原Drools规则优先级（salience）和激活组（activation-group）：
     * - UserLevelUpgrade: salience 20, activation-group "UserLevelUpgrade"
     * - UserLevelDowngrade: salience 15, activation-group "UserLevelUpgrade"
     * - UserLevelUpgradeLogRecord: salience 10, activation-group "UserLevelUpgrade"
     * <p>
     * 注意：activation-group表示只能激活一个规则，按优先级执行
     *
     * @param userLevelUpgradeRule 用户等级升级规则对象
     * @param userLevelRel 用户等级关系对象
     * @param userLevelLog 用户等级日志对象
     */
    public void executeUserLevelUpgradeRules(UserLevelUpgradeRule userLevelUpgradeRule,
                                             UserLevelRel userLevelRel,
                                             UserLevelLog userLevelLog) {

        log.debug("开始执行用户等级升级规则 - 用户ID: {}, 成长值变化: {}",
                userLevelUpgradeRule.getYeelightUser().getId(),
                userLevelUpgradeRule.getIncreasedGrowth());

        // 检查基本条件
        if (userLevelUpgradeRule.getYeelightUser() == null) {
            log.warn("用户信息为空，跳过等级升级规则执行");
            return;
        }

        // 按照原Drools规则的优先级顺序执行
        // 由于使用了activation-group，只会执行第一个匹配的规则

        // 1. 优先级最高：UserLevelUpgrade (salience 20)
        if (tryExecuteUserLevelUpgrade(userLevelUpgradeRule, userLevelRel, userLevelLog)) {
            log.debug("执行了UserLevelUpgrade规则");
            return;
        }

        // 2. 优先级中等：UserLevelDowngrade (salience 15)
        if (tryExecuteUserLevelDowngrade(userLevelUpgradeRule, userLevelRel, userLevelLog)) {
            log.debug("执行了UserLevelDowngrade规则");
            return;
        }

        // 3. 优先级最低：UserLevelUpgradeLogRecord (salience 10)
        if (tryExecuteUserLevelUpgradeLogRecord(userLevelUpgradeRule, userLevelRel, userLevelLog)) {
            log.debug("执行了UserLevelUpgradeLogRecord规则");
            return;
        }

        log.debug("没有匹配的等级升级规则 - 用户ID: {}", userLevelUpgradeRule.getYeelightUser().getId());
    }

    /**
     * 规则: UserLevelUpgrade (salience 20)
     * 当成长值增长且符合升级条件时执行
     */
    private boolean tryExecuteUserLevelUpgrade(UserLevelUpgradeRule userLevelUpgradeRule,
                                               UserLevelRel userLevelRel,
                                               UserLevelLog userLevelLog) {

        // 检查规则条件：成长值增长 >= 0
        if (userLevelUpgradeRule.getIncreasedGrowth() < 0) {
            return false;
        }

        // 查找符合升级条件的等级
        Optional<UserLevel> targetLevel = findTargetLevel(userLevelUpgradeRule, userLevelRel);
        if (!targetLevel.isPresent()) {
            return false;
        }

        UserLevel userLevel = targetLevel.get();

        log.debug("执行UserLevelUpgrade规则 - 用户ID: {}, 目标等级: {}",
                userLevelUpgradeRule.getYeelightUser().getId(), userLevel.getLevelName());

        // 设置升级前信息
        userLevelLog.setBeforeLevel(userLevelRel.getLevelId());
        userLevelLog.setBeforeGrowth(userLevelRel.getGrowth());

        // 更新用户等级关系
        userLevelRel.setGrowth(userLevelRel.getGrowth() + userLevelUpgradeRule.getIncreasedGrowth());
        userLevelRel.setLevelId(userLevel.getId());
        userLevelRel.setLevelName(userLevel.getLevelName());
        userLevelRel.setLevelType(userLevel.getLevelType());
        userLevelRel.setYeelightUserId(userLevelUpgradeRule.getYeelightUser().getId());

        // 设置升级后信息
        userLevelLog.setAfterLevel(userLevelRel.getLevelId());
        userLevelLog.setAfterGrowth(userLevelRel.getGrowth());

        // 设置描述信息
        if (userLevelLog.getBeforeLevel().equals(userLevelLog.getAfterLevel())) {
            userLevelLog.setDescription(UserLevelTypes.getTypesByCode(userLevelRel.getLevelType()).getDescription() + "-成长值增加");
        } else {
            userLevelLog.setDescription(UserLevelTypes.getTypesByCode(userLevelRel.getLevelType()).getDescription() + "-等级提升");
        }

        // 执行数据库操作
        userLevelRelService.replaceLevelRel(userLevelRel);
        userLevelLogService.insertSelective(userLevelLog);

        log.info("用户等级升级完成 - 用户ID: {}, 等级: {} -> {}, 成长值: {} -> {}",
                userLevelUpgradeRule.getYeelightUser().getId(),
                userLevelLog.getBeforeLevel(), userLevelLog.getAfterLevel(),
                userLevelLog.getBeforeGrowth(), userLevelLog.getAfterGrowth());

        return true;
    }

    /**
     * 规则: UserLevelDowngrade (salience 15)
     * 当成长值减少且符合降级条件时执行
     */
    private boolean tryExecuteUserLevelDowngrade(UserLevelUpgradeRule userLevelUpgradeRule,
                                                 UserLevelRel userLevelRel,
                                                 UserLevelLog userLevelLog) {

        // 检查规则条件：成长值增长 < 0（实际是减少）
        if (userLevelUpgradeRule.getIncreasedGrowth() >= 0) {
            return false;
        }

        // 查找符合降级条件的等级
        Optional<UserLevel> targetLevel = findTargetLevelForDowngrade(userLevelUpgradeRule, userLevelRel);
        if (!targetLevel.isPresent()) {
            return false;
        }

        UserLevel userLevel = targetLevel.get();

        log.debug("执行UserLevelDowngrade规则 - 用户ID: {}, 目标等级: {}",
                userLevelUpgradeRule.getYeelightUser().getId(), userLevel.getLevelName());

        // 设置降级前信息
        userLevelLog.setBeforeLevel(userLevelRel.getLevelId());
        userLevelLog.setBeforeGrowth(userLevelRel.getGrowth());

        // 更新用户等级关系
        userLevelRel.setGrowth(userLevelRel.getGrowth() + userLevelUpgradeRule.getIncreasedGrowth());
        userLevelRel.setLevelId(userLevel.getId());
        userLevelRel.setLevelName(userLevel.getLevelName());
        userLevelRel.setLevelType(userLevel.getLevelType());
        userLevelRel.setYeelightUserId(userLevelUpgradeRule.getYeelightUser().getId());

        // 设置降级后信息
        userLevelLog.setAfterLevel(userLevelRel.getLevelId());
        userLevelLog.setAfterGrowth(userLevelRel.getGrowth());

        // 设置描述信息
        if (userLevelLog.getBeforeLevel().equals(userLevelLog.getAfterLevel())) {
            userLevelLog.setDescription(UserLevelTypes.getTypesByCode(userLevelRel.getLevelType()).getDescription() + "-成长值减少");
        } else {
            userLevelLog.setDescription(UserLevelTypes.getTypesByCode(userLevelRel.getLevelType()).getDescription() + "-等级降低");
        }

        // 执行数据库操作
        userLevelRelService.replaceLevelRel(userLevelRel);
        userLevelLogService.insertSelective(userLevelLog);

        log.info("用户等级降级完成 - 用户ID: {}, 等级: {} -> {}, 成长值: {} -> {}",
                userLevelUpgradeRule.getYeelightUser().getId(),
                userLevelLog.getBeforeLevel(), userLevelLog.getAfterLevel(),
                userLevelLog.getBeforeGrowth(), userLevelLog.getAfterGrowth());

        return true;
    }

    /**
     * 规则: UserLevelUpgradeLogRecord (salience 10)
     * 记录等级变化日志，当其他规则都不匹配时执行
     */
    private boolean tryExecuteUserLevelUpgradeLogRecord(UserLevelUpgradeRule userLevelUpgradeRule,
                                                        UserLevelRel userLevelRel,
                                                        UserLevelLog userLevelLog) {

        // 查找符合条件的等级（条件相对宽松）
        Optional<UserLevel> targetLevel = findTargetLevelForLogRecord(userLevelUpgradeRule, userLevelRel);
        if (!targetLevel.isPresent()) {
            return false;
        }

        UserLevel userLevel = targetLevel.get();

        log.debug("执行UserLevelUpgradeLogRecord规则 - 用户ID: {}, 目标等级: {}",
                userLevelUpgradeRule.getYeelightUser().getId(), userLevel.getLevelName());

        // 设置日志信息
        userLevelLog.setBeforeLevel(userLevelRel.getLevelId());
        userLevelLog.setBeforeGrowth(userLevelRel.getGrowth());
        userLevelLog.setAfterLevel(userLevel.getId());
        userLevelLog.setAfterGrowth(userLevelRel.getGrowth() + userLevelUpgradeRule.getIncreasedGrowth());

        // 设置描述信息
        if (userLevelUpgradeRule.getIncreasedGrowth() < 0) {
            userLevelLog.setDescription(UserLevelTypes.getTypesByCode(userLevelRel.getLevelType()).getDescription() + "-成长值减少");
        } else {
            userLevelLog.setDescription(UserLevelTypes.getTypesByCode(userLevelRel.getLevelType()).getDescription() + "-成长值增加");
        }

        // 更新用户等级关系
        userLevelRel.setLevelId(userLevel.getId());
        userLevelRel.setLevelName(userLevel.getLevelName());
        userLevelRel.setGrowth(userLevelRel.getGrowth() + userLevelUpgradeRule.getIncreasedGrowth());

        // 执行数据库操作
        userLevelRelService.replaceLevelRel(userLevelRel);
        userLevelLogService.insertSelective(userLevelLog);

        log.info("用户等级日志记录完成 - 用户ID: {}, 等级: {} -> {}, 成长值: {} -> {}",
                userLevelUpgradeRule.getYeelightUser().getId(),
                userLevelLog.getBeforeLevel(), userLevelLog.getAfterLevel(),
                userLevelLog.getBeforeGrowth(), userLevelLog.getAfterGrowth());

        return true;
    }

    /**
     * 查找符合升级条件的目标等级
     * 对应Drools规则中的条件：
     * requiredGrowth >= $userLevelRel.growth,
     * requiredGrowth <= ($userLevelRel.growth + $userLevelUpgradeRule.increasedGrowth),
     * levelType == $userLevelRel.levelType
     */
    private Optional<UserLevel> findTargetLevel(UserLevelUpgradeRule userLevelUpgradeRule, UserLevelRel userLevelRel) {
        List<UserLevel> userLevelList = userLevelUpgradeRule.getUserLevelList();
        if (userLevelList == null || userLevelList.isEmpty()) {
            return Optional.empty();
        }

        int currentGrowth = userLevelRel.getGrowth();
        int newGrowth = currentGrowth + userLevelUpgradeRule.getIncreasedGrowth();

        return userLevelList.stream()
                .filter(level -> level.getRequiredGrowth() >= currentGrowth)
                .filter(level -> level.getRequiredGrowth() <= newGrowth)
                .filter(level -> level.getLevelType().equals(userLevelRel.getLevelType()))
                .findFirst();
    }

    /**
     * 查找符合降级条件的目标等级
     * 降级逻辑与升级类似，但成长值是减少的
     */
    private Optional<UserLevel> findTargetLevelForDowngrade(UserLevelUpgradeRule userLevelUpgradeRule, UserLevelRel userLevelRel) {
        List<UserLevel> userLevelList = userLevelUpgradeRule.getUserLevelList();
        if (userLevelList == null || userLevelList.isEmpty()) {
            return Optional.empty();
        }

        int currentGrowth = userLevelRel.getGrowth();
        // increasedGrowth是负数
        int newGrowth = currentGrowth + userLevelUpgradeRule.getIncreasedGrowth();

        return userLevelList.stream()
                // 注意这里是newGrowth
                .filter(level -> level.getRequiredGrowth() >= newGrowth)
                .filter(level -> level.getRequiredGrowth() <= currentGrowth)
                .filter(level -> level.getLevelType().equals(userLevelRel.getLevelType()))
                .findFirst();
    }

    /**
     * 查找符合日志记录条件的目标等级
     * 对应Drools规则中的条件：
     * requiredGrowth <= $userLevelRel.growth,
     * levelType == $userLevelRel.levelType
     */
    private Optional<UserLevel> findTargetLevelForLogRecord(UserLevelUpgradeRule userLevelUpgradeRule, UserLevelRel userLevelRel) {
        List<UserLevel> userLevelList = userLevelUpgradeRule.getUserLevelList();
        if (userLevelList == null || userLevelList.isEmpty()) {
            return Optional.empty();
        }

        int currentGrowth = userLevelRel.getGrowth();

        return userLevelList.stream()
                .filter(level -> level.getRequiredGrowth() <= currentGrowth)
                .filter(level -> level.getLevelType().equals(userLevelRel.getLevelType()))
                .findFirst();
    }
}