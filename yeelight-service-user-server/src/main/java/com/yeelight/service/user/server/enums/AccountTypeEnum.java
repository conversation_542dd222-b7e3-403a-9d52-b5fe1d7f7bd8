package com.yeelight.service.user.server.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * @description: 账号类型枚举
 * <AUTHOR>
 */
@Getter
public enum AccountTypeEnum implements BaseEnum<String> {
    /**
     * 账号类型
     */
    EMAIL("EMAIL", "邮箱"),
    PHONE_NUMBER("PHONE_NUMBER", "手机号"),
    USERNAME("USERNAME", "用户名"),
    ID("ID", "ID"),
    ;
    public final String code;
    public final String msg;

    AccountTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
