package com.yeelight.service.user.server.enums;

import com.yeelight.service.user.server.request.auth.*;
import me.zhyd.oauth.config.AuthSource;
import me.zhyd.oauth.request.AuthDefaultRequest;
import org.apache.commons.lang3.StringUtils;

/**
 * 自定义第三方授权登录
 *
 * <AUTHOR>
 */
public enum AuthCustomSource implements AuthSource {
    /**
     * 自定义三方
     */
    APPLE {
        @Override
        public String authorize() {
            return "https://appleid.apple.com/auth/authorize";
        }

        @Override
        public String accessToken() {
            return "https://appleid.apple.com/auth/token";
        }

        @Override
        public String userInfo() {
            return StringUtils.EMPTY;
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthAppleRequest.class;
        }
    },
    XIAOMI {
        @Override
        public String authorize() {
            return "https://account.xiaomi.com/oauth2/authorize";
        }

        @Override
        public String accessToken() {
            return "https://account.xiaomi.com/oauth2/token";
        }

        @Override
        public String userInfo() {
            return "https://open.account.xiaomi.com/user/profile";
        }

        @Override
        public String refresh() {
            return "https://account.xiaomi.com/oauth2/token";
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthXiaoMiRequest.class;
        }
    },
    /**
     * Amazon Alexa
     */
    AMAZON_ALEXA {
        @Override
        public String authorize() {
            return "https://www.amazon.com/ap/oa";
        }

        @Override
        public String accessToken() {
            return "https://api.amazon.com/auth/o2/token";
        }

        @Override
        public String userInfo() {
            return "https://api.amazon.com/user/profile";
        }

        @Override
        public String refresh() {
            return "https://api.amazon.com/auth/o2/token";
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthAmazonAlexaRequest.class;
        }
    },
    FEISHU_APP {
        @Override
        public String authorize() {
            return "https://passport.feishu.cn/suite/passport/oauth/authorize";
        }

        @Override
        public String accessToken() {
            return "https://passport.feishu.cn/suite/passport/oauth/token";
        }

        @Override
        public String userInfo() {
            return "https://passport.feishu.cn/suite/passport/oauth/userinfo";
        }

        @Override
        public String refresh() {
            return "https://passport.feishu.cn/suite/passport/oauth/token";
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthFeishuAppRequest.class;
        }
    },

    /**
     * 微信开放平台 for 师傅端 app
     */
    WECHAT_OPEN_APP {
        @Override
        public String authorize() {
            return "https://open.weixin.qq.com/connect/qrconnect";
        }

        @Override
        public String accessToken() {
            return "https://api.weixin.qq.com/sns/oauth2/access_token";
        }

        @Override
        public String userInfo() {
            return "https://api.weixin.qq.com/sns/userinfo";
        }

        @Override
        public String refresh() {
            return "https://api.weixin.qq.com/sns/oauth2/refresh_token";
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthWeChatOpenAppRequest.class;
        }
    },

    /**
     * 微信开放平台 for yeelight app
     */
    WECHAT_YEELIGHT_APP {
        @Override
        public String authorize() {
            return "https://open.weixin.qq.com/connect/qrconnect";
        }

        @Override
        public String accessToken() {
            return "https://api.weixin.qq.com/sns/oauth2/access_token";
        }

        @Override
        public String userInfo() {
            return "https://api.weixin.qq.com/sns/userinfo";
        }

        @Override
        public String refresh() {
            return "https://api.weixin.qq.com/sns/oauth2/refresh_token";
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthWeChatYeelightAppRequest.class;
        }
    },

    /**
     * 微信小程序
     */
    WECHAT_MINI_SHOP {
        @Override
        public String authorize() {
            throw new UnsupportedOperationException("不支持获取授权 url，请使用小程序内置函数 wx.login() 登录获取 code");
        }

        @Override
        public String accessToken() {
            return "https://api.weixin.qq.com/sns/jscode2session";
        }

        @Override
        public String userInfo() {
            throw new UnsupportedOperationException("不支持获取用户信息 url，请使用小程序内置函数 wx.getUserProfile() 获取用户信息");
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthWechatMiniProgramRequest.class;
        }
    },

    /**
     * Sonos
     *
     * @see <a href="https://docs.sonos.com/docs/authorize">Sonos Identity API</a>
     */
    SONOS {
        @Override
        public String authorize() {
            return "https://api.sonos.com/login/v3/oauth";
        }

        @Override
        public String accessToken() {
            return "https://api.sonos.com/login/v3/oauth/access";
        }

        @Override
        public String userInfo() {
            return "https://api.ws.sonos.com/control/api/v1/households";
        }

        @Override
        public String refresh() {
            return "https://api.sonos.com/login/v3/oauth/access";
        }

        @Override
        public Class<? extends AuthDefaultRequest> getTargetClass() {
            return AuthSonosRequest.class;
        }
    }
}
