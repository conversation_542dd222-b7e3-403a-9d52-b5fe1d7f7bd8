package com.yeelight.service.user.server.enums;

import com.yeelight.service.framework.msg.IResultCode;
import lombok.Getter;

/**
 * @description: 返回码枚举类
 * <AUTHOR>
 */
@Getter
public enum ResultCodeEnum  implements IResultCode {
    // 用户
    用户名不存在("10000", "用户名不存在"),
    用户已存在("11000", "用户已存在"),
    手机号或邮箱不存在("11001", "手机号或邮箱不存在"),
    用户名或密码错误("10001", "用户名或密码错误"),
    密码不一致("10021", "密码不一致"),
    账户已禁用("10002", "您的账户已禁用，请联系客服"),
    验证码错误("10003", "您输入的验证码错误"),
    请输入验证码("10004", "请输入验证码"),
    请输入手机验证码("10005", "请输入手机验证码"),
    手机号未注册("10006", "您输入的手机号未注册，请先注册"),
    三方账号未绑定("10007", "您的三方账号未绑定，请先登录后到个人中心进行绑定"),
    三方账号用户不存在("10008", "您的三方账号用户不存在"),
    获取验证码失败("10018", "获取验证码失败"),
    获取手机验证码失败("10009", "获取手机验证码失败"),
    密码错误("10010", "密码错误"),
    需要微信信息("10011", "需要微信信息"),
    需要三方信息("10012", "需要三方信息"),
    邮箱未注册("10013", "您输入的邮箱未注册，请先注册"),
    请输入APP名称("10014", "请输入APP名称"),
    请输入一键登录TOKEN("10015", "请输入一键登录TOKEN"),
    账号未注册("10016", "账号未注册"),

    认证失败("20001", "认证失败"),
    会话失效("20002", "会话失效"),
    会话过期("20002", "会话失效"),

    超出频率限制("99999", "超出频率限制"),

    // Oauth
    禁止访问("403", "禁止访问"),
    服务器异常("500", "服务器异常"),
    未授权("401", "未授权"),
    非法请求("405", "非法请求"),
    参数异常("400", "参数异常"),
    ;

    public final String code;

    public final String msg;

    ResultCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getIntegerCode() {
        return Integer.valueOf(this.code);
    }
}
