package com.yeelight.service.user.server.enums;

import com.yeelight.service.framework.enums.BaseEnum;
import lombok.Getter;

/**
 * @description: 扫码登录状态枚举类
 * <AUTHOR>
 */
@Getter
public enum ScanLoginStatusEnum implements BaseEnum<String> {
    /**
     * 扫码登录状态
     */
    CREATED("CREATED", "二维码已生成"),
    SCANNED("SCANNED", "扫码成功"),
    CONFIRM("CONFIRM", "确认登录"),
    LOGIN("LOGIN", "登录成功"),
    EXPIRED("EXPIRED", "二维码过期"),
    ;
    public final String code;
    public final String msg;

    ScanLoginStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
