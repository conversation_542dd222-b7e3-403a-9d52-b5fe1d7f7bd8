package com.yeelight.service.user.server.exception;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 新版自定义认证入口点
 * 适配器模式，将NewCustomOAuth2ErrorHandler适配为AuthenticationEntryPoint
 * 
 * <AUTHOR>
 * @description: 新版认证入口点，适配NewCustomOAuth2ErrorHandler
 */
@Slf4j
@Component("newCustomAuthenticationEntryPoint")
public class NewCustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final NewCustomOAuth2ErrorHandler errorHandler;

    public NewCustomAuthenticationEntryPoint(NewCustomOAuth2ErrorHandler errorHandler) {
        this.errorHandler = errorHandler;
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, 
                        AuthenticationException authException) throws IOException {
        log.debug("Authentication entry point triggered: {}", authException.getMessage());
        
        // 委托给NewCustomOAuth2ErrorHandler处理
        errorHandler.onAuthenticationFailure(request, response, authException);
    }
}
