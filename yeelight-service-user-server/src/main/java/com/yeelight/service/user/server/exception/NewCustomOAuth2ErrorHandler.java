package com.yeelight.service.user.server.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.exception.AuthException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.util.HtmlUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 新版自定义OAuth2错误处理器
 * 基于Spring Authorization Server的异常处理机制
 * 完全兼容旧版CustomOauth2WebResponseExceptionTranslator的所有功能
 * 
 * <AUTHOR>
 * @description: 新版OAuth2异常处理器，与旧版CustomOauth2WebResponseExceptionTranslator保持兼容
 */
@Slf4j
@Component("newCustomOAuth2ErrorHandler")
public class NewCustomOAuth2ErrorHandler implements AuthenticationFailureHandler, AccessDeniedHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String INVALID_REFRESH_TOKEN = "Invalid refresh token: ";

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, 
                                      AuthenticationException exception) throws IOException {
        log.debug("OAuth2 authentication failure: {}", exception.getMessage());
        handleException(request, response, exception);
    }

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, 
                      AccessDeniedException accessDeniedException) throws IOException {
        log.debug("OAuth2 access denied: {}", accessDeniedException.getMessage());
        handleException(request, response, accessDeniedException);
    }

    /**
     * 处理各种类型的异常
     * 与旧版CustomOauth2WebResponseExceptionTranslator的translate方法保持一致
     */
    public void handleException(HttpServletRequest request, HttpServletResponse response, 
                               Exception exception) throws IOException {
        try {
            // 分析异常类型并转换为相应的错误响应
            CustomErrorResponse errorResponse = translateException(exception);
            
            // 设置响应头
            response.setStatus(HttpStatus.OK.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Cache-Control", "no-store");
            response.setHeader("Pragma", "no-cache");
            
            // 写入响应体
            objectMapper.writeValue(response.getWriter(), errorResponse);
            
        } catch (Exception ex) {
            log.error("Error handling OAuth2 exception", ex);
            // 如果处理异常时出错，返回通用服务器错误
            writeGenericError(response);
        }
    }

    /**
     * 将异常转换为自定义错误响应
     * 与旧版CustomOauth2WebResponseExceptionTranslator的异常处理逻辑完全一致
     */
    private CustomErrorResponse translateException(Exception exception) {
        // 检查OAuth2AuthenticationException
        if (exception instanceof OAuth2AuthenticationException oauth2Exception) {
            return handleOAuth2AuthenticationException(oauth2Exception);
        }
        
        // 检查AuthException（JustAuth异常）
        if (exception instanceof AuthException) {
            return createErrorResponse(
                ResultCodeEnum.参数异常.code,
                I18nUtil.getMessage("ResultCode.Common.BAD_REQUEST"),
                exception.getMessage()
            );
        }
        
        // 检查PasswordErrorException
        if (exception instanceof PasswordErrorException) {
            return createErrorResponse(
                ResultCodeEnum.参数异常.code,
                I18nUtil.getMessage("ResultCode.Common.BAD_REQUEST"),
                exception.getMessage()
            );
        }
        
        // 检查CaptchaException
        if (exception instanceof CaptchaException) {
            return createErrorResponse(
                ResultCodeEnum.参数异常.code,
                I18nUtil.getMessage("ResultCode.Common.BAD_REQUEST"),
                exception.getMessage()
            );
        }
        
        // 检查AuthenticationException
        if (exception instanceof AuthenticationException) {
            return createErrorResponse(
                ResultCodeEnum.未授权.code,
                I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"),
                exception.getMessage()
            );
        }
        
        // 检查AccessDeniedException
        if (exception instanceof AccessDeniedException) {
            return createErrorResponse(
                ResultCodeEnum.禁止访问.code,
                I18nUtil.getMessage("ResultCode.Common.FORBIDDEN"),
                exception.getMessage()
            );
        }
        
        // 检查HttpRequestMethodNotSupportedException
        if (exception instanceof HttpRequestMethodNotSupportedException) {
            return createErrorResponse(
                ResultCodeEnum.非法请求.code,
                I18nUtil.getMessage("ResultCode.Common.ILLEGAL_REQUEST"),
                exception.getMessage()
            );
        }
        
        // 默认服务器内部错误
        return createErrorResponse(
            ResultCodeEnum.服务器异常.code,
            I18nUtil.getMessage("ResultCode.Common.INTERNAL_SERVER_ERROR"),
            HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()
        );
    }

    /**
     * 处理OAuth2AuthenticationException
     * 与旧版的InvalidGrantException处理逻辑保持一致
     */
    private CustomErrorResponse handleOAuth2AuthenticationException(OAuth2AuthenticationException exception) {
        OAuth2Error error = exception.getError();
        
        // 处理无效的刷新令牌 - 与旧版逻辑完全一致
        if (OAuth2ErrorCodes.INVALID_GRANT.equals(error.getErrorCode())) {
            String description = error.getDescription();
            if (description != null && description.contains(INVALID_REFRESH_TOKEN)) {
                String refreshToken = description.substring(description.indexOf(INVALID_REFRESH_TOKEN) + INVALID_REFRESH_TOKEN.length());
                if (StringUtils.isNotBlank(refreshToken)) {
                    String clientId = JwtUtil.parseClientIdFromTokenWithoutVerify(refreshToken);
                    if (StringUtils.isNotBlank(clientId) && Constants.REQUIRE_STANDARD_RESPONSE_CLIENT_IDS.contains(clientId)) {
                        // 对于特定客户端ID，返回标准OAuth2错误响应
                        return createStandardOAuth2ErrorResponse(error);
                    }
                }
            }
        }
        
        // 默认处理
        return createErrorResponse(
            ResultCodeEnum.参数异常.code,
            I18nUtil.getMessage("ResultCode.Common.BAD_REQUEST"),
            error.getDescription() != null ? error.getDescription() : error.getErrorCode()
        );
    }

    /**
     * 创建自定义错误响应
     * 与旧版CustomOauthExceptionJacksonSerializer的序列化格式完全一致
     */
    private CustomErrorResponse createErrorResponse(String code, String errorMsg, String message) {
        CustomErrorResponse response = new CustomErrorResponse();
        response.setSuccess(false);
        response.setCode(code);
        response.setMsg(message != null ? HtmlUtils.htmlEscape(message) : "");
        response.setErrorMsg(errorMsg != null ? HtmlUtils.htmlEscape(errorMsg) : "");
        response.setData(null);
        return response;
    }

    /**
     * 创建标准OAuth2错误响应
     * 用于特定客户端的标准响应格式
     */
    private CustomErrorResponse createStandardOAuth2ErrorResponse(OAuth2Error error) {
        CustomErrorResponse response = new CustomErrorResponse();
        response.setSuccess(false);
        response.setCode(error.getErrorCode());
        response.setMsg(error.getDescription() != null ? error.getDescription() : "");
        response.setErrorMsg(error.getErrorCode());
        response.setData(null);
        return response;
    }

    /**
     * 写入通用错误响应
     */
    private void writeGenericError(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        Map<String, Object> errorMap = new HashMap<>(8);
        errorMap.put("success", false);
        errorMap.put("code", ResultCodeEnum.服务器异常.code);
        errorMap.put("msg", "Internal Server Error");
        errorMap.put("errorMsg", I18nUtil.getMessage("ResultCode.Common.INTERNAL_SERVER_ERROR"));
        errorMap.put("data", null);
        
        objectMapper.writeValue(response.getWriter(), errorMap);
    }

    /**
     * 自定义错误响应类
     * 与旧版CustomOauthExceptionJacksonSerializer的输出格式完全一致
     */
    @Data
    public static class CustomErrorResponse implements Serializable {
        // Getters and Setters
        private boolean success;
        private String code;
        private String msg;
        private String errorMsg;
        private Object data;
    }
}
