package com.yeelight.service.user.server.exception;

import org.springframework.security.core.AuthenticationException;

/**
 * 密码错误异常
 * <AUTHOR>
 */
public class PasswordErrorException extends AuthenticationException {

    public PasswordErrorException(String message, Throwable cause) {
        super(message, cause);
    }

    public PasswordErrorException(String message) {
        super(message);
    }

}
