package com.yeelight.service.user.server.filter;

import com.yeelight.service.user.client.token.EmailCodeAuthenticationToken;
import lombok.Setter;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.util.Assert;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 邮箱验证码登录认证过滤器
 * <p>
 * 参考 {@link UsernamePasswordAuthenticationFilter}
 * </p>
 * <AUTHOR>
 */
public class EmailCodeAuthenticationFilter extends AbstractAuthenticationProcessingFilter {
    private String emailParameter = "email";
    @Setter
    private boolean postOnly = true;

    /**
     * 仅匹配 [POST /authentication/email]
     */
    public EmailCodeAuthenticationFilter() {
        super(new AntPathRequestMatcher("/authentication/email", "POST"));
    }

    /**
     * 尝试进行用户认证。
     * 该方法首先检查请求方法是否为POST，如果不是，则抛出认证异常。
     * 然后从请求中获取电子邮件地址，创建一个EmailCodeAuthenticationToken，并将其传递给认证管理器进行认证。
     *
     * @param request  HttpServletRequest，表示客户端的HTTP请求。
     * @param response HttpServletResponse，表示服务器端的HTTP响应。
     * @return Authentication，表示认证结果的对象。
     * @throws AuthenticationException 如果认证过程中出现异常。
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request,
                                                HttpServletResponse response) throws AuthenticationException {
        // 检查是否仅支持POST请求，如果不是则抛出异常
        if (postOnly && !HttpMethod.POST.name().equalsIgnoreCase(request.getMethod())) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }
        String email = obtainEmail(request);

        // 确保email不为空
        if (email == null) {
            email = "";
        }

        // 去除email两端的空白字符
        email = email.trim();

        EmailCodeAuthenticationToken authRequest = new EmailCodeAuthenticationToken(email);

        // 允许子类设置"details"属性
        setDetails(request, authRequest);

        // 调用认证管理器进行认证
        return this.getAuthenticationManager().authenticate(authRequest);
    }


    /**
     * 从HTTP请求中获取电子邮件地址。
     * @param request HttpServletRequest对象，代表一个HTTP请求。
     * @return 返回从请求中获取到的电子邮件参数值，如果未找到则返回null。
     */
    protected String obtainEmail(HttpServletRequest request) {
        // 通过参数名从请求参数中获取电子邮件地址
        return request.getParameter(emailParameter);
    }


    /**
     * 设置认证请求的详细信息。
     * 该方法通过调用 {@link AuthenticationDetailsSource# buildDetails(HttpServletRequest)} 方法，
     * 从HTTP请求中构建认证详情，并将其设置到认证请求对象中。
     *
     * @param request HttpServletRequest对象，代表当前的HTTP请求。
     * @param authRequest EmailCodeAuthenticationToken对象，代表当前的认证请求。
     *                    此对象用于存储认证过程中的相关信息，包括用户标识和验证码等。
     */
    protected void setDetails(HttpServletRequest request, EmailCodeAuthenticationToken authRequest) {
        // 使用authenticationDetailsSource从HTTP请求构建认证详情，并将其设置到authRequest中
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    public void setEmailParameter(String emailParameter) {
        Assert.hasText(emailParameter, "Email parameter must not be empty or null");
        this.emailParameter = emailParameter;
    }

    public final String getEmailParameter() {
        return emailParameter;
    }

}
