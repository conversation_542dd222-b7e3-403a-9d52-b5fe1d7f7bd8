package com.yeelight.service.user.server.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.token.JustAuthAuthenticationToken;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.request.AuthRequest;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * JustAuthAuthenticationFilter
 *
 * <AUTHOR> Yu
 * @date 9/13/21 4:20 PM
 */
@Setter
@Slf4j
public class JustAuthAuthenticationFilter extends AbstractAuthenticationProcessingFilter {
    private boolean postOnly = false;
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;

    /**
     * Set URL and http method for Filter
     */
    public JustAuthAuthenticationFilter() {
        super(new AntPathRequestMatcher(SecurityConstants.THIRD_PARTY_CALLBACK, null));
    }

    /**
     * 尝试进行用户认证。
     * 该方法首先检查请求方法是否为POST，如果配置为仅支持POST且请求方法不是POST，则抛出认证异常。
     * 如果请求方法符合要求，那么将从远程API获取用户认证信息，创建认证令牌，并进行认证流程。
     *
     * @param request  HttpServletRequest，代表客户端的HTTP请求。
     * @param response HttpServletResponse，代表服务器对客户端的HTTP响应。
     * @return Authentication，认证结果对象。
     * @throws AuthenticationException 如果认证过程中出现异常，则抛出此异常。
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        // 检查是否仅支持POST请求，并对请求方法进行验证
        if (this.postOnly && !HttpMethod.POST.name().equalsIgnoreCase(request.getMethod())) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        } else {
            // 从远程API获取认证信息，并尝试进行认证
            try {
                // 处理错误回调
                handlerErrorCallback(request);
                // 创建认证令牌
                JustAuthAuthenticationToken token = new JustAuthAuthenticationToken(obtainAuthUser(request));
                // 设置认证令牌的详细信息
                this.setDetails(request, token);
                // 调用认证管理器进行认证
                return this.getAuthenticationManager().authenticate(token);
            } catch (AuthenticationException authException) {
                // 如果认证过程中抛出认证异常，则将其封装并重新抛出
                throw new AuthenticationServiceException(authException.getMessage());
            }
        }
    }

    /**
     * 获取用户信息
     *
     * @param request HttpServletRequest对象，用于获取用户认证相关的请求信息
     * @return AuthUser 用户认证信息对象，包含用户的授权信息和来源
     * @throws AuthenticationServiceException 当认证失败时抛出
     */
    protected AuthUser obtainAuthUser(HttpServletRequest request) {
        // 解析回调来源
        String source = this.parseCallbackSource(request);
        // 获取认证回调信息
        AuthCallback authCallback = getCallback(request);
        // 针对Amazon Alexa来源，设置授权码
        if (source != null) {
            boolean isAmazonAlexa = source.contains(AuthCustomSource.AMAZON_ALEXA.getName().toLowerCase()) || source.contains(AuthCustomSource.AMAZON_ALEXA.getName().toUpperCase());
            if (isAmazonAlexa) {
                authCallback.setAuthorization_code((String) request.getAttribute("yeelightAuthCode"));
                log.info(JSON.toJSONString(authCallback));
            }
        }
        // 根据来源获取对应的认证请求对象
        AuthRequest authRequest = yeelightAuthRequestFactory.get(source);
        // 执行登录操作，获取认证响应
        AuthResponse<?> response = authRequest.login(authCallback);
        log.info("source:{}, JustAuth response: {}", source, JSON.toJSONString(response));

        // 认证成功处理
        if (response.getCode() == AuthResponseStatus.SUCCESS.getCode()) {
            // 将认证响应数据转换为AuthUser对象
            AuthUser authUser = BeanUtils.objToBean(response.getData(), AuthUser.class);
            // 提取电话信息
            extractPhoneInfo(authUser);
            // 设置用户来源
            authUser.setSource(UserVendorHolder.attachVendor(authUser.getSource()));
            return authUser;
        } else {
            // 认证失败处理，记录日志并抛出异常
            log.warn("{} Authentication failed: {}", source, response.getMsg());
            throw new AuthenticationServiceException(source + " Authentication failed:" + response.getMsg());
        }
    }

    /**
     * 处理错误回调。
     * 此方法用于处理认证过程中的错误回调，如果请求参数中包含错误描述，则抛出一个认证异常。
     *
     * @param request HttpServletRequest对象，用于获取请求参数。
     * @throws AuthenticationServiceException 如果请求参数中存在"error_description"，且其值不为空，则抛出此异常，异常信息包含"error"和"error_description"参数的值。
     */
    private void handlerErrorCallback(HttpServletRequest request) {
        // 检查请求参数中是否存在错误描述，并抛出认证异常
        if (StringUtils.isNotBlank(request.getParameter(Constants.ERROR_DESCRIPTION_KEY))) {
            throw new AuthenticationServiceException(request.getParameter(Constants.ERROR_KEY) + ":" +request.getParameter(Constants.ERROR_DESCRIPTION_KEY));
        }
    }

    /**
     * 从AuthUser对象中提取电话信息，并更新AuthUser对象的电话字段。
     * <p>
     * 对传入的AuthUser对象进行解析，尝试从其rawUserInfo中提取电话信息。优先级从高到低为：phone、mobile、phone_number、phoneNumber。
     * 如果找到了有效的电话信息，会移除电话号码前的"+86"（如果存在），然后更新AuthUser对象的电话字段。
     * <p>
     * 参数:
     * authUser - AuthUser对象，包含用户认证信息和原始用户信息。
     * <p>
     * 返回值: 无
     */
    private void extractPhoneInfo(AuthUser authUser) {
        // 如果AuthUser对象为null，则直接返回，不进行任何操作
        if (Objects.isNull(authUser)) {
            return;
        }
        String phone = null;
        // 尝试从原始用户信息中解析出包含用户数据的JSONObject
        JSONObject rawUserInfo = authUser.getRawUserInfo();
        if (Objects.nonNull(rawUserInfo)) {
            // 有些情况下，原始用户信息可能包含在"data"字段中，需要进行二次解析
            JSONObject rawUserData = rawUserInfo.getJSONObject("data");
            if (Objects.nonNull(rawUserData)) {
                rawUserInfo = rawUserData;
            }
            // 尝试从多个可能的字段中提取电话信息，优先使用第一个不为空的字段
            if (StringUtils.isNotBlank(rawUserInfo.getString(Constants.PHONE_KEY))) {
                phone = rawUserInfo.getString(Constants.PHONE_KEY);
            }
            if (StringUtils.isNotBlank(rawUserInfo.getString(Constants.MOBILE_KEY))) {
                phone = rawUserInfo.getString(Constants.MOBILE_KEY);
            }
            if (StringUtils.isNotBlank(rawUserInfo.getString(Constants.PHONE_NUMBER_KEY))) {
                phone = rawUserInfo.getString(Constants.PHONE_NUMBER_KEY);
            }
            if (StringUtils.isNotBlank(rawUserInfo.getString(Constants.PHONE_NUMBER_CAMEL_KEY))) {
                phone = rawUserInfo.getString(Constants.PHONE_NUMBER_CAMEL_KEY);
            }

            // 如果找到了电话信息，移除可能存在的"+86"前缀
            if (StringUtils.isNotBlank(phone)) {
                phone = phone.replaceFirst("\\+86", "");
            }
            // 更新AuthUser对象的电话字段
            authUser.setPhone(phone);
        }
    }

    /**
     * 构建AuthCallback实体。
     * 该方法通过解析HTTP请求参数，创建一个AuthCallback对象，用于处理认证回调。
     *
     * @param request HttpServletRequest对象，用于获取请求参数。
     * @return AuthCallback对象，包含了认证过程中的各种回调参数。
     */
    private AuthCallback getCallback(HttpServletRequest request) {
        // 使用Builder模式构建AuthCallback对象，设置各种回调参数
        return AuthCallback.builder()
                .code(request.getParameter("code"))
                .auth_code(request.getParameter("auth_code"))
                .authorization_code(request.getParameter("authorization_code"))
                .oauth_token(request.getParameter("oauth_token"))
                .state(request.getParameter(OAuth2Utils.STATE))
                .oauth_verifier(request.getParameter("oauth_verifier"))
                .build();
    }

    /**
     * 解析回调源
     * TODO: 优化解析URI的逻辑
     * @param request HttpServletRequest对象，用于获取请求信息
     * @return 返回解析后的回调源字符串。如果解析失败或未指定源，返回null。
     */
    private String parseCallbackSource(HttpServletRequest request) {
        // 获取请求的URI, 如 /context/open/oauth/callback/gitee
        String uri = request.getRequestURI();
        // 获取请求参数中的source值
        String source = request.getParameter("source");
        if (StringUtils.isNotBlank(source)) {
            // 如果请求参数中指定了source，则直接使用该source
            return UserVendorHolder.attachVendor(source);
        }
        // 计算通用回调路径长度，去掉最后的"/"， 如 "/open/oauth/callback/".length()
        int common = SecurityConstants.THIRD_PARTY_CALLBACK.length() - 2;
        int start = uri.indexOf(SecurityConstants.THIRD_PARTY_CALLBACK.substring(0, common));
        if (start == -1) {
            // 如果URI中不包含通用回调路径，则记录日志并返回null
            log.warn("Not found source，uri={}", uri);
            return null;
        }
        // 从URI中提取回调源，并返回，如 gitee
        return UserVendorHolder.attachVendor(uri.substring(start + common));
    }

    protected void setDetails(HttpServletRequest request, JustAuthAuthenticationToken authRequest) {
        authRequest.setDetails(this.authenticationDetailsSource.buildDetails(request));
    }

}
