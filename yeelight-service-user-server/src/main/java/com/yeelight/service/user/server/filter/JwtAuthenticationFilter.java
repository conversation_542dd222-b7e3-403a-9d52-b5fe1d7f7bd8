/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.filter
 * Description: 自定义解析前后端分离后传递session ID 过滤器
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-24 16:40:16:40
 */
package com.yeelight.service.user.server.filter;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.user.client.domain.LoginSessionInfo;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.exception.NotLoginException;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.utils.AuthUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;


/**
 * Desc: Jwt方式登录 过滤器
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-24 16:40:16:40
 */
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    private final JwtAuthService jwtAuthService;

    public JwtAuthenticationFilter(JwtAuthService jwtAuthService) {
        this.jwtAuthService = jwtAuthService;
    }

    /**
     * 处理过滤请求，用于认证和授权流程。
     * 从请求头中提取JWT Token，验证用户登录状态，并设置认证信息。
     * 如果没有JWT Token则直接放行。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求
     * @param response HttpServletResponse对象，用于向客户端发送响应
     * @param filterChain 过滤器链，用于继续传递请求或终止请求处理
     * @throws ServletException 如果处理请求时发生Servlet相关异常
     * @throws IOException 如果处理请求时发生IO相关异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            String token = jwtAuthService.getToken(request);

            // 如果请求头中没有jwt token信息则直接放行
            if (!StringUtils.hasLength(token)) {
                filterChain.doFilter(request, response);
                return;
            }
            // 如果请求头中有token，则进行解析，并且设置认证信息
            LoginSessionInfo loginSessionInfo = jwtAuthService.getLoginSessionByToken(UserVendorHolder.getVendor(), token);
            if (Objects.nonNull(loginSessionInfo) && Objects.nonNull(loginSessionInfo.getUser())) {
                YeelightUser user = loginSessionInfo.getUser();
                // 清空“密码”属性
                user.setPassword(null);
                // 创建验证通过的令牌对象
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
                // 设置令牌到安全上下文中
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }

            filterChain.doFilter(request, response);
        } catch (BizException | CaptchaException  | NotLoginException e) {
            // 处理业务异常、验证码异常和未登录异常
            if (Objects.nonNull(e.getCause()) && e.getCause() instanceof BizException) {
                AuthUtils.authFail(response, HttpServletResponse.SC_BAD_REQUEST, ResultCodeEnum.参数异常.getCode(), e.getCause().getMessage());
            }
            AuthUtils.authFail(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, ResultCodeEnum.服务器异常.getCode(), e.getMessage());
        }
    }
}