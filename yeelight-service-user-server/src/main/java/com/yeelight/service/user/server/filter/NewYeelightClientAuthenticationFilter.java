package com.yeelight.service.user.server.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.HttpRequestMethodNotSupportedException;

import java.io.IOException;
import java.util.Collections;

/**
 * 新版自定义客户端认证过滤器
 * 基于Spring Authorization Server的客户端认证机制
 * 完全兼容旧版YeelightClientCredentialsTokenEndpointFilter的PKCE功能
 * 
 * <AUTHOR>
 * @description: 新版客户端认证过滤器，与旧版YeelightClientCredentialsTokenEndpointFilter保持兼容
 */
@Slf4j
@Component("newYeelightClientAuthenticationFilter")
public class NewYeelightClientAuthenticationFilter extends OncePerRequestFilter {

    private final RegisteredClientRepository registeredClientRepository;
    /**
     * -- SETTER --
     *  设置是否只允许POST请求
     *  与旧版YeelightClientCredentialsTokenEndpointFilter保持一致
     */
    @Setter
    private boolean allowOnlyPost = false;

    public NewYeelightClientAuthenticationFilter(RegisteredClientRepository registeredClientRepository) {
        this.registeredClientRepository = registeredClientRepository;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // 只处理token端点的请求
        if (!isTokenEndpointRequest(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // 尝试进行客户端认证 - 与旧版YeelightClientCredentialsTokenEndpointFilter逻辑一致
            Authentication authentication = attemptAuthentication(request, response);
            
            if (authentication != null) {
                // 设置认证信息到安全上下文
                SecurityContextHolder.getContext().setAuthentication(authentication);
                log.debug("Client authentication successful for: {}", authentication.getName());
            }
            
        } catch (Exception ex) {
            log.error("Client authentication failed", ex);
            // 认证失败时，清除安全上下文
            SecurityContextHolder.clearContext();
            // 可以选择抛出异常或继续处理
            // 这里选择继续处理，让后续的认证机制处理
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 尝试进行客户端认证
     * 与旧版YeelightClientCredentialsTokenEndpointFilter的attemptAuthentication方法完全一致
     */
    private Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException {
        
        // 检查请求方法是否支持，仅支持POST请求 - 与旧版逻辑一致
        if (allowOnlyPost && !"POST".equalsIgnoreCase(request.getMethod())) {
            throw new HttpRequestMethodNotSupportedException(request.getMethod(), Collections.singletonList("POST"));
        }

        // 从请求中获取code_verifier参数 - 与旧版逻辑完全一致
        String codeVerifier = request.getParameter("code_verifier");
        if (codeVerifier != null) {
            // 获取client_id参数，并校验其是否存在
            String clientId = request.getParameter("client_id");
            if (!StringUtils.hasText(clientId)) {
                throw new BadCredentialsException("No client_id parameter");
            }
            
            // 加载客户端详情 - 使用新版RegisteredClientRepository
            RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
            if (registeredClient == null) {
                throw new BadCredentialsException("Invalid client_id: " + clientId);
            }
            
            // 返回基于预认证的Authentication对象 - 与旧版逻辑一致
            log.debug("PKCE client authentication for client: {}", clientId);
            return new PreAuthenticatedAuthenticationToken(
                registeredClient.getClientId(), 
                null,
                    // 新版中权限通过不同方式管理
                Collections.emptyList()
            );
        }
        
        // 如果不符合PKCE条件，返回null让其他认证机制处理
        return null;
    }

    /**
     * 判断是否是token端点请求
     */
    private boolean isTokenEndpointRequest(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        return requestUri != null && requestUri.endsWith("/oauth/token");
    }

    /**
     * 检查当前请求是否需要进行客户端认证
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        // 只对token端点进行过滤
        return !isTokenEndpointRequest(request);
    }
}
