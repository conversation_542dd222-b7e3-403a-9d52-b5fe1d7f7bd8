package com.yeelight.service.user.server.filter;

import com.yeelight.service.user.client.token.SmsAuthenticationToken;
import lombok.Setter;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.util.Assert;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 短信登录认证过滤器
 * <p>
 * 参考 {@link UsernamePasswordAuthenticationFilter}
 * </p>
 * <AUTHOR>
 */
public class SmsAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    public static final String SUNNY_SMS_PHONE_NUMBER_KEY = "phone_number";

    private String phoneNumberParameter = SUNNY_SMS_PHONE_NUMBER_KEY;

    @Setter
    private boolean postOnly = true;

    /**
     * 仅匹配 [POST /authentication/phone_number]
     */
    public SmsAuthenticationFilter() {
        super(new AntPathRequestMatcher("/authentication/phone_number", "POST"));
    }

    /**
     * 尝试进行用户认证。
     * 该方法首先检查请求方法是否为POST，如果不是，则抛出认证异常。
     * 然后从请求中获取电话号码，创建一个SmsAuthenticationToken，并将其传给认证管理器进行认证。
     *
     * @param request  HttpServletRequest，代表客户端的HTTP请求。
     * @param response HttpServletResponse，代表服务器对客户端的响应。
     * @return Authentication，认证结果对象。
     * @throws AuthenticationException 如果认证过程中出现异常。
     */
    @Override
    public Authentication attemptAuthentication(HttpServletRequest request,
                                                HttpServletResponse response) throws AuthenticationException {
        // 检查是否仅支持POST请求，如果不是则抛出异常
        if (postOnly && !HttpMethod.POST.name().equalsIgnoreCase(request.getMethod())) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }
        // 从请求中获取电话号码，并进行处理
        String phoneNumber = obtainPhoneNumber(request);

        // 如果电话号码为空，则设置为空字符串
        if (phoneNumber == null) {
            phoneNumber = "";
        }

        // 去除电话号码两端的空白字符
        phoneNumber = phoneNumber.trim();

        // 创建一个SmsAuthenticationToken对象，用于认证
        SmsAuthenticationToken authRequest = new SmsAuthenticationToken(phoneNumber);

        // 允许子类设置"details"属性
        setDetails(request, authRequest);

        // 调用认证管理器进行认证
        return this.getAuthenticationManager().authenticate(authRequest);
    }


    /**
     * 从HTTP请求中获取电话号码。
     *
     * @param request HttpServletRequest对象，代表一个HTTP请求。
     * @return 返回从请求中获取到的电话号码，如果请求中没有提供电话号码，则返回null。
     */
    protected String obtainPhoneNumber(HttpServletRequest request) {
        // 通过请求参数名获取电话号码参数的值
        return request.getParameter(phoneNumberParameter);
    }


    /**
     * 设置认证请求的详细信息。
     * 该方法通过从HTTP请求中提取信息，来构建并设置认证请求的详细信息。
     *
     * @param request HttpServletRequest对象，代表客户端的HTTP请求。
     * @param authRequest SmsAuthenticationToken对象，代表正在进行认证的请求。
     */
    protected void setDetails(HttpServletRequest request, SmsAuthenticationToken authRequest) {
        // 使用authenticationDetailsSource从HTTP请求构建认证详情，并设置到authRequest中
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    public void setPhoneNumberParameter(String phoneNumberParameter) {
        Assert.hasText(phoneNumberParameter, "Phone Number parameter must not be empty or null");
        this.phoneNumberParameter = phoneNumberParameter;
    }

    public final String getPhoneNumberParameter() {
        return phoneNumberParameter;
    }

}
