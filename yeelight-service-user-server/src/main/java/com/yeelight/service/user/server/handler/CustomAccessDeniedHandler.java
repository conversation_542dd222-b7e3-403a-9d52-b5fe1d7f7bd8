/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.handler
 * Description: 自定义访问权限拒绝处理器
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-18 17:22:17:22
 */
package com.yeelight.service.user.server.handler;

import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.AuthUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-18 17:22:17:22
 */
public class CustomAccessDeniedHandler implements AccessDeniedHandler {
    /**
     * 处理访问权限拒绝的情况。
     * 当用户请求访问受保护的资源但缺乏必要的权限时，此方法将被调用。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     * @param accessDeniedException 访问被拒绝时抛出的异常对象，包含拒绝访问的具体信息。
     * @throws IOException 如果在处理过程中发生I/O错误。
     */
    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException {
        // 判断是否为前后端分离的请求
        if(AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            // 对于前后端分离的请求，使用自定义的未授权响应格式返回
            AuthUtils.authFail(response, ResultCodeEnum.未授权.getIntegerCode(), accessDeniedException.getMessage());
        } else {
            // 对于非前后端分离的请求，使用HTTP状态码401 Unauthorized响应
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED,accessDeniedException.getMessage());
        }
    }
}
