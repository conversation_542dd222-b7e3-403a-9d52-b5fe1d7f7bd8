package com.yeelight.service.user.server.handler;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 登录失败处理器（废弃，自定义的不好，会导致oauth异常）
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomAuthenticationFailureHandler implements AuthenticationFailureHandler {
    private final RequestCache requestCache = new HttpSessionRequestCache();
    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    private String defaultFailureUrl;
    private boolean forwardToDestination = false;
    @Setter
    private boolean allowSessionCreation = true;
    /**
     * -- SETTER --
     *  Allows overriding of the behaviour when redirecting to a target URL.
     */
    @Setter
    private RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    public CustomAuthenticationFailureHandler() {
    }

    public CustomAuthenticationFailureHandler(String defaultFailureUrl) {
        setDefaultFailureUrl(defaultFailureUrl);
    }

    /**
     * 处理认证失败的逻辑。如果设置了重定向或转发到 {@code defaultFailureUrl}，则进行重定向或转发，
     * 否则返回 401 错误代码。
     * 在进行重定向或转发前，会将异常信息和用户名保存到会话中，以便在目标视图中使用。
     * 如果是前后端分离的请求，会响应一个包含认证失败信息的JSON数据。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     * @param exception 认证异常对象，封装了认证失败的详细信息。
     * @throws IOException 如果在处理过程中发生IO错误。
     * @throws ServletException 如果在处理过程中触发了Servlet相关的异常。
     */
    @Override
    public void onAuthenticationFailure(HttpServletRequest request,
                                        HttpServletResponse response, AuthenticationException exception)
            throws IOException, ServletException {
        // 获取当前的认证信息和用户名，并准备异常信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = request.getParameter(SecurityConstants.USERNAME_KEY);
        String message = I18nUtil.getMessage("ResultCode.Common.ILLEGAL_REQUEST");
        if (Objects.nonNull(exception)) {
            message = Optional.ofNullable(exception.getLocalizedMessage()).orElse(exception.getMessage());
        }

        // 将用户名和认证异常信息保存到session中
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.setAttribute(SecurityConstants.USERNAME_KEY, username);
            session.setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, message);
        }

        // 尝试获取之前被缓存的请求
        SavedRequest savedRequest = this.requestCache.getRequest(request, response);

        // 判断请求是否来自前后端分离的架构
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)  || isThirdPartyFrontBackendSeparatedRequest(request)) {
            handleFrontendBackendSeparatedRequest(request, response, username, message);
        } else {
            // 对于非前后端分离的请求，根据配置进行重定向或转发到指定的失败处理URL
            if (defaultFailureUrl == null) {
                String redirectUrl = gatewayOauthConfig.getLoginPageUrl(request) + "?" + SecurityConstants.USERNAME_KEY + "=" + username;
                redirectStrategy.sendRedirect(request, response, redirectUrl);
            } else {
                // 在进行重定向或转发前，保存异常信息
                saveException(request, exception);

                // 根据配置决定是转发还是重定向到失败处理URL
                if (forwardToDestination) {
                    log.debug("Forwarding to {}", defaultFailureUrl);

                    request.getRequestDispatcher(defaultFailureUrl)
                            .forward(request, response);
                }
                else {
                    log.debug("Redirecting to {}", defaultFailureUrl);
                    redirectStrategy.sendRedirect(request, response, defaultFailureUrl);
                }
            }
        }
    }

    /**
     * 处理前后端分离的请求。对于前后端分离的请求，返回JSON格式的认证失败信息。
     *
     * @param request HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     * @param username 用户名。
     * @param message 认证失败的详细信息。
     * @throws IOException 如果在处理过程中发生IO错误。
     */
    private void handleFrontendBackendSeparatedRequest(HttpServletRequest request, HttpServletResponse response, String username, String message) throws IOException {
        // 对于前后端分离的请求，返回JSON格式的认证失败信息
        log.info("auth authFail");
        Map<String, Object> data = new HashMap<>(2);
        data.put(SecurityConstants.USERNAME_KEY, username);
        String redirectUrl = defaultFailureUrl;
        if (redirectUrl == null) {
            redirectUrl = gatewayOauthConfig.getLoginPageUrl(request) + "?" + SecurityConstants.USERNAME_KEY + "=" + username;
        }
        data.put(SecurityConstants.REDIRECT_URL_KEY, redirectUrl);
        // 对于第三方前后端分离的请求，返回JSON格式的认证失败信息
        if (isThirdPartyFrontBackendSeparatedRequest(request)) {
            // 给 redirectUrl 添加错误信息, 需要判断原来是否有参数并处理
            redirectUrl = redirectUrl + (redirectUrl.contains("?") ? "&" : "?") + "error=" + message;
            redirectStrategy.sendRedirect(request, response, redirectUrl);
        }
        AuthUtils.authFail(response, ResultCodeEnum.认证失败.getIntegerCode(), message, data);
    }


    /**
     * 缓存 {@code AuthenticationException} 以用于视图呈现。
     * <p>
     * 如果 {@code forwardToDestination} 被设置为 true，将会使用请求作用域来缓存异常，
     * 否则，它会尝试将异常存储在会话中。如果没有会话并且 {@code allowSessionCreation} 设置为 {@code true}，
     * 则会创建一个会话。否则，异常将不会被存储。
     *
     * @param request HttpServletRequest对象，用于访问请求和会话信息。
     * @param exception AuthenticationException对象，表示认证过程中遇到的异常。
     */
    protected final void saveException(HttpServletRequest request,
                                       AuthenticationException exception) {
        // 如果设置为直接转发，将异常信息存入请求作用域
        if (forwardToDestination) {
            request.setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, exception);
        }
        else {
            // 尝试获取会话，如果不存在并且允许创建会话，则创建新会话
            HttpSession session = request.getSession(false);

            if (session != null || allowSessionCreation) {
                // 将异常信息存入会话
                request.getSession().setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION,
                        exception);
            }
        }
    }


    /**
     * 将用作故障目标的 URL
     *
     * @param defaultFailureUrl the failure URL, for example "/loginFailed.jsp".
     */
    public void setDefaultFailureUrl(String defaultFailureUrl) {
        Assert.isTrue(UrlUtils.isValidRedirectUrl(defaultFailureUrl),
                () -> "'" + defaultFailureUrl + "' is not a valid redirect URL");
        this.defaultFailureUrl = defaultFailureUrl;
    }

    private boolean isThirdPartyFrontBackendSeparatedRequest(HttpServletRequest request) {
        String state = request.getParameter(Constants.STATE_KEY);
        if (com.yeelight.service.framework.util.StringUtils.isNotBlank(state)) {
            return state.contains(Constants.FRONTEND_BACKEND_SEPARATED_REQUEST_THIRD_PARTY_STATE_PREFIX);
        }
        return false;
    }

    /**
     * 判断是否使用转发。
     *
     * @return boolean - 如果转发到目的地，则返回true；否则返回false。
     */
    protected boolean isUseForward() {
        return forwardToDestination;
    }


    /**
     * 设置是否使用转发到失败目标URL而不是重定向。
     * 如果设置为<tt>true</tt>，则会执行到失败目标URL的转发。
     * 默认值为<tt>false</tt>，即默认使用重定向。
     *
     * @param forwardToDestination 指定是否使用转发而不是重定向。
     *                             <tt>true</tt>表示使用转发，<tt>false</tt>表示使用重定向。
     */
    public void setUseForward(boolean forwardToDestination) {
        // 更新是否使用转发的设置
        this.forwardToDestination = forwardToDestination;
    }

    protected RedirectStrategy getRedirectStrategy() {
        return redirectStrategy;
    }

    protected boolean isAllowSessionCreation() {
        return allowSessionCreation;
    }
}
