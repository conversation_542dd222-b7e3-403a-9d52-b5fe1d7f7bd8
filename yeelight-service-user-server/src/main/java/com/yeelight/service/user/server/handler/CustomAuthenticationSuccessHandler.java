package com.yeelight.service.user.server.handler;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.DefaultSavedRequest;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 登录认证成功处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {
    private RequestCache requestCache = new HttpSessionRequestCache();

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    @Resource
    private JwtAuthService jwtAuthService;

    /**
     * 认证成功时的处理逻辑。
     * 当用户成功通过认证系统验证后，会执行此方法进行后续处理，例如设置安全上下文、记录登录信息、
     * 判断并处理重定向逻辑等。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求
     * @param response HttpServletResponse对象，用于向客户端发送响应
     * @param authentication 认证对象，包含认证成功的用户信息
     * @throws IOException 如果处理过程中发生IO错误
     * @throws ServletException 如果处理过程中发生Servlet相关错误
     */
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        // 设置安全上下文中的认证信息
        SecurityContext securityContext = SecurityContextHolder.getContext();
        if (Objects.nonNull(securityContext)) {
            securityContext.setAuthentication(authentication);
        }
        // 记录用户登录时的环境信息
        AuthUtils.logUserLoginEnvironmentInfo(request);

        // 认证成功后的重定向逻辑
        String redirectUrl = parseRedirectUrl(request, response);

        // 清除认证相关的属性
        this.clearAuthenticationAttributes(request);

        // 判断请求是否来自前后端分离的架构
        if (AuthUtils.isFrontendBackendSeparatedRequest(request) || isThirdPartyFrontBackendSeparatedRequest(request)) {
            handleFrontendBackendSeparatedRequest(request, response, authentication, redirectUrl);
        } else {
            // 对于非前后端分离的请求，根据配置重定向到目标URL或默认URL
            String targetUrlParameter = getTargetUrlParameter();
            if (isAlwaysUseDefaultTargetUrl()
                    || (targetUrlParameter != null && StringUtils.hasText(request
                    .getParameter(targetUrlParameter)))) {
                requestCache.removeRequest(request, response);
                super.onAuthenticationSuccess(request, response, authentication);

                return;
            }

            // 执行重定向逻辑
            log.info("Redirecting to DefaultSavedRequest Url : {}", redirectUrl);
            this.getRedirectStrategy().sendRedirect(request, response, redirectUrl);
        }
    }

    /**
     * 解析重定向URL。
     * 该方法用于解析重定向URL，如果请求中包含了保存的请求，则返回保存的请求URL，否则返回首页URL。
     *
     * @param request HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     * @return String 重定向URL。
     */
    private String parseRedirectUrl(HttpServletRequest request, HttpServletResponse response) {
        String redirectUrl = gatewayOauthConfig.getIndexPageUrl(request);
        SavedRequest savedRequest = this.requestCache.getRequest(request, response);
        if (savedRequest == null  || !isValidRedirectUrl(request, savedRequest.getRedirectUrl())) {
            // 如果没有保存的请求或保存的请求URL无效，则跳转到首页
            log.info("SavedRequest is null");
        } else {
            // 处理有效的重定向请求
            log.info("SavedRequest RedirectUrl is:{}", savedRequest.getRedirectUrl());
            log.info("SavedRequest Parameters is:{}", JSON.toJSONString(savedRequest.getParameterMap()));
            String targetUrlParameter = this.getTargetUrlParameter();
            if (!this.isAlwaysUseDefaultTargetUrl() && (targetUrlParameter == null || !StringUtils.hasText(request.getParameter(targetUrlParameter)))) {
                redirectUrl = savedRequest.getRedirectUrl();
                // todo 这里可能会有问题
                // 根据请求头中的"x-forwarded-prefix"处理重定向URL
                if (savedRequest instanceof DefaultSavedRequest) {
                    List<String> headers = savedRequest.getHeaderValues("x-forwarded-prefix");
                    if (CollectionUtils.isNotEmpty(headers) && com.yeelight.service.framework.util.StringUtils.isNotBlank(gatewayOauthConfig.getPrefixHost(request))) {
                        String[] forwardedPrefix = headers.getFirst().split(",");
                        redirectUrl =  gatewayOauthConfig.getUrl(request, forwardedPrefix[0] + ((DefaultSavedRequest) savedRequest).getRequestURI(),
                                ((DefaultSavedRequest) savedRequest).getQueryString());
                    }
                }
            }
        }
        return redirectUrl;
    }

    /**
     * 处理前后端分离的请求。对于前后端分离的请求，返回JSON格式的认证成功信息。
     *
     * @param request HttpServletRequest对象，代表客户端的HTTP请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     * @param authentication 认证对象，包含认证成功的用户信息。
     * @param redirectUrl 重定向URL。
     * @throws IOException 如果在处理过程中发生IO错误。
     */
    private void handleFrontendBackendSeparatedRequest(HttpServletRequest request, HttpServletResponse response, Authentication authentication, String redirectUrl) throws IOException {
        // 对于前后端分离的请求，返回JSON格式的认证成功信息和令牌
        log.info("auth authSuccess");
        Map<String, Object> data = new HashMap<>(2);
        data.put(SecurityConstants.JWT_AUTH_TOKEN_KEY, jwtAuthService.generateToken(UserVendorHolder.getVendor(), authentication.getName(), request.getSession().getId()));
        data.put(SecurityConstants.REDIRECT_URL_KEY, redirectUrl);
        // 对于第三方前后端分离的请求，返回JSON格式的认证成功信息和重定向URL
        if (isThirdPartyFrontBackendSeparatedRequest(request)) {
            // 给 redirectUrl 添加jwt token, 需要判断原来是否有参数并处理
             redirectUrl = redirectUrl
                     + (redirectUrl.contains("?") ? "&" : "?")
                     + SecurityConstants.JWT_AUTH_TOKEN_KEY
                     + "=" + data.get(SecurityConstants.JWT_AUTH_TOKEN_KEY)
             + "&" + Constants.STATE_KEY + "=" + Constants.FRONTEND_BACKEND_SEPARATED_REQUEST_THIRD_PARTY_STATE_PREFIX + request.getParameter(Constants.STATE_KEY)
             + "&" + SecurityConstants.REDIRECT_URL_KEY + "=" + redirectUrl;
            // 执行重定向逻辑
            log.info("Redirecting to DefaultSavedRequest Url: {}", redirectUrl);
            this.getRedirectStrategy().sendRedirect(request, response, redirectUrl);
        }
        AuthUtils.authSuccess(response, data);
    }

    @Override
    public void setRequestCache(RequestCache requestCache) {
        this.requestCache = requestCache;
    }

    /**
     * 验证重定向URL是否有效。
     * 该方法主要用于检查给定的重定向URL是否为预定义的安全URL之一，或者是否为特定的错误页面URL。
     *
     * @param request HttpServletRequest对象，用于获取请求相关信息。
     * @param redirectUrl 需要验证的重定向URL。
     * @return 返回true如果重定向URL是预定义的安全URL之一或不是特定的错误URL；否则返回false。
     */
    private boolean isValidRedirectUrl(HttpServletRequest request, String redirectUrl) {
        // 首先检查重定向URL是否为空
        if (!StringUtils.hasText(redirectUrl)) {
            return false;
        }
        // 检查重定向URL是否为一组预定义的安全URL之一
        if (redirectUrl.contains(gatewayOauthConfig.getIndexPageUrl(request)) ||
            redirectUrl.contains(gatewayOauthConfig.getLoginPageUrl(request)) ||
            redirectUrl.contains(gatewayOauthConfig.getRegisterPageUrl(request)) ||
            redirectUrl.contains(gatewayOauthConfig.getGrantPageUrl(request)) ||
            redirectUrl.contains(gatewayOauthConfig.getResetPasswordUrl(request))
        )
        {
            return true;
        }
        // 检查重定向URL是否为特定的错误URL
        return !redirectUrl.contains(gatewayOauthConfig.getPrefixHost(request)) || !redirectUrl.contains("/error");
    }

    private boolean isThirdPartyFrontBackendSeparatedRequest(HttpServletRequest request) {
        String state = request.getParameter(Constants.STATE_KEY);
        if (com.yeelight.service.framework.util.StringUtils.isNotBlank(state)) {
            return state.contains(Constants.FRONTEND_BACKEND_SEPARATED_REQUEST_THIRD_PARTY_STATE_PREFIX);
        }
        return false;
    }

}
