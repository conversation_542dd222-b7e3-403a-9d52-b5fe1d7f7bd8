package com.yeelight.service.user.server.handler;

import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 登出成功处理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomLogoutSuccessHandler extends SimpleUrlLogoutSuccessHandler {
    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    @Resource
    private TokenService tokenService;

    @Resource
    private UserSessionManagementService userSessionManagementService;

    /**
     * 当用户登出成功时的处理逻辑。
     *
     * @param request  HttpServletRequest对象，用于获取请求信息。
     * @param response HttpServletResponse对象，用于响应客户端请求。
     * @param authentication Authentication对象，表示当前用户的认证信息。
     * @throws IOException 如果发生输入/输出错误。
     * @throws ServletException 如果发生Servlet相关异常。
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        // 清理access_token，从请求中获取token并调用tokenService进行登出操作
        String token = request.getParameter(YeelightOAuth2AccessToken.ACCESS_TOKEN);
        tokenService.logoutCurrentToken(token);

        // 获取并处理登出后的重定向路径
        String followUp = request.getParameter("followUp");
        // 设置默认的重定向路径，如果followUp为空，则使用配置的登录页面作为重定向地址
        String redirectUrl =  StringUtils.defaultIfBlank(followUp, gatewayOauthConfig.getLoginPageUrl(request));

        // 清理用户会话
        userSessionManagementService.expireSession(request.getRequestedSessionId());

        log.info("==> logout success");

        // 判断前后端是否分离，进行不同的处理
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            // 前后端分离模式下的处理：返回给前端登出成功的信息及重定向地址
            Map<String, Object> data = new HashMap<>(2);
            data.put(SecurityConstants.REDIRECT_URL_KEY, redirectUrl);
            AuthUtils.authSuccess(response, "logout success");
        } else {
            // 非前后端分离模式下的处理：重定向到默认的目标URL
            log.info("Redirect to {}", redirectUrl);
            super.setDefaultTargetUrl(redirectUrl);
            // 调用父类的登出成功处理方法
            super.onLogoutSuccess(request, response, authentication);
        }
    }
}
