package com.yeelight.service.user.server.handler;

import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.utils.AuthUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 三方登录失败处理器
 * <AUTHOR>
 */
@Component
public class CustomThirdpartyAuthenticationFailureHandler implements AuthenticationFailureHandler {
    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();
    private final RequestCache requestCache = new HttpSessionRequestCache();
    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    /**
     * 处理认证失败的逻辑。
     * 当用户认证失败时，这个方法将被调用。它将根据不同的场景处理失败的响应，例如，
     * 如果是前后端分离的请求，将错误信息返回给前端；否则，重定向到错误页面。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求
     * @param response HttpServletResponse对象，用于向客户端发送HTTP响应
     * @param exception 认证异常对象，封装了认证失败的详细信息
     * @throws IOException 如果在处理HTTP响应时发生IO错误
     */
    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException {
        // 获取认证错误的描述信息
        String error = request.getParameter("error_description");
        // 构造错误页面的重定向URL，附带错误信息
        String redirectUrl =  gatewayOauthConfig.getErrorUrl(request) + "?error=" + error;
        // 尝试获取当前会话，可能为null
        HttpSession session = request.getSession(false);
        // 尝试从请求缓存中获取之前保存的请求
        SavedRequest savedRequest = this.requestCache.getRequest(request, response);

        // 如果会话存在，将认证异常保存到会话中
        if (session != null) {
            session.setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, exception);
        }

        // 判断是否为前后端分离的请求，并分别处理
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            // 向响应中写入认证失败的数据，供前端处理
            Map<String, Object> data = new HashMap<>(2);
            data.put(SecurityConstants.REDIRECT_URL_KEY, redirectUrl);
            AuthUtils.authFail(response, error, data);
        } else {
            // 重定向到错误页面
            redirectStrategy.sendRedirect(request, response, redirectUrl);
        }
    }
}
