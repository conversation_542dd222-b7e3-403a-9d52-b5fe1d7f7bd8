package com.yeelight.service.user.server.handler;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.server.config.GatewayOauthConfig;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.ResultCodeEnum;
import com.yeelight.service.user.server.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 登录失败处理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class EmailCodeAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    @Resource
    private GatewayOauthConfig gatewayOauthConfig;

    /**
     * 处理认证失败的逻辑。
     * 当用户认证失败时，根据请求的类型（前后端分离或非分离），决定响应的方式。
     * 对于前后端分离的请求，返回JSON格式的认证失败信息；对于非分离的请求，重定向到登录页面，并附带错误信息。
     *
     * @param request  HttpServletRequest对象，代表客户端的HTTP请求
     * @param response HttpServletResponse对象，用于向客户端发送响应
     * @param exception 认证异常对象，包含认证失败的详细信息
     * @throws IOException 如果处理过程中发生IO错误
     */
    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                    AuthenticationException exception) throws IOException {
        // 初始化默认的异常信息
        String message = I18nUtil.getMessage("ResultCode.Common.ILLEGAL_REQUEST");
        // 尝试从请求中获取邮箱参数
        String email = request.getParameter(SecurityConstants.EMAIL_KEY);
        // 如果存在异常信息，则使用异常信息覆盖默认信息
        if (Objects.nonNull(exception)) {
            message = Optional.ofNullable(exception.getLocalizedMessage()).orElse(exception.getMessage());
        }

        // 判断请求是否为前后端分离模式
        if (AuthUtils.isFrontendBackendSeparatedRequest(request)) {
            // 前后端分离模式下，返回JSON格式的认证失败信息
            log.info("auth authFail");
            Map<String, Object> data = new HashMap<>(2);
            data.put(SecurityConstants.EMAIL_KEY, email);
            AuthUtils.authFail(response, ResultCodeEnum.认证失败.getIntegerCode(), message, data);
        } else {
            // 非分离模式下，将用户邮箱和认证失败信息保存到会话中，并重定向到登录页面
            HttpSession session = request.getSession(false);
            if (session != null) {
                session.setAttribute(SecurityConstants.USERNAME_KEY, email);
                session.setAttribute(WebAttributes.AUTHENTICATION_EXCEPTION, message);
            }

            // 构造重定向URL，并进行重定向
            String redirectUrl =  gatewayOauthConfig.getLoginPageUrl(request) + "?type=email&username=" + email;
            redirectStrategy.sendRedirect(request, response, redirectUrl);
        }
    }

}
