package com.yeelight.service.user.server.listener;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.user.client.domain.YeelightUserAccountExample;
import com.yeelight.service.user.client.domain.YeelightUserBankExample;
import com.yeelight.service.user.client.domain.YeelightUserExtendExample;
import com.yeelight.service.user.client.dto.YeelightUserEvent;
import com.yeelight.service.user.client.dto.YeelightUserEventDto;
import com.yeelight.service.user.client.query.UserLevelLogQuery;
import com.yeelight.service.user.client.query.UserLevelRelQuery;
import com.yeelight.service.user.client.query.UserWithdrawsQuery;
import com.yeelight.service.user.client.service.*;
import com.yeelight.service.user.server.mapper.user.SocialUserAuthMapper;
import com.yeelight.service.user.client.service.UserLevelLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Objects;

/**
 * @program: yeelight-service-user
 * @description:
 * @author: lixiaodong
 * @create: 2022-04-18 18:25
 **/
@Component
@ConditionalOnBean(name = "userEventKafkaTemplate")
@Slf4j
public class YeelightUserEventListener {
    @Resource
    private YeelightUsersExtendService yeelightUsersExtendService;
    @Resource
    private YeelightUserBankService yeelightUserBankService;
    @Resource
    private YeelightUserAccountService yeelightUserAccountService;
    @Resource
    private UserLevelLogService userLevelLogService;
    @Resource
    private UserLevelRelService userLevelRelService;
    @Resource
    private UserEarningsService userEarningsService;
    @Resource
    private UserWithdrawsService userWithdrawsService;
    @Resource
    private SocialUserAuthMapper socialUserAuthMapper;

    /**
     * 监听Kafka中的用户事件消息，并根据事件类型执行相应的处理逻辑。
     *
     * @param record 用户事件的消息内容，格式应为可解析为{@link YeelightUserEvent}的字符串。
     * @param ack 消息确认机制，用于确认消息已被成功处理。
     */
    @KafkaListener(topics = YeelightUserEvent.TOPIC_NAME + "${kafka.topic.suffix:}", groupId = YeelightUserEvent.CONSUMER_GROUP_PREFIX + "USER")
    public void eventListener(String record, Acknowledgment ack) {
        // 如果消息为空或只包含空白字符，则直接返回不进行处理
        if (StringUtils.isBlank(record)) {
            return;
        }
        // 记录开始消费消息的日志
        log.info("Thread {}, 用户事件消息开始消费 参数:{}", Thread.currentThread().getName(), record);

        // 解析消息内容为用户事件对象
        YeelightUserEvent<YeelightUserEventDto> event = YeelightUserEvent.parse(record, YeelightUserEventDto.class);
        if (Objects.nonNull(event)) {
            // 根据事件类型执行相应的处理逻辑
            switch (event.getEventType()) {
                case REMOVED:
                    // 处理用户被移除的事件
                    YeelightUserEventDto eventDto = event.getPayload();
                    UserVendorHolder.setVendor(eventDto.getVendor());
                    Long yeelightId = eventDto.getId();

                    // 删除与该用户相关的所有数据
                    yeelightUserAccountService.deleteByExample(YeelightUserAccountExample.builder().yeelightUserId(yeelightId).build());
                    yeelightUserBankService.deleteByExample(YeelightUserBankExample.builder().yeelightUserId(yeelightId).build());
                    yeelightUsersExtendService.deleteByExample(YeelightUserExtendExample.builder().yeelightUserId(yeelightId).build());
                    userLevelLogService.deleteByExample(UserLevelLogQuery.builder().yeelightUserId(yeelightId).build());
                    userLevelRelService.deleteByExample(UserLevelRelQuery.builder().yeelightUserId(yeelightId).build());
                    userEarningsService.removeUserEarningByUserId(yeelightId);
                    userWithdrawsService.deleteByExample(UserWithdrawsQuery.builder().yeelightUserId(yeelightId).build());
                    socialUserAuthMapper.deleteSocialUserAuth(yeelightId);
                    break;
                default:
                    // 对于未处理的事件类型，不做任何操作
                    break;
            }
            // 确认消息已被处理
            ack.acknowledge();
        }
    }
}
