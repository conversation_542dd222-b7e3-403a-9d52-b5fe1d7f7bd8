package com.yeelight.service.user.server.mapper.oauth;

import com.yeelight.service.framework.util.MyMapper;
import com.yeelight.service.user.client.domain.ClientDetail;

/**
 * <AUTHOR>
 */
public interface ClientDetailMapper extends MyMapper<ClientDetail> {

	/**
	 * 根据客户端ID查找唯一的客户端详细信息。
	 *
	 * @param clientId 客户端的唯一标识符。
	 * @return 返回匹配的客户端详细信息对象。
	 */
	ClientDetail findOneByClientId(String clientId);

	/**
	 * 根据客户端ID删除客户端详细信息。
	 *
	 * @param clientId 需要被删除的客户端的唯一标识符。
	 */
	void deleteByClientId(String clientId);

	/**
	 * 创建一个新的客户端详细信息。
	 *
	 * @param clientDetail 包含客户端详细信息的对象。
	 */
	void createClient(ClientDetail clientDetail);

	/**
	 * 更新现有的客户端详细信息。
	 *
	 * @param clientDetail 包含需要更新的客户端详细信息的对象。
	 */
	void updateClient(ClientDetail clientDetail);

}