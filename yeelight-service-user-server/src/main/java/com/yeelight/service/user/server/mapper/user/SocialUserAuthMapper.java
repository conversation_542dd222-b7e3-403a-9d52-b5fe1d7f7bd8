package com.yeelight.service.user.server.mapper.user;

import com.yeelight.service.framework.util.MyMapper;
import com.yeelight.service.user.server.domain.SocialUserAuth;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SocialUserAuthMapper extends MyMapper<SocialUserAuth> {
    /**
     * 根据社交用户ID查询唯一的社交用户认证信息。
     *
     * @param socialUserId 社交用户的唯一标识ID
     * @return SocialUserAuth 返回匹配的社交用户认证信息，如果没有找到则返回null。
     */
    SocialUserAuth selectSocialUserAuthOne(Long socialUserId);

    /**
     * 根据Yeelight ID查询所有的社交用户认证信息。
     *
     * @param yeelightId Yeelight设备的唯一标识ID
     * @return List<SocialUserAuth> 返回匹配的所有社交用户认证信息列表，如果没有找到则返回空列表。
     */
    List<SocialUserAuth> selectSocialUserAuthsWithYeelightId(Long yeelightId);

    /**
     * 根据Yeelight ID和来源查询唯一的社交用户认证信息。
     *
     * @param yeelightId Yeelight设备的唯一标识ID
     * @param source 来源标识
     * @return SocialUserAuth 返回匹配的社交用户认证信息，如果没有找到则返回null。
     */
    SocialUserAuth selectSocialUserAuthsWithYeelightIdAndSource(Long yeelightId, String source);

    /**
     * 根据Yeelight ID和社交用户ID删除一条社交用户认证信息。
     *
     * @param yeelightId Yeelight设备的唯一标识ID
     * @param socialUserId 社交用户的唯一标识ID
     */
    void deleteSocialUserAuthOne(Long yeelightId, Long socialUserId);

    /**
     * 根据社交用户ID删除一条社交用户认证信息。
     *
     * @param socialUserId 社交用户的唯一标识ID
     */
    void deleteSocialUserAuth(Long socialUserId);

    /**
     * 根据Yeelight ID删除所有关联的社交用户认证信息。
     *
     * @param yeelightId Yeelight设备的唯一标识ID
     */
    void deleteSocialUserAuthByYeelightId(Long yeelightId);
}
