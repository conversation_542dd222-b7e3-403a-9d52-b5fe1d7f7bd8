package com.yeelight.service.user.server.mapper.user;

import com.yeelight.service.framework.util.MyMapper;
import com.yeelight.service.user.server.domain.SocialUser;

import java.util.List;

/**
 * @description:  社交用户Mapper接口
 * <AUTHOR>
 */
public interface SocialUserMapper extends MyMapper<SocialUser> {
    /**
     * 根据UUID和来源源查询一个社交用户。
     * @param uuid 用户的唯一标识符。
     * @param source 用户的来源。
     * @return 返回匹配的社交用户对象，如果没有找到则返回null。
     */
    SocialUser selectSocialUserOne(String uuid, String source);

    /**
     * 根据Yeelight ID查询社交用户列表。
     * @param yeelightId Yeelight设备的ID。
     * @return 返回与指定Yeelight ID相关联的社交用户列表，如果没有找到则返回空列表。
     */
    List<SocialUser> selectSocialUsersByYeelightId(Long yeelightId);

    /**
     * 根据Yeelight ID和来源源查询一个社交用户。
     * @param yeelightId Yeelight设备的ID。
     * @param source 用户的来源。
     * @return 返回匹配的社交用户对象，如果没有找到则返回null。
     */
    SocialUser selectSocialUserOneByYeelightIdAndSource(Long yeelightId, String source);

    /**
     * 根据Union ID查询一个社交用户。
     * @param unionId 用户的联合ID。
     * @return 返回匹配的社交用户对象，如果没有找到则返回null。
     */
    SocialUser selectSocialUserOneByUnionId(String unionId);
}
