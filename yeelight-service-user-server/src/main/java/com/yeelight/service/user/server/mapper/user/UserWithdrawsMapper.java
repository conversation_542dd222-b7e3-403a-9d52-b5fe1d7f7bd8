package com.yeelight.service.user.server.mapper.user;

import com.yeelight.service.framework.util.MyMapper;
import com.yeelight.service.user.client.domain.UserWithdraws;
import com.yeelight.service.user.client.dto.UserWithdrawDetailDto;
import com.yeelight.service.user.client.query.UserWithdrawsDetailQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserWithdrawsMapper extends MyMapper<UserWithdraws> {
    /**
     * 提现详细信息分页
     *
     * @param query 查询条件
     * @return 提现详细信息
     */
    List<UserWithdrawDetailDto> pageWithdrawsDetail(UserWithdrawsDetailQuery query);
}