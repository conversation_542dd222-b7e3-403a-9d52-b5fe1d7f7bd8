package com.yeelight.service.user.server.mapper.user;

import com.yeelight.service.framework.util.MyMapper;
import com.yeelight.service.user.client.domain.YeelightUserEntity;
import com.yeelight.service.user.client.domain.YeelightUserExample;
import com.yeelight.service.user.server.domain.YeelightUser;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 用户Mapper
 *
 * <AUTHOR>
 * @date 2020/8/23
 */
public interface YeelightUserMapper extends MyMapper<YeelightUser> {
    /**
     * 获取指定属性记录数
     *
     * @param condition 属性条件，键值对表示属性名和属性值
     * @return 满足条件的记录数
     */
    Integer getCountByProperty(HashMap<String, Object> condition);

    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 找到的用户，若不存在则返回null
     */
    YeelightUser findUserById(Long id);

    /**
     * 根据ID列表查找用户
     *
     * @param ids 用户ID列表
     * @return 找到的用户列表，若不存在则返回空列表
     */
    List<YeelightUser> findUserByIds(@Param("ids") List<Long> ids);

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 找到的用户，若不存在则返回null
     */
    YeelightUser findUserByUsername(String username);

    /**
     * 根据用户名删除用户
     *
     * @param username 用户名
     */
    void removeUserByUsername(String username);

    /**
     * 根据电话号码查找用户
     *
     * @param phoneNumber 电话号码
     * @return 找到的用户对象，如果没有找到返回null
     */
    YeelightUser findUserByPhoneNumber(String phoneNumber);

    /**
     * 根据电子邮件查找用户
     *
     * @param email 电子邮件地址
     * @return 找到的用户对象，如果没有找到返回null
     */
    YeelightUser findUserByEmail(String email);

    /**
     * 根据账户名查找用户
     *
     * @param account 账户名
     * @return 找到的用户对象，如果没有找到返回null
     */
    YeelightUser findUserByAccount(String account);

    /**
     * 获取有效ID集合
     *
     * @param ids 原始ID集合
     * @return 经过验证的有效ID集合
     */
    Set<Long> getValidIds(Set<Long> ids);

    /**
     * 根据条件查找用户及其扩展信息
     *
     * @param example 查询条件对象
     * @return 用户及其扩展信息的列表
     */
    List<YeelightUserEntity> findUserAndExtend(YeelightUserExample example);

}
