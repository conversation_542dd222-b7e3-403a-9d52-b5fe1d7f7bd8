package com.yeelight.service.user.server.migration;

import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2RefreshToken;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * 旧版OAuth2对象到新版OAuth2Authorization的转换器
 * <p>
 * 从NewCustomRedisAuthorizationService中提取出来的转换逻辑
 * 保留原有的优化，支持YeelightOAuth2AccessToken等自定义类型
 * 
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
public class LegacyOAuth2AuthorizationConverter {

    private final RegisteredClientRepository registeredClientRepository;

    public LegacyOAuth2AuthorizationConverter(RegisteredClientRepository registeredClientRepository) {
        this.registeredClientRepository = registeredClientRepository;
    }

    /**
     * 从旧版对象构建OAuth2Authorization
     * 保留原有的逻辑和优化
     */
    public OAuth2Authorization buildOAuth2AuthorizationFromLegacyObjects(
            Object accessTokenObj, String refreshTokenValue, Object authenticationObj, String accessTokenValue) {
        
        try {
            // 1. 提取基本信息
            String authorizationId = extractAuthorizationIdFromLegacyObjects(accessTokenObj, refreshTokenValue, accessTokenValue);
            String principalName = extractPrincipalNameFromLegacyObjects(accessTokenObj, authenticationObj);
            String clientId = extractClientIdFromLegacyObjects(accessTokenObj, authenticationObj);
            
            if (authorizationId == null || principalName == null || clientId == null) {
                log.warn("无法提取必要的授权信息 - authorizationId: {}, principalName: {}, clientId: {}", 
                        authorizationId, principalName, clientId);
                return null;
            }
            
            // 2. 获取或创建RegisteredClient
            RegisteredClient registeredClient = getOrCreateRegisteredClient(clientId);
            
            // 3. 创建OAuth2Authorization.Builder
            OAuth2Authorization.Builder builder = OAuth2Authorization.withRegisteredClient(registeredClient)
                    .id(authorizationId)
                    .principalName(principalName)
                    // 假设是密码模式
                    .authorizationGrantType(AuthorizationGrantType.PASSWORD);
            
            // 4. 添加AccessToken（如果存在）
            if (accessTokenObj != null && accessTokenValue != null) {
                addAccessTokenToBuilder(builder, accessTokenObj, accessTokenValue);
            }
            
            // 5. 添加RefreshToken（如果存在）
            if (StringUtils.hasText(refreshTokenValue)) {
                addRefreshTokenToBuilder(builder, refreshTokenValue);
            }
            
            // 6. 添加Authentication信息
            if (authenticationObj != null) {
                addAuthenticationToBuilder(builder, authenticationObj);
            }
            
            return builder.build();
            
        } catch (Exception e) {
            log.error("构建OAuth2Authorization失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 提取授权ID
     * 保留原有的YeelightOAuth2AccessToken处理逻辑
     */
    private String extractAuthorizationIdFromLegacyObjects(Object accessTokenObj, String refreshTokenValue, String accessTokenValue) {
        // 优先从AccessToken的additionalInformation中获取jti
        if (accessTokenObj instanceof YeelightOAuth2AccessToken accountToken) {
            Map<String, Object> additionalInfo = accountToken.getAdditionalInformation();
            if (additionalInfo != null) {
                Object jti = additionalInfo.get("jti");
                if (jti != null) {
                    return jti.toString();
                }
            }
        }

        if (accessTokenObj instanceof Map<?, ?> tokenMap) {
            Object additionalInfo = tokenMap.get("additionalInformation");
            if (additionalInfo instanceof Map<?, ?> additionalMap) {
                Object jti = additionalMap.get("jti");
                if (jti != null) {
                    return jti.toString();
                }
            }
        }

        // 如果没有jti，使用accessToken或refreshToken作为ID
        if (StringUtils.hasText(accessTokenValue)) {
            return accessTokenValue;
        } else if (StringUtils.hasText(refreshTokenValue)) {
            return refreshTokenValue;
        }

        return UUID.randomUUID().toString();
    }

    /**
     * 提取用户主体名称
     * 保留原有的YeelightOAuth2Authentication处理逻辑
     */
    private String extractPrincipalNameFromLegacyObjects(Object accessTokenObj, Object authenticationObj) {
        // 从Authentication对象中提取
        if (authenticationObj instanceof YeelightOAuth2Authentication authentication) {
            Object userObj = authentication.getPrincipal();
            if (userObj instanceof JSONObject user) {
                return user.getString("username");
            }

            if (userObj instanceof Map<?, ?> userMap) {
                return extractValueFromMap(userMap, "username");
            }
        }

        if (authenticationObj instanceof Map<?, ?> authMap) {
            Object name = authMap.get("name");
            if (name != null) {
                return name.toString();
            }
        }

        // 从AccessToken的additionalInformation中提取
        if (accessTokenObj instanceof YeelightOAuth2AccessToken accountToken) {
            Map<String, Object> additionalInfo = accountToken.getAdditionalInformation();
            if (additionalInfo != null) {
                return extractValueFromMap(additionalInfo, "username");
            }
        }

        if (accessTokenObj instanceof Map<?, ?> tokenMap) {
            Object additionalInfo = tokenMap.get("additionalInformation");
            if (additionalInfo instanceof Map<?, ?>) {
                return extractValueFromMap(additionalInfo, "username");
            }
        }

        return null;
    }

    /**
     * 提取客户端ID
     * 保留原有的处理逻辑
     */
    private String extractClientIdFromLegacyObjects(Object accessTokenObj, Object authenticationObj) {
        // 从AccessToken的additionalInformation中提取
        if (accessTokenObj instanceof YeelightOAuth2AccessToken accountToken) {
            Map<String, Object> additionalInfo = accountToken.getAdditionalInformation();
            return extractValueFromMap(additionalInfo, "client_id");
        }

        if (accessTokenObj instanceof Map<?, ?> tokenMap) {
            Object additionalInfo = tokenMap.get("additionalInformation");
            return extractValueFromMap(additionalInfo, "client_id");
        }

        // 从Authentication对象中提取
        if (authenticationObj instanceof Map<?, ?> authMap) {
            Object oAuth2Request = authMap.get("oAuth2Request");
            return extractValueFromMap(oAuth2Request, "clientId");
        }

        return null;
    }

    private String extractValueFromMap(Object obj, String key) {
        if (obj instanceof Map<?, ?> authMap) {
            Object value = authMap.get(key);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }

    /**
     * 获取或创建RegisteredClient
     */
    private RegisteredClient getOrCreateRegisteredClient(String clientId) {
        try {
            RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
            if (registeredClient != null) {
                return registeredClient;
            }
        } catch (Exception e) {
            log.debug("获取注册客户端失败: {}", e.getMessage());
        }

        // 创建一个默认的RegisteredClient
        return RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId(clientId)
                .clientSecret("{noop}secret")
                .authorizationGrantType(AuthorizationGrantType.PASSWORD)
                .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                .scope("read")
                .scope("write")
                .build();
    }

    /**
     * 添加AccessToken到Builder
     * 保留原有的处理逻辑
     */
    @SuppressWarnings("unchecked")
    private void addAccessTokenToBuilder(OAuth2Authorization.Builder builder, Object accessTokenObj, String accessTokenValue) {
        try {
            Instant expiresAt;
            Set<String> scopes;

            if (accessTokenObj instanceof YeelightOAuth2AccessToken accountToken) {
                expiresAt = accountToken.getExpiration() != null ? 
                    accountToken.getExpiration().toInstant() : 
                    Instant.now().plus(Duration.ofHours(1));
                scopes = accountToken.getScope() != null ? 
                    accountToken.getScope() : 
                    Set.of("read", "write");
            } else if (accessTokenObj instanceof Map) {
                Map<String, Object> tokenMap = (Map<String, Object>) accessTokenObj;
                expiresAt = extractExpirationTime(tokenMap);
                scopes = extractScopes(tokenMap);
            } else {
                expiresAt = Instant.now().plus(Duration.ofHours(1));
                scopes = Set.of("read", "write");
            }

            // 创建OAuth2AccessToken
            OAuth2AccessToken accessToken = new OAuth2AccessToken(
                    OAuth2AccessToken.TokenType.BEARER,
                    accessTokenValue,
                    // issuedAt
                    Instant.now(),
                    expiresAt,
                    scopes
            );

            // 添加到builder
            builder.token(accessToken);

            log.debug("成功添加AccessToken到Builder");
        } catch (Exception e) {
            log.error("添加AccessToken到Builder失败: {}", e.getMessage());
        }
    }

    /**
     * 添加RefreshToken到Builder
     */
    private void addRefreshTokenToBuilder(OAuth2Authorization.Builder builder, String refreshTokenValue) {
        try {
            // 创建OAuth2RefreshToken
            OAuth2RefreshToken refreshToken = new OAuth2RefreshToken(
                refreshTokenValue,
                Instant.now(),
                Instant.now().plus(Duration.ofDays(90))
            );

            // 添加到builder
            builder.token(refreshToken);

            log.debug("成功添加RefreshToken到Builder");
        } catch (Exception e) {
            log.error("添加RefreshToken到Builder失败: {}", e.getMessage());
        }
    }

    /**
     * 添加Authentication信息到Builder
     */
    @SuppressWarnings("unchecked")
    private void addAuthenticationToBuilder(OAuth2Authorization.Builder builder, Object authenticationObj) {
        try {
            if (authenticationObj instanceof YeelightOAuth2Authentication authentication) {
                addAuthenticationToBuilder(builder, authentication);
            } else if (authenticationObj instanceof Map) {
                addAuthenticationToBuilder(builder, (Map<String, Object>) authenticationObj);
            }
        } catch (Exception e) {
            log.error("添加Authentication信息到Builder失败: {}", e.getMessage());
        }
    }

    private static void addAuthenticationToBuilder(OAuth2Authorization.Builder builder, Map<String, Object> authenticationObj) {
        // 提取用户信息
        Object principal = authenticationObj.get("principal");
        if (principal instanceof Map) {
            Map<String, Object> principalMap = (Map<String, Object>) principal;

            // 添加用户属性
            Map<String, Object> attributes = new HashMap<>(4);
            attributes.put("principal", principalMap);

            // 提取用户ID
            Object userId = principalMap.get("id");
            if (userId != null) {
                attributes.put("user_id", userId);
            }

            // 提取其他有用信息
            Object authorities = authenticationObj.get("authorities");
            if (authorities != null) {
                attributes.put("authorities", authorities);
            }

            builder.attributes(attrs -> attrs.putAll(attributes));
        }
    }

    private static void addAuthenticationToBuilder(OAuth2Authorization.Builder builder, YeelightOAuth2Authentication authentication) {
        // 提取用户信息
        Object principal = authentication.getPrincipal();
        if (principal instanceof JSONObject principalMap) {
            // 添加用户属性
            Map<String, Object> attributes = new HashMap<>(4);
            attributes.put("principal", principalMap);

            // 提取用户ID
            Object userId = principalMap.get("id");
            if (userId != null) {
                attributes.put("user_id", userId);
            }

            // 提取权限信息
            if (authentication.getAuthorities() != null) {
                attributes.put("authorities", authentication.getAuthorities());
            }

            builder.attributes(attrs -> attrs.putAll(attributes));
        }
    }

    /**
     * 提取过期时间
     */
    private Instant extractExpirationTime(Map<String, Object> tokenMap) {
        try {
            Object expiration = tokenMap.get("expiration");
            if (expiration instanceof Number) {
                long expirationTime = ((Number) expiration).longValue();
                return Instant.ofEpochMilli(expirationTime);
            } else if (expiration instanceof Date) {
                return ((Date) expiration).toInstant();
            }
        } catch (Exception e) {
            log.debug("提取过期时间失败: {}", e.getMessage());
        }

        // 默认1小时后过期
        return Instant.now().plus(Duration.ofHours(1));
    }

    /**
     * 提取作用域
     */
    @SuppressWarnings("unchecked")
    private Set<String> extractScopes(Map<String, Object> tokenMap) {
        try {
            Object scope = tokenMap.get("scope");
            if (scope instanceof Collection) {
                return new HashSet<>((Collection<String>) scope);
            } else if (scope instanceof String) {
                return Set.of(scope.toString().split(" "));
            }
        } catch (Exception e) {
            log.debug("提取作用域失败: {}", e.getMessage());
        }

        // 默认作用域
        return Set.of("read", "write");
    }
}
