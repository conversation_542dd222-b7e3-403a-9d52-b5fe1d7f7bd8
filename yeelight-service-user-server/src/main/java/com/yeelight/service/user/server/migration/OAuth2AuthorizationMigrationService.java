package com.yeelight.service.user.server.migration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

/**
 * OAuth2授权数据迁移服务
 * 将旧版的oauth_access_token、oauth_refresh_token、oauth_code表数据
 * 迁移到新版的oauth2_authorization表
 * 
 * <AUTHOR>
 * @since 2024-07-31
 */
@Slf4j
@Service
public class OAuth2AuthorizationMigrationService {

    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 旧版表查询SQL
     */
    private static final String SELECT_ACCESS_TOKENS_SQL = """
        SELECT token_id, token, authentication_id, user_name, client_id,
               authentication, refresh_token
        FROM oauth_access_token
        """;

    private static final String SELECT_REFRESH_TOKENS_SQL = """
        SELECT token_id, token, authentication
        FROM oauth_refresh_token
        """;

    private static final String SELECT_CODES_SQL = """
        SELECT code, authentication
        FROM oauth_code
        """;

    /**
     * 新版表插入SQL
     */
    private static final String INSERT_AUTHORIZATION_SQL = """
        INSERT INTO oauth2_authorization
        (id, registered_client_id, principal_name, authorization_grant_type,
         authorized_scopes, attributes, state,
         access_token_value, access_token_issued_at, access_token_expires_at,
         access_token_metadata, access_token_type, access_token_scopes,
         refresh_token_value, refresh_token_issued_at, refresh_token_expires_at,
         refresh_token_metadata,
         authorization_code_value, authorization_code_issued_at, authorization_code_expires_at,
         authorization_code_metadata)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;

    public OAuth2AuthorizationMigrationService(DataSource oauthDataSource) {
        this.jdbcTemplate = new JdbcTemplate(oauthDataSource);
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 执行授权数据迁移
     */
    public void migrateAuthorizations() {
        try {
            log.info("开始迁移OAuth2授权数据...");
            
            // 清空新表数据
            jdbcTemplate.update("DELETE FROM oauth2_authorization");
            
            int migratedCount = 0;
            
            // 迁移访问令牌数据
            migratedCount += migrateAccessTokens();
            
            // 迁移授权码数据
            migratedCount += migrateAuthorizationCodes();
            
            log.info("OAuth2授权数据迁移完成，共迁移 {} 条记录", migratedCount);
            
        } catch (Exception ex) {
            log.error("OAuth2授权数据迁移失败", ex);
            throw new RuntimeException("授权数据迁移失败", ex);
        }
    }

    /**
     * 迁移访问令牌数据
     */
    private int migrateAccessTokens() {
        log.info("迁移访问令牌数据...");
        
        try {
            List<Map<String, Object>> accessTokens = jdbcTemplate.queryForList(SELECT_ACCESS_TOKENS_SQL);
            int count = 0;
            
            for (Map<String, Object> tokenData : accessTokens) {
                try {
                    migrateAccessToken(tokenData);
                    count++;
                } catch (Exception ex) {
                    log.error("迁移访问令牌失败: {}", tokenData.get("token_id"), ex);
                }
            }
            
            log.info("访问令牌迁移完成，共 {} 条", count);
            return count;
            
        } catch (Exception ex) {
            log.error("查询访问令牌数据失败", ex);
            return 0;
        }
    }

    /**
     * 迁移单个访问令牌
     */
    private void migrateAccessToken(Map<String, Object> tokenData) throws JsonProcessingException {
        String tokenId = (String) tokenData.get("token_id");
        byte[] tokenBytes = (byte[]) tokenData.get("token");
        String userName = (String) tokenData.get("user_name");
        String clientId = (String) tokenData.get("client_id");
        byte[] refreshTokenBytes = (byte[]) tokenData.get("refresh_token");
        
        // 转换二进制数据为Base64字符串
        String token = tokenBytes != null ? Base64.getEncoder().encodeToString(tokenBytes) : null;
        String refreshToken = refreshTokenBytes != null ? Base64.getEncoder().encodeToString(refreshTokenBytes) : null;
        
        // 生成新的授权ID
        String authorizationId = UUID.randomUUID().toString();
        
        // 获取注册客户端ID
        String registeredClientId = getRegisteredClientId(clientId);
        if (registeredClientId == null) {
            log.warn("找不到客户端 {} 的注册信息，跳过令牌迁移", clientId);
            return;
        }
        
        // 设置基本信息
        String principalName = StringUtils.hasText(userName) ? userName : "unknown";
        // 默认授权码模式
        String grantType = "authorization_code";
        // 默认范围
        String scopes = "read,write";
        
        // 创建令牌元数据
        Map<String, Object> tokenMetadata = new HashMap<>(2);
        tokenMetadata.put("token.invalidated", false);
        tokenMetadata.put("token.type", "Bearer");
        
        // 创建刷新令牌元数据
        Map<String, Object> refreshTokenMetadata = new HashMap<>(1);
        refreshTokenMetadata.put("token.invalidated", false);
        
        try {
            // 插入授权记录
            jdbcTemplate.update(INSERT_AUTHORIZATION_SQL,
                    // id
                    authorizationId,
                    // registered_client_id
                    registeredClientId,
                    // principal_name
                    principalName,
                    // authorization_grant_type
                    grantType,
                    // authorized_scopes
                    scopes,
                    // attributes
                    "{}",
                    // state
                    null,
                    // access_token_value
                    token,
                    // access_token_issued_at
                    LocalDateTime.from(Instant.now()),
                    // access_token_expires_at (90天)
                    LocalDateTime.from(Instant.now().plusSeconds(7776000)),
                    // access_token_metadata
                    objectMapper.writeValueAsString(tokenMetadata),
                    // access_token_type
                    "Bearer",
                    // access_token_scopes
                    scopes,
                    // refresh_token_value
                    refreshToken,
                    // refresh_token_issued_at
                    refreshToken != null ? LocalDateTime.from(Instant.now()) : null,
                    // refresh_token_expires_at (1年)
                    refreshToken != null ? LocalDateTime.from(Instant.now().plusSeconds(31536000)) : null,
                    // refresh_token_metadata
                    refreshToken != null ? objectMapper.writeValueAsString(refreshTokenMetadata) : null,
                    // authorization_code_value
                    null,
                    // authorization_code_issued_at
                    null,
                    // authorization_code_expires_at
                    null,
                    // authorization_code_metadata
                    null
            );
            
            log.debug("成功迁移访问令牌: {}", tokenId);
            
        } catch (Exception ex) {
            log.error("插入授权记录失败: {}", tokenId, ex);
            throw ex;
        }
    }

    /**
     * 迁移授权码数据
     */
    private int migrateAuthorizationCodes() {
        log.info("迁移授权码数据...");
        
        try {
            List<Map<String, Object>> codes = jdbcTemplate.queryForList(SELECT_CODES_SQL);
            int count = 0;
            
            for (Map<String, Object> codeData : codes) {
                try {
                    migrateAuthorizationCode(codeData);
                    count++;
                } catch (Exception ex) {
                    log.error("迁移授权码失败: {}", codeData.get("code"), ex);
                }
            }
            
            log.info("授权码迁移完成，共 {} 条", count);
            return count;
            
        } catch (Exception ex) {
            log.error("查询授权码数据失败", ex);
            return 0;
        }
    }

    /**
     * 迁移单个授权码
     */
    private void migrateAuthorizationCode(Map<String, Object> codeData) throws JsonProcessingException {
        String code = (String) codeData.get("code");
        byte[] authenticationBytes = (byte[]) codeData.get("authentication");
        
        // 生成新的授权ID
        String authorizationId = UUID.randomUUID().toString();
        
        // 从authentication字节数组中解析clientId和principalName
        AuthenticationInfo authInfo = parseAuthenticationBytes(authenticationBytes);
        String clientId = authInfo.getClientId();
        String principalName = authInfo.getPrincipalName();

        if (clientId == null || principalName == null) {
            log.warn("无法从授权码authentication中解析clientId或principalName，跳过迁移: code={}", code);
            return;
        }
        
        // 获取注册客户端ID
        String registeredClientId = getRegisteredClientId(clientId);
        if (registeredClientId == null) {
            log.warn("找不到客户端 {} 的注册信息，跳过授权码迁移", clientId);
            return;
        }
        
        // 创建授权码元数据
        Map<String, Object> codeMetadata = new HashMap<>(1);
        codeMetadata.put("code.invalidated", false);
        
        try {
            // 插入授权记录
            jdbcTemplate.update(INSERT_AUTHORIZATION_SQL,
                    // id
                    authorizationId,
                    // registered_client_id
                    registeredClientId,
                    // principal_name
                    principalName,
                    // authorization_grant_type
                    "authorization_code",
                    // authorized_scopes
                    "read,write",
                    // attributes
                    "{}",
                    // state
                    null,
                    // access_token_value
                    null,
                    // access_token_issued_at
                    null,
                    // access_token_expires_at
                    null,
                    // access_token_metadata
                    null,
                    // access_token_type
                    null,
                    // access_token_scopes
                    null,
                    // refresh_token_value
                    null,
                    // refresh_token_issued_at
                    null,
                    // refresh_token_expires_at
                    null,
                    // refresh_token_metadata
                    null,
                    // authorization_code_value
                    code,
                    // authorization_code_issued_at
                    LocalDateTime.from(Instant.now()),
                    // authorization_code_expires_at (10分钟)
                    LocalDateTime.from(Instant.now().plusSeconds(600)),
                    // authorization_code_metadata
                    objectMapper.writeValueAsString(codeMetadata)
            );
            
            log.debug("成功迁移授权码: {}", code);
            
        } catch (Exception ex) {
            log.error("插入授权码记录失败: {}", code, ex);
            throw ex;
        }
    }

    /**
     * 根据客户端ID获取注册客户端ID
     */
    private String getRegisteredClientId(String clientId) {
        try {
            return jdbcTemplate.queryForObject(
                "SELECT id FROM oauth2_registered_client WHERE client_id = ?",
                String.class, clientId);
        } catch (Exception ex) {
            log.debug("找不到客户端 {} 的注册信息", clientId);
            return null;
        }
    }

    /**
     * 检查旧版表是否存在数据
     */
    public boolean hasOldAuthorizationData() {
        try {
            Integer accessTokenCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_access_token", Integer.class);
            Integer codeCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_code", Integer.class);
            
            return (accessTokenCount != null && accessTokenCount > 0) || (codeCount != null && codeCount > 0);
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 获取迁移统计信息
     */
    public Map<String, Integer> getMigrationStats() {
        Map<String, Integer> stats = new HashMap<>(4);
        
        try {
            stats.put("old_access_tokens", jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_access_token", Integer.class));
        } catch (Exception ex) {
            stats.put("old_access_tokens", 0);
        }
        
        try {
            stats.put("old_refresh_tokens", jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_refresh_token", Integer.class));
        } catch (Exception ex) {
            stats.put("old_refresh_tokens", 0);
        }
        
        try {
            stats.put("old_codes", jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_code", Integer.class));
        } catch (Exception ex) {
            stats.put("old_codes", 0);
        }
        
        try {
            stats.put("new_authorizations", jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_authorization", Integer.class));
        } catch (Exception ex) {
            stats.put("new_authorizations", 0);
        }
        
        return stats;
    }

    /**
     * 解析authentication字节数组，提取clientId和principalName
     */
    private AuthenticationInfo parseAuthenticationBytes(byte[] authenticationBytes) {
        if (authenticationBytes == null || authenticationBytes.length == 0) {
            return new AuthenticationInfo(null, null);
        }

        try {
            // 方法1：尝试JDK反序列化
            Object authObj = deserializeJdk(authenticationBytes);
            if (authObj != null) {
                AuthenticationInfo info = extractFromAuthenticationObject(authObj);
                if (info.isValid()) {
                    return info;
                }
            }
        } catch (Exception e) {
            log.debug("JDK反序列化authentication失败: {}", e.getMessage());
        }

        log.warn("无法解析authentication字节数组，长度: {}", authenticationBytes.length);
        return new AuthenticationInfo(null, null);
    }

    /**
     * JDK反序列化
     */
    private Object deserializeJdk(byte[] bytes) throws Exception {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
             ObjectInputStream ois = new ObjectInputStream(bis)) {
            return ois.readObject();
        }
    }

    /**
     * 从认证对象中提取信息
     */
    private AuthenticationInfo extractFromAuthenticationObject(Object authObj) {
        try {
            // 检查是否是YeelightOAuth2Authentication
            if (authObj.getClass().getSimpleName().contains("OAuth2Authentication")) {
                return extractFromOAuth2Authentication(authObj);
            }

            // 检查是否有oAuth2Request字段
            Field oAuth2RequestField = findField(authObj.getClass(), "oAuth2Request");
            if (oAuth2RequestField != null) {
                oAuth2RequestField.setAccessible(true);
                Object oAuth2Request = oAuth2RequestField.get(authObj);
                if (oAuth2Request != null) {
                    return extractFromOAuth2Request(oAuth2Request);
                }
            }

            // 检查是否有userAuthentication字段
            Field userAuthField = findField(authObj.getClass(), "userAuthentication");
            if (userAuthField != null) {
                userAuthField.setAccessible(true);
                Object userAuth = userAuthField.get(authObj);
                if (userAuth != null) {
                    String principalName = extractPrincipalName(userAuth);
                    return new AuthenticationInfo(null, principalName);
                }
            }

        } catch (Exception e) {
            log.debug("从认证对象提取信息失败: {}", e.getMessage());
        }

        return new AuthenticationInfo(null, null);
    }

    /**
     * 从OAuth2Authentication对象中提取信息
     */
    private AuthenticationInfo extractFromOAuth2Authentication(Object oauth2Auth) {
        try {
            String clientId = null;
            String principalName = null;

            // 提取oAuth2Request中的clientId
            Field oAuth2RequestField = findField(oauth2Auth.getClass(), "oAuth2Request");
            if (oAuth2RequestField != null) {
                oAuth2RequestField.setAccessible(true);
                Object oAuth2Request = oAuth2RequestField.get(oauth2Auth);
                if (oAuth2Request != null) {
                    AuthenticationInfo requestInfo = extractFromOAuth2Request(oAuth2Request);
                    clientId = requestInfo.getClientId();
                }
            }

            // 提取userAuthentication中的principalName
            Field userAuthField = findField(oauth2Auth.getClass(), "userAuthentication");
            if (userAuthField != null) {
                userAuthField.setAccessible(true);
                Object userAuth = userAuthField.get(oauth2Auth);
                if (userAuth != null) {
                    principalName = extractPrincipalName(userAuth);
                }
            }

            return new AuthenticationInfo(clientId, principalName);

        } catch (Exception e) {
            log.debug("从OAuth2Authentication提取信息失败: {}", e.getMessage());
            return new AuthenticationInfo(null, null);
        }
    }

    /**
     * 从OAuth2Request对象中提取clientId
     */
    private AuthenticationInfo extractFromOAuth2Request(Object oAuth2Request) {
        try {
            Field clientIdField = findField(oAuth2Request.getClass(), "clientId");
            if (clientIdField != null) {
                clientIdField.setAccessible(true);
                String clientId = (String) clientIdField.get(oAuth2Request);
                return new AuthenticationInfo(clientId, null);
            }
        } catch (Exception e) {
            log.debug("从OAuth2Request提取clientId失败: {}", e.getMessage());
        }

        return new AuthenticationInfo(null, null);
    }

    /**
     * 从用户认证对象中提取principalName
     */
    private String extractPrincipalName(Object userAuth) {
        try {
            // 尝试调用getName()方法
            Method getNameMethod = findMethod(userAuth.getClass(), "getName");
            if (getNameMethod != null) {
                getNameMethod.setAccessible(true);
                return (String) getNameMethod.invoke(userAuth);
            }

            // 尝试调用getPrincipal()方法
            Method getPrincipalMethod = findMethod(userAuth.getClass(), "getPrincipal");
            if (getPrincipalMethod != null) {
                getPrincipalMethod.setAccessible(true);
                Object principal = getPrincipalMethod.invoke(userAuth);
                if (principal instanceof String) {
                    return (String) principal;
                } else if (principal != null) {
                    // 如果principal是对象，尝试获取其username或name字段
                    return extractUsernameFromPrincipal(principal);
                }
            }

        } catch (Exception e) {
            log.debug("从用户认证对象提取principalName失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从principal对象中提取用户名
     */
    private String extractUsernameFromPrincipal(Object principal) {
        try {
            // 尝试username字段
            Field usernameField = findField(principal.getClass(), "username");
            if (usernameField != null) {
                usernameField.setAccessible(true);
                return (String) usernameField.get(principal);
            }

            // 尝试name字段
            Field nameField = findField(principal.getClass(), "name");
            if (nameField != null) {
                nameField.setAccessible(true);
                return (String) nameField.get(principal);
            }

            // 尝试getUsername()方法
            Method getUsernameMethod = findMethod(principal.getClass(), "getUsername");
            if (getUsernameMethod != null) {
                getUsernameMethod.setAccessible(true);
                return (String) getUsernameMethod.invoke(principal);
            }

        } catch (Exception e) {
            log.debug("从principal对象提取用户名失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 查找字段（包括父类）
     */
    private Field findField(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 查找方法（包括父类）
     */
    private Method findMethod(Class<?> clazz, String methodName) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            try {
                return currentClass.getDeclaredMethod(methodName);
            } catch (NoSuchMethodException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 认证信息封装类
     */
    @Getter
    private static class AuthenticationInfo {
        private final String clientId;
        private final String principalName;

        public AuthenticationInfo(String clientId, String principalName) {
            this.clientId = clientId;
            this.principalName = principalName;
        }

        public boolean isValid() {
            return clientId != null || principalName != null;
        }

        @Override
        public String toString() {
            return String.format("AuthenticationInfo{clientId='%s', principalName='%s'}", clientId, principalName);
        }
    }
}
