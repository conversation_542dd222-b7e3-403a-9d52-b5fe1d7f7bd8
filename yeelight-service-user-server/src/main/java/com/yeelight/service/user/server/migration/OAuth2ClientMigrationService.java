package com.yeelight.service.user.server.migration;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.JdbcRegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * OAuth2客户端数据迁移服务
 * 将旧版oauth_client_details表的数据迁移到新版oauth2_registered_client表
 * 
 * <AUTHOR>
 * @description: 负责OAuth2客户端配置的数据迁移
 */
@Slf4j
@Service
public class OAuth2ClientMigrationService {

    private final JdbcTemplate jdbcTemplate;

    @Resource
    private JdbcRegisteredClientRepository jdbcRegisteredClientRepository;

    /**
     * 旧版客户端详情查询SQL
     */
    private static final String SELECT_OLD_CLIENTS_SQL = """
        SELECT client_id, client_secret, scope, authorized_grant_types,
               web_server_redirect_uri, authorities, access_token_validity,
               refresh_token_validity, additional_information, autoapprove
        FROM oauth_client_details
        """;

    public OAuth2ClientMigrationService(DataSource oauthDataSource) {
        this.jdbcTemplate = new JdbcTemplate(oauthDataSource);
    }

    /**
     * 执行客户端数据迁移
     * 从oauth_client_details迁移到oauth2_registered_client
     */
    public void migrateClients() {
        try {
            log.info("开始迁移OAuth2客户端数据...");
            
            // 检查新表是否存在，不存在则创建
            createNewClientTableIfNotExists();
            
            // 清空新表数据
            jdbcTemplate.update("DELETE FROM oauth2_registered_client");
            
            // 查询旧版客户端数据
            List<Map<String, Object>> oldClients = jdbcTemplate.queryForList(SELECT_OLD_CLIENTS_SQL);
            
            int migratedCount = 0;
            for (Map<String, Object> oldClient : oldClients) {
                try {
                    RegisteredClient newClient = convertToRegisteredClient(oldClient);
                    jdbcRegisteredClientRepository.save(newClient);
                    migratedCount++;
                    log.debug("成功迁移客户端: {}", oldClient.get("client_id"));
                } catch (Exception ex) {
                    log.error("迁移客户端失败: {}", oldClient.get("client_id"), ex);
                }
            }
            
            log.info("OAuth2客户端数据迁移完成，共迁移 {} 个客户端", migratedCount);
        } catch (Exception ex) {
            log.error("OAuth2客户端数据迁移失败", ex);
            throw new RuntimeException("客户端数据迁移失败", ex);
        }
    }

    /**
     * 将旧版客户端详情转换为新版注册客户端
     */
    private RegisteredClient convertToRegisteredClient(Map<String, Object> oldClient) {
        String clientId = (String) oldClient.get("client_id");
        String clientSecret = (String) oldClient.get("client_secret");
        String scope = (String) oldClient.get("scope");
        String grantTypes = (String) oldClient.get("authorized_grant_types");
        String redirectUris = (String) oldClient.get("web_server_redirect_uri");
        Integer accessTokenValidity = (Integer) oldClient.get("access_token_validity");
        Integer refreshTokenValidity = (Integer) oldClient.get("refresh_token_validity");
        String autoApprove = (String) oldClient.get("autoapprove");

        RegisteredClient.Builder builder = RegisteredClient.withId(UUID.randomUUID().toString())
                .clientId(clientId)
                .clientName(clientId)
                .clientIdIssuedAt(Instant.now());

        // 设置客户端密钥
        if (StringUtils.hasText(clientSecret)) {
            builder.clientSecret(clientSecret);
        }

        // 设置客户端认证方法
        builder.clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
               .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_POST);

        // 设置授权类型
        if (StringUtils.hasText(grantTypes)) {
            String[] grantTypeArray = grantTypes.split(",");
            for (String grantType : grantTypeArray) {
                switch (grantType.trim()) {
                    case "authorization_code":
                        builder.authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE);
                        break;
                    case "refresh_token":
                        builder.authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN);
                        break;
                    case "client_credentials":
                        builder.authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
                        break;
                    case "password":
                        // 新版通过自定义AuthenticationProvider支持password模式
                        builder.authorizationGrantType(new AuthorizationGrantType("password"));
                        log.info("客户端 {} 使用了password授权模式，已通过自定义Provider支持", clientId);
                        break;
                    case "implicit":
                        // 新版通过自定义AuthenticationProvider支持implicit模式
                        builder.authorizationGrantType(new AuthorizationGrantType("implicit"));
                        log.info("客户端 {} 使用了implicit授权模式，已通过自定义Provider支持", clientId);
                        break;
                }
            }
        }  else {
            log.warn("客户端 {} 的授权类型为空，默认全部支持", clientId);
            builder.authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                    .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
                    .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS);
            builder.authorizationGrantType(new AuthorizationGrantType("password"));
            builder.authorizationGrantType(new AuthorizationGrantType("implicit"));
            log.info("客户端 {} 的授权类型已全部支持", clientId);
        }

        // 设置重定向URI
        if (StringUtils.hasText(redirectUris)) {
            String[] uriArray = redirectUris.split(",");
            for (String uri : uriArray) {
                builder.redirectUri(uri.trim());
            }
        } else {
            log.warn("客户端 {} 的重定向URI为空，默认为http://127.0.0.1", clientId);
            builder.redirectUri("http://127.0.0.1");
        }

        // 设置授权范围
        if (StringUtils.hasText(scope)) {
            String[] scopeArray = scope.split(",");
            for (String s : scopeArray) {
                builder.scope(s.trim());
            }
        }

        // 设置客户端设置 - 与旧版CustomUserApprovalHandler逻辑保持一致
        ClientSettings.Builder clientSettingsBuilder = ClientSettings.builder();

        // 设置授权同意要求
        if ("true".equals(autoApprove) || "1".equals(autoApprove)) {
            clientSettingsBuilder.requireAuthorizationConsent(false);
        } else {
            clientSettingsBuilder.requireAuthorizationConsent(true);
        }

        // 启用PKCE支持 - 与旧版PkceAuthorizationCodeTokenGranter保持兼容
        // 新版Spring Authorization Server内置了PKCE支持，只需要启用即可
        clientSettingsBuilder.requireProofKey(true);

        builder.clientSettings(clientSettingsBuilder.build());

        // 设置Token设置 - 与旧版DefaultTokenServices保持一致
        TokenSettings.Builder tokenSettingsBuilder = TokenSettings.builder();

        // 访问令牌有效期：如果数据库中有配置则使用，否则使用旧版默认的90天
        if (accessTokenValidity != null && accessTokenValidity > 0) {
            tokenSettingsBuilder.accessTokenTimeToLive(Duration.ofSeconds(accessTokenValidity));
        } else {
            // 使用与旧版DefaultTokenServices相同的90天默认值
            tokenSettingsBuilder.accessTokenTimeToLive(Duration.ofDays(90));
        }

        // 刷新令牌有效期：如果数据库中有配置则使用，否则使用旧版默认的永不过期
        if (refreshTokenValidity != null && refreshTokenValidity > 0) {
            tokenSettingsBuilder.refreshTokenTimeToLive(Duration.ofSeconds(refreshTokenValidity));
        } else {
            // 使用与旧版DefaultTokenServices相同的永不过期设置
            // 在新版中，设置为一个很长的时间来模拟永不过期
            // 10年
            tokenSettingsBuilder.refreshTokenTimeToLive(Duration.ofDays(365 * 10));
        }

        // 设置不重用刷新令牌 - 与旧版DefaultTokenServices保持一致
        tokenSettingsBuilder.reuseRefreshTokens(false);

        builder.tokenSettings(tokenSettingsBuilder.build());

        return builder.build();
    }

    /**
     * 创建新版客户端表（如果不存在）
     */
    private void createNewClientTableIfNotExists() {
        String createTableSql = """
            CREATE TABLE IF NOT EXISTS oauth2_registered_client (
                id varchar(100) NOT NULL,
                client_id varchar(100) NOT NULL,
                client_id_issued_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                client_secret varchar(200) DEFAULT NULL,
                client_secret_expires_at datetime DEFAULT NULL,
                client_name varchar(200) NOT NULL,
                client_authentication_methods varchar(1000) NOT NULL,
                authorization_grant_types varchar(1000) NOT NULL,
                redirect_uris varchar(4096) DEFAULT NULL,
                post_logout_redirect_uris varchar(1000) DEFAULT NULL,
                scopes varchar(1000) NOT NULL,
                client_settings varchar(2000) NOT NULL,
                token_settings varchar(2000) NOT NULL,
                PRIMARY KEY (id)
            )
            """;
        
        jdbcTemplate.execute(createTableSql);
        log.info("确保oauth2_registered_client表存在");
    }

    /**
     * 检查是否需要迁移
     */
    public boolean needsMigration() {
        try {
            Integer oldClientCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_client_details", Integer.class);
            Integer newClientCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_registered_client", Integer.class);
            
            return (oldClientCount != null && oldClientCount > 0) && (newClientCount != null && newClientCount == 0);
        } catch (Exception ex) {
            log.warn("检查迁移状态失败", ex);
            return false;
        }
    }
}
