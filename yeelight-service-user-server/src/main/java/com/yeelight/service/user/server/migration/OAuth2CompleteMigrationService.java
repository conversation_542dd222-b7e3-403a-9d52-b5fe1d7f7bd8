package com.yeelight.service.user.server.migration;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

/**
 * OAuth2完整数据库迁移服务
 * 负责从旧版OAuth2 Security迁移到新版Spring Authorization Server的完整数据库结构
 * <p>
 * 迁移内容包括：
 * 1. 客户端配置：oauth_client_details -> oauth2_registered_client
 * 2. 授权码：oauth_code -> oauth2_authorization (code类型)
 * 3. 访问令牌：oauth_access_token -> oauth2_authorization (access_token类型)
 * 4. 刷新令牌：oauth_refresh_token -> oauth2_authorization (refresh_token类型)
 * 5. 授权同意：oauth_approvals -> oauth2_authorization_consent
 * 
 * <AUTHOR>
 * @since 2024-07-31
 */
@Slf4j
@Service
public class OAuth2CompleteMigrationService {

    private final JdbcTemplate jdbcTemplate;
    
    @Resource
    private OAuth2ClientMigrationService clientMigrationService;
    
    @Resource
    private OAuth2AuthorizationMigrationService authorizationMigrationService;
    
    @Resource
    private OAuth2ConsentMigrationService consentMigrationService;

    public OAuth2CompleteMigrationService(DataSource oauthDataSource) {
        this.jdbcTemplate = new JdbcTemplate(oauthDataSource);
    }

    /**
     * 执行完整的OAuth2数据库迁移
     * 按照依赖顺序执行各个表的迁移
     */
    public void performCompleteMigration() {
        log.info("🚀 开始执行OAuth2完整数据库迁移...");
        
        try {
            // 步骤1：创建所有新版表结构
            createNewTables();
            
            // 步骤2：迁移客户端配置（最重要，其他表都依赖它）
            log.info("📋 步骤1：迁移客户端配置...");
            clientMigrationService.migrateClients();
            
            // 步骤3：迁移授权数据（令牌、授权码）
            log.info("🔑 步骤2：迁移授权数据...");
            authorizationMigrationService.migrateAuthorizations();
            
            // 步骤4：迁移授权同意数据
            log.info("✅ 步骤3：迁移授权同意数据...");
            consentMigrationService.migrateConsents();
            
            // 步骤5：验证迁移结果
            validateMigration();
            
            log.info("🎉 OAuth2完整数据库迁移成功完成！");
            
        } catch (Exception ex) {
            log.error("❌ OAuth2数据库迁移失败", ex);
            throw new RuntimeException("OAuth2数据库迁移失败", ex);
        }
    }

    /**
     * 创建所有新版表结构
     */
    private void createNewTables() {
        log.info("📊 创建新版OAuth2表结构...");
        
        // 1. 创建oauth2_registered_client表
        createRegisteredClientTable();
        
        // 2. 创建oauth2_authorization表
        createAuthorizationTable();
        
        // 3. 创建oauth2_authorization_consent表
        createAuthorizationConsentTable();
        
        log.info("✅ 新版OAuth2表结构创建完成");
    }

    /**
     * 创建oauth2_registered_client表
     */
    private void createRegisteredClientTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS oauth2_registered_client (
                id varchar(100) NOT NULL,
                client_id varchar(100) NOT NULL,
                client_id_issued_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
                client_secret varchar(200) DEFAULT NULL,
                client_secret_expires_at datetime DEFAULT NULL,
                client_name varchar(200) NOT NULL,
                client_authentication_methods varchar(1000) NOT NULL,
                authorization_grant_types varchar(1000) NOT NULL,
                redirect_uris varchar(4096) DEFAULT NULL,
                post_logout_redirect_uris varchar(1000) DEFAULT NULL,
                scopes varchar(1000) NOT NULL,
                client_settings varchar(2000) NOT NULL,
                token_settings varchar(2000) NOT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY uk_client_id (client_id)
            )
            """;
        
        jdbcTemplate.execute(sql);
        log.debug("✅ oauth2_registered_client表创建完成");
    }

    /**
     * 创建oauth2_authorization表
     * 用于存储授权码、访问令牌、刷新令牌等所有授权相关数据
     */
    private void createAuthorizationTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS oauth2_authorization (
                id varchar(100) NOT NULL,
                registered_client_id varchar(100) NOT NULL,
                principal_name varchar(200) NOT NULL,
                authorization_grant_type varchar(100) NOT NULL,
                authorized_scopes varchar(1000) DEFAULT NULL,
                attributes text DEFAULT NULL,
                state varchar(500) DEFAULT NULL,
                authorization_code_value text DEFAULT NULL,
                authorization_code_issued_at datetime DEFAULT NULL,
                authorization_code_expires_at datetime DEFAULT NULL,
                authorization_code_metadata text DEFAULT NULL,
                access_token_value text DEFAULT NULL,
                access_token_issued_at datetime DEFAULT NULL,
                access_token_expires_at datetime DEFAULT NULL,
                access_token_metadata text DEFAULT NULL,
                access_token_type varchar(100) DEFAULT NULL,
                access_token_scopes varchar(1000) DEFAULT NULL,
                oidc_id_token_value text DEFAULT NULL,
                oidc_id_token_issued_at datetime DEFAULT NULL,
                oidc_id_token_expires_at datetime DEFAULT NULL,
                oidc_id_token_metadata text DEFAULT NULL,
                refresh_token_value text DEFAULT NULL,
                refresh_token_issued_at datetime DEFAULT NULL,
                refresh_token_expires_at datetime DEFAULT NULL,
                refresh_token_metadata text DEFAULT NULL,
                user_code_value text DEFAULT NULL,
                user_code_issued_at datetime DEFAULT NULL,
                user_code_expires_at datetime DEFAULT NULL,
                user_code_metadata text DEFAULT NULL,
                device_code_value text DEFAULT NULL,
                device_code_issued_at datetime DEFAULT NULL,
                device_code_expires_at datetime DEFAULT NULL,
                device_code_metadata text DEFAULT NULL,
                PRIMARY KEY (id),
                KEY idx_registered_client_id (registered_client_id),
                KEY idx_principal_name (principal_name),
                KEY idx_authorization_grant_type (authorization_grant_type),
                KEY idx_access_token_value (access_token_value(255)),
                KEY idx_refresh_token_value (refresh_token_value(255)),
                KEY idx_authorization_code_value (authorization_code_value(255))
            )
            """;
        
        jdbcTemplate.execute(sql);
        log.debug("✅ oauth2_authorization表创建完成");
    }

    /**
     * 创建oauth2_authorization_consent表
     */
    private void createAuthorizationConsentTable() {
        String sql = """
            CREATE TABLE IF NOT EXISTS oauth2_authorization_consent (
                registered_client_id varchar(100) NOT NULL,
                principal_name varchar(200) NOT NULL,
                authorities varchar(1000) NOT NULL,
                PRIMARY KEY (registered_client_id, principal_name)
            )
            """;
        
        jdbcTemplate.execute(sql);
        log.debug("✅ oauth2_authorization_consent表创建完成");
    }

    /**
     * 验证迁移结果
     */
    private void validateMigration() {
        log.info("🔍 验证迁移结果...");
        
        try {
            // 验证客户端数据
            Integer oldClientCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_client_details", Integer.class);
            Integer newClientCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_registered_client", Integer.class);
            
            log.info("📊 客户端数据：旧表 {} 条，新表 {} 条", oldClientCount, newClientCount);
            
            // 验证授权数据
            Integer authorizationCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_authorization", Integer.class);
            
            log.info("📊 授权数据：新表 {} 条", authorizationCount);
            
            // 验证授权同意数据
            Integer consentCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_authorization_consent", Integer.class);
            
            log.info("📊 授权同意数据：新表 {} 条", consentCount);
            
            if (newClientCount != null && newClientCount == 0 && oldClientCount != null && oldClientCount > 0) {
                throw new RuntimeException("客户端数据迁移失败：新表为空但旧表有数据");
            }
            
            log.info("✅ 迁移结果验证通过");
            
        } catch (Exception ex) {
            log.error("❌ 迁移结果验证失败", ex);
            throw new RuntimeException("迁移结果验证失败", ex);
        }
    }

    /**
     * 检查是否需要完整迁移
     */
    public boolean needsCompleteMigration() {
        try {
            // 检查是否有旧版数据
            boolean hasOldData = hasOldVersionData();
            
            // 检查新版表是否为空
            boolean newTablesEmpty = areNewTablesEmpty();
            
            return hasOldData && newTablesEmpty;
            
        } catch (Exception ex) {
            log.warn("检查迁移状态失败", ex);
            return false;
        }
    }

    /**
     * 检查是否有旧版数据
     */
    private boolean hasOldVersionData() {
        try {
            Integer clientCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_client_details", Integer.class);
            return clientCount != null && clientCount > 0;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 检查新版表是否为空
     */
    private boolean areNewTablesEmpty() {
        try {
            Integer clientCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_registered_client", Integer.class);
            Integer authorizationCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_authorization", Integer.class);
            
            return clientCount != null && clientCount == 0 && authorizationCount != null && authorizationCount == 0;
        } catch (Exception ex) {
            // 如果表不存在，认为是空的
            return true;
        }
    }

    /**
     * 获取迁移状态报告
     */
    public String getMigrationStatusReport() {
        StringBuilder report = new StringBuilder();
        report.append("OAuth2数据库迁移状态报告\n");
        report.append("========================\n");
        
        try {
            // 旧版表统计
            report.append("旧版表数据统计：\n");
            report.append(String.format("- oauth_client_details: %d 条\n", 
                getTableCount("oauth_client_details")));
            
            // 新版表统计
            report.append("新版表数据统计：\n");
            report.append(String.format("- oauth2_registered_client: %d 条\n", 
                getTableCount("oauth2_registered_client")));
            report.append(String.format("- oauth2_authorization: %d 条\n", 
                getTableCount("oauth2_authorization")));
            report.append(String.format("- oauth2_authorization_consent: %d 条\n", 
                getTableCount("oauth2_authorization_consent")));
            
            // 迁移建议
            if (needsCompleteMigration()) {
                report.append("\n建议：需要执行完整迁移\n");
            } else {
                report.append("\n状态：迁移已完成或无需迁移\n");
            }
            
        } catch (Exception ex) {
            report.append("获取状态失败：").append(ex.getMessage()).append("\n");
        }
        
        return report.toString();
    }

    /**
     * 获取表记录数
     */
    private Integer getTableCount(String tableName) {
        try {
            return jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM " + tableName, Integer.class);
        } catch (Exception ex) {
            return 0;
        }
    }
}
