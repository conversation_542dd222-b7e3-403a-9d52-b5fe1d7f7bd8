package com.yeelight.service.user.server.migration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OAuth2授权同意数据迁移服务
 * 将旧版的oauth_approvals表数据迁移到新版的oauth2_authorization_consent表
 * 
 * <AUTHOR>
 * @since 2024-07-31
 */
@Slf4j
@Service
public class OAuth2ConsentMigrationService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 旧版表查询SQL
     */
    private static final String SELECT_APPROVALS_SQL = """
        SELECT userId, clientId, scope, status, expiresAt, lastModifiedAt
        FROM oauth_approvals
        WHERE status = 'APPROVED'
        """;

    /**
     * 新版表插入SQL
     */
    private static final String INSERT_CONSENT_SQL = """
        INSERT INTO oauth2_authorization_consent
        (registered_client_id, principal_name, authorities)
        VALUES (?, ?, ?)
        """;

    public OAuth2ConsentMigrationService(DataSource oauthDataSource) {
        this.jdbcTemplate = new JdbcTemplate(oauthDataSource);
    }

    /**
     * 执行授权同意数据迁移
     */
    public void migrateConsents() {
        try {
            log.info("开始迁移OAuth2授权同意数据...");
            
            // 清空新表数据
            jdbcTemplate.update("DELETE FROM oauth2_authorization_consent");
            
            // 查询旧版授权同意数据
            List<Map<String, Object>> approvals = jdbcTemplate.queryForList(SELECT_APPROVALS_SQL);
            
            // 按用户和客户端分组，合并同一用户对同一客户端的所有授权范围
            Map<String, List<Map<String, Object>>> groupedApprovals = approvals.stream()
                .collect(Collectors.groupingBy(approval -> 
                    approval.get("userId") + ":" + approval.get("clientId")));
            
            int migratedCount = 0;
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedApprovals.entrySet()) {
                try {
                    migrateConsentGroup(entry.getValue());
                    migratedCount++;
                } catch (Exception ex) {
                    log.error("迁移授权同意失败: {}", entry.getKey(), ex);
                }
            }
            
            log.info("OAuth2授权同意数据迁移完成，共迁移 {} 个用户-客户端组合", migratedCount);
            
        } catch (Exception ex) {
            log.error("OAuth2授权同意数据迁移失败", ex);
            throw new RuntimeException("授权同意数据迁移失败", ex);
        }
    }

    /**
     * 迁移单个用户-客户端的授权同意组
     */
    private void migrateConsentGroup(List<Map<String, Object>> approvalGroup) {
        if (approvalGroup.isEmpty()) {
            return;
        }
        
        // 获取第一个记录的基本信息
        Map<String, Object> firstApproval = approvalGroup.getFirst();
        String userId = (String) firstApproval.get("userId");
        String clientId = (String) firstApproval.get("clientId");
        
        // 获取注册客户端ID
        String registeredClientId = getRegisteredClientId(clientId);
        if (registeredClientId == null) {
            log.warn("找不到客户端 {} 的注册信息，跳过授权同意迁移", clientId);
            return;
        }
        
        // 收集所有授权范围
        Set<String> scopes = approvalGroup.stream()
            .map(approval -> (String) approval.get("scope"))
            .filter(StringUtils::hasText)
            .collect(Collectors.toSet());
        
        // 转换为权限字符串（新版使用SCOPE_前缀）
        String authorities = scopes.stream()
            .map(scope -> "SCOPE_" + scope)
            .collect(Collectors.joining(","));
        
        if (!StringUtils.hasText(authorities)) {
            log.warn("用户 {} 对客户端 {} 没有有效的授权范围，跳过迁移", userId, clientId);
            return;
        }
        
        try {
            // 插入授权同意记录
            jdbcTemplate.update(INSERT_CONSENT_SQL,
                registeredClientId,
                userId,
                authorities
            );
            
            log.debug("成功迁移授权同意: 用户={}, 客户端={}, 权限={}", userId, clientId, authorities);
            
        } catch (Exception ex) {
            log.error("插入授权同意记录失败: 用户={}, 客户端={}", userId, clientId, ex);
            throw ex;
        }
    }

    /**
     * 根据客户端ID获取注册客户端ID
     */
    private String getRegisteredClientId(String clientId) {
        try {
            return jdbcTemplate.queryForObject(
                "SELECT id FROM oauth2_registered_client WHERE client_id = ?",
                String.class, clientId);
        } catch (Exception ex) {
            log.debug("找不到客户端 {} 的注册信息", clientId);
            return null;
        }
    }

    /**
     * 检查旧版表是否存在数据
     */
    public boolean hasOldConsentData() {
        try {
            Integer approvalCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_approvals WHERE status = 'APPROVED'", Integer.class);
            return approvalCount != null && approvalCount > 0;
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 获取迁移统计信息
     */
    public Map<String, Integer> getMigrationStats() {
        Map<String, Integer> stats = new HashMap<>(4);
        
        try {
            stats.put("old_approvals", jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth_approvals WHERE status = 'APPROVED'", Integer.class));
        } catch (Exception ex) {
            stats.put("old_approvals", 0);
        }
        
        try {
            stats.put("old_approval_users", jdbcTemplate.queryForObject(
                "SELECT COUNT(DISTINCT userId) FROM oauth_approvals WHERE status = 'APPROVED'", Integer.class));
        } catch (Exception ex) {
            stats.put("old_approval_users", 0);
        }
        
        try {
            stats.put("old_approval_clients", jdbcTemplate.queryForObject(
                "SELECT COUNT(DISTINCT clientId) FROM oauth_approvals WHERE status = 'APPROVED'", Integer.class));
        } catch (Exception ex) {
            stats.put("old_approval_clients", 0);
        }
        
        try {
            stats.put("new_consents", jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_authorization_consent", Integer.class));
        } catch (Exception ex) {
            stats.put("new_consents", 0);
        }
        
        return stats;
    }

    /**
     * 清理过期的授权同意数据
     * 在迁移前可以调用此方法清理旧版表中的过期数据
     */
    public int cleanupExpiredApprovals() {
        try {
            String cleanupSql = """
                DELETE FROM oauth_approvals
                WHERE expiresAt IS NOT NULL AND expiresAt < NOW()
                """;
            
            int deletedCount = jdbcTemplate.update(cleanupSql);
            log.info("清理过期授权同意数据完成，删除 {} 条记录", deletedCount);
            return deletedCount;
            
        } catch (Exception ex) {
            log.error("清理过期授权同意数据失败", ex);
            return 0;
        }
    }

    /**
     * 验证迁移结果
     */
    public boolean validateMigration() {
        try {
            // 检查是否有数据迁移
            Integer newConsentCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM oauth2_authorization_consent", Integer.class);
            
            if (newConsentCount == null || newConsentCount == 0) {
                log.warn("新版授权同意表为空，可能迁移失败或原本就没有数据");
                return false;
            }
            
            // 检查数据完整性
            Integer distinctUserClientPairs = jdbcTemplate.queryForObject(
                "SELECT COUNT(DISTINCT CONCAT(userId, ':', clientId)) FROM oauth_approvals WHERE status = 'APPROVED'", 
                Integer.class);
            
            if (!newConsentCount.equals(distinctUserClientPairs)) {
                log.warn("迁移数据数量不匹配：期望 {} 条，实际 {} 条", distinctUserClientPairs, newConsentCount);
                return false;
            }
            
            log.info("授权同意数据迁移验证通过");
            return true;
            
        } catch (Exception ex) {
            log.error("验证授权同意数据迁移失败", ex);
            return false;
        }
    }

    /**
     * 获取迁移详细报告
     */
    public String getMigrationReport() {
        StringBuilder report = new StringBuilder();
        report.append("OAuth2授权同意数据迁移报告\n");
        report.append("============================\n");
        
        Map<String, Integer> stats = getMigrationStats();
        
        report.append("旧版数据统计：\n");
        report.append(String.format("- 已批准的授权记录：%d 条\n", stats.get("old_approvals")));
        report.append(String.format("- 涉及用户数：%d 个\n", stats.get("old_approval_users")));
        report.append(String.format("- 涉及客户端数：%d 个\n", stats.get("old_approval_clients")));
        
        report.append("\n新版数据统计：\n");
        report.append(String.format("- 授权同意记录：%d 条\n", stats.get("new_consents")));
        
        // 验证结果
        boolean isValid = validateMigration();
        report.append(String.format("\n迁移验证：%s\n", isValid ? "通过" : "失败"));
        
        return report.toString();
    }
}
