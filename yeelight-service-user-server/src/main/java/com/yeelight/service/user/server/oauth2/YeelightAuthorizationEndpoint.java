package com.yeelight.service.user.server.oauth2;

import com.yeelight.service.user.client.oauth2.YeelightAuthorizationRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.SessionStatus;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.RedirectView;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Yeelight自建AuthorizationEndpoint
 * 完全兼容org.springframework.security.oauth2.provider.endpoint.AuthorizationEndpoint
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@Component
public class YeelightAuthorizationEndpoint {

    private final OAuth2AuthorizationService authorizationService;
    private final RegisteredClientRepository registeredClientRepository;
    private final YeelightOAuth2RequestFactory oauth2RequestFactory;

    public YeelightAuthorizationEndpoint(OAuth2AuthorizationService authorizationService,
                                        RegisteredClientRepository registeredClientRepository,
                                        YeelightOAuth2RequestFactory oauth2RequestFactory) {
        this.authorizationService = authorizationService;
        this.registeredClientRepository = registeredClientRepository;
        this.oauth2RequestFactory = oauth2RequestFactory;
    }

    /**
     * 处理授权批准或拒绝
     * 兼容旧版AuthorizationEndpoint.approveOrDeny()方法
     * 
     * @param approvalParameters 批准参数
     * @param model 模型数据
     * @param sessionStatus 会话状态
     * @param principal 当前用户认证信息
     * @return 重定向视图
     */
    public View approveOrDeny(Map<String, String> approvalParameters, 
                             Map<String, ?> model, 
                             SessionStatus sessionStatus, 
                             Authentication principal) {
        
        try {
            // 从模型中获取授权请求
            YeelightAuthorizationRequest authorizationRequest = 
                (YeelightAuthorizationRequest) model.get("authorizationRequest");
            
            if (authorizationRequest == null) {
                throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_REQUEST, 
                    "Authorization request not found in session", null));
            }

            // 检查用户是否批准了授权
            String userApproval = approvalParameters.get("user_oauth_approval");
            boolean approved = "true".equals(userApproval);
            
            authorizationRequest.setApproved(approved);

            // 处理授权范围的批准
            processApprovalScopes(approvalParameters, authorizationRequest);

            if (approved) {
                // 用户批准授权，生成授权码并重定向
                return handleApprovedAuthorization(authorizationRequest, principal);
            } else {
                // 用户拒绝授权，重定向到错误页面
                return handleDeniedAuthorization(authorizationRequest);
            }

        } catch (OAuth2AuthenticationException e) {
            log.error("OAuth2 authentication error during approval", e);
            return createErrorRedirect(approvalParameters.get("redirect_uri"), 
                e.getError().getErrorCode(), e.getError().getDescription(), 
                approvalParameters.get("state"));
        } catch (Exception e) {
            log.error("Unexpected error during authorization approval", e);
            return createErrorRedirect(approvalParameters.get("redirect_uri"), 
                "server_error", "Internal server error", 
                approvalParameters.get("state"));
        }
    }

    /**
     * 处理批准的授权
     */
    private View handleApprovedAuthorization(YeelightAuthorizationRequest authorizationRequest, 
                                           Authentication principal) {
        String redirectUri = authorizationRequest.getRedirectUri();
        String responseType = authorizationRequest.getResponseType();
        String state = authorizationRequest.getState();

        if ("code".equals(responseType)) {
            // 授权码模式
            String authorizationCode = generateAuthorizationCode(authorizationRequest, principal);
            return createSuccessRedirect(redirectUri, "code", authorizationCode, state);
        } else if ("token".equals(responseType)) {
            // 隐式授权模式
            String accessToken = generateAccessToken(authorizationRequest, principal);
            return createSuccessRedirect(redirectUri, "access_token", accessToken, state);
        } else {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.UNSUPPORTED_RESPONSE_TYPE, 
                "Unsupported response type: " + responseType, null));
        }
    }

    /**
     * 处理拒绝的授权
     */
    private View handleDeniedAuthorization(YeelightAuthorizationRequest authorizationRequest) {
        String redirectUri = authorizationRequest.getRedirectUri();
        String state = authorizationRequest.getState();
        
        return createErrorRedirect(redirectUri, "access_denied", 
            "The user denied the request", state);
    }

    /**
     * 处理授权范围的批准
     */
    private void processApprovalScopes(Map<String, String> approvalParameters, 
                                     YeelightAuthorizationRequest authorizationRequest) {
        // 检查用户对各个scope的批准情况
        for (Map.Entry<String, String> entry : approvalParameters.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            if (key.startsWith("scope.") && "true".equals(value)) {
                // 移除"scope."前缀
                String scope = key.substring(6);
                authorizationRequest.getScope().add(scope);
            }
        }
    }

    /**
     * 生成授权码
     */
    private String generateAuthorizationCode(YeelightAuthorizationRequest authorizationRequest, 
                                           Authentication principal) {
        // 这里应该调用新版Spring Authorization Server的授权码生成逻辑
        // 为了兼容性，这里返回一个模拟的授权码
        // 实际实现中应该集成到新版的OAuth2AuthorizationService中
        
        // 生成一个简单的授权码（实际应该使用更安全的方式）
        String code = "AUTH_CODE_" + System.currentTimeMillis() + "_" + 
                     authorizationRequest.getClientId().hashCode();
        
        log.info("Generated authorization code for client: {}, user: {}", 
                authorizationRequest.getClientId(), principal.getName());
        
        return code;
    }

    /**
     * 生成访问令牌（隐式模式）
     */
    private String generateAccessToken(YeelightAuthorizationRequest authorizationRequest, 
                                     Authentication principal) {
        // 这里应该调用新版Spring Authorization Server的令牌生成逻辑
        // 为了兼容性，这里返回一个模拟的访问令牌
        
        String token = "ACCESS_TOKEN_" + System.currentTimeMillis() + "_" + 
                      authorizationRequest.getClientId().hashCode();
        
        log.info("Generated access token for client: {}, user: {}", 
                authorizationRequest.getClientId(), principal.getName());
        
        return token;
    }

    /**
     * 创建成功重定向视图
     */
    private View createSuccessRedirect(String redirectUri, String paramName, 
                                     String paramValue, String state) {
        StringBuilder url = new StringBuilder(redirectUri);
        
        // 检查URL是否已经包含查询参数
        if (redirectUri.contains("?")) {
            url.append("&");
        } else {
            url.append("?");
        }
        
        url.append(paramName).append("=").append(urlEncode(paramValue));
        
        if (state != null) {
            url.append("&state=").append(urlEncode(state));
        }
        
        return new RedirectView(url.toString());
    }

    /**
     * 创建错误重定向视图
     */
    private View createErrorRedirect(String redirectUri, String error, 
                                   String errorDescription, String state) {
        if (redirectUri == null) {
            // 如果没有重定向URI，返回一个错误页面
            return new RedirectView("/oauth/error?error=" + urlEncode(error) + 
                                  "&error_description=" + urlEncode(errorDescription));
        }
        
        StringBuilder url = new StringBuilder(redirectUri);
        
        if (redirectUri.contains("?")) {
            url.append("&");
        } else {
            url.append("?");
        }
        
        url.append("error=").append(urlEncode(error));
        
        if (errorDescription != null) {
            url.append("&error_description=").append(urlEncode(errorDescription));
        }
        
        if (state != null) {
            url.append("&state=").append(urlEncode(state));
        }
        
        return new RedirectView(url.toString());
    }

    /**
     * URL编码
     */
    private String urlEncode(String value) {
        if (value == null) {
            return "";
        }
        return URLEncoder.encode(value, StandardCharsets.UTF_8);
    }
}
