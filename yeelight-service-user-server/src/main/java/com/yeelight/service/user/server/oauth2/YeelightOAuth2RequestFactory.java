package com.yeelight.service.user.server.oauth2;

import com.yeelight.service.user.client.oauth2.YeelightAuthorizationRequest;
import com.yeelight.service.user.client.oauth2.YeelightClientDetails;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;
import com.yeelight.service.user.client.oauth2.YeelightTokenRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Yeelight自建OAuth2RequestFactory
 * 完全兼容org.springframework.security.oauth2.provider.OAuth2RequestFactory
 * 用于替代旧版OAuth2依赖，保持向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@Component
public class YeelightOAuth2RequestFactory {

    /**
     * 客户端详情仓库适配器
     */
    private final YeelightRegisteredClientRepositoryAdapter clientDetailsService;

    public YeelightOAuth2RequestFactory(YeelightRegisteredClientRepositoryAdapter clientDetailsService) {
        this.clientDetailsService = clientDetailsService;
    }

    /**
     * 创建授权请求
     * 兼容旧版OAuth2RequestFactory.createAuthorizationRequest()方法
     */
    public YeelightAuthorizationRequest createAuthorizationRequest(Map<String, String> authorizationParameters) {
        String clientId = authorizationParameters.get("client_id");
        if (clientId == null || clientId.trim().isEmpty()) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_CLIENT, "Missing client_id parameter", null));
        }

        // 验证客户端是否存在
        YeelightClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        if (clientDetails == null) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_CLIENT, "Invalid client: " + clientId, null));
        }

        // 创建授权请求
        YeelightAuthorizationRequest authorizationRequest = YeelightAuthorizationRequest.fromParameters(authorizationParameters);

        // 设置客户端的默认授权范围
        Set<String> requestedScopes = authorizationRequest.getScope();
        Set<String> clientScopes = clientDetails.getScope();
        
        if (requestedScopes.isEmpty() && !clientScopes.isEmpty()) {
            // 如果请求中没有指定scope，使用客户端的默认scope
            authorizationRequest.setScope(new HashSet<>(clientScopes));
        } else if (!requestedScopes.isEmpty()) {
            // 验证请求的scope是否在客户端允许的范围内
            for (String scope : requestedScopes) {
                if (!clientScopes.contains(scope)) {
                    throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_SCOPE,
                        "Invalid scope: " + scope + " for client: " + clientId, null));
                }
            }
        }

        // 设置客户端的资源ID
        authorizationRequest.setResourceIds(new HashSet<>(clientDetails.getResourceIds()));

        // 验证响应类型
        String responseType = authorizationRequest.getResponseType();
        if (responseType != null) {
            Set<String> authorizedGrantTypes = clientDetails.getAuthorizedGrantTypes();
            if ("code".equals(responseType) && !authorizedGrantTypes.contains("authorization_code")) {
                throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT,
                    "Client not authorized for authorization code grant", null));
            }
            if ("token".equals(responseType) && !authorizedGrantTypes.contains("implicit")) {
                throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT,
                    "Client not authorized for implicit grant", null));
            }
        }

        // 验证重定向URI
        String redirectUri = authorizationRequest.getRedirectUri();
        if (redirectUri != null) {
            Set<String> registeredRedirectUris = clientDetails.getRegisteredRedirectUri();
            if (!registeredRedirectUris.isEmpty() && !registeredRedirectUris.contains(redirectUri)) {
                throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_REQUEST,
                    "Invalid redirect URI: " + redirectUri, null));
            }
        }

        return authorizationRequest;
    }

    /**
     * 创建OAuth2请求
     * 兼容旧版OAuth2RequestFactory.createOAuth2Request()方法
     */
    public YeelightOAuth2Request createOAuth2Request(YeelightAuthorizationRequest request) {
        return request.createOAuth2Request();
    }

    /**
     * 创建OAuth2请求（从客户端详情）
     * 兼容旧版OAuth2RequestFactory.createOAuth2Request()方法
     */
    public YeelightOAuth2Request createOAuth2Request(YeelightClientDetails client, YeelightTokenRequest tokenRequest) {
        String clientId = tokenRequest.getClientId();
        if (client.getClientId().equals(clientId)) {
            // 验证授权类型
            String grantType = tokenRequest.getGrantType();
            if (!client.getAuthorizedGrantTypes().contains(grantType)) {
                throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT,
                    "Client not authorized for grant type: " + grantType, null));
            }

            // 验证授权范围
            Set<String> requestedScopes = tokenRequest.getScope();
            Set<String> clientScopes = client.getScope();
            
            if (!requestedScopes.isEmpty()) {
                for (String scope : requestedScopes) {
                    if (!clientScopes.contains(scope)) {
                        throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_SCOPE,
                            "Invalid scope: " + scope, null));
                    }
                }
            } else {
                // 使用客户端的默认scope
                requestedScopes = new HashSet<>(clientScopes);
            }

            // 创建权限集合
            Collection<GrantedAuthority> authorities = client.getAuthorities();
            Set<String> authorityStrings = authorities.stream()
                    .map(GrantedAuthority::getAuthority)
                    .collect(Collectors.toSet());

            return YeelightOAuth2Request.builder()
                    .clientId(clientId)
                    .requestParameters(new HashMap<>(tokenRequest.getRequestParameters()))
                    .scope(requestedScopes)
                    .authorities(authorityStrings)
                    .approved(true)
                    .resourceIds(new HashSet<>(client.getResourceIds()))
                    .grantType(grantType)
                    .build();
        }
        throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_CLIENT,
            "Client ID mismatch", null));
    }

    /**
     * 创建Token请求
     * 兼容旧版OAuth2RequestFactory.createTokenRequest()方法
     */
    public YeelightTokenRequest createTokenRequest(Map<String, String> requestParameters, 
                                                   YeelightAuthorizationRequest authorizationRequest) {
        String clientId = authorizationRequest.getClientId();
        Set<String> scopes = authorizationRequest.getScope();
        String grantType = requestParameters.get("grant_type");

        return new YeelightTokenRequest(requestParameters, clientId, scopes, grantType);
    }

    /**
     * 创建Token请求（从请求参数）
     * 兼容旧版OAuth2RequestFactory.createTokenRequest()方法
     */
    public YeelightTokenRequest createTokenRequest(Map<String, String> requestParameters, String clientId) {
        String grantType = requestParameters.get("grant_type");
        Set<String> scopes = parseScopes(requestParameters.get("scope"));
        
        return new YeelightTokenRequest(requestParameters, clientId, scopes, grantType);
    }

    /**
     * 解析授权范围字符串
     */
    private Set<String> parseScopes(String scopeString) {
        if (scopeString == null || scopeString.trim().isEmpty()) {
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(scopeString.trim().split("\\s+")));
    }

    /**
     * 验证客户端详情
     */
    private void validateClient(String clientId) {
        YeelightClientDetails client = clientDetailsService.loadClientByClientId(clientId);
        if (client == null) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_CLIENT,
                "Invalid client: " + clientId, null));
        }
    }

    /**
     * 验证授权范围
     */
    private void validateScope(Set<String> requestedScopes, Set<String> clientScopes, String clientId) {
        if (requestedScopes.isEmpty()) {
            return; // 空scope是允许的
        }
        
        for (String scope : requestedScopes) {
            if (!clientScopes.contains(scope)) {
                throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_SCOPE,
                    "Invalid scope: " + scope + " for client: " + clientId, null));
            }
        }
    }

    /**
     * 验证重定向URI
     */
    private void validateRedirectUri(String redirectUri, Set<String> registeredUris) {
        if (redirectUri == null) {
            return; // 某些情况下重定向URI可以为空
        }
        
        if (!registeredUris.isEmpty() && !registeredUris.contains(redirectUri)) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_REQUEST,
                "Invalid redirect URI: " + redirectUri, null));
        }
    }

    /**
     * 验证授权类型
     */
    private void validateGrantType(String grantType, Set<String> authorizedGrantTypes, String clientId) {
        if (grantType == null) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_REQUEST,
                "Missing grant_type parameter", null));
        }
        
        if (!authorizedGrantTypes.contains(grantType)) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT,
                "Client " + clientId + " not authorized for grant type: " + grantType, null));
        }
    }
}
