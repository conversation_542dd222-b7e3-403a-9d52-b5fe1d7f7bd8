package com.yeelight.service.user.server.oauth2;

import com.yeelight.service.user.client.oauth2.YeelightClientDetails;
import com.yeelight.service.user.client.oauth2.YeelightClientDetailsImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Yeelight注册客户端仓库适配器
 * 将新版RegisteredClientRepository适配为YeelightClientDetails
 * 用于连接新旧版本的客户端仓库
 *
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@Component
public class YeelightRegisteredClientRepositoryAdapter {

    private final RegisteredClientRepository registeredClientRepository;

    public YeelightRegisteredClientRepositoryAdapter(RegisteredClientRepository registeredClientRepository) {
        this.registeredClientRepository = registeredClientRepository;
    }

    /**
     * 根据客户端ID加载客户端详情
     * 将新版RegisteredClient转换为YeelightClientDetails
     *
     * @param clientId 客户端ID
     * @return 客户端详情，如果不存在则返回null
     */
    public YeelightClientDetails loadClientByClientId(String clientId) {
        if (clientId == null || clientId.trim().isEmpty()) {
            throw new IllegalArgumentException("Client ID cannot be null or empty");
        }

        try {
            RegisteredClient registeredClient = registeredClientRepository.findByClientId(clientId);
            if (registeredClient == null) {
                return null;
            }

            return convertToYeelightClientDetails(registeredClient);
        } catch (Exception e) {
            log.error("Error loading client by ID: {}", clientId, e);
            return null;
        }
    }

    /**
     * 将RegisteredClient转换为YeelightClientDetails
     */
    private YeelightClientDetails convertToYeelightClientDetails(RegisteredClient registeredClient) {
        if (registeredClient == null) {
            return null;
        }

        // 创建YeelightClientDetailsImpl构建器
        YeelightClientDetailsImpl.YeelightClientDetailsImplBuilder builder = YeelightClientDetailsImpl.builder()
                .clientId(registeredClient.getClientId())
                .clientSecret(registeredClient.getClientSecret())
                .accessTokenValiditySeconds((int) registeredClient.getTokenSettings().getAccessTokenTimeToLive().toSeconds())
                .refreshTokenValiditySeconds((int) registeredClient.getTokenSettings().getRefreshTokenTimeToLive().toSeconds())
                // 默认自动批准
                .autoApprove(true);

        // 设置授权类型
        Set<String> authorizedGrantTypes = registeredClient.getAuthorizationGrantTypes().stream()
                .map(grantType -> switch (grantType.getValue()) {
                    case "authorization_code" -> "authorization_code";
                    case "refresh_token" -> "refresh_token";
                    case "client_credentials" -> "client_credentials";
                    case "password" -> "password";
                    case "urn:ietf:params:oauth:grant-type:jwt-bearer" -> "jwt-bearer";
                    default -> grantType.getValue();
                })
                .collect(Collectors.toSet());
        builder.authorizedGrantTypes(authorizedGrantTypes);

        // 设置重定向URI
        Set<String> redirectUris = new HashSet<>(registeredClient.getRedirectUris());
        builder.registeredRedirectUri(redirectUris);

        // 设置授权范围
        Set<String> scopes = new HashSet<>(registeredClient.getScopes());
        builder.scope(scopes);

        // 设置资源ID
        // 注意：新版RegisteredClient没有直接对应的resourceIds字段
        // 这里使用clientId作为默认资源ID
        builder.resourceIds(Set.of(registeredClient.getClientId()));

        // 设置权限
        // 注意：新版RegisteredClient没有直接对应的authorities字段
        // 这里使用默认的ROLE_CLIENT权限
        builder.authorities(Set.of(new SimpleGrantedAuthority("ROLE_CLIENT")));

        // 设置附加信息
        // 注意：新版RegisteredClient没有直接对应的additionalInformation字段
        builder.additionalInformation(new java.util.HashMap<>());

        return builder.build();
    }

    /**
     * 将授权类型字符串转换为Set
     */
    private Set<String> parseAuthorizedGrantTypes(String authorizedGrantTypesStr) {
        if (authorizedGrantTypesStr == null || authorizedGrantTypesStr.trim().isEmpty()) {
            return new HashSet<>();
        }
        return Arrays.stream(authorizedGrantTypesStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * 将授权范围字符串转换为Set
     */
    private Set<String> parseScope(String scopeStr) {
        if (scopeStr == null || scopeStr.trim().isEmpty()) {
            return new HashSet<>();
        }
        return Arrays.stream(scopeStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    /**
     * 将权限字符串转换为Set
     */
    private Set<SimpleGrantedAuthority> parseAuthorities(String authoritiesStr) {
        if (authoritiesStr == null || authoritiesStr.trim().isEmpty()) {
            return new HashSet<>();
        }
        return Arrays.stream(authoritiesStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toSet());
    }
}
