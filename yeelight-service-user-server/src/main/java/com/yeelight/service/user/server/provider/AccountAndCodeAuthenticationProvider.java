package com.yeelight.service.user.server.provider;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.AccountTypeEnum;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;

/**
 * 账号+验证码认证器
 * <AUTHOR>
 */
@Component
public class AccountAndCodeAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageService;

    /**
     * 重写retrieveUser方法，用于在认证过程中获取用户详细信息。
     * 根据账户和认证信息从数据库中检索用户，并进行验证码的验证。
     *
     * @param account 用户账户名
     * @param authentication 用户认证信息，包含密码和验证码等
     * @return UserDetails 用户的详细信息对象
     * @throws UsernameNotFoundException 当用户不存在时抛出
     * @throws CaptchaException 当验证码验证失败时抛出
     * @throws DisabledException 当用户被禁用时抛出
     */
    @Override
    protected UserDetails retrieveUser(String account, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查是否为验证码模式的认证细节
        if (authentication.getDetails() instanceof LinkedHashMap) {
            AccountAndCodeDetail details = BeanUtils.objToBean(authentication.getDetails(), AccountAndCodeDetail.class);
            String accountType = details.getAccountType();

            // 参数缺失检查
            if (StringUtils.isEmpty(accountType)) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.Common.PARAMS_MISS"));
            } else {
                // 不支持email模式
                if (AccountTypeEnum.EMAIL.code.equals(accountType)) {
                    throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.Common.PARAMS_MISS"));
                }

                // 验证码参数检查
                String captchaCachePrefix = details.getCaptchaCachePrefix();
                String captchaKey = details.getCaptchaKey();
                String captcha = details.getCaptcha();
                if (StringUtils.isEmpty(captchaCachePrefix) || StringUtils.isEmpty(captchaKey) || StringUtils.isEmpty(captcha)) {
                    throw new CaptchaException(I18nUtil.getMessage("ResultCode.验证码错误"));
                }

                // 验证码已经前置验证的情况
                CaptchaResult verifyCode;
                if (SecurityConstants.CHECK_LAST_RESULT.equals(captcha)) {
                    verifyCode = captchaMessageService.checkLastResult(captchaKey, captchaCachePrefix);
                } else {
                    verifyCode = captchaMessageService.checkCaptcha(captchaKey, captcha, account, captchaCachePrefix, false);
                }
                if (!verifyCode.isSuccess()) {
                    throw new CaptchaException(verifyCode.getMessage());
                }

                // 用户存在性和启用状态检查
                YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByAccount(account);
                if (yeelightUserDto == null) {
                    throw new DisabledException(I18nUtil.message("User.Exception.user.notExist", account));
                }
                YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                if (!yeelightUser.isEnabled()) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                }
                // 返回用户详细信息
                return yeelightUser;
            }
        }
        // 默认异常，当认证细节不匹配或用户信息无法获取时抛出
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }

    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class AccountAndCodeDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;
        private String password;
        private String accountType;
        private String captchaCachePrefix;
        private String captchaKey;
        private String captcha;
    }
}
