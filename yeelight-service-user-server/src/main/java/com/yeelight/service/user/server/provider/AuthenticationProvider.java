package com.yeelight.service.user.server.provider;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.custom.CustomWebAuthenticationDetails;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.RedisKeyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 自定义认证器
 * <AUTHOR>
 */
@Component
public class AuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {

    private static final Long ERROR_COUNT_EXPIRE_TIME = 5L;

    private static final Long STATUS_EXPIRE_TIME = 10L;

    private static final int MAX_ERROR_TIMES = 5;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private PasswordEncoder userPasswordEncoder;

    @Resource
    private RedisManager redisManager;

    private ThreadLocal<Long> yeelightId;

    @PostConstruct
    private void init() {
        yeelightId = new ThreadLocal<>();
    }

    /**
     * 重写方法，用于在认证过程中检索用户详情。
     *
     * @param username 用户名
     * @param authentication 认证令牌，包含用户登录时提供的凭证
     * @return UserDetails 用户详情对象，包含了用户的角色和权限等信息
     * @throws UsernameNotFoundException 如果用户不存在时抛出
     * @throws DisabledException 如果用户被禁用时抛出
     * @throws PasswordErrorException 如果密码错误时抛出
     */
    @Override
    protected UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查认证信息中是否包含密码凭证
        if (Objects.isNull(authentication.getCredentials())) {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        } else {
            // 通过用户名查询用户信息
            YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByAccount(username);
            // 将查询到的用户信息转换为业务对象
            YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
            // 用户不存在时抛出异常
            if (yeelightUser == null) {
                throw new DisabledException(I18nUtil.getMessage("ResultCode.账号未注册"));
            }
            // 用户被禁用时抛出异常
            if (!yeelightUser.isEnabled()) {
                throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
            }
            // 检查账号是否被锁定
            if (redisManager.oauthRedisTemplate().hasKey(RedisKeyUtils.getUserLockStatusKey(yeelightUserDto.getId()))) {
                long expireTime = Long.parseLong(redisManager.oauthRedisTemplate().getExpire(RedisKeyUtils.getUserLockStatusKey(yeelightUserDto.getId())).toString());
                // 账号锁定时抛出异常，同时附带锁定剩余时间
                throw new DisabledException(I18nUtil.message("ResultCode.账号已锁定", Long.toString(expireTime)));
            }
            // 记录用户ID
            yeelightId.set(yeelightUser.getId());
            // 加载用户详情
            return yeelightUser;
        }
    }


    /**
     * 在默认的认证检查基础上进行额外的认证验证。
     * 主要进行验证码校验和密码校验。如果配置了验证码，则必须先通过验证码验证才能进行密码验证。
     * 对于密码验证，还实现了输错次数限制和账户锁定功能。
     *
     * @param userDetails UserDetails对象，表示当前登录的用户详情。
     * @param authentication Authentication对象，表示当前的认证请求。
     * @throws AuthenticationException 如果认证失败，抛出相应的异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查验证码
        checkCaptcha(authentication);
        // 检查密码是否正确
        String password = userDetails.getPassword();
        // 用户输入的密码
        String rawPassword = authentication.getCredentials().toString();

        // 验证密码是否匹配
        boolean match = userPasswordEncoder.matches(rawPassword, password);
        if (!match) {
            ValueOperations<String, Object> valueOperations = redisManager.oauthRedisTemplate().opsForValue();

            // 记录密码输错次数，并处理第一次输错设置超时时间。输错次数+1， 如果是第一次输错，设置超时时间5分钟（5分钟内输错5次，就会锁定账号）
            valueOperations.increment(RedisKeyUtils.getUserErrorCountKey(yeelightId.get()), 1);
            if (Integer.parseInt(valueOperations.get(RedisKeyUtils.getUserErrorCountKey(yeelightId.get())).toString()) == 1) {
                redisManager.oauthRedisTemplate().expire(RedisKeyUtils.getUserErrorCountKey(yeelightId.get()), ERROR_COUNT_EXPIRE_TIME, TimeUnit.MINUTES);
            }

            // 如果输错次数达到或超过设定的最大错误次数，则锁定账号。输错次数超过/等于5次 锁定账号，清空输错次数
            Long errorCount = Long.valueOf(Objects.requireNonNull(valueOperations.get(RedisKeyUtils.getUserErrorCountKey(yeelightId.get()))).toString());
            if (errorCount >= MAX_ERROR_TIMES) {
                valueOperations.set(RedisKeyUtils.getUserLockStatusKey(yeelightId.get()), RedisKeyUtils.USER_LOCK_STATUS, STATUS_EXPIRE_TIME, TimeUnit.MINUTES);
                redisManager.oauthRedisTemplate().delete(RedisKeyUtils.getUserErrorCountKey(yeelightId.get()));

                throw new PasswordErrorException(I18nUtil.message("ResultCode.密码连续输入错误", String.valueOf(errorCount)));
            }

            // 抛出密码错误还可尝试的异常，提示用户剩余尝试次数
            throw new PasswordErrorException(I18nUtil.message("ResultCode.密码不正确还可以尝试", String.valueOf(MAX_ERROR_TIMES - errorCount)));
        } else {
            // 密码匹配成功，清除相关错误记录和锁定状态
            redisManager.oauthRedisTemplate().delete(RedisKeyUtils.getUserLockStatusKey(yeelightId.get()));
            redisManager.oauthRedisTemplate().delete(RedisKeyUtils.getUserErrorCountKey(yeelightId.get()));
        }
        // 清理认证细节和移除yeelightId
        authentication.setDetails(null);
        yeelightId.remove();
    }

    private static void checkCaptcha(UsernamePasswordAuthenticationToken authentication) throws PasswordErrorException {
        if (Optional.of(SecurityConstants.IS_ENABLE_CAPTCHA).orElse(false).equals(true)) {
            // 检查验证码
            if (authentication.getDetails() instanceof CustomWebAuthenticationDetails details) {
                String inputCaptcha = details.getInputCaptcha();
                String cacheCaptcha = details.getCacheCaptcha();
                if (StringUtils.isEmpty(inputCaptcha)) {
                    throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.请输入验证码"));
                }
                if (!StringUtils.equalsIgnoreCase(inputCaptcha, cacheCaptcha)) {
                    throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.验证码错误"));
                }
            }
        }
    }
}
