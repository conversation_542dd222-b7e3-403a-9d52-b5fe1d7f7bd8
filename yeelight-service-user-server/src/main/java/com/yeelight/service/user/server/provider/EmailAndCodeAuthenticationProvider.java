package com.yeelight.service.user.server.provider;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.AccountTypeEnum;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;

/**
 * 邮箱验证码认证器
 * 该认证器会根据邮箱自动注册
 * <AUTHOR>
 */
@Component
public class EmailAndCodeAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageService;



    /**
     * 重写retrieveUser方法，用于根据邮箱和验证码认证用户详情。
     *
     * @param email 用户邮箱，作为用户名。
     * @param authentication 认证令牌，包含用户输入的凭证信息。
     * @return UserDetails 用户详情对象，如果用户存在且验证成功。
     * @throws UsernameNotFoundException 如果用户不存在或者验证失败。
     */
    @Override
    protected UserDetails retrieveUser(String email, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查是否为包含验证码详情的认证模式
        if (authentication.getDetails() instanceof LinkedHashMap) {
            EmailAndCodeDetail details = BeanUtils.objToBean(authentication.getDetails(), EmailAndCodeDetail.class);
            String accountType = details.getAccountType();
            // 验证邮箱格式
            if (!UserUtils.isEmail(details.getUsername())) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.邮箱未注册"));
            }
            // 检查账号类型是否为邮箱
            if (StringUtils.isEmpty(accountType) || !AccountTypeEnum.EMAIL.code.equals(details.getAccountType())) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.Common.PARAMS_MISS"));
            }
            // 验证验证码相关参数
            String captcha = details.getCaptcha();
            if (StringUtils.isEmpty(details.getCaptchaKey())) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.验证码错误"));
            }
            if (StringUtils.isEmpty(captcha)) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.请输入验证码"));
            } else {
                // 验证验证码
                String captchaCachePrefix = details.getCaptchaCachePrefix();
                String captchaKey = details.getCaptchaKey();
                if (StringUtils.isEmpty(captchaCachePrefix)) {
                    throw new CaptchaException(I18nUtil.getMessage("ResultCode.请输入验证码"));
                }
                if (StringUtils.isEmpty(captchaKey)) {
                    throw new CaptchaException(I18nUtil.getMessage("ResultCode.请输入验证码"));
                }
                if (StringUtils.isEmpty(captcha)) {
                    throw new CaptchaException(I18nUtil.getMessage("ResultCode.请输入验证码"));
                }

                CaptchaResult verifyCode;
                        // 根据验证码类型进行验证
                if (SecurityConstants.CHECK_LAST_RESULT.equals(captcha)) {
                    verifyCode = captchaMessageService.checkLastResult(captchaKey, captchaCachePrefix);
                } else {
                    verifyCode = captchaMessageService.checkCaptcha(captchaKey, captcha, email, captchaCachePrefix, false);
                }

                if (!verifyCode.isSuccess()) {
                    throw new CaptchaException(verifyCode.getMessage());
                }
                // 根据邮箱查询用户
                YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByEmail(email);

                // 如果用户不存在，则自动注册
                if (yeelightUserDto == null) {
                    yeelightUserWriteService.create(CreateUserRequest.builder().email(email).name(email).build());
                    yeelightUserDto = yeelightUserReadService.findUserByEmail(email);
                }

                // 转换用户对象并检查账户状态
                YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                if (!yeelightUser.isEnabled()) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                }
                // 返回用户详情
                return yeelightUser;
            }
        }
        // 如果不匹配验证码模式，抛出用户名或密码错误异常
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }

    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class EmailAndCodeDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;
        private String password;
        private String accountType;
        private String captchaCachePrefix;
        private String captchaKey;
        private String captcha;
    }
}
