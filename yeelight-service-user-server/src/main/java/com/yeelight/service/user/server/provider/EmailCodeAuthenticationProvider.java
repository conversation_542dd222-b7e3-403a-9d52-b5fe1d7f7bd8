package com.yeelight.service.user.server.provider;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.client.token.EmailCodeAuthenticationToken;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.custom.EmailCodeAuthenticationDetails;
import com.yeelight.service.user.server.custom.YeelightUserDetailsService;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.core.authority.mapping.NullAuthoritiesMapper;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.Assert;

/**
 * 邮箱验证码登录认证器
 * <p>
 * 参考 {@link AbstractUserDetailsAuthenticationProvider}，{@link DaoAuthenticationProvider}
 *
 * <AUTHOR>
 */
@Slf4j
public class EmailCodeAuthenticationProvider implements AuthenticationProvider {
    @Getter
    private final YeelightUserDetailsService userDetailsService;

    private final GrantedAuthoritiesMapper authoritiesMapper = new NullAuthoritiesMapper();

    private final CaptchaMessageDubboService captchaMessageHelper;

    public EmailCodeAuthenticationProvider(YeelightUserDetailsService userDetailsService, CaptchaMessageDubboService captchaMessageHelper) {
        this.userDetailsService = userDetailsService;
        this.captchaMessageHelper = captchaMessageHelper;
    }

    /**
     * 根据 {@link EmailCodeAuthenticationToken} 对用户进行身份验证。
     * 此方法负责通过检索来验证用户的身份
     * 来自自定义用户详细信息服务的用户详细信息，并执行其他
     * 在创建成功的身份验证对象之前进行的身份验证检查。
     *
     * @param authentication {@link Authentication} 对象，包含用户的
     * 用于身份验证的电子邮件和验证码。
     * @return 一个 {@link Authentication} 对象，表示经过身份验证的用户。
     * @throws AuthenticationException 如果身份验证因凭据无效而失败
     * 或任何其他与身份验证相关的问题。
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // 确保提供的身份验证是 EmailCodeAuthenticationToken 的实例。
        Assert.isInstanceOf(EmailCodeAuthenticationToken.class, authentication,
                        "Only EmailCodeAuthenticationToken is supported");

        // 从身份验证对象中检索电子邮件，如果为空则默认为 "NONE_PROVIDED"。
        String email = (authentication.getPrincipal() == null) ? "NONE_PROVIDED" : authentication.getName();

        // 从用户详细信息服务中检索给定电子邮件的 UserDetails 对象。
        UserDetails user = retrieveUser(email, (EmailCodeAuthenticationToken) authentication);
        // 确保 retrieveUser 方法返回非空的 UserDetails 对象。
        Assert.notNull(user, "retrieveUser returned null - a violation of the interface contract");

        // 对用户的凭证进行额外的身份验证检查。
        additionalAuthenticationChecks(user, (EmailCodeAuthenticationToken) authentication);

        // 为用户创建并返回成功的身份验证对象。
        return createSuccessAuthentication(user, authentication, user);
    }

    /**
     * 从用户服务中检索用户详情。
     * 这个方法被调用是为了获取用户的详细信息，基于用户的电子邮件和认证令牌。
     *
     * @param email 用户的电子邮件，作为检索用户详情的依据。
     * @param authentication EmailCodeAuthenticationToken 类型的认证令牌，包含用户的认证信息，虽然在本方法定义中未使用，但可能在实现中用到。
     * @return UserDetails 用户详情对象，包含了用户的认证信息及其他详细资料。
     * @throws AuthenticationException 如果无法检索到用户详情或出现其他认证异常，则抛出此异常。
     */
    protected UserDetails retrieveUser(String email, EmailCodeAuthenticationToken authentication)
                    throws AuthenticationException {
        // 通过getUserDetailsService()方法获取用户详情服务，并使用该服务根据电子邮件加载用户详情
        return getUserDetailsService().loadUserByEmail(email);
    }

    /**
     * 在默认的认证检查之外执行额外的认证检查。
     * 这个方法主要用于校验用户提交的邮箱验证码是否正确。
     *
     * @param userDetails 用户详情，由Spring Security框架提供，包含用户的基本信息。
     * @param authentication 用户的认证信息，包含用户提供的认证令牌。
     * @throws AuthenticationException 如果认证失败，抛出此异常。
     */
    protected void additionalAuthenticationChecks(UserDetails userDetails, EmailCodeAuthenticationToken authentication)
                    throws AuthenticationException {
        // 断言认证详情是 EmailCodeAuthenticationDetails 的实例
        Assert.isInstanceOf(EmailCodeAuthenticationDetails.class, authentication.getDetails());
        EmailCodeAuthenticationDetails details = (EmailCodeAuthenticationDetails) authentication.getDetails();
        // 获取邮箱地址，如果不存在，则默认为 "NONE_PROVIDED"
        String email = (authentication.getPrincipal() == null) ? "NONE_PROVIDED" : authentication.getName();
        // 检查验证码相关信息是否完整，不完整则抛出异常
        checkCaptcha(details, email);
        // 清除认证中的验证码详情，避免重复使用
        authentication.setDetails(null);
    }

    private void checkCaptcha(EmailCodeAuthenticationDetails details, String email) throws PasswordErrorException {
        String inputCaptcha = details.getInputCaptcha();
        String captchaKey = details.getCaptchaKey();
        if (StringUtils.isEmpty(details.getCaptchaKey())) {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.验证码错误"));
        }
        if (StringUtils.isAnyEmpty(inputCaptcha, captchaKey)) {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.请输入验证码"));
        }

        // 根据输入的验证码进行验证码验证处理
        CaptchaResult captchaResult;
        // 如果输入的验证码是请求验证最后结果的特殊标识，则检查最后的验证码验证结果
        if (SecurityConstants.CHECK_LAST_RESULT.equals(inputCaptcha)) {
            captchaResult = captchaMessageHelper.checkLastResult(captchaKey, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        } else { // 默认为常规的验证码验证
            captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, inputCaptcha, email,
                    SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
        }

        // 验证码验证不通过，则抛出认证错误异常
        if (!captchaResult.isSuccess()) {
            throw new PasswordErrorException(captchaResult.getMessage());
        }
    }

    /**
     * 创建一个成功的认证对象，用于当用户认证通过时。
     *
     * @param principal 认证主体，通常是用户信息。
     * @param authentication 用户的认证对象，包含认证过程中使用的信息。
     * @param user 用户的详细信息，包含角色和权限等。
     * @return 返回一个定制的EmailCodeAuthenticationToken对象，表示认证成功。
     */
    protected Authentication createSuccessAuthentication(Object principal, Authentication authentication,
                                                         UserDetails user) {
        // 创建一个新的EmailCodeAuthenticationToken对象，包含主体信息和权限信息
        EmailCodeAuthenticationToken result =
                        new EmailCodeAuthenticationToken(principal, authoritiesMapper.mapAuthorities(user.getAuthorities()));
        // 设置额外的认证详情信息
        result.setDetails(authentication.getDetails());

        return result;
    }

    /**
     * 只有 {@link EmailCodeAuthenticationToken} 类型才使用该认证器
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return (EmailCodeAuthenticationToken.class.isAssignableFrom(authentication));
    }
}
