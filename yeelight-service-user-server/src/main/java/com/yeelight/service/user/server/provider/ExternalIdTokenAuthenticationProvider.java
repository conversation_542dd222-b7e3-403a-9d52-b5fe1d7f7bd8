package com.yeelight.service.user.server.provider;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.IdTokenEncryptUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 外部系统根据用户ID一键登录认证器
 * 该认证器会根据idToken解密出来的用户ID进行授权认证
 * <AUTHOR>
 */
@Component
public class ExternalIdTokenAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;


    /**
     * 重写retrieveUser方法，用于根据用户名和身份验证令牌检索用户详情。
     * @param username 用户名
     * @param authentication 身份验证令牌
     * @return UserDetails 用户详情对象
     * @throws UsernameNotFoundException 当用户不存在时抛出
     */
    @Override
    protected UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查是否包含externalIdToken参数
        if (authentication.getDetails() instanceof LinkedHashMap) {
            ExternalIdTokenDetail details = BeanUtils.objToBean(authentication.getDetails(), ExternalIdTokenDetail.class);
            String externalIdToken = details.getExternalIdToken();
            // 若externalIdToken为空，则抛出用户名不存在异常
            if (StringUtils.isEmpty(externalIdToken)) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.请输入id token"));
            } else {
                try {
                    YeelightUserDto yeelightUserDto = null;
                    String clientId = details.getClientId();
                    String clientSecret = details.getClientSecret();
                    Map<String, String> idTokenMap = IdTokenEncryptUtils.decryptIdToken(externalIdToken, clientSecret);

                    String account = idTokenMap.get(IdTokenEncryptUtils.YEELIGHT_ACCOUNT_KEY);
                    Long yeelightId = Long.parseLong(idTokenMap.get(IdTokenEncryptUtils.YEELIGHT_ID_KEY));
                    String yeelightIdSign = idTokenMap.get(IdTokenEncryptUtils.YEELIGHT_ID_SIGN_KEY);
                    String clientIdInToken = idTokenMap.get(IdTokenEncryptUtils.CLIENT_ID_KEY);

                    // account为空则抛出用户名不存在异常
                    if (StringUtils.isEmpty(account)) {
                        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名不存在"));
                    }

                    // 如果clientId不是bestsyd，则校验clientId是否一致, 此处是为了兼容旧版本
                    if (!"bestsyd".equals(clientId)) {
                        // 校验clientId是否一致
                        if (!com.yeelight.service.framework.util.StringUtils.equals(clientId, clientIdInToken)) {
                            throw new UsernameNotFoundException("客户端id不匹配");
                        }
                        // 校验yeelightId是否一致
                        if (!IdTokenEncryptUtils.verifyYeelightId(yeelightId, yeelightIdSign)) {
                            throw new UsernameNotFoundException("yeelightId签名不匹配");
                        }

                        // 根据用户ID查询用户
                        yeelightUserDto = yeelightUserReadService.findUserById(yeelightId);

                    } else {
                        // 根据账号查询用户
                        yeelightUserDto = yeelightUserReadService.findUserByAccount(account);
                        if (yeelightUserDto == null) {
                            yeelightUserDto = createOrGetUserByAccount(account);
                        }
                    }

                    // 如果用户不存在，则抛出用户不存在异常
                    if (yeelightUserDto == null) {
                        throw new DisabledException(I18nUtil.message("User.Exception.user.notExist", username));
                    }

                    YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                    // 如果用户被禁用，则抛出用户禁用异常
                    if (!yeelightUser.isEnabled()) {
                        throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                    }
                    return yeelightUser;
                } catch (Exception e) {
                    // 若处理过程中发生异常，则抛出验证码异常
                    throw new CaptchaException(e.getMessage());
                }
            }
        }
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }

    /**
     * 根据账户信息创建或获取用户。
     * <p>
     * 该方法首先会判断传入的账户信息是电话号码、电子邮件还是用户名，然后创建一个用户实体并尝试在系统中创建或获取该用户。
     * 如果用户创建成功，会返回该用户的详细信息；如果创建失败或用户已存在但无法通过ID获取详细信息，则返回null。
     * </p>
     * @param account 用户的账户信息，可以是电话号码、电子邮件或用户名。
     * @return 创建或获取的用户信息，如果失败或无法获取则返回null。
     */
    private YeelightUserDto createOrGetUserByAccount(String account) {
        // 初始化用户创建请求
        CreateUserRequest createUserRequest = new CreateUserRequest();
        createUserRequest.setName(account);

        // 根据账户信息设置用户的电话号码、电子邮件或用户名
        if (UserUtils.isPhoneNumber(account)) {
            createUserRequest.setPhoneNumber(account);
        } else if (UserUtils.isEmail(account)) {
            createUserRequest.setEmail(account);
        } else {
            createUserRequest.setUsername(account);
        }

        // 尝试创建用户并获取创建后的用户ID
        Long yeelightId = yeelightUserWriteService.create(createUserRequest);
        if (yeelightId != null) {
            // 如果用户创建成功，根据ID获取用户详细信息
            return yeelightUserReadService.findUserById(yeelightId);
        } else {
            // 用户创建失败或ID获取异常，返回null
            return null;
        }
    }

    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class ExternalIdTokenDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String externalIdToken;
    }
}
