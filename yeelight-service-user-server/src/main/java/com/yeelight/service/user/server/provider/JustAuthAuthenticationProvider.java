package com.yeelight.service.user.server.provider;

import com.yeelight.service.user.client.token.JustAuthAuthenticationToken;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.SpringSecurityMessageSource;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.core.authority.mapping.NullAuthoritiesMapper;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.util.Optional;

/**
 * JustAuthAuthenticationProvider
 *
 * <AUTHOR>
 * @date 9/9/21 11:19 AM
 */
@Component
public class JustAuthAuthenticationProvider implements AuthenticationProvider {
    protected MessageSourceAccessor messages = SpringSecurityMessageSource.getAccessor();
    private final GrantedAuthoritiesMapper authoritiesMapper = new NullAuthoritiesMapper();

    @Resource
    private JustAuthUserDetailsService userDetailsService;

    /**
     * 对给定的认证信息进行认证处理。
     *
     * @param authentication 提供待认证的用户信息的 {@link Authentication} 对象。
     * @return 经过认证的 {@link Authentication} 对象，包含用户详细信息。
     * @throws AuthenticationException 如果认证过程中出现异常。
     */
    @Override
    public Authentication authenticate(Authentication authentication)
            throws AuthenticationException {
        // 确保传入的认证对象是 JustAuthAuthenticationToken 的实例
        Assert.isInstanceOf(JustAuthAuthenticationToken.class, authentication,
                () -> messages.getMessage(
                        "ExtendAuthenticationProvider.onlySupports",
                        "Only ExtendAuthenticationToken is supported"));

        JustAuthAuthenticationToken justAuthAuthenticationToken = (JustAuthAuthenticationToken) authentication;

        // 从数据库或其他来源检索用户详细信息
        UserDetails user = this.retrieveUser(justAuthAuthenticationToken);

        // 创建并返回一个成功的认证对象
        return createSuccessAuthentication(user, justAuthAuthenticationToken, user);
    }

    /**
     * 判断当前类是否支持指定的认证类型。
     *
     * @param authentication 待检查的认证类型，为Class<?>类型，表示一个泛型的类对象。
     * @return boolean 返回true表示支持该认证类型，即该类型可以被当前类处理；
     *         返回false表示不支持该认证类型。
     */
    @Override
    public boolean supports(Class<?> authentication) {
        // 检查传入的认证类型是否为JustAuthAuthenticationToken的子类或本身
        return (JustAuthAuthenticationToken.class
                .isAssignableFrom(authentication));
    }

    /**
     * 从认证令牌中检索用户详细信息
     *
     * @param authenticationToken 用户的认证令牌，用于获取用户详细信息
     * @return UserDetails 用户的详细信息对象
     * <AUTHOR> Yu
     * @date 9/14/21 4:04 PM
     * @throws AuthenticationException 如果用户详细信息加载失败，则抛出认证异常
     */
    protected UserDetails retrieveUser(JustAuthAuthenticationToken authenticationToken) throws AuthenticationException {
        try {
            // 尝试通过认证令牌从用户详细服务中加载用户详细信息
            UserDetails loadedUser = userDetailsService.loadUserByToken(authenticationToken);
            // 如果加载的用户详细信息为null，则抛出内部认证服务异常（违反了接口契约）
            return Optional.ofNullable(loadedUser)
                    .orElseThrow(() -> new InternalAuthenticationServiceException("UserDetailsService returned null, which is an interface contract violation"));
        } catch (UsernameNotFoundException | InternalAuthenticationServiceException ex) {
            // 捕获并重新抛出特定的认证异常
            throw ex;
        } catch (Exception ex) {
            // 捕获其他所有异常，并将其转换为内部认证服务异常
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }
    }


    /**
     * 创建一个成功的Authentication对象。
     *
     * @param principal 用户实体，代表认证的主体。
     * @param authentication 当前的认证token，包含认证信息。
     * @param user 用户详情，从数据库或其它源中获取的详细用户信息。
     * @return 返回一个新创建的JustAuthAuthenticationToken对象，表示认证成功。
     */
    protected Authentication createSuccessAuthentication(Object principal, JustAuthAuthenticationToken authentication, UserDetails user) {
        // 根据用户权限信息，创建新的认证token
        return new JustAuthAuthenticationToken(principal, authentication.getCredentials(), authoritiesMapper.mapAuthorities(user.getAuthorities()));
    }

}
