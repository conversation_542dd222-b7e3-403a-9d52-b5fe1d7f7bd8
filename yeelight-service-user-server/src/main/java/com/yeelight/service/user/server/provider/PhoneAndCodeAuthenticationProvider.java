package com.yeelight.service.user.server.provider;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;

/**
 * 手机号验证码认证器
 * 该认证器会根据手机号自动注册
 * <AUTHOR>
 */
@Component
public class PhoneAndCodeAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private CaptchaMessageDubboService captchaMessageService;

    /**
     * 重写获取用户详情的方法。
     * 根据提供的电话号码和认证信息，检索用户详情。该方法首先验证用户输入的验证码，
     * 如果验证通过，则查询或自动注册用户，并返回用户详情。
     *
     * @param phoneNumber 用户的电话号码，作为用户名。
     * @param authentication 用户的认证信息，包含密码和验证码等。
     * @return UserDetails 用户详情对象，包含了用户的授权信息。
     * @throws UsernameNotFoundException 如果用户不存在或验证码验证失败。
     */
    @Override
    protected UserDetails retrieveUser(String phoneNumber, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查是否为电话号码和验证码认证模式
        if (authentication.getDetails() instanceof LinkedHashMap) {
            // 将认证详情转换为自定义的PhoneAndCodeDetail对象
            PhoneAndCodeDetail details = BeanUtils.objToBean(authentication.getDetails(), PhoneAndCodeDetail.class);
            String captcha = details.getCaptcha();

            // 验证电话号码格式是否正确
            if (!UserUtils.isPhoneNumber(details.getUsername())) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.手机号未注册"));
            }

            // 确保验证码不为空
            if (StringUtils.isEmpty(captcha)) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.请输入手机验证码"));
            } else {
                // 验证验证码相关参数完整性
                String captchaCachePrefix = details.getCaptchaCachePrefix();
                String captchaKey = details.getCaptchaKey();
                if (StringUtils.isEmpty(captchaCachePrefix) || StringUtils.isEmpty(captchaKey)) {
                    throw new CaptchaException(I18nUtil.getMessage("ResultCode.验证码错误"));
                }

                // 验证验证码的正确性
                CaptchaResult verifyCode;
                if (SecurityConstants.CHECK_LAST_RESULT.equals(captcha)) {
                    verifyCode = captchaMessageService.checkLastResult(captchaKey, captchaCachePrefix);
                } else {
                    verifyCode = captchaMessageService.checkCaptcha(captchaKey, captcha, phoneNumber, captchaCachePrefix, false);
                }

                if (!verifyCode.isSuccess()) {
                    throw new CaptchaException(verifyCode.getMessage());
                }

                // 根据电话号码查询用户，如果不存在则自动注册
                YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);

                if (yeelightUserDto == null) {
                    // 如果账号是空，自动根据手机号注册账号
                    Long yeelightId = yeelightUserWriteService.create(CreateUserRequest.builder().phoneNumber(phoneNumber).name(phoneNumber).build());

                    yeelightUserDto = yeelightUserReadService.findUserById(yeelightId);
                }

                // 将用户信息转换为User对象，并检查账户是否启用
                YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                if (!yeelightUser.isEnabled()) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                }
                // 返回用户详情
                return yeelightUser;
            }
        }
        // 如果不是预期的认证模式或认证信息不完整，抛出用户名不存在异常
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }

    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class PhoneAndCodeDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;
        private String password;
        private String captchaCachePrefix;
        private String captchaKey;
        private String captcha;
    }
}
