package com.yeelight.service.user.server.provider;

import com.yeelight.basic.platform.rpc.service.JVerifyDubboService;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.exception.CaptchaException;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;

/**
 * 手机号+极光一键认证Token认证器
 * 该认证器会根据手机号自动注册
 * <AUTHOR>
 */
@Component
public class PhoneAndJverifyTokenAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @DubboReference(version = "0.0.1",timeout = 5000)
    private JVerifyDubboService jVerifyDubboService;


    /**
     * 重写方法，用于在用户登录时检索用户详细信息。
     *
     * @param username 用户名
     * @param authentication 用户认证信息
     * @return UserDetails 用户详细信息对象
     * @throws UsernameNotFoundException 当用户不存在时抛出
     */
    @Override
    protected UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查是否为手机验证码登录模式
        if (authentication.getDetails() instanceof LinkedHashMap) {
            // 将认证详情转换为PhoneAndJVerifyTokenDetail对象
            PhoneAndJverifyTokenDetail details = BeanUtils.objToBean(authentication.getDetails(), PhoneAndJverifyTokenDetail.class);
            String appName = details.getAppName();

            // 验证APP名称是否为空
            if (StringUtils.isEmpty(appName)) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.请输入APP名称"));
            } else {
                String loginToken = details.getLoginToken();

                // 验证一键登录TOKEN是否为空
                if (StringUtils.isEmpty(loginToken)) {
                    throw new CaptchaException(I18nUtil.getMessage("ResultCode.请输入一键登录TOKEN"));
                }

                try {
                    // 验证登录TOKEN并获取手机号
                    String phoneNumber = jVerifyDubboService.loginTokenVerify(appName, loginToken, null);
                    // 验证手机号是否已注册
                    if (!UserUtils.isPhoneNumber(phoneNumber)) {
                        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.手机号未注册"));
                    }

                    // 根据手机号查找用户
                    YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByPhoneNumber(phoneNumber);

                    // 如果用户不存在，自动注册新用户
                    if (yeelightUserDto == null) {
                        Long yeelightId = yeelightUserWriteService.create(CreateUserRequest.builder().phoneNumber(phoneNumber).name(phoneNumber).build());

                        yeelightUserDto = yeelightUserReadService.findUserById(yeelightId);
                    }

                    // 将用户信息转换为YeelightUser对象
                    YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                    // 验证用户是否被禁用
                    if (!yeelightUser.isEnabled()) {
                        throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                    }

                    // 返回用户详细信息
                    return yeelightUser;
                } catch (BizException e) {
                    // 抛出验证码异常
                    throw new CaptchaException(e.getMessage());
                }
            }
        }
        // 当不是手机验证码登录模式时抛出用户名或密码错误异常
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }


    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class PhoneAndJverifyTokenDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;
        private String appName;
        private String loginToken;
    }
}
