package com.yeelight.service.user.server.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.EnumUtil;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.AuthToken;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.enums.AuthUserGender;
import com.yeelight.service.user.client.enums.UserSocialSource;
import com.yeelight.service.user.server.custom.YeelightUserDetailsService;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.Optional;

/**
 * 手机号微信信息认证器
 * 该认证器会根据手机号/微信信息自动注册
 * 该认证器只给 小程序商城使用，不具有通用性
 * <AUTHOR>
 */
@Component
@Slf4j
public class PhoneAndWechatMiniShopInfoAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Resource
    private YeelightUserDetailsService detailsService;


    /**
     * 通过电话号码和微信认证信息检索用户详情。
     * @param phoneNumber 用户的电话号码。
     * @param authentication 用户的认证信息，包含微信认证的详细数据。
     * @return UserDetails 用户的详细信息对象。
     * @throws UsernameNotFoundException 当用户不存在或微信信息不完整时抛出。
     */
    @Override
    public UserDetails retrieveUser(String phoneNumber, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {
        // 检查是否有微信认证详情
        if (authentication.getDetails() instanceof LinkedHashMap) {
            // 将认证信息转换为自定义的PhoneAndWechatAuthDetail对象
            PhoneAndWechatAuthDetail details = BeanUtils.objToBean(authentication.getDetails(), PhoneAndWechatAuthDetail.class);
            // 将微信信息字符串转换为WechatAuthDetail对象
            WechatAuthDetail wechatAuthDetail = BeanUtils.objToBean(JSON.parse(details.getWxInfo()), WechatAuthDetail.class);
            // 如果未获取到微信认证信息，则抛出异常
            if (Objects.isNull(wechatAuthDetail)) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.需要微信信息"));
            } else {
                // 检查微信UnionID是否为空
                String unionId = wechatAuthDetail.getUnionId();
                if (StringUtils.isBlank(unionId)) {
                    throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.需要微信信息"));
                }
                // 根据微信信息创建或更新用户
                AuthUser authUser = wechatAuthDetailToAuthUser(UserSocialSource.WECHAT_MINI_SHOP.getCode(), wechatAuthDetail);
                authUser.setPhone(phoneNumber);
                YeelightUserDto yeelightUserDto = justAuthUserDetailsService.autoRegisterSocialUser(authUser, null);
                try {
                    // 将服务端返回的用户信息转换为本地使用的用户对象
                    YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                    // 检查用户是否被禁用
                    if (!yeelightUser.isEnabled()) {
                        throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                    }
                    // 通过用户名加载用户详情，用于进一步认证
                    return yeelightUser;
                } catch (Exception e){
                    // 记录注册或登录失败的日志
                    log.error("手机号+微信信息方式注册/登录失败，原因：{}, Trace:{}" , e.getMessage(), ExceptionUtils.getStackTrace(e));
                    throw new BizException(I18nUtil.getMessage("User.Action.RegisterOrLogin.fail"));
                }
            }
        }
        // 当认证信息不符合预期时抛出用户名不存在异常
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }

    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class PhoneAndWechatAuthDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;
        private String wxInfo;
    }

    @Data
    public static class WechatAuthDetail {
        private String openId;
        private String nickName;
        private Integer gender;
        private String language;
        private String city;
        private String province;
        private String country;
        private String avatarUrl;
        private String unionId;
    }

    private AuthUser wechatAuthDetailToAuthUser(String source, WechatAuthDetail wechatAuthDetail) {
        AuthUser authUser = new AuthUser();
        authUser.setUuid(wechatAuthDetail.getOpenId());
        authUser.setNickname(wechatAuthDetail.getNickName());
        authUser.setAvatar(wechatAuthDetail.getAvatarUrl());
        authUser.setLocation(wechatAuthDetail.getCountry());
        AuthUserGender authUserGender = EnumUtil.getEnumConstantByCode(Optional.ofNullable(wechatAuthDetail.getGender()).orElse(0).toString(), AuthUserGender.class);
        if (Objects.isNull(authUserGender)) {
            authUserGender = AuthUserGender.UNKNOWN;
        }
        authUser.setGender(authUserGender);
        authUser.setSource(source);

        authUser.setRawUserInfo(JSONObject.parseObject(JSON.toJSONString(wechatAuthDetail)));
        AuthToken authToken = AuthToken.builder()
                .openId(wechatAuthDetail.getOpenId())
                .unionId(wechatAuthDetail.getUnionId())
                .build();
        authUser.setToken(authToken);
        return authUser;
    }
}
