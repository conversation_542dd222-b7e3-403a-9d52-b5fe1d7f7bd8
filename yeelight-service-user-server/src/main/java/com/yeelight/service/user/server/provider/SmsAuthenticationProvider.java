package com.yeelight.service.user.server.provider;

import com.yeelight.basic.platform.rpc.dto.CaptchaResult;
import com.yeelight.basic.platform.rpc.service.CaptchaMessageDubboService;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.client.token.SmsAuthenticationToken;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.custom.SmsAuthenticationDetails;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.custom.YeelightUserDetailsService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.core.authority.mapping.NullAuthoritiesMapper;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.Assert;

/**
 * 短信登录认证器
 * <p>
 * 参考 {@link AbstractUserDetailsAuthenticationProvider}，{@link DaoAuthenticationProvider}
 *
 * <AUTHOR>
 */
@Slf4j
public class SmsAuthenticationProvider implements AuthenticationProvider {
    @Getter
    private final YeelightUserDetailsService userDetailsService;

    private final GrantedAuthoritiesMapper authoritiesMapper = new NullAuthoritiesMapper();

    private final CaptchaMessageDubboService captchaMessageHelper;

    public SmsAuthenticationProvider(YeelightUserDetailsService userDetailsService, CaptchaMessageDubboService captchaMessageHelper) {
        this.userDetailsService = userDetailsService;
        this.captchaMessageHelper = captchaMessageHelper;
    }

    /**
     * 根据提供的 {@link SmsAuthenticationToken} 对用户进行身份验证。
     * 此方法验证令牌，检索用户详细信息，执行其他
     * 检查，如果验证通过，则创建成功身份验证响应。
     *
     * @param authentication The {@link Authentication} 对象，代表用户的凭证。
     *                       它必须是 {@link SmsAuthenticationToken} 的实例。
     * @return 一个经过认证的 {@link Authentication} 对象，代表已认证的用户。
     * @throws AuthenticationException 如果在过程中发生身份验证错误，
     *                                 例如提供的令牌不是 {@link SmsAuthenticationToken} 的实例
     *                                 或者无法检索用户详细信息时。
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        // 确保提供的身份验证是 SmsAuthenticationToken 的实例
        Assert.isInstanceOf(SmsAuthenticationToken.class, authentication,
                        "Only SmsAuthenticationToken is supported");
        // 从身份验证主体中提取电话号码，或使用默认值
        String phoneNumber = (authentication.getPrincipal() == null) ? "NONE_PROVIDED" : authentication.getName();
        // 根据电话号码和身份验证令牌检索用户详细信息
        UserDetails user = retrieveUser(phoneNumber, (SmsAuthenticationToken) authentication);
        // 确保 retrieveUser 方法返回非空的 UserDetails 对象
        Assert.notNull(user, "retrieveUser returned null - a violation of the interface contract");
        // 对用户和令牌执行额外的检查，如密码验证
        additionalAuthenticationChecks(user, (SmsAuthenticationToken) authentication);
        // 为用户创建并返回成功的身份验证对象
        return createSuccessAuthentication(user, authentication, user);
    }


    /**
     * 从用户服务中检索用户详情。
     * 这个方法被用来在认证过程中获取用户详细信息，基于用户提供的电话号码。
     *
     * @param phoneNumber 用户的电话号码，作为检索用户详情的依据。
     * @param authentication SmsAuthenticationToken 对象，包含用户的认证信息，但在这个方法中未使用。
     * @return UserDetails 用户详情对象，包含了用户的基本信息和权限等。
     * @throws AuthenticationException 如果无法检索到用户详情或出现其他认证异常，则抛出。
     */
    protected UserDetails retrieveUser(String phoneNumber, SmsAuthenticationToken authentication)
            throws AuthenticationException {
        // 通过用户服务加载指定电话号码的用户详情
        return getUserDetailsService().loadUserByPhoneNumber(phoneNumber);
    }

    /**
     * 进行额外的认证检查。
     * 本方法主要用于在用户通过短信验证码进行登录时，对验证码进行验证。
     *
     * @param userDetails 用户详情，由框架自动传入，一般包含用户名、密码等信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如验证码。
     * @throws AuthenticationException 如果认证失败，抛出此异常。
     */
    protected void additionalAuthenticationChecks(UserDetails userDetails, SmsAuthenticationToken authentication)
                        throws AuthenticationException {
        // 确保传入的认证详情是SmsAuthenticationDetails类型的实例
        Assert.isInstanceOf(SmsAuthenticationDetails.class, authentication.getDetails());
        SmsAuthenticationDetails details = (SmsAuthenticationDetails) authentication.getDetails();
        // 获取手机号，如果不存在则默认为"NONE_PROVIDED"
        String phoneNumber = (authentication.getPrincipal() == null) ? "NONE_PROVIDED" : authentication.getName();

        // 验证验证码是否为空
        String inputCaptcha = details.getInputCaptcha();
        String captchaKey = details.getCaptchaKey();
        if (StringUtils.isEmpty(details.getCaptchaKey())) {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.验证码错误"));
        }
        // 验证输入的验证码和验证码键是否都存在
        if (StringUtils.isAnyEmpty(inputCaptcha, captchaKey)) {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.请输入手机验证码"));
        }

        // 验证码已经前置验证的情况, 验证验证码的逻辑
        CaptchaResult captchaResult;
        // 如果输入的验证码是请求最后结果的标识，则检查最后的结果
        if (SecurityConstants.CHECK_LAST_RESULT.equals(inputCaptcha)) {
            captchaResult = captchaMessageHelper.checkLastResult(captchaKey, SecurityConstants.CAPTCHA_CACHE_PREFIX);
        } else {
            // 否则，常规的验证码验证流程
            captchaResult = captchaMessageHelper.checkCaptcha(captchaKey, inputCaptcha, phoneNumber,
                    SecurityConstants.CAPTCHA_CACHE_PREFIX, false);
        }

        // 清除认证详情中的验证码信息，避免重复使用
        authentication.setDetails(null);

        // 如果验证码验证失败，抛出异常
        if (!captchaResult.isSuccess()) {
            throw new PasswordErrorException(captchaResult.getMessage());
        }
    }

    /**
     * 创建一个成功的认证对象。
     *
     * @param principal 认证主体，通常是用户信息。
     * @param authentication 原始的认证对象。
     * @param user 用户详情信息，包含用户权限等。
     * @return 返回一个新的认证对象，基于提供的用户信息和权限。
     */
    protected Authentication createSuccessAuthentication(Object principal, Authentication authentication,
                                                         UserDetails user) {
        // 创建一个新的SmsAuthenticationToken，用以承载认证主体和权限信息
        SmsAuthenticationToken result =
                        new SmsAuthenticationToken(principal, authoritiesMapper.mapAuthorities(user.getAuthorities()));
        // 将原认证对象的详细信息拷贝到新认证对象中
        result.setDetails(authentication.getDetails());

        return result;
    }


    /**
     * 只有 {@link SmsAuthenticationToken} 类型才使用该认证器
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return (SmsAuthenticationToken.class.isAssignableFrom(authentication));
    }

}
