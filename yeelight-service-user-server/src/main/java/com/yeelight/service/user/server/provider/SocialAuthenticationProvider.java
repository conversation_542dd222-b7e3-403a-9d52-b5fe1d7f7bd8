package com.yeelight.service.user.server.provider;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.YeelightSocialUserService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * 第三方信息认证器
 * <AUTHOR>
 */
@Component
public class SocialAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightSocialUserService yeelightSocialUserService;


    /**
     * 从第三方登录平台获取用户详细信息。
     *
     * @param username 用户名，此场景下可能未直接使用
     * @param authentication 用户认证信息，包含第三方登录的详细信息
     * @return UserDetails 用户详细信息对象
     * @throws UsernameNotFoundException 当用户不存在或认证信息不匹配时抛出
     * @throws DisabledException 当用户被禁用时抛出
     */
    @Override
    public UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException, DisabledException {
        // 检查是否为第三方登录模式, 如果没有captcha参数，则不匹配该模式
        if (authentication.getDetails() instanceof LinkedHashMap) {
            // 将认证信息中的详情转换为SocialInfoDetail对象
            SocialInfoDetail details = BeanUtils.objToBean(authentication.getDetails(), SocialInfoDetail.class);
            // 验证三方信息的完整性和有效性
            if (Objects.isNull(details) || StringUtils.isEmpty(details.getProviderName())) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.需要三方信息"));
            } else {
                // 提取三方登录的UUID和平台名称
                String providerUuId = details.getProviderUuId();
                String providerName = details.getProviderName();
                YeelightUserDto yeelightUserDto;

                // 检查三方用户信息是否存在
                if (StringUtils.isAnyEmpty(providerUuId, providerName)) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.需要三方信息"));
                }

                // 根据UUID和平台名称查询社交用户信息
                SocialUserDto socialUserDto = yeelightSocialUserService.findSocialUserBySourceAndUuid(providerUuId, providerName);

                // 处理三方用户未注册的情况
                if (Objects.isNull(socialUserDto)) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.三方账号用户不存在"));
                } else {
                    // 根据社交用户ID查询对应的平台用户信息
                    yeelightUserDto = yeelightUserReadService.findUserById(socialUserDto.getYeelightId());
                    // 处理平台用户不存在的情况
                    if (Objects.isNull(yeelightUserDto)) {
                        throw new DisabledException(I18nUtil.message("User.Exception.user.notExist", username));
                    }
                }

                // 将平台用户信息转换为内部用户模型
                YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                // 检查用户是否被禁用
                if (!yeelightUser.isEnabled()) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                }
                // 返回用户详细信息
                return yeelightUser;
            }
        }
        // 当不是第三方登录模式时，抛出用户名或密码错误异常
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }


    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails 用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class SocialInfoDetail {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;
        private String providerUuId;
        private String providerName;
    }
}