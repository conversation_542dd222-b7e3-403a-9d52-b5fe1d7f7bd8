package com.yeelight.service.user.server.provider;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.exception.PasswordErrorException;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.support.UserConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.request.AuthRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Objects;

/**
 * 微信小程序认证器
 * 该认证器只给 小程序使用，不具有通用性
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class WechatMiniProgramAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Resource
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;


    /**
     * 通过电话号码和微信认证信息检索用户详情。
     *
     * @param username           用户的登录生成的code
     * @param authentication 用户的认证信息，包含微信认证的详细数据。
     * @return UserDetails 用户的详细信息对象。
     * @throws UsernameNotFoundException 当用户不存在或微信信息不完整时抛出。
     */
    @Override
    public UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws UsernameNotFoundException {

        // 检查是否有微信认证详情
        if (authentication.getDetails() instanceof LinkedHashMap) {
            // 将认证信息转换为自定义的WechatAuthDetail对象
            WechatMiniProgramAuth wechatMiniProgramAuth = BeanUtils.objToBean(authentication.getDetails(), WechatMiniProgramAuth.class);
            if (null == wechatMiniProgramAuth) {
                throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.需要微信信息"));
            }

            // 根据小程序认证信息获取用户信息
            YeelightUserDto yeelightUserDto = wechatMiniProgramLogin(wechatMiniProgramAuth);

            try {
                if (Objects.isNull(yeelightUserDto)) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.三方账号用户不存在"));
                }
                // 将服务端返回的用户信息转换为本地使用的用户对象
                YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
                // 检查用户是否被禁用
                if (!yeelightUser.isEnabled()) {
                    throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
                }
                // 通过用户名加载用户详情，用于进一步认证
                return yeelightUser;
            } catch (Exception e) {
                // 记录注册或登录失败的日志
                log.error("手机号+微信信息方式注册/登录失败，原因：{}, Trace:{}", e.getMessage(), ExceptionUtils.getStackTrace(e));
                throw new BizException(I18nUtil.getMessage("User.Action.RegisterOrLogin.fail"));
            }
        }
        // 当认证信息不符合预期时抛出用户名不存在异常
        throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
    }

    private YeelightUserDto wechatMiniProgramLogin(WechatMiniProgramAuth wechatMiniProgramAuth) throws UsernameNotFoundException {
        // 校验必填
        if (StringUtils.isAnyBlank(wechatMiniProgramAuth.getMiniProgramType(), wechatMiniProgramAuth.getCode())) {
            log.warn("miniProgramType({})或code({})，无法解析用户个人信息", wechatMiniProgramAuth.getMiniProgramType(), wechatMiniProgramAuth.getCode());
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.需要微信信息"));
        }

        // 获取authRequest
        AuthRequest authRequest = yeelightAuthRequestFactory.get(wechatMiniProgramAuth.getMiniProgramType());

        // 登录
        AuthToken authToken = authRequest.getAccessToken(AuthCallback.builder().code(wechatMiniProgramAuth.getCode()).auth_code(wechatMiniProgramAuth.getPhoneCode()).build());
        if (Objects.isNull(authToken)) {
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.需要微信信息"));
        }
        // 获取三方用户信息
        me.zhyd.oauth.model.AuthUser authUserRaw = authRequest.getUserInfo(authToken);
        AuthUser authUser = BeanUtils.objToBean(authUserRaw, AuthUser.class);

        // 设置用户信息来源
        authUser.setSource(wechatMiniProgramAuth.getMiniProgramType());

        // 如果有手机号码，则设置手机号码
        if (Objects.nonNull(authUser.getRawUserInfo()) && authUser.getRawUserInfo().containsKey(Constants.PHONE_KEY)) {
            authUser.setPhone(authUser.getRawUserInfo().getString(Constants.PHONE_KEY));
        }

        // 设置sessionKey
        if (Objects.nonNull(authUser.getToken())) {
            authUser.getToken().setSessionKey(authUser.getToken().getAccessToken());
        }

        // 根据微信信息创建或更新用户
        YeelightUserDto yeelightUserDto = justAuthUserDetailsService.autoRegisterSocialUser(authUser, null);
        if (Objects.isNull(yeelightUserDto)) {
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.账号未注册"));
        }
        return yeelightUserDto;
    }

    /**
     * 在默认的认证流程之外执行额外的认证检查。
     * 本方法主要用来检查验证码是否正确。
     *
     * @param userDetails    用户详情，由框架根据用户名查询并传入，包含用户的所有认证信息。
     * @param authentication 认证令牌，包含用户提交的认证信息，如用户名和密码。
     * @throws AuthenticationException 如果认证检查失败，抛出此异常。
     */
    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        // 检查传入的认证详情是否为 LinkedHashMap 类型，若是，则移除该详情；若不是，抛出密码错误异常
        if (authentication.getDetails() instanceof LinkedHashMap) {
            authentication.setDetails(null);
        } else {
            throw new PasswordErrorException(I18nUtil.getMessage("ResultCode.用户名或密码错误"));
        }
    }

    @Data
    public static class WechatMiniProgramAuth implements Serializable {
        private String grantType;
        private String clientId;
        private String clientSecret;
        private String username;

        /**
         * 微信小程序
         * @see com.yeelight.service.user.client.enums.UserSocialSource
         */
        private String miniProgramType;

        /**
         * 小程序登录时获取的 code
         */
        private String code;

        /**
         * 换取用户手机号的code
         * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html">获取用户手机号</a>
         */
        private String phoneCode;

        /**
         * 包括敏感数据在内的完整用户信息的加密数据
         * 详见 <a href="https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/signature.html#%E5%8A%A0%E5%AF%86%E6%95%B0%E6%8D%AE%E8%A7%A3%E5%AF%86%E7%AE%97%E6%B3%95">用户数据的签名验证和加解密</a>
         */
        // private String encryptedData;

        /**
         * 加密算法的初始向量
         * 详见 <a href="https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/signature.html#%E5%8A%A0%E5%AF%86%E6%95%B0%E6%8D%AE%E8%A7%A3%E5%AF%86%E7%AE%97%E6%B3%95">用户数据的签名验证和加解密</a>
         */
        // private String iv;
    }
}
