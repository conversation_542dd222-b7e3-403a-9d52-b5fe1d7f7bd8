package com.yeelight.service.user.server.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AlexaSkillEnablementRequest implements Serializable {
    private String stage = "live";

    private AccountLinkRequest accountLinkRequest = new AccountLinkRequest();

    public AlexaSkillEnablementRequest(String authCode, String redirectUri) {
        this.accountLinkRequest.setAuthCode(authCode);
        this.accountLinkRequest.setRedirectUri(redirectUri);
    }

    @Data
    private static class AccountLinkRequest implements Serializable {
        private String redirectUri;

        private String authCode;

        private String type = "AUTH_CODE";
    }
}
