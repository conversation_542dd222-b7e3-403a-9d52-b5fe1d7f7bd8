package com.yeelight.service.user.server.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: Alexa Skill Event Request
 * <AUTHOR>
 */
@Data
public class AlexaSkillEventRequest implements Serializable {
    private String version;
    private SkillContext context;
    private SkillRequest request;


    @Data
    public static class SkillContext implements Serializable {
        @JsonProperty(value = "System")
        private SkillSystem System;
    }

    @Data
    public static class SkillSystem implements Serializable {
        private SkillApplication application;
        private SkillUser user;
        private SkillPerson person;
        private String apiEndpoint;
        private String apiAccessToken;
    }

    @Data
    public static class SkillApplication implements Serializable {
        private String applicationId;
    }

    @Data
    public static class SkillUser implements Serializable {
        private String userId;
        private String accessToken;
        private SkillPermissions permissions;
    }

    @Data
    public static class SkillRequest implements Serializable {
        private String type;
        private String requestId;
        private Date timestamp;
        private Date eventCreationTime;
        private Date eventPublishingTime;
        private JSONObject body;
    }

    @Data
    public static class SkillPermissions implements Serializable {
        private String consentToken;
    }


    @Data
    public static class SkillPerson implements Serializable {
        private String personId;
        private String accessToken;
    }
}
