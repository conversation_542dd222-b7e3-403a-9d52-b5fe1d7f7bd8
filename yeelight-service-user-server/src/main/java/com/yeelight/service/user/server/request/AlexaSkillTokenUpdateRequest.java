package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AlexaSkillTokenUpdateRequest implements Serializable {
    @NotBlank(message = "alexa token 不允许为空")
    private String accessToken;
    @NotBlank(message = "alexa refresh token 不允许为空")
    private String refreshToken;
    @NotBlank(message = "alexa tokenType 不允许为空")
    private String tokenType;
    @NotNull(message = "alexa token 过期实际不允许为空")
    private Integer expiresIn;
    @NotBlank(message = "token 不允许为空")
    private String yeelightToken;
}
