package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: Apple验证请求
 */
@Data
public class AppleVerifyRequest implements Serializable {
    @NotBlank(message = "ID token 不允许为空")
    private String identityToken;
    @NotBlank(message = "ClientId 不允许为空")
    private String yeelightClientId;
    private Long yeelightId;
}
