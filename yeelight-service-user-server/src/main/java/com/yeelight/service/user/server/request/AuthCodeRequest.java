package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AuthCodeRequest implements Serializable {
    @NotBlank(message = "token 不允许为空")
    private String token;

    @NotBlank(message = "clientId 不允许为空")
    private String clientId;

    @NotBlank(message = "redirectUri 不允许为空")
    private String redirectUri;

    private String state;

    private String scope;
}
