/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.request
 * Description: 注销用户请求
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-28 10:31:10:31
 */
package com.yeelight.service.user.server.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Desc: 校验密码请求
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-28 10:31:10:31
 */
@Data
public class CheckUserPasswordRequest implements Serializable {
    /**
     * 注销验证码验证的账号 一般为手机号或者邮箱
     */
    //@NotBlank(message = "{User.Exception.password.notBlank}")
    //private String password;

    @NotBlank(message = "{User.Exception.oldPassword.notBlank}")
    @Length(min = 1, max = 50, message = "密码长度需要在{min}~{max}之间")
    private String oldPassword;
}
