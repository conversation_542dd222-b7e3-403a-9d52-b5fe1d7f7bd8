package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ChinaTelecomTokenNotifyRequest implements Serializable {
    /**
     * 流水号。建议格式为时间戳+序列号：YYYYMMDDHHMMSSxxxx，其中xxxx为序列号，从0001开始，排满9999后重新循环。
     */
    @NotBlank(message = "流水号 不允许为空")
    private String sequenceNo;
    /**
     * 密文，非通用参数入参json字符串加密，使用AES对称加密，加密模式：AES/ECB/PKCS5Padding，密钥长度：128。
     */
    @NotBlank(message = "密文 不允许为空")
    private String encryptParam;
    /**
     * 身份校验id
     */
    @NotBlank(message = "身份校验id 不允许为空")
    private String partnerId;
    /**
     * 签名；MD5(sequenceNo+encryptParam+ partnerSecret)
     * partnerSecret由平台分配，和partnerId配对。
     */
    @NotBlank(message = "签名 不允许为空")
    private String signature;
}
