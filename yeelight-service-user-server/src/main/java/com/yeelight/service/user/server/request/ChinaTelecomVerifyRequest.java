package com.yeelight.service.user.server.request;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ChinaTelecomVerifyRequest implements Serializable {
    /**
     * 小翼管家TokenCode
     */
    @NotBlank(message = "小翼管家TokenCode 不允许为空")
    private String tokenCode;
    /**
     * 该字段需通过授权重定向接口回传至电信HIOT平台
     */
    @NotBlank(message = "重定向地址 不允许为空")
    private String state;

    /**
     * 预留字段
     * key:clientId
     * value:clientId
     */
    @NotNull(message = "预留字段信息 不允许为空")
    private JSONObject extraParam;
}
