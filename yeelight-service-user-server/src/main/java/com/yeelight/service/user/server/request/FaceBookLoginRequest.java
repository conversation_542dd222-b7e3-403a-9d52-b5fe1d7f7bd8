package com.yeelight.service.user.server.request;

import lombok.Data;
import me.zhyd.oauth.config.AuthDefaultSource;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class FaceBookLoginRequest implements Serializable {
    @NotBlank(message = "ClientId 不允许为空")
    private String yeelightClientId;

    @NotBlank(message = "{ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED}")
    private String facebookToken;
    @NotNull(message = "{ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED}")
    private Integer facebookTokenExpireIn;

    private String source = AuthDefaultSource.FACEBOOK.getName();
}
