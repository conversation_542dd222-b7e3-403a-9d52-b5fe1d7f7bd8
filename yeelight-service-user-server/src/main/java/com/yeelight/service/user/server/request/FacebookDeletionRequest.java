package com.yeelight.service.user.server.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class FacebookDeletionRequest implements Serializable {
    @JSONField(name = "signed_request")
    @NotBlank(message = "signed_request is null")
    private String signedRequest;
}
