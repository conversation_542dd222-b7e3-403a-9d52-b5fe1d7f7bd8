package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MobileCaptchaRequest implements Serializable {
    @NotBlank(message = "手机号不能为空")
    private String phoneNumber;

    /**
     * 是否检查手机号存在
     */
    private Boolean exist = Boolean.FALSE;

    /**
     * 客户端使用动态参数接口返回的key对称加密所得
     */
    @NotBlank(message = "ticket need")
    private String ticket;

    /**
     * 动态参数接口获取到的timestamp
     */
    @NotNull(message = "timestamp need")
    private Long timestamp;
}
