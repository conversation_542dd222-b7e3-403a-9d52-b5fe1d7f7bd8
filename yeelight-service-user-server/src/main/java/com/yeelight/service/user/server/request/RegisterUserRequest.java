package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RegisterUserRequest implements Serializable {

    @NotBlank(message = "{Domain.RegisterUser.password.notBlank}")
    private String password;

    @NotBlank(message = "{Domain.RegisterUser.password2.notBlank}")
    private String password2;

    @NotBlank(message = "{Domain.RegisterUser.captcha.notBlank}")
    private String captcha;

    @NotBlank(message = "{Domain.RegisterUser.captchaKey.notBlank}")
    private String captchaKey;

    @NotBlank(message = "{Domain.RegisterUser.phoneNumber.notBlank}")
    private String phoneNumber;

}
