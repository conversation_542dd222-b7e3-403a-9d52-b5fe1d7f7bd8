/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.request
 * Description: 注销用户请求
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-28 10:31:10:31
 */
package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Desc: 注销用户请求
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-28 10:31:10:31
 */
@Data
public class RemoveUserRequest implements Serializable {
    /**
     * 注销验证码验证的账号 一般为手机号或者邮箱
     */
    @NotBlank(message = "{ResultCode.手机号或邮箱不存在}")
    private String account;

    /**
     * 验证码
     */
    @NotBlank(message = "{Domain.RegisterUser.captcha.notBlank}")
    private String captcha;

    /**
     * 验证码key
     */
    @NotBlank(message = "{Domain.RegisterUser.captchaKey.notBlank}")
    private String captchaKey;
}
