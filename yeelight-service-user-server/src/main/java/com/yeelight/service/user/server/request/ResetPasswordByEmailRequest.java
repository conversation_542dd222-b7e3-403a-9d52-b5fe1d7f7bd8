package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ResetPasswordByEmailRequest implements Serializable {

    @NotBlank(message = "{Domain.ResetPasswordByEmail.password.notBlank}")
    private String password;

    @NotBlank(message = "{Domain.ResetPasswordByEmail.captcha.notBlank}")
    private String captcha;

    @NotBlank(message = "{Domain.ResetPasswordByEmail.captchaKey.notBlank}")
    private String captchaKey;

    @NotBlank(message = "{Domain.ResetPasswordByEmail.email.notBlank}")
    private String email;

}
