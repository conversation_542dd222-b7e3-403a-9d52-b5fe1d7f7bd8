package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ResetPasswordByPhoneNumberRequest implements Serializable {

    @NotBlank(message = "{Domain.ResetPassword.password.notBlank}")
    private String password;

    @NotBlank(message = "{Domain.ResetPassword.captcha.notBlank}")
    private String captcha;

    @NotBlank(message = "{Domain.ResetPassword.captchaKey.notBlank}")
    private String captchaKey;

    @NotBlank(message = "{Domain.ResetPassword.phoneNumber.notBlank}")
    private String phoneNumber;

}
