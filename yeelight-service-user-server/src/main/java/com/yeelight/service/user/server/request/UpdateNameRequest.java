package com.yeelight.service.user.server.request;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UpdateNameRequest implements Serializable {
        @NotBlank(message = "{ResultCode.用户名不存在}")
        @Length(min = 1, max = 50, message = "用户名长度必须在{min}~{max}之间")
        private String name;
}
