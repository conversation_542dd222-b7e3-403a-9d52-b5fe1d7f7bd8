package com.yeelight.service.user.server.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UpdatePasswordRequest implements Serializable {

    private Long id;

    @NotBlank(message = "{User.Exception.oldPassword.notBlank}")
    private String oldPassword;

    @NotBlank(message = "{User.Exception.newPassword.notBlank}")
    private String newPassword;

}
