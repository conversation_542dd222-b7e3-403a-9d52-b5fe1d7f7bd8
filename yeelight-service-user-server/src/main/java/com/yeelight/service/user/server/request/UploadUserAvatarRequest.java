package com.yeelight.service.user.server.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UploadUserAvatarRequest implements Serializable {
    /**
     * 文件名称
     */
    private String fileName;

    private byte[] bytes;

}
