package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xkcoding.http.constants.Constants;
import com.xkcoding.http.support.HttpHeader;
import com.xkcoding.http.util.UrlUtil;
import com.yeelight.service.framework.domain.HttpResult;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.EnvironmentPropertyUtils;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import com.yeelight.service.user.server.request.AlexaSkillEnablementRequest;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.log.Log;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.*;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Amazon登录
 * Login with Amazon for Websites Overview： <a href="https://developer.amazon.com/zh/docs/login-with-amazon/register-web.html">...</a>
 * Login with Amazon SDK for JavaScript Reference Guide：<a href="https://developer.amazon.com/zh/docs/login-with-amazon/javascript-sdk-reference.html">...</a>
 *
 * <AUTHOR> (yadong.zhang0415(a)gmail.com)
 * @since 1.16.0
 */
public class AuthAmazonAlexaRequest extends AuthDefaultRequest {
    public static final String ALEXA_API_ENDPOINT = "https://api.amazonalexa.com/v1/alexaApiEndpoint";
    private static final String CONFIG_PREFIX = "justauth.extend.config";
    private static final String AUD = "aud";

    public AuthAmazonAlexaRequest(AuthConfig config) {
        super(config, AuthCustomSource.AMAZON_ALEXA);
    }

    public AuthAmazonAlexaRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthCustomSource.AMAZON_ALEXA, authStateCache);
    }

    /**
     * <a href="https://developer.amazon.com/zh/docs/login-with-amazon/authorization-code-grant.html#authorization-request">...</a>
     *
     * @param state state 验证授权流程的参数，可以防止csrf
     * @return String
     */
    @Override
    public String authorize(String state) {
        UrlBuilder builder = UrlBuilder.fromBaseUrl(source.authorize())
            .queryParam(OAuth2Utils.CLIENT_ID, config.getClientId())
            .queryParam(OAuth2Utils.SCOPE, "alexa::skills:account_linking")
            .queryParam(OAuth2Utils.REDIRECT_URI, config.getRedirectUri())
            .queryParam(OAuth2Utils.RESPONSE_TYPE, "code")
            .queryParam(OAuth2Utils.STATE, getRealState(state));

        if (config.isPkce()) {
            String cacheKey = this.source.getName().concat(":code_verifier:").concat(config.getClientId());
            String codeVerifier = PkceUtil.generateCodeVerifier();
            String codeChallengeMethod = "S256";
            String codeChallenge = PkceUtil.generateCodeChallenge(codeChallengeMethod, codeVerifier);
            builder.queryParam("code_challenge", codeChallenge)
                .queryParam("code_challenge_method", codeChallengeMethod);
            // 缓存 codeVerifier 十分钟
            this.authStateCache.cache(cacheKey, codeVerifier, TimeUnit.MINUTES.toMillis(10));
        }

        return builder.build();
    }

    /**
     * 统一的登录入口。当通过{@link AuthDefaultRequest#authorize(String)}授权成功后，会跳转到调用方的相关回调方法中
     * 方法的入参可以使用{@code AuthCallback}，{@code AuthCallback}类中封装好了OAuth2授权回调所需要的参数
     *
     * @param authCallback 用于接收回调参数的实体
     * @return AuthResponse
     */
    @Override
    public AuthResponse login(AuthCallback authCallback) {
        try {
            AuthChecker.checkCode(source, authCallback);
            String alexaEnablementUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(UserVendorHolder.attachVendor(source.getName()), "alexa-enablement-uri")).orElseThrow(() -> new BizException("Could not find the correct alexa-enablement-uri"));
            String yeelightRedirectUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(UserVendorHolder.attachVendor(source.getName()), "yeelight-redirect-uri")).orElseThrow(() -> new BizException("Could not find the correct yeelight-redirect-uri"));

            if (!config.isIgnoreCheckState()) {
                AuthChecker.checkState(authCallback.getState(), source, authStateCache);
            }
            if (StringUtils.isEmpty(authCallback.getAuthorization_code())) {
                Log.debug(JSON.toJSONString(authCallback));
                throw new AuthException("Alexa only support skill enablement. Could not find the auth code  for the user");
            }

            AuthToken authToken = this.getAccessToken(authCallback);
            AuthUser user = this.enableSkillAndLinkAccount(authToken, authCallback.getAuthorization_code(), alexaEnablementUri, yeelightRedirectUri);
            return AuthResponse.builder().code(AuthResponseStatus.SUCCESS.getCode()).data(user).build();
        } catch (Exception e) {
            Log.error("Failed to login with oauth authorization.", e);
            return this.responseError(e);
        }
    }

    /**
     * <a href="https://developer.amazon.com/zh/docs/login-with-amazon/authorization-code-grant.html#access-token-request">...</a>
     *
     * @return access token
     */
    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        Map<String, String> form = new HashMap<>(9);
        form.put(OAuth2Utils.GRANT_TYPE, "authorization_code");
        form.put("code", authCallback.getCode());
        form.put(OAuth2Utils.REDIRECT_URI, config.getRedirectUri());
        form.put(OAuth2Utils.CLIENT_ID, config.getClientId());
        form.put("client_secret", config.getClientSecret());

        if (config.isPkce()) {
            String cacheKey = this.source.getName().concat(":code_verifier:").concat(config.getClientId());
            String codeVerifier = this.authStateCache.get(cacheKey);
            form.put("code_verifier", codeVerifier);
        }
        return getToken(form, this.source.accessToken());
    }

    /**
     * 使用刷新令牌来获取新的访问令牌。
     *
     * @param authToken 包含访问令牌和刷新令牌的认证令牌对象。
     * @return 返回一个授权响应对象，包含新的访问令牌和其他相关数据。
     */
    @Override
    public AuthResponse<AuthToken> refresh(AuthToken authToken) {
        // 准备刷新令牌的请求参数
        Map<String, String> form = new HashMap<>(7);
        form.put(OAuth2Utils.GRANT_TYPE, YeelightOAuth2AccessToken.REFRESH_TOKEN);
        form.put(YeelightOAuth2AccessToken.REFRESH_TOKEN, authToken.getRefreshToken());
        form.put(OAuth2Utils.CLIENT_ID, config.getClientId());
        form.put(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_SECRET, config.getClientSecret());

        // 使用刷新令牌换取新的访问令牌，并构建授权响应对象
        return AuthResponse.<AuthToken>builder()
            .code(AuthResponseStatus.SUCCESS.getCode())
            .data(getToken(form, this.source.refresh()))
            .build();
    }


    /**
     * 通过传递参数和URL获取认证令牌。
     *
     * @param param 包含认证所需参数的Map
     * @param url 认证服务的URL地址
     * @return AuthToken 包含访问令牌、令牌类型、过期时间、刷新令牌的对象
     */
    private AuthToken getToken(Map<String, String> param, String url) {
        // 构建请求头部，设置Host和Content-Type
        Headers headers = new Headers.Builder()
                .add("Host", "api.amazon.com")
                .add(com.xkcoding.http.constants.Constants.CONTENT_TYPE, "application/x-www-form-urlencoded;charset=UTF-8")
                .build();

        // 构建请求体，使用传递进来的参数
        RequestBody requestBody = com.yeelight.service.framework.util.HttpUtils.buildFormRequestBody(param);

        // 发起POST请求并获取响应
        HttpResult response = com.yeelight.service.framework.util.HttpUtils.post(url, headers, requestBody);

        // 解析响应数据为JSONObject
        JSONObject jsonObject = JSONObject.parseObject(response.getData());

        // 检查响应内容是否符合预期
        this.checkResponse(jsonObject);

        // 从JSON对象中提取令牌信息，并构建AuthToken对象返回
        return AuthToken.builder()
            .accessToken(jsonObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
            .tokenType(jsonObject.getString(YeelightOAuth2AccessToken.TOKEN_TYPE))
            .expireIn(jsonObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
            .refreshToken(jsonObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
            .build();
    }


    /**
     * 根据提供的AuthToken获取用户信息。
     * <p>
     * 使用AuthToken中的accessToken来向指定的API发送请求，获取用户信息，并构建AuthUser对象返回。
     * 这个过程中会校验token的有效性以及API响应的正确性。
     *
     * @param authToken 包含访问令牌的AuthToken对象。
     * @return AuthUser 包含用户信息的AuthUser对象。
     */
    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        // 获取accessToken并进行token校验
        String accessToken = authToken.getAccessToken();
        this.checkToken(accessToken);

        // 构建HTTP请求头，包含必要的认证信息
        HttpHeader httpHeader = new HttpHeader();
        httpHeader.add("Host", "api.amazon.com");
        httpHeader.add("Authorization", "bearer " + accessToken);

        // 发起获取用户信息的HTTP GET请求
        String userInfo = new HttpUtils(config.getHttpConfig()).get(this.source.userInfo(), new HashMap<>(0), httpHeader, false).getBody();

        // 解析用户信息并进行响应校验
        JSONObject jsonObject = JSONObject.parseObject(userInfo);
        this.checkResponse(jsonObject);

        // 根据解析出的用户信息，构建并返回AuthUser对象
        return AuthUser.builder()
                .rawUserInfo(jsonObject)
                .uuid(jsonObject.getString("user_id"))
                .username(jsonObject.getString("name"))
                .nickname(jsonObject.getString("name"))
                .email(jsonObject.getString("email"))
                // 设置性别为未知
                .gender(AuthUserGender.UNKNOWN)
                .source(source.toString())
                .token(authToken)
                .build();
    }


    /**
     * 启用技能并链接账户。
     * 该方法通过调用Alexa的技能启用API来实现账户的链接，主要用在技能授权的场景中。
     * 在这个过程中，会使用到Alexa的访问令牌来验证权限，并通过指定的URI来请求启用技能。
     * 如果账户已链接，将返回用户的认证信息。
     *
     * @see <a href="https://developer.amazon.com/en-US/docs/alexa/account-linking/skill-activation-api.html#post-request-enablement">...</a>
     * @param authToken 用户的Alexa访问令牌，用于身份验证和授权。
     * @param yeelightAuthCode Yeelight提供的授权码，用于技能和账户的链接。
     * @param alexaEnablementUri Alexa启用技能的API URI。
     * @param yeelightRedirectUri Yeelight的重定向URI，用于授权回调。
     * @return AuthUser 认证用户的对象，包含用户ID等信息。
     * @throws AuthException 如果无法找到正确的用户端点、启用技能失败或账户链接失败时抛出。
     */
    private AuthUser enableSkillAndLinkAccount(AuthToken authToken, String yeelightAuthCode, String alexaEnablementUri, String yeelightRedirectUri) {
        // 使用访问令牌进行身份验证
        String accessToken = authToken.getAccessToken();
        this.checkToken(accessToken);

        // 查询Alexa API端点
        List<String> endpoints = queryAlexaApiEndpoints(authToken.getAccessToken());
        if (CollectionUtils.isEmpty(endpoints)) {
            throw new AuthException("Could not find the correct endpoint for the user");
        }

        // 准备启用技能的请求参数
        AlexaSkillEnablementRequest alexaSkillEnablementRequest = new AlexaSkillEnablementRequest(yeelightAuthCode, yeelightRedirectUri);

        // 设置请求头部，包含内容类型和授权令牌
        Headers headers = new Headers.Builder()
                .add(Constants.CONTENT_TYPE, "application/json; charset=utf-8")
                .add("Authorization", "Bearer " + accessToken)
                .build();

        // 创建请求体
        MediaType mediaType = MediaType.Companion.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.Companion.create(JSON.toJSONString(alexaSkillEnablementRequest), mediaType);

        // 遍历所有查询到的端点，尝试启用技能并链接账户
        for (String endpoint : endpoints) {
            // 发起启用技能的POST请求
            HttpResult response = com.yeelight.service.framework.util.HttpUtils.post("https://" + endpoint + alexaEnablementUri, headers, requestBody);
            JSONObject jsonObject = JSONObject.parseObject(response.getData());
            this.checkResponse(jsonObject);

            // 检查账户链接状态
            JSONObject accountLink = jsonObject.getJSONObject("accountLink");
            JSONObject user = jsonObject.getJSONObject("user");
            if ("LINKED".equals(accountLink.getString("status"))) {
                // 如果账户已链接，返回用户的认证信息
                return AuthUser.builder()
                        .uuid(user.getString("id"))
                        .gender(AuthUserGender.UNKNOWN)
                        .source(source.toString())
                        .token(authToken)
                        .rawUserInfo(jsonObject)
                        .build();
            }
        }

        // 如果所有端点都无法成功链接账户，则抛出异常
        throw new AuthException("alexa skill accountLink failed.");
    }


    /**
     * 查询Alexa API的端点信息。
     * 通过提供的访问令牌（accessToken）向Alexa API发起请求，获取端点信息列表。
     *
     * @param accessToken 用户的访问令牌，用于身份验证和授权。
     * @return 返回一个包含多个端点信息的字符串列表。
     * @throws BizException 如果请求过程中出现业务错误或响应验证失败时抛出。
     */
    private List<String> queryAlexaApiEndpoints(String accessToken) throws BizException {
        // 构建HTTP请求头，添加Authorization信息
        HttpHeader httpHeader = new HttpHeader();
        httpHeader.add("Authorization", "Bearer " + accessToken);

        // 向Alexa API发送GET请求，获取端点信息
        String endpoints = new HttpUtils(config.getHttpConfig()).get(ALEXA_API_ENDPOINT, new HashMap<>(0), httpHeader, false).getBody();

        // 解析响应体为JSON对象
        JSONObject jsonObject = JSONObject.parseObject(endpoints);

        // 检查响应体，确保没有错误
        this.checkResponse(jsonObject);

        // 从JSON对象中提取并返回端点信息列表
        return jsonObject.getObject("endpoints", new TypeReference<List<String>>(){});
    }

    /**
     * 校验访问令牌的有效性。
     * 该方法通过向亚马逊API发送请求，验证令牌是否由预期的客户端ID颁发。
     * 如果令牌的颁发者不是预期的客户端，将抛出认证异常。
     *
     * @param accessToken 要校验的访问令牌字符串。
     * @throws AuthException 如果访问令牌的颁发者不是预期的客户端ID，抛出此异常。
     */
    private void checkToken(String accessToken) {
        // 向亚马逊API发送请求，获取令牌信息
        String tokenInfo = new HttpUtils(config.getHttpConfig()).get("https://api.amazon.com/auth/o2/tokeninfo?access_token=" + UrlUtil.urlEncode(accessToken)).getBody();
        JSONObject jsonObject = JSONObject.parseObject(tokenInfo);
        // 验证返回的令牌信息中的客户端ID是否与预期一致
        if (!config.getClientId().equals(jsonObject.getString(AUD))) {
            throw new AuthException(AuthResponseStatus.ILLEGAL_TOKEN);
        }
    }

    /**
     * 构建用户信息的URL。
     * 该方法根据提供的AuthToken信息，构建一个用于获取用户信息的URL。
     *
     * @param authToken 包含用户认证信息的AuthToken对象。需要包含用户ID和屏幕名称。
     * @return 返回构建好的用户信息URL。URL中包含了用户ID、屏幕名称和实体信息。
     */
    @Override
    protected String userInfoUrl(AuthToken authToken) {
        // 使用UrlBuilder从用户信息的基URL开始构建
        return UrlBuilder.fromBaseUrl(source.userInfo())
            // 添加用户ID作为查询参数
            .queryParam("user_id", authToken.getUserId())
            // 添加屏幕名称作为查询参数
            .queryParam("screen_name", authToken.getScreenName())
            // 设置"include_entities"查询参数为true，以包含实体信息
            .queryParam("include_entities", true)
            // 构建并返回最终的URL
            .build();
    }


    /**
     * 处理{@link AuthDefaultRequest#login(AuthCallback)} 发生异常的情况，统一响应参数
     *
     * @param e 具体的异常
     * @return AuthResponse
     */
    private AuthResponse responseError(Exception e) {
        int errorCode = AuthResponseStatus.FAILURE.getCode();
        String errorMsg = e.getMessage();
        if (e instanceof AuthException authException) {
            errorCode = authException.getErrorCode();
            if (StringUtils.isNotEmpty(authException.getErrorMsg())) {
                errorMsg = authException.getErrorMsg();
            }
        }
        return AuthResponse.builder().code(errorCode).msg(errorMsg).build();
    }

    /**
     * 校验响应内容是否正确
     *
     * @param jsonObject 响应内容
     */
    private void checkResponse(JSONObject jsonObject) {
        if (jsonObject.containsKey(com.yeelight.service.user.server.constant.Constants.ERROR_KEY)) {
            throw new AuthException(jsonObject.getString(com.yeelight.service.user.server.constant.Constants.ERROR_KEY).concat(" ") + jsonObject.getString(com.yeelight.service.user.server.constant.Constants.ERROR_DESCRIPTION_KEY));
        }
        if (jsonObject.containsKey(com.yeelight.service.user.server.constant.Constants.MESSAGE_KEY)) {
            throw new AuthException(jsonObject.getString(com.yeelight.service.user.server.constant.Constants.MESSAGE_KEY));
        }
    }

    private String getPropertyKey(String source, String key) {
        StringBuilder sb = new StringBuilder(CONFIG_PREFIX);
        if(com.yeelight.service.framework.util.StringUtils.isNotBlank(source)) {
            sb.append(".").append(source.toUpperCase());
        }
        if(com.yeelight.service.framework.util.StringUtils.isNotBlank(key)) {
            sb.append(".").append(key);
        }
        return sb.toString();
    }
}
