package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.EnvironmentPropertyUtils;
import com.yeelight.service.framework.util.SpringBeanUtil;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.UrlBuilder;
import org.apache.commons.codec.binary.Base64;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;

import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Sign in with Apple
 *
 * <AUTHOR> Yu
 * @date 10/13/21 10:47 AM
 */
@Slf4j
public class AuthAppleRequest extends AuthDefaultRequest {
    private static final String APPLE_CLIENT_SECRET_KEY = "thirdparty:apple:client_secret:";
    public static final String APPLE_BINDING_USER = "thirdparty:apple:binding_user:";

    private static final String CONFIG_PREFIX = "justauth.extend.config";

    private RedisManager redisManager;

    public AuthAppleRequest(AuthConfig config) {
        super(config, AuthCustomSource.APPLE);
    }

    public AuthAppleRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthCustomSource.APPLE, authStateCache);
    }

    /**
     * 通过授权回调获取访问令牌。
     *
     * @param authCallback 包含授权码的认证回调对象。
     * @return 构建好的AuthToken对象，包含访问令牌、过期时间、令牌类型、刷新令牌和ID令牌。
     */
    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        // 使用授权码进行POST请求，获取访问令牌的响应
        String response = doPostAuthorizationCode(authCallback.getCode());
        // 将响应解析为JSONObject对象
        JSONObject accessTokenObject = JSONObject.parseObject(response);

        // 检查响应是否成功
        this.checkResponse(accessTokenObject);

        // 从解析后的JSON对象中提取令牌信息，构建并返回AuthToken对象
        return AuthToken.builder()
                .accessToken(accessTokenObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
                .expireIn(accessTokenObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
                .tokenType(accessTokenObject.getString(YeelightOAuth2AccessToken.TOKEN_TYPE))
                .refreshToken(accessTokenObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
                .idToken(accessTokenObject.getString("id_token"))
                .build();
    }


    /**
     * 构建访问令牌的URL。
     * 该方法通过授权码（code）和其他必要的参数，构建一个用于获取OAuth2访问令牌的URL。
     *
     * @param code 授权码，是获取访问令牌的关键参数之一。
     * @return 返回构建好的访问令牌请求URL。
     */
    @Override
    protected String accessTokenUrl(String code) {
        // 从基础URL开始构建访问令牌的请求URL
        return UrlBuilder.fromBaseUrl(source.accessToken())
                // 添加授权码作为查询参数
                .queryParam("code", code)
                // 添加客户端ID作为查询参数
                .queryParam(OAuth2Utils.CLIENT_ID, config.getClientId())
                // 生成并添加客户端密钥作为查询参数
                .queryParam(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_SECRET, autoGenerateClientSecret())
                // 添加授权类型作为查询参数
                .queryParam(OAuth2Utils.GRANT_TYPE, "authorization_code")
                // 添加重定向URI作为查询参数
                .queryParam(OAuth2Utils.REDIRECT_URI, config.getRedirectUri())
                .build();
    }


    /**
     * 返回获取accessToken的url
     *
     * @param refreshToken refreshToken
     * @return 返回获取accessToken的url
     */
    @Override
    protected String refreshTokenUrl(String refreshToken) {
        return UrlBuilder.fromBaseUrl(source.refresh())
                .queryParam(OAuth2Utils.CLIENT_ID, config.getClientId())
                .queryParam("client_secret", autoGenerateClientSecret())
                .queryParam(YeelightOAuth2AccessToken.REFRESH_TOKEN, refreshToken)
                .queryParam(OAuth2Utils.GRANT_TYPE, YeelightOAuth2AccessToken.REFRESH_TOKEN)
                .queryParam(OAuth2Utils.REDIRECT_URI, config.getRedirectUri())
                .build();
    }

    /**
     * 检查响应对象是否包含错误信息。
     * <p>此方法会检查传入的JSONObject是否包含"error"键，如果存在，则抛出AuthException异常，
     * 异常信息为"error_description"字段的值。</p>
     *
     * @param object 要检查的JSONObject对象。
     * @throws AuthException 如果响应对象包含错误信息，则抛出此异常。
     */
    private void checkResponse(JSONObject object) {
        // 检查响应对象中是否包含错误信息
        if (object.containsKey(Constants.ERROR_KEY)) {
            throw new AuthException(object.getString(Constants.ERROR_DESCRIPTION_KEY));
        }
    }


    /**
     * 进行用户授权流程的初始化。
     * 该方法首先会尝试从SecurityContextHolder中获取当前的认证信息（Authentication），如果用户已认证且认证主体是{@link YeelightUser}实例，
     * 则将用户信息以JSON格式存储在Redis中，用于后续的用户状态保持。
     * 然后，构造并返回一个授权URL，该URL用于重定向用户到OAuth2授权服务器进行授权。
     *
     * @param state 用于保持状态的参数，可以用来防止CSRF攻击或在授权完成后保持用户状态。
     * @return 构造好的授权URL，包含了客户端需要的各类参数如响应类型、客户端ID、重定向URI等。
     */
    @Override
    public String authorize(String state) {
        // 尝试从SecurityContextHolder中获取当前的认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        // 检查认证信息是否非空、用户是否已认证且认证主体是否是YeelightUser实例
        if (Objects.nonNull(auth) && auth.isAuthenticated() && auth.getPrincipal() instanceof YeelightUser yeelightUser) {
            // 将认证主体转换为YeelightUser类型
            // 从Spring应用上下文中获取RedisManager实例
            ApplicationContext ctx = SpringBeanUtil.getCtx();
            redisManager = ctx.getBean(RedisManager.class);
            // 将用户信息存储在Redis中，设置过期时间为30分钟
            redisManager.redisTemplate().opsForValue().set(APPLE_BINDING_USER + state, JSONObject.toJSONString(yeelightUser), 30, TimeUnit.MINUTES);
        }
        // 构造授权URL，包括响应类型、响应模式、作用域、客户端ID、重定向URI和状态参数
        return UrlBuilder.fromBaseUrl(source.authorize())
                .queryParam(OAuth2Utils.RESPONSE_TYPE, "code")
                .queryParam("response_mode", "form_post")
                .queryParam(OAuth2Utils.SCOPE, "name email")
                .queryParam(OAuth2Utils.CLIENT_ID, config.getClientId())
                .queryParam(OAuth2Utils.REDIRECT_URI, config.getRedirectUri())
                .queryParam(OAuth2Utils.STATE, getRealState(state))
                .build();
    }

    /**
     * 根据提供的AuthToken获取用户信息。
     * <p>
     * 解析AuthToken中的idToken，从中获取用户的基本信息，构建并返回AuthUser对象。
     *
     * @param authToken 用户的认证令牌，用于获取用户信息。
     * @return AuthUser 包含用户基本信息的对象。
     */
    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        // 解析idToken以获取用户信息
        DecodedJWT decodedJwt = JWT.decode(authToken.getIdToken());

        // 构建并返回包含用户信息的AuthUser对象
        return AuthUser.builder()
                // 用户的唯一标识
                .uuid(decodedJwt.getSubject())
                // 默认设置性别为未知
                .gender(AuthUserGender.UNKNOWN)
                // 设置认证令牌
                .token(authToken)
                // 设置用户信息来源
                .source(source.toString())
                .build();
    }



    /**
     * 自动生成Apple客户端密钥。
     * 该JWT有效期为6个月，这是Apple允许的最大生命周期。
     *
     * @return 返回配置中的客户端密钥。
     * <AUTHOR> Yu
     * @date 10/13/21 11:57 AM
     */
    private String autoGenerateClientSecret() {
        try {
            // 尝试从Spring上下文中获取Redis管理器
            ApplicationContext ctx = SpringBeanUtil.getCtx();
            redisManager = ctx.getBean(RedisManager.class);

            // 从Redis中获取或生成客户端密钥
            String clientSecret = (String) redisManager.redisTemplate().opsForValue().get(APPLE_CLIENT_SECRET_KEY + config.getClientId());
            if (StringUtils.isBlank(clientSecret)) {
                // 如果密钥不存在，则构建新密钥并存储到Redis中
                clientSecret = buildClientSecret();
                redisManager.redisTemplate().opsForValue().set(APPLE_CLIENT_SECRET_KEY + config.getClientId(), clientSecret, 160, TimeUnit.DAYS);
            }

            // 更新配置并返回客户端密钥
            config.setClientSecret(clientSecret);
            return config.getClientSecret();
        } catch (Exception e) {
            // 捕获异常并记录错误，抛出认证异常
            log.error("apple Client Secret build failed");
            throw new AuthException("apple social login config error.");
        }
    }


    /**
     * 构建客户端密钥。
     * 此方法用于根据配置信息和加密密钥生成一个客户端密钥（JWT格式）。
     *
     * @throws NoSuchAlgorithmException 如果加密算法无法找到
     * @throws InvalidKeySpecException 如果密钥规范不正确
     * @return 生成的客户端密钥（JWT格式）
     */
    private String buildClientSecret() throws NoSuchAlgorithmException, InvalidKeySpecException {
        // 获取配置的客户端ID、密钥ID（kid）和发行者（iss）
        String clientId = config.getClientId();
        String kid = EnvironmentPropertyUtils.getProperty(getPropertyKey("kid"), "kid");
        String iss = EnvironmentPropertyUtils.getProperty(getPropertyKey("iss"), "iss");

        // 构建JWT的头部信息，包含密钥ID
        Map<String, Object> header = new HashMap<>(4);
        header.put("kid", kid);

        // 构建JWT的声明信息，包含发行者、签发时间、过期时间、受众和主题
        Map<String, Object> claims = new HashMap<>(4);
        claims.put("iss", iss);
        long now = System.currentTimeMillis() / 1000;
        // 签发时间
        claims.put("iat", now);
        // 过期时间：最长半年，单位秒
        claims.put("exp", now + 86400 * 180);
        // 受众
        claims.put("aud", "https://appleid.apple.com");
        // 主题
        claims.put("sub", clientId);

        // 加载私钥，并使用私钥签名JWT
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(readAuthKey()));
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);

        // 使用JWT工具类构建并返回签名后的JWT
        return Jwts.builder().header().empty().add(header).and().claims(claims).signWith(privateKey, Jwts.SIG.ES256).compact();

    }


    private String readAuthKey() {
        return EnvironmentPropertyUtils.getProperty(getPropertyKey("auth-key"), "auth-key");
    }

    private String getPropertyKey(String key) {
        String sourceName = UserVendorHolder.attachVendor(source.getName());
        StringBuilder sb = new StringBuilder(CONFIG_PREFIX);
        sb.append(".").append(sourceName.toUpperCase());
        if(StringUtils.isNotBlank(key)) {
            sb.append(".").append(key);
        }
        return sb.toString();
    }
}
