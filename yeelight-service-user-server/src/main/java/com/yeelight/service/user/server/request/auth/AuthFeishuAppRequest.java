package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xkcoding.http.support.HttpHeader;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.GlobalAuthUtils;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import com.yeelight.service.user.client.utils.OAuth2Utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 飞书应用登录 <a href="https://open.feishu.cn/document/common-capabilities/sso/web-application-sso/web-app-overview">飞书应用登录文档</a>
 * <AUTHOR>
 */
public class AuthFeishuAppRequest extends AuthDefaultRequest {

    public AuthFeishuAppRequest(AuthConfig config) {
        super(config, AuthCustomSource.FEISHU_APP);
    }

    public AuthFeishuAppRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthCustomSource.FEISHU_APP, authStateCache);
    }

    /**
     * 开发者网页前端或客户端 获取 code 之后，需要把 code 传递给开发者的服务器，然后通过开发者服务器调用飞书服务器来获取可用于访问用户信息的 access_token。access_token 是开发者用户获取用户信息的唯一凭证，开发者服务器需要严格保证 access_token 的安全，并禁止把 access_token 传递给客户端。
     * <p>
     * 当应用没有服务端时，必须使用挑战码模式在客户端通过 code 和挑战码获取 access_token。客户端的 access_token 应当加密存储，或立即销毁。
     * <a href="https://open.feishu.cn/document/common-capabilities/sso/api/get-access_token">...</a>
     */
    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        Map<String, String> form = new HashMap<>(8);
        form.put(OAuth2Utils.GRANT_TYPE, "authorization_code");
        form.put("code", authCallback.getCode());
        form.put(OAuth2Utils.CLIENT_ID, config.getClientId());
        form.put("client_secret", config.getClientSecret());
        form.put(OAuth2Utils.REDIRECT_URI, config.getRedirectUri());

        String response = new HttpUtils(config.getHttpConfig()).post(source.accessToken(), form, false).getBody();

        return getAuthToken(response);
    }

    /**
     * 从响应字符串中解析 AuthToken。
     *
     * @param response 服务器返回的授权令牌响应字符串
     * @return 解析出的 AuthToken 对象
     * @throws AuthException 如果响应中包含错误信息，则抛出 AuthException
     */
    private AuthToken getAuthToken(String response) {
        JSONObject accessTokenObject = JSONObject.parseObject(response);

        // 检查响应是否包含错误信息
        if (accessTokenObject.containsKey(Constants.ERROR_KEY)) {
            throw new AuthException(accessTokenObject.getString(Constants.ERROR_DESCRIPTION_KEY));
        }

        // 解析并构建 AuthToken 对象
        return AuthToken.builder()
                .accessToken(accessTokenObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
                .expireIn(accessTokenObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
                .tokenType(accessTokenObject.getString(YeelightOAuth2AccessToken.TOKEN_TYPE))
                .refreshToken(accessTokenObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
                .refreshTokenExpireIn(accessTokenObject.getIntValue("refresh_expires_in"))
                .build();
    }


    /**
     * 根据令牌获取用户信息。
     * <p>
     * 使用提供的令牌（accessToken）向远程服务发送请求，以获取用户的详细信息，并更新令牌的相关属性。
     * <p>
     * @param authToken 用户的认证令牌，包含访问令牌（accessToken）等信息。
     * @return AuthUser 用户信息对象，包含了用户的详细信息及认证令牌。
     */
    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        // 从令牌中获取访问令牌
        String accessToken = authToken.getAccessToken();
        // 向指定URL发送GET请求，获取用户信息，设置请求头包含授权令牌
        String response = new HttpUtils(config.getHttpConfig()).get(source.userInfo(), null, new HttpHeader()
            .add("Content-Type", "application/json")
            .add("Authorization", "Bearer " + accessToken), false).getBody();
        // 解析响应内容
        JSONObject object = JSON.parseObject(response);
        // 检查响应内容是否符合预期
        this.checkResponse(object);
        // 更新令牌信息，包括用户ID、开放ID和联合ID
        authToken.setUid(object.getString("user_id"));
        authToken.setOpenId(object.getString("open_id"));
        authToken.setUnionId(object.getString("union_id"));
        // 构建并返回用户信息对象
        return AuthUser.builder()
                .rawUserInfo(object)
                .uuid(object.getString("open_id"))
                .username(object.getString("name"))
                .nickname(object.getString("name"))
                .avatar(object.getString("avatar_url"))
                .company(object.getString("tenant_key"))
                .email(object.getString("email"))
                // 设置性别为未知
                .gender(AuthUserGender.UNKNOWN)
                // 设置用户来源名称
                .source(source.getName())
                // 设置认证令牌
                .token(authToken)
                // 设置用户来源字符串表示
                .source(source.toString())
                .build();
    }


    /**
     * access_token 过期后，开发者需要使用 refresh_token 重新获取一个有效的 access_token。开发者服务器需要严格保证 refresh_token 的安全，并禁止把 refresh_token 传递给网页前端或客户端。部分模式不支持刷新。
     * <p>
     * 当应用没有服务端时，客户端的 refresh_token 应当加密存储，或立即销毁。
     * @param authToken token
     * @return AuthResponse
     */
    @Override
    public AuthResponse<AuthToken> refresh(AuthToken authToken) {
        Map<String, String> form = new HashMap<>(2);
        form.put(YeelightOAuth2AccessToken.REFRESH_TOKEN, authToken.getRefreshToken());
        form.put(OAuth2Utils.GRANT_TYPE, YeelightOAuth2AccessToken.REFRESH_TOKEN);

        String response = new HttpUtils(config.getHttpConfig()).post(source.refresh(), form, false).getBody();
        return AuthResponse.<AuthToken>builder()
                .code(AuthResponseStatus.SUCCESS.getCode())
                .data(getAuthToken(response))
                .build();

    }

    /**
     * 应用请求用户身份验证时，需按如下方式构造授权登录页面链接，并引导用户跳转至此页面。用户在此页面确认授权之后会产生一个授权码（code），并通过 redirect_uri携带至开发者的服务器。
     * @param state state
     * @return String
     */
    @Override
    public String authorize(String state) {
        return UrlBuilder.fromBaseUrl(source.authorize())
            .queryParam(OAuth2Utils.CLIENT_ID, config.getClientId())
            .queryParam(OAuth2Utils.REDIRECT_URI, GlobalAuthUtils.urlEncode(config.getRedirectUri()))
            .queryParam(OAuth2Utils.RESPONSE_TYPE, "code")
            .queryParam(OAuth2Utils.STATE, getRealState(state))
            .build();
    }


    /**
     * 校验响应内容是否正确
     *
     * @param jsonObject 响应内容
     */
    private void checkResponse(JSONObject jsonObject) {
        if (jsonObject.getIntValue(Constants.CODE_KEY) != 0) {
            throw new AuthException(jsonObject.getString(Constants.MESSAGE_KEY));
        }
    }

}
