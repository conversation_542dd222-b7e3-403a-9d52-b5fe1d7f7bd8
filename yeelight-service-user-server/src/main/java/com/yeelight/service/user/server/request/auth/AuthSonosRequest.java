/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.request.auth
 * Description: Sonos登录
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-07-02 17:47:17:47
 */
package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xkcoding.http.support.HttpHeader;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import com.yeelight.service.user.server.request.scope.AuthSonosScope;
import lombok.Data;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.AuthScopeUtils;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import org.springframework.http.HttpHeaders;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Desc: Sonos登录
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2025-07-02 17:47:17:47
 */
public class AuthSonosRequest extends AuthDefaultRequest {

    public AuthSonosRequest(AuthConfig config) {
        super(config, AuthCustomSource.SONOS);
    }

    public AuthSonosRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthCustomSource.SONOS, authStateCache);
    }

    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        String response = doPostAuthorizationCode(authCallback.getCode());
        JSONObject accessTokenObject = JSONObject.parseObject(response);
        this.checkResponse(accessTokenObject);
        return AuthToken.builder()
                .accessToken(accessTokenObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
                .expireIn(accessTokenObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
                .tokenType(accessTokenObject.getString(YeelightOAuth2AccessToken.TOKEN_TYPE))
                .refreshToken(accessTokenObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
                .scope(accessTokenObject.getString(OAuth2Utils.SCOPE))
                .build();
    }

    @Override
    public AuthResponse<AuthToken> refresh(AuthToken oldToken) {
        String response = doPostRefresh(oldToken.getRefreshToken());
        JSONObject refreshTokenObject = JSONObject.parseObject(response);
        this.checkResponse(refreshTokenObject);
        return AuthResponse.<AuthToken>builder()
                .code(AuthResponseStatus.SUCCESS.getCode())
                .data(AuthToken.builder()
                        .accessToken(refreshTokenObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
                        .expireIn(refreshTokenObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
                        .tokenType(refreshTokenObject.getString(YeelightOAuth2AccessToken.TOKEN_TYPE))
                        .refreshToken(refreshTokenObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
                        .scope(refreshTokenObject.getString(OAuth2Utils.SCOPE))
                        .build())
                .build();
    }

    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        String response = doGetUserInfo(authToken);
        JSONObject object = JSONObject.parseObject(response);
        this.checkResponse(object);
        JSONArray households = object.getJSONArray("households");
        String sonosUserId = households.stream()
                // 类型检查
                .filter(item -> item instanceof JSONObject)
                .map(item -> (JSONObject) item)
                // 安全获取字段, 字段有id, swVersion, ownerLuid
                .map(obj -> obj.getString("ownerLuid"))
                // 过滤掉null值
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);

        // 如果sonosUserId为空，则抛出异常
        if (StringUtils.isBlank(sonosUserId)) {
            throw new AuthException("households is empty", this.source);
        }
        authToken.setUid(sonosUserId);
        // 将households中的id字段拼接成字符串，用##连接，存放在token的idToken字段
        authToken.setIdToken(households.stream().map(item -> {
            JSONObject jsonObject = (JSONObject) item;
            return jsonObject.getString("id");
        }).collect(Collectors.joining("##")));

        return AuthUser.builder()
                .rawUserInfo(object)
                .uuid(sonosUserId)
                .username("")
                .token(authToken)
                .source(source.toString())
                .build();
    }

    /**
     * 检查响应内容是否正确
     *
     * @param object 请求响应内容
     */
    private void checkResponse(JSONObject object) {
        if (StringUtils.isNotBlank(object.getString(Constants.ERROR_KEY))) {
            throw new AuthException(object.getString(Constants.ERROR_DESCRIPTION_KEY), this.source);
        }
    }

    /**
     * 返回带{@code state}参数的授权url，授权回调时会带上这个{@code state}
     *
     * @param state state 验证授权流程的参数，可以防止csrf
     * @return 返回授权地址
     * @since 1.9.3
     */
    @Override
    public String authorize(String state) {
        return UrlBuilder.fromBaseUrl(source.authorize())
                .queryParam(OAuth2Utils.RESPONSE_TYPE, "code")
                .queryParam(OAuth2Utils.CLIENT_ID, config.getClientId())
                .queryParam(OAuth2Utils.REDIRECT_URI, config.getRedirectUri())
                .queryParam(OAuth2Utils.SCOPE, this.getScopes(" ", true, AuthScopeUtils.getDefaultScopes(AuthSonosScope.values())))
                .queryParam(OAuth2Utils.STATE, getRealState(state))
                .build();
    }

    /**
     * 返回获取accessToken的url
     *
     * @param code 授权码
     * @return 返回获取accessToken的url
     */
    @Override
    public String accessTokenUrl(String code) {
        return UrlBuilder.fromBaseUrl(source.accessToken())
                .build();
    }

    /**
     * 获取刷新access token的url
     *
     * @param refreshToken 刷新token
     * @return 刷新access token的url
     */
    @Override
    public String refreshTokenUrl(String refreshToken) {
        return UrlBuilder.fromBaseUrl(source.refresh())
                .build();
    }

    /**
     * 返回获取userInfo的url
     *
     * @param authToken token
     * @return 返回获取userInfo的url
     */
    @Override
    public String userInfoUrl(AuthToken authToken) {
        return UrlBuilder.fromBaseUrl(source.userInfo())
                .queryParam("connectedOnly", false)
                .build();
    }

    /**
     * 通用的 authorizationCode 协议
     *
     * @param code code码
     * @return Response
     */
    @Override
    protected String doPostAuthorizationCode(String code) {
        Map<String, String> form = new HashMap<>(2);
        form.put(OAuth2Utils.GRANT_TYPE, "authorization_code");
        form.put("code", code);
        form.put(OAuth2Utils.REDIRECT_URI, config.getRedirectUri());
        return new HttpUtils(config.getHttpConfig()).post(accessTokenUrl(code), form, buildAuthHttpHeader(),  false).getBody();
    }

    private String doPostRefresh(String refreshToken) {
        Map<String, String> form = new HashMap<>(2);
        form.put(OAuth2Utils.GRANT_TYPE, "refresh_token");
        form.put("refresh_token", refreshToken);
        return new HttpUtils(config.getHttpConfig()).post(refreshTokenUrl(refreshToken), form, buildAuthHttpHeader(),  false).getBody();
    }

    @Override
    protected String doGetUserInfo(AuthToken authToken) {
        // sonos没有获取用户信息的接口，所以这里请求获取 households 的接口，返回的households中包含家庭的账户列表。
        HttpHeader httpHeader = new HttpHeader();
        httpHeader.add(HttpHeaders.CONTENT_TYPE, "application/json;charset=utf-8");
        httpHeader.add(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", authToken.getAccessToken()));

        return new HttpUtils(config.getHttpConfig()).get(userInfoUrl(authToken), null,httpHeader,  false).getBody();
    }

    private HttpHeader buildAuthHttpHeader() {
        HttpHeader httpHeader = new HttpHeader();
        httpHeader.add(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded;charset=utf-8");
        httpHeader.add(HttpHeaders.AUTHORIZATION, String.format("Basic %s", Base64.getEncoder().encodeToString((config.getClientId() + ":" + config.getClientSecret()).getBytes(StandardCharsets.UTF_8))));
        return httpHeader;
    }

    @Data
    private static class HouseHold implements Serializable {
        private String id;
    }
}

