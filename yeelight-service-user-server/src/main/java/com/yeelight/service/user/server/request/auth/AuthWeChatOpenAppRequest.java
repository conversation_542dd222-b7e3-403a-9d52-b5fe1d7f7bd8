package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;

/**
 * 微信开放平台登录
 *
 * <AUTHOR> (<a href="https://xkcoding.com">...</a>)
 * @since 1.1.0
 */
public class AuthWeChatOpenAppRequest extends AuthDefaultRequest {
    public AuthWeChatOpenAppRequest(AuthConfig config) {
        super(config, AuthCustomSource.WECHAT_OPEN_APP);
    }

    public AuthWeChatOpenAppRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthCustomSource.WECHAT_OPEN_APP, authStateCache);
    }

    /**
     * 微信的特殊性，此时返回的信息同时包含 openid 和 access_token
     *
     * @param authCallback 回调返回的参数
     * @return 所有信息
     */
    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        return this.getToken(accessTokenUrl(authCallback.getCode()));
    }

    /**
     * 根据认证令牌获取用户信息。
     * <p>
     * 该方法通过认证令牌（AuthToken）获取用户的详细信息，并构建一个AuthUser对象返回。
     * 这些信息包括用户的昵称、头像、地理位置、性别等。
     * 如果可用，还会包括用户的union id（统一标识）。
     *
     * @param authToken 认证令牌，用于获取用户信息。
     * @return AuthUser对象，包含从用户令牌中解析出的用户信息。
     */
    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        // 从令牌中获取用户的开放ID
        String openId = authToken.getOpenId();

        // 向第三方平台发送请求，以获取用户信息
        String response = doGetUserInfo(authToken);
        // 将响应解析为JSON对象
        JSONObject object = JSONObject.parseObject(response);

        // 检查响应是否成功，如果不成功，则抛出异常
        this.checkResponse(object);

        // 构建用户地理位置字符串
        String location = String.format("%s-%s-%s", object.getString("country"), object.getString("province"), object.getString("city"));

        // 如果响应中包含unionid，将其设置到令牌中
        if (object.containsKey(Constants.UNION_ID_KEY)) {
            authToken.setUnionId(object.getString(Constants.UNION_ID_KEY));
        }

        // 构建并返回AuthUser对象，包含用户的详细信息
        return AuthUser.builder()
            .rawUserInfo(object)
            .username(object.getString("nickname"))
            .nickname(object.getString("nickname"))
            .avatar(object.getString("headimgurl"))
            .location(location)
            .uuid(openId)
            .gender(AuthUserGender.getWechatRealGender(object.getString("sex")))
            .token(authToken)
            .source(source.toString())
            .build();
    }


    /**
     * 刷新认证令牌。
     * 该方法使用一个旧的认证令牌（refresh token）来获取一个新的认证令牌。
     *
     * @param oldToken 旧的认证令牌对象，其中包含了刷新令牌（refresh token）。
     * @return 返回一个认证响应对象，包含新的认证令牌和操作状态码。
     */
    @Override
    public AuthResponse<AuthToken> refresh(AuthToken oldToken) {
        // 使用旧令牌中的刷新令牌来获取新的令牌
        return AuthResponse.<AuthToken>builder()
                // 设置操作状态码为成功
            .code(AuthResponseStatus.SUCCESS.getCode())
                // 获取并设置新的令牌数据
            .data(this.getToken(refreshTokenUrl(oldToken.getRefreshToken())))
            .build();
    }


    /**
     * 检查响应内容是否正确
     *
     * @param object 请求响应内容
     */
    private void checkResponse(JSONObject object) {
        if (object.containsKey(Constants.ERR_CODE_KEY)) {
            throw new AuthException(object.getIntValue(Constants.ERR_CODE_KEY), object.getString(Constants.ERR_MSG_KEY));
        }
    }

    /**
     * 获取token，适用于获取access_token和刷新token
     *
     * @param accessTokenUrl 实际请求token的地址
     * @return token对象
     */
    private AuthToken getToken(String accessTokenUrl) {
        String response = new HttpUtils(config.getHttpConfig()).get(accessTokenUrl).getBody();
        JSONObject accessTokenObject = JSONObject.parseObject(response);

        this.checkResponse(accessTokenObject);

        return AuthToken.builder()
            .accessToken(accessTokenObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
            .refreshToken(accessTokenObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
            .expireIn(accessTokenObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
            .openId(accessTokenObject.getString("openid"))
            .build();
    }

    /**
     * 返回带{@code state}参数的授权url，授权回调时会带上这个{@code state}
     *
     * @param state state 验证授权流程的参数，可以防止csrf
     * @return 返回授权地址
     * @since 1.9.3
     */
    @Override
    public String authorize(String state) {
        return UrlBuilder.fromBaseUrl(source.authorize())
            .queryParam(OAuth2Utils.RESPONSE_TYPE, "code")
            .queryParam("appid", config.getClientId())
            .queryParam(OAuth2Utils.REDIRECT_URI, config.getRedirectUri())
            .queryParam(OAuth2Utils.SCOPE, "snsapi_login")
            .queryParam(OAuth2Utils.STATE, getRealState(state))
            .build();
    }

    /**
     * 返回获取accessToken的url
     *
     * @param code 授权码
     * @return 返回获取accessToken的url
     */
    @Override
    protected String accessTokenUrl(String code) {
        return UrlBuilder.fromBaseUrl(source.accessToken())
            .queryParam("code", code)
            .queryParam("appid", config.getClientId())
            .queryParam("secret", config.getClientSecret())
            .queryParam(OAuth2Utils.GRANT_TYPE, "authorization_code")
            .build();
    }

    /**
     * 返回获取userInfo的url
     *
     * @param authToken 用户授权后的token
     * @return 返回获取userInfo的url
     */
    @Override
    protected String userInfoUrl(AuthToken authToken) {
        return UrlBuilder.fromBaseUrl(source.userInfo())
            .queryParam(YeelightOAuth2AccessToken.ACCESS_TOKEN, authToken.getAccessToken())
            .queryParam("openid", authToken.getOpenId())
            .queryParam("lang", "zh_CN")
            .build();
    }

    /**
     * 返回获取userInfo的url
     *
     * @param refreshToken getAccessToken方法返回的refreshToken
     * @return 返回获取userInfo的url
     */
    @Override
    protected String refreshTokenUrl(String refreshToken) {
        return UrlBuilder.fromBaseUrl(source.refresh())
            .queryParam("appid", config.getClientId())
            .queryParam(YeelightOAuth2AccessToken.REFRESH_TOKEN, refreshToken)
            .queryParam(OAuth2Utils.GRANT_TYPE, YeelightOAuth2AccessToken.REFRESH_TOKEN)
            .build();
    }
}
