/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.request.auth
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-06-26 11:26:11:26
 */
package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.xkcoding.http.support.HttpHeader;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.config.AuthDefaultSource;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 微信小程序授权登录
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
public class AuthWechatMiniProgramRequest extends AuthDefaultRequest {
    public AuthWechatMiniProgramRequest(AuthConfig config) {
        super(config, AuthDefaultSource.WECHAT_MINI_PROGRAM);
    }

    public AuthWechatMiniProgramRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthDefaultSource.WECHAT_MINI_PROGRAM, authStateCache);
    }

    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        // 参见 https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html 文档
        // 使用 code 获取对应的 openId、unionId 等字段
        String response = new HttpUtils(config.getHttpConfig()).get(accessTokenUrl(authCallback.getCode())).getBody();
        AuthWechatMiniProgramRequest.JSCode2SessionResponse accessTokenObject = JSONObject.parseObject(response, AuthWechatMiniProgramRequest.JSCode2SessionResponse.class);
        assert accessTokenObject != null;
        checkResponse(accessTokenObject);
        // 拼装结果
        return AuthToken.builder()
                .openId(accessTokenObject.getOpenid())
                .unionId(accessTokenObject.getUnionId())
                .accessToken(accessTokenObject.getSessionKey())
                .accessCode(authCallback.getAuth_code())
                .build();
    }

    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        // 参见 https://developers.weixin.qq.com/miniprogram/dev/api/open-api/user-info/wx.getUserProfile.html 文档
        // 如果需要用户信息，需要在小程序调用函数后传给后端
        return AuthUser.builder()
                .username("")
                .nickname("")
                .avatar("")
                .uuid(authToken.getOpenId())
                .token(authToken)
                .source(source.toString())
                .rawUserInfo(getPhoneInfo(authToken))
                .build();
    }

    private JSONObject getPhoneInfo(AuthToken authToken) {
        // 组织用户信息
        JSONObject userInfo = new JSONObject();

        // 根据手机号code获取手机号
        // 参见 https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html 文档
        if (StringUtils.isNotBlank(authToken.getAccessCode())) {
            Map<String, String> data = new HashMap<>(1);
            data.put(Constants.CODE_KEY, authToken.getAccessCode());
            HttpHeader httpHeader = new HttpHeader();
            httpHeader.add(com.xkcoding.http.constants.Constants.CONTENT_TYPE, "application/json");
            String phoneNumberResponse = new HttpUtils(config.getHttpConfig()).post(phoneUrl(getToken(Boolean.FALSE)),  JSONObject.toJSONString(data), httpHeader).getBody();
            PhoneInfo phoneInfo = JSONObject.parseObject(phoneNumberResponse, PhoneInfo.class);
            if (phoneInfo.getErrorCode() != 0) {
                if (phoneInfo.getErrorCode() == 40001) {
                    // access code已过期，重新获取
                    getToken(Boolean.TRUE);

                    // 重新获取手机号
                    phoneNumberResponse = new HttpUtils(config.getHttpConfig()).post(phoneUrl(getToken(Boolean.FALSE)), JSONObject.toJSONString(data), httpHeader).getBody();
                    phoneInfo = JSONObject.parseObject(phoneNumberResponse, PhoneInfo.class);
                    checkResponse(phoneInfo);
                }
                checkResponse(phoneInfo);
            }
            String phone = phoneInfo.getPhoneInfo().getPurePhoneNumber();
            userInfo.put(Constants.PHONE_KEY, phone);
            // 获取手机号后，将access code置空
            authToken.setAccessCode(null);
        }
        return userInfo;
    }

    /**
     * 获取接口调用凭据
     * 详见 <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getAccessToken.html">获取接口调用凭据</a>
     * @return 接口调用凭据
     */
    private String getToken(Boolean forceRefresh) {
        String token;
        String tokenCacheKey = this.source.getName().concat(":client_credential:").concat(config.getClientId());
        // 先获取接口调用凭证
        if (!this.authStateCache.containsKey(tokenCacheKey) || forceRefresh) {
            String tokenResponse = new HttpUtils(config.getHttpConfig()).get(tokenUrl()).getBody();
            TokenInfo tokenObject = JSONObject.parseObject(tokenResponse, TokenInfo.class);
            checkResponse(tokenObject);
            token = tokenObject.getAccessToken();
            // 缓存接口调用凭证
            this.authStateCache.cache(tokenCacheKey, token, TimeUnit.SECONDS.toMillis(tokenObject.getExpiresIn()));
        } else {
            // 获取缓存的接口调用凭证
            token = this.authStateCache.get(tokenCacheKey);
        }
        return token;
    }

    /**
     * 检查响应内容是否正确
     *
     * @param response 请求响应内容
     */
    private void checkResponse(WechatMiniResponse response) {
        if (response.getErrorCode() != 0) {
            throw new AuthException(response.getErrorCode(), response.getErrorMsg());
        }
    }

    @Override
    protected String accessTokenUrl(String code) {
        return UrlBuilder.fromBaseUrl(source.accessToken())
                .queryParam("appid", config.getClientId())
                .queryParam("secret", config.getClientSecret())
                .queryParam("js_code", code)
                .queryParam("grant_type", "authorization_code")
                .build();
    }

    /**
     * 获取接口调用凭据请求地址
     * 详见 <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-access-token/getAccessToken.html">获取接口调用凭据</a>
     * @return 接口调用凭据请求地址
     */
    private String tokenUrl() {
        return UrlBuilder.fromBaseUrl("https://api.weixin.qq.com/cgi-bin/token")
                .queryParam("appid", config.getClientId())
                .queryParam("secret", config.getClientSecret())
                .queryParam("grant_type", "client_credential")
                .build();
    }

    private String phoneUrl(String accessToken) {
        return UrlBuilder.fromBaseUrl("https://api.weixin.qq.com/wxa/business/getuserphonenumber")
                .queryParam("access_token", accessToken)
                .build();
    }

    @Data
    @SuppressWarnings("SpellCheckingInspection")
    private static class WechatMiniResponse implements Serializable {
        @JSONField(name = "errcode")
        private int errorCode;
        @JSONField(name = "errmsg")
        private String errorMsg;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @SuppressWarnings("SpellCheckingInspection")
    private static class JSCode2SessionResponse extends WechatMiniResponse {
        @JSONField(name = "session_key")
        private String sessionKey;
        private String openid;
        @JSONField(name = "unionid")
        private String unionId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @SuppressWarnings("SpellCheckingInspection")
    private static class TokenInfo extends WechatMiniResponse {
        /**
         * 获取到的凭证
         */
        @JSONField(name = "access_token")
        private String accessToken;

        /**
         * 凭证有效时间，单位：秒
         * 目前是7200秒之内的值。
         */
        @JSONField(name = "expires_in")
        private int expiresIn;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @SuppressWarnings("SpellCheckingInspection")
    private static class PhoneInfo extends WechatMiniResponse {
        /**
         * 用户手机号信息
         */
        @JSONField(name = "phone_info")
        private PhoneNumber phoneInfo;

        @Data
        private static class PhoneNumber {
            /**
             * 用户绑定的手机号（国外手机号会有区号）
             */
            private String phoneNumber;

            /**
             * 没有区号的手机号
             */
            private String purePhoneNumber;

            /**
             * 区号
             */
            private String countryCode;
        }
    }
}