package com.yeelight.service.user.server.request.auth;

import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.enums.scope.AuthMiScope;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.AuthScopeUtils;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.utils.OAuth2Utils;

/**
 * @program: yeelight-oauth-api
 * @description:
 * @author: lixiaodong
 * @create: 2022-01-14 18:50
 **/
public class AuthXiaoMiRequest extends AuthDefaultRequest {
    private static final String PREFIX = "&&&START&&&";

    public AuthXiaoMiRequest(AuthConfig config) {
        super(config, AuthCustomSource.XIAOMI);
    }

    public AuthXiaoMiRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthCustomSource.XIAOMI, authStateCache);
    }

    /**
     * 通过授权回调获取访问令牌。
     *
     * @param authCallback 包含授权代码的认证回调对象，用于获取访问令牌。
     * @return 返回通过授权代码换取的 AuthToken 对象。
     */
    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        // 使用授权代码获取访问令牌
        return getToken(accessTokenUrl(authCallback.getCode()));
    }


    /**
     * 通过访问令牌URL获取授权令牌。
     *
     * @param accessTokenUrl 访问令牌的URL地址。
     * @return AuthToken 包含访问令牌、过期时间、作用域、令牌类型、刷新令牌等信息的对象。
     * @throws AuthException 如果获取授权令牌过程中出现错误，抛出此异常。
     */
    private AuthToken getToken(String accessTokenUrl) {
        // 使用HTTP工具类获取响应字符串
        String response = new HttpUtils(config.getHttpConfig()).get(accessTokenUrl).getBody();
        // 移除响应前缀
        String jsonStr = response.replace(PREFIX, com.xkcoding.http.constants.Constants.EMPTY);
        // 将JSON字符串解析为对象
        JSONObject accessTokenObject = JSONObject.parseObject(jsonStr);

        // 检查JSON对象中是否包含错误信息
        if (accessTokenObject.containsKey(Constants.ERROR_KEY)) {
            // 如果包含错误信息，抛出授权异常
            throw new AuthException(accessTokenObject.getString(Constants.ERROR_DESCRIPTION_KEY));
        }

        // 构建并返回AuthToken对象
        return AuthToken.builder()
                .accessToken(accessTokenObject.getString(YeelightOAuth2AccessToken.ACCESS_TOKEN))
                .expireIn(accessTokenObject.getIntValue(YeelightOAuth2AccessToken.EXPIRES_IN))
                .scope(accessTokenObject.getString(OAuth2Utils.SCOPE))
                .tokenType(accessTokenObject.getString(YeelightOAuth2AccessToken.TOKEN_TYPE))
                .refreshToken(accessTokenObject.getString(YeelightOAuth2AccessToken.REFRESH_TOKEN))
                .openId(accessTokenObject.getString("openId"))
                .unionId(accessTokenObject.getString("union_id"))
                .macAlgorithm(accessTokenObject.getString("mac_algorithm"))
                .macKey(accessTokenObject.getString("mac_key"))
                .build();
    }


    /**
     * 根据令牌获取用户信息。
     *
     * @param authToken 用户的认证令牌，用于获取用户详细信息。
     * @return AuthUser 包含用户详细信息的对象。
     * @throws AuthException 如果获取用户信息过程中出现错误，则抛出认证异常。
     */
    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        // 通过认证令牌获取用户基本信息
        String userResponse = doGetUserInfo(authToken);

        JSONObject userProfile = JSONObject.parseObject(userResponse);
        // 检查是否获取用户信息成功，若失败则抛出异常
        if (Constants.ERROR_KEY.equalsIgnoreCase(userProfile.getString(Constants.RESULT_KEY))) {
            throw new AuthException(userProfile.getString("description"));
        }

        JSONObject object = userProfile.getJSONObject("data");

        // 构建 AuthUser 对象，设置用户的基本信息
        return AuthUser.builder()
                .rawUserInfo(object)
                .uuid(object.getString("userId"))
                .username(object.getString("miliaoNick"))
                .nickname(object.getString("miliaoNick"))
                .avatar(object.getString("miliaoIcon"))
                .email(object.getString("mail"))
                .gender(AuthUserGender.UNKNOWN)
                .token(authToken)
                .source(source.toString())
                .rawUserInfo(userProfile)
                .build();
    }

    /**
     * 刷新access token （续期）
     *
     * @param authToken 登录成功后返回的Token信息
     * @return AuthResponse
     */
    @Override
    public AuthResponse<AuthToken> refresh(AuthToken authToken) {
        return AuthResponse.<AuthToken>builder()
                .code(AuthResponseStatus.SUCCESS.getCode())
                .data(getToken(refreshTokenUrl(authToken.getRefreshToken())))
                .build();
    }

    /**
     * 返回带{@code state}参数的授权url，授权回调时会带上这个{@code state}
     *
     * @param state state 验证授权流程的参数，可以防止csrf
     * @return 返回授权地址
     * @since 1.9.3
     */
    @Override
    public String authorize(String state) {
        return UrlBuilder.fromBaseUrl(super.authorize(state))
                .queryParam("skip_confirm", "false")
                .queryParam(OAuth2Utils.SCOPE, this.getScopes(" ", true, AuthScopeUtils.getDefaultScopes(AuthMiScope.values())))
                .build();
    }

    /**
     * 返回获取userInfo的url
     *
     * @param authToken 用户授权后的token
     * @return 返回获取userInfo的url
     */
    @Override
    protected String userInfoUrl(AuthToken authToken) {
        return UrlBuilder.fromBaseUrl(source.userInfo())
                .queryParam("clientId", config.getClientId())
                .queryParam(SecurityConstants.TOKEN_KEY, authToken.getAccessToken())
                .build();
    }
}