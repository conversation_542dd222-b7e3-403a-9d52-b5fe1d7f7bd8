/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.request.auth
 * Description: 自定义AuthRequest工厂类
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-10-19 10:26:10:26
 */
package com.yeelight.service.user.server.request.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.xkcoding.http.config.HttpConfig;
import com.xkcoding.justauth.AuthRequestFactory;
import com.xkcoding.justauth.autoconfigure.ExtendProperties;
import com.xkcoding.justauth.autoconfigure.JustAuthProperties;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.user.server.enums.AuthCustomSource;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.config.AuthDefaultSource;
import me.zhyd.oauth.config.AuthSource;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.request.*;
import org.springframework.util.CollectionUtils;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Desc: 自定义AuthRequest工厂类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-10-19 10:26:10:26
 */
@Slf4j
public class YeelightAuthRequestFactory extends AuthRequestFactory {
    private final JustAuthProperties properties;
    private final AuthStateCache authStateCache;

    public YeelightAuthRequestFactory(JustAuthProperties properties, AuthStateCache authStateCache) {
        super(properties, authStateCache);
        this.properties = properties;
        this.authStateCache = authStateCache;
    }

    /**
     * 返回当前Oauth列表。
     * 这个方法会综合默认Oauth类型和扩展配置来生成一个包含所有可用Oauth类型的列表。
     *
     * @return 返回一个包含所有Oauth类型的字符串列表。
     */
    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public List<String> oauthList() {
        // 初始化默认Oauth类型列表，基于配置属性中的类型键集。
        List<String> defaultList = new ArrayList<>(properties.getType().keySet());

        // 初始化扩展Oauth类型列表。
        List<String> extendList = new ArrayList<>();
        ExtendProperties extend = properties.getExtend();
        if (null != extend) {
            // 获取扩展配置中指定的枚举类。
            Class enumClass = extend.getEnumClass();
            // 从枚举类中获取所有名称。
            List<String> names = EnumUtil.getNames(enumClass);

            // 填充扩展列表，包含在扩展配置中找到且匹配枚举名称或包含用户供应商简写的项。
            extendList = extend.getConfig()
                    .keySet()
                    .stream()
                    .filter(x -> names.contains(x.toUpperCase()) ||
                            (
                                    names.stream().anyMatch(name -> x.toUpperCase().contains(name)) &&
                                    Arrays.stream(UserVendorEnum.values()).anyMatch(vendor -> x.toUpperCase().contains(UserVendorHolder.getSimpleVendor(vendor.getCode())))
                            ))
                    .map(String::toUpperCase)
                    .collect(Collectors.toList());
        }

        // 合并默认列表和扩展列表，返回合并后的Oauth类型列表。
        return (List<String>) CollUtil.addAll(defaultList, extendList);
    }


    /**
     * 根据提供的来源源信息, 返回相应的AuthRequest对象。
     *
     * @param source 来源信息，指定认证请求的来源。类型为{@link AuthSource}。
     * @return 返回一个{@link AuthRequest}对象，用于进行认证请求。
     * @throws AuthException 如果没有有效的来源信息或找不到对应的AuthRequest实现时，抛出此异常。
     */
    @Override
    public AuthRequest get(String source) {
        // 检查来源信息是否为空
        if (StrUtil.isBlank(source)) {
            throw new AuthException(AuthResponseStatus.NO_AUTH_SOURCE);
        }
        // 添加供应商信息到来源
        source = UserVendorHolder.attachVendor(source);
        // 初始化映射来源，用于后续处理
        String mappingSource;

        // 特殊处理默认供应商下的WECHAT_YEELIGHT_APP来源，避免特定字符串被移除
        if (UserVendorHolder.getVendorEnum().getCode().equals(UserVendorEnum.DEFAULT.getCode()) && AuthCustomSource.WECHAT_YEELIGHT_APP.name().equals(source)) {
            // 针对 WECHAT_YEELIGHT_APP 特殊处理。否则会被removeVendor 移除掉 中间的 YEELIGHT_
            mappingSource = source;
        } else {
            // 移除来源中的供应商信息，得到映射来源
            mappingSource = UserVendorHolder.removeVendor(source);
        }

        // 尝试从默认配置中获取AuthRequest对象
        AuthRequest authRequest = getDefaultRequest(source, mappingSource);

        // 如果默认配置中没有找到，尝试从扩展配置中获取
        if (authRequest == null) {
            authRequest = getExtendRequest(properties.getExtend().getEnumClass(), source, mappingSource);
        }

        // 如果始终没有找到对应的AuthRequest对象，则抛出异常
        if (authRequest == null) {
            throw new AuthException(AuthResponseStatus.UNSUPPORTED);
        }

        return authRequest;
    }


    /**
     * 获取自定义的 request
     *
     * @param clazz  枚举类 {@link AuthSource}
     * @param source {@link AuthSource}
     * @return {@link AuthRequest}
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private AuthRequest getExtendRequest(Class clazz, String source, String mappingSource) {
        String upperSource = source.toUpperCase();
        String upperMappingSource = mappingSource.toUpperCase();
        try {
            EnumUtil.fromString(clazz, upperMappingSource);
        } catch (IllegalArgumentException e) {
            // 无自定义匹配
            return null;
        }

        Map<String, ExtendProperties.ExtendRequestConfig> extendConfig = properties.getExtend().getConfig();

        // key 转大写
        Map<String, ExtendProperties.ExtendRequestConfig> upperConfig = new HashMap<>(6);
        extendConfig.forEach((k, v) -> upperConfig.put(k.toUpperCase(), v));

        ExtendProperties.ExtendRequestConfig extendRequestConfig = upperConfig.get(upperSource);
        if (extendRequestConfig != null) {

            // 配置 http config
            configureHttpConfig(upperMappingSource, extendRequestConfig, properties.getHttpConfig());

            Class<? extends AuthRequest> requestClass = extendRequestConfig.getRequestClass();

            if (requestClass != null) {
                // 反射获取 Request 对象，所以必须实现 2 个参数的构造方法
                return ReflectUtil.newInstance(requestClass, extendRequestConfig, authStateCache);
            }
        }

        return null;
    }


    /**
     * 获取默认的 Request
     *
     * @param source {@link AuthSource}
     * @return {@link AuthRequest}
     */
    private AuthRequest getDefaultRequest(String source, String mappingSource) {
        AuthDefaultSource authDefaultSource;

        try {
            authDefaultSource = EnumUtil.fromString(AuthDefaultSource.class, mappingSource.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 无自定义匹配
            return null;
        }

        AuthConfig config = properties.getType().get(source.toUpperCase());
        // 找不到对应关系，直接返回空
        if (config == null) {
            return null;
        }

        // 配置 http config
        configureHttpConfig(authDefaultSource.name(), config, properties.getHttpConfig());

        return getAuthDefaultRequest(authDefaultSource, config);
    }

    private AuthDefaultRequest getAuthDefaultRequest(AuthDefaultSource authDefaultSource, AuthConfig config) {
        Class<? extends AuthDefaultRequest> targetClass = authDefaultSource.getTargetClass();
        if (null == targetClass) {
            throw new AuthException(AuthResponseStatus.NOT_IMPLEMENTED);
        }
        try {
            if (this.authStateCache == null) {
                return targetClass.getDeclaredConstructor(AuthConfig.class).newInstance(config);
            } else {
                return targetClass.getDeclaredConstructor(AuthConfig.class, AuthStateCache.class).newInstance(config, authStateCache);
            }
        } catch (Exception e) {
            throw new AuthException(AuthResponseStatus.NOT_IMPLEMENTED);
        }
    }

    /**
     * 配置 http 相关的配置
     *
     * @param authSource {@link AuthSource}
     * @param authConfig {@link AuthConfig}
     */
    private void configureHttpConfig(String authSource, AuthConfig authConfig, JustAuthProperties.JustAuthHttpConfig httpConfig) {
        if (null == httpConfig) {
            return;
        }
        Map<String, JustAuthProperties.JustAuthProxyConfig> proxyConfigMap = httpConfig.getProxy();
        if (CollectionUtils.isEmpty(proxyConfigMap)) {
            return;
        }
        JustAuthProperties.JustAuthProxyConfig proxyConfig = proxyConfigMap.get(authSource);

        if (null == proxyConfig) {
            return;
        }

        authConfig.setHttpConfig(HttpConfig.builder()
                .timeout(httpConfig.getTimeout())
                .proxy(new Proxy(Proxy.Type.valueOf(proxyConfig.getType()), new InetSocketAddress(proxyConfig.getHostname(), proxyConfig.getPort())))
                .build());
    }
}
