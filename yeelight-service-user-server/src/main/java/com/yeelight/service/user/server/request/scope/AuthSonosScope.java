/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.request.scope
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2025-07-02 18:01:18:01
 */
package com.yeelight.service.user.server.request.scope;

import lombok.AllArgsConstructor;
import lombok.Getter;
import me.zhyd.oauth.enums.scope.AuthScope;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2025-07-02 18:01:18:01
 */
@Getter
@AllArgsConstructor
public enum AuthSonosScope implements AuthScope {
    /**
     * {@code scope} 含义，以{@code description} 为准
     */
    USER("playback-control-all", "播放收藏夹和播放列表", true),
    ;
    private final String scope;
    private final String description;
    private final boolean isDefault;
}
