package com.yeelight.service.user.server.service;

import com.yeelight.service.framework.exception.BizException;
import me.zhyd.oauth.model.AuthToken;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @program: yeelight-oauth-api
 * @description:
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2022-02-10 09:25
 **/
public interface AmazonAlexaService {
    /**
     * 获取LWA（Login with Amazon）的备用URL。
     * @param source 参数来源标识。
     * @param request HttpServletRequest对象，用于获取请求信息。
     * @return 返回LWA的备用URL字符串。
     * @throws BizException 业务异常。
     */
    String getLwaFallbackUrl(String source, HttpServletRequest request) throws BizException;

    /**
     * 获取Alexa应用的URL。
     * @param source 参数来源标识。
     * @param request HttpServletRequest对象，用于获取请求信息。
     * @return 返回Alexa应用的URL字符串。
     * @throws BizException 业务异常。
     */
    String getAlexaAppUrl(String source, HttpServletRequest request) throws BizException;

    /**
     * 查询Alexa认证令牌。
     * @param source 参数来源标识。
     * @param code 授权码。
     * @param state 状态码。
     * @return 返回AuthToken对象。
     * @throws BizException 业务异常。
     */
    AuthToken queryAlexaAuthToken(String source, String code, String state) throws BizException;

    /**
     * 生成Yeelight认证码。
     * @param source 参数来源标识。
     * @param yeelightToken Yeelight令牌。
     * @param state 状态码。
     * @return 返回生成的Yeelight认证码字符串。
     * @throws BizException 业务异常。
     */
    String generateYeelightAuthCode(String source, String yeelightToken, String state) throws BizException;

    /**
     * 查询Alexa API端点。
     * @param source 参数来源标识。
     * @param token 认证令牌。
     * @return 返回Alexa API端点的字符串列表。
     * @throws BizException 业务异常。
     */
    List<String> queryAlexaApiEndpoints(String source, String token) throws BizException;

    /**
     * 查询技能及账号链接状态。
     * @param source 参数来源标识。
     * @param alexaToken Alexa的认证令牌。
     * @return 返回技能及账号链接状态的布尔值。
     * @throws BizException 业务异常。
     */
    Boolean querySkillAndLinkAccountStatus(String source, String alexaToken) throws BizException;
}
