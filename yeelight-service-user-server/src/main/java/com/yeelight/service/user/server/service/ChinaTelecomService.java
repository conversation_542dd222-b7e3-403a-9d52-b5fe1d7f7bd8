package com.yeelight.service.user.server.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.server.request.ChinaTelecomTokenNotifyRequest;
import com.yeelight.service.user.server.request.ChinaTelecomVerifyRequest;
import com.yeelight.service.user.server.vo.ChinaTelecomToken;
import com.yeelight.service.user.server.vo.ChinaTelecomUserInfo;

/**
 * 快捷操作oauth数据的服务
 * <AUTHOR>
 */
public interface ChinaTelecomService {
    /**
     * 从tokenNotifyRequest中提取tokenCode
     * @param tokenNotifyRequest tokenNotifyRequest
     * @return ChinaTelecomVerifyRequest
     * @throws BizException BizException
     */
    ChinaTelecomVerifyRequest extractTokenCode(ChinaTelecomTokenNotifyRequest tokenNotifyRequest) throws BizException;

    /**
     * 根据TokenCode换取token-B接口
     * @link <a href="https://id.dlife.cn/api?initialSrc=/html/api_detail_662.html">API</a>
     * @param tokenCode tokenCode
     * @return ChinaTelecomToken
     * @throws BizException BizException
     */
    ChinaTelecomToken getTokenByTokenCode(String tokenCode) throws BizException;

    /**
     * 获取用户信息
     * @link <a href="https://id.dlife.cn/api?initialSrc=/html/api_detail_607.html">API</a>
     * @param accessToken accessToken
     * @return ChinaTelecomUserInfo
     * @throws BizException BizException
     */
    ChinaTelecomUserInfo getUserInfo(String accessToken) throws BizException;
}
