package com.yeelight.service.user.server.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.token.JustAuthAuthenticationToken;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * JustAuthUserDetailsService Interface
 *
 * <AUTHOR> Yu
 * @date 9/13/21 4:52 PM
 */
public interface JustAuthUserDetailsService {
    /**
     * 根据令牌加载用户信息。
     *
     * @param authAuthenticationToken 用户认证令牌，类型为JustAuthAuthenticationToken。
     * @return 返回 YeelightUser 用户对象。
     * @throws UsernameNotFoundException 如果用户不存在，抛出此异常。
     */
    YeelightUser loadUserByToken(JustAuthAuthenticationToken authAuthenticationToken) throws UsernameNotFoundException;

    /**
     * 根据Yeelight ID和来源获取社交用户信息。
     *
     * @param yeelightId 用户的Yeelight ID。
     * @param source 用户信息来源。
     * @return 返回 SocialUserDto 社交用户数据传输对象。
     * @throws BizException 业务异常。
     */
    SocialUserDto getSocialUser(Long yeelightId, String source) throws BizException;

    /**
     * 根据Yeelight令牌和来源获取社交用户信息
     *
     * @param yeelightToken Yeelight用户的认证令牌
     * @param source 用户来源，比如社交平台的标识
     * @return 返回SocialUserDto社交用户信息对象
     * @throws BizException 如果获取用户信息发生业务异常，则抛出此异常
     */
    SocialUserDto getSocialUser(String yeelightToken, String source) throws BizException;

    /**
     * 自动注册社交用户。
     *
     * @param authUser 认证用户信息。
     * @param currentLoginUserId 当前登录用户的ID。
     * @return 返回 YeelightUserDto 用户数据传输对象。
     * @throws BizException 业务异常。
     */
    YeelightUserDto autoRegisterSocialUser(AuthUser authUser, Long currentLoginUserId) throws BizException;

}
