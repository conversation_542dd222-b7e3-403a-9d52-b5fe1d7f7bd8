/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service
 * Description: Jwt登录方式
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-25 10:59:10:59
 */
package com.yeelight.service.user.server.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.domain.LoginSessionInfo;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.server.vo.JwtAuthGrantInfoVo;

import jakarta.servlet.http.HttpServletRequest;

/**
 * Desc: Jwt登录方式
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-25 10:59:10:59
 */
public interface JwtAuthService {
    /**
     * 签名生成
     *
     * @param vendor   供应商
     * @param username  用户名
     * @param sessionId 会话ID
     * @return token 令牌
     */
    String generateToken(String vendor, String username, String sessionId);

    /**
     * 验证token
     *
     * @param vendor 供应商
     * @param token  token
     * @return sessionID 会话ID
     * @throws BizException 业务异常
     */
    String validateToken(String vendor, String token) throws BizException;


    /**
     * 通过token获取登录信息
     *
     * @param vendor 供应商
     * @param token token
     * @return 登录信息
     * @throws BizException 业务异常
     */
    LoginSessionInfo getLoginSessionByToken(String vendor, String token) throws BizException;

    /**
     * 签名查询
     * @param request 请求
     * @return token 令牌
     */
    String getToken(HttpServletRequest request);

    /**
     * 签名查询
     * @param request 请求
     * @return token 令牌
     * @throws BizException 业务异常
     */
    String getTokenThrowException(HttpServletRequest request) throws BizException;

    /**
     * 通过token获取用户信息
     *
     * @param vendor 供应商
     * @param token  token
     * @return 用户信息
     * @throws BizException 业务异常
     */
    YeelightUserDto getUserInfoByToken(String vendor, String token) throws BizException;

    /**
     * 根据token获取用户
     * 如果SecurityContextHolder中存在有效的认证信息且认证主体是YeelightUser类型，则直接从认证主体中获取用户信息。
     * 否则，通过调用jwtAuthService的getUserInfoByToken方法，使用token从外部认证源获取用户信息。
     *
     * @param vendor 供应商
     * @param token  用户的token，用于认证和获取用户信息。
     * @return yeelightUserDto 用户
     * @throws BizException 如果用户信息获取失败或用户不存在时抛出。
     */
    YeelightUserDto getCurrentUserInfoByToken(String vendor, String token) throws BizException;

    /**
     * 根据授权确认相关参数获取授权确认与未确认的scope相关参数
     *
     * @param vendor   供应商
     * @param token    token
     * @param clientId 客户端id
     * @param scope    scope权限
     * @return 授权页面所需数据
     * @throws BizException 业务异常
     */
    JwtAuthGrantInfoVo getAuthGrantInfoByToken(String vendor, String token, String clientId, String scope) throws BizException;

    /**
     * 登出
     *
     * @param vendor 供应商
     * @param token  token
     * @throws BizException 业务异常
     */
    void logout(String vendor, String token) throws BizException;

    /**
     * 为第三方流程缓存jwt token
     *
     * @param state   state
     * @param request 请求
     */
    void cacheTokenForThirdParty(String state, HttpServletRequest request);
}
