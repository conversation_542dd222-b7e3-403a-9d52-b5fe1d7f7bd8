package com.yeelight.service.user.server.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.server.request.AuthCodeRequest;

import java.util.Map;

/**
 * 快捷操作oauth数据的服务
 * <AUTHOR>
 */
public interface OauthKnifeService {
    /**
     * 生成授权码
     * 根据授权码请求对象生成一个授权码字符串
     *
     * @param request 授权码请求对象，包含生成授权码所需的信息
     * @return 生成的授权码字符串
     * @throws BizException 如果无法生成授权码，则抛出业务异常
     */
    String generateAuthCode(AuthCodeRequest request) throws BizException;

    /**
     * 快速创建访问令牌
     * 根据客户端ID、用户信息和扩展参数快速创建OAuth2访问令牌
     *
     * @param clientId 客户端ID，标识请求访问令牌的应用
     * @param yeelightUser 用户信息对象，包含用户相关的信息
     * @param extendParameters 扩展参数，包含额外的参数信息
     * @return 创建的OAuth2访问令牌
     * @throws BizException 如果无法创建访问令牌，则抛出业务异常
     */
    OAuth2Token fastCreateAccessToken(String clientId, YeelightUser yeelightUser, Map<String, String> extendParameters) throws BizException;
}
