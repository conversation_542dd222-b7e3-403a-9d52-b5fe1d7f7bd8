package com.yeelight.service.user.server.service;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.server.request.UpdatePasswordRequest;
import com.yeelight.service.user.server.request.UploadUserAvatarRequest;
import com.yeelight.service.user.server.request.UpdateUserNameRequest;

/**
 * @description: 个人信息管理服务
 * <AUTHOR>
 */
@Deprecated
public interface PersonalInfoManageService {
    /**
     * 更新用户用户名
     *
     * @param request 用户请求对象，包含更新用户名所需的信息
     * @throws BizException 如果更新过程中发生业务逻辑相关的异常，则抛出此异常
     */
    void updateUsername(UpdateUserNameRequest request) throws BizException;

    /**
     * 上传用户头像
     *
     * @param uploadOauthAvatar 包含用户OAuth信息和头像数据的对象
     * @param id 用户ID，指定需要更新头像的用户
     * @return 返回更新后的用户头像URL
     * @throws BizException 如果上传过程中发生业务逻辑相关的异常，则抛出此异常
     */
    String uploadAvatar(UploadUserAvatarRequest uploadOauthAvatar, Long id) throws BizException;

    /**
     * 修改用户密码
     *
     * @param updatePassword 包含用户ID和新旧密码的对象，用于验证和更新密码
     * @throws BizException 如果修改过程中发生业务逻辑相关的异常，则抛出此异常
     */
    void changePassword(UpdatePasswordRequest updatePassword) throws BizException;

    /**
     * 查询用户详细信息
     *
     * @param id 用户ID，指定需要查询的用户
     * @return 返回包含用户详细信息的YeelightUserDto对象
     */
    YeelightUserDto userDetail(Long id);

    /**
     * 更新用户姓名
     *
     * @param name 新的用户姓名
     * @param id 用户ID，指定需要更新姓名的用户
     * @return 返回一个Result对象，表示更新操作的结果
     * @throws BizException 如果更新过程中发生业务逻辑相关的异常，则抛出此异常
     */
    Result<?> updateName(String name, Long id) throws BizException;

    /**
     * 检查用户旧密码是否正确
     *
     * @param oldPassword 用户的旧密码
     * @param id 用户ID，指定需要验证密码的用户
     * @return 返回一个布尔值，表示旧密码是否正确
     * @throws BizException 如果检查过程中发生业务逻辑相关的异常，则抛出此异常
     */
    boolean checkOldPasswordIsCorrect(String oldPassword, Long id) throws BizException;
}
