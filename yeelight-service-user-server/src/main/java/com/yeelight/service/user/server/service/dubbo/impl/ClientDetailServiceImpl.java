package com.yeelight.service.user.server.service.dubbo.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.user.client.domain.ClientDetail;
import com.yeelight.service.user.client.query.ClientDetailQuery;
import com.yeelight.service.user.client.request.ClientDetailRequest;
import com.yeelight.service.user.client.service.ClientDetailService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.mapper.oauth.ClientDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Method;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @description: 客户端信息服务实现类
 * <AUTHOR>
 **/
@Slf4j
@DubboService(timeout = 5000, methods = {@Method(name = "getClient", timeout = 1000)})
public class ClientDetailServiceImpl implements ClientDetailService {

    @Resource
    private ClientDetailMapper mapper;

    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public PageResultSet<ClientDetail> pageClientDetail(ClientDetailQuery query) {
        Page<ClientDetail> page = PageHelper.startPage(query.getPageNo(), query.getPageSize());
        PageResultSet<ClientDetail> pageResultSet = new PageResultSet<>();
        List<ClientDetail> clientDetails = mapper.selectByExample(exportExample(query));
        pageResultSet.setTotal(page.getTotal());
        pageResultSet.setRows(clientDetails);
        return pageResultSet;
    }

    private Weekend<ClientDetail> exportExample(ClientDetailQuery query) {
        Weekend<ClientDetail> weekend = Weekend.of(ClientDetail.class);
        WeekendCriteria<ClientDetail, Object> criteria = weekend.weekendCriteria();
        if (null == query) {
            return weekend;
        }
        if (StringUtils.isNotBlank(query.getClientId())) {
            criteria.andEqualTo(ClientDetail::getClientId, query.getClientId());
        }
        return weekend;
    }

    @Override
    public ClientDetail getClient(String clientId) {
        return mapper.selectByPrimaryKey(clientId);
    }

    @Override
    public Void addClient(ClientDetailRequest request) throws BizException {
        ClientDetail checkClientDetail = mapper.selectByPrimaryKey(request.getClientId());
        if (null != checkClientDetail) {
            throw new BizException("客户端ID已存在！");
        }
        try {
            ClientDetail clientDetail = new ClientDetail();
            org.springframework.beans.BeanUtils.copyProperties(request, clientDetail);
            clientDetail.setScope("read,write");
            clientDetail.setAuthorities("ROLE_USER,ROLE_CLIENT");
            clientDetail.setClientSecret(StringUtils.isNotBlank(request.getSecret()) ? passwordEncoder.encode(request.getSecret()) : null);
            mapper.insertSelective(clientDetail);
            BizOperateLogUtils.sendAddBizOperateLog(request.getClientId(), BizTypeEnums.OAUTH应用.getCode(), "添加客户端", clientDetail);
        } catch (Exception e) {
            log.error("add Client error:{}", e.getMessage());
            throw new BizException("add client error: " + e.getMessage());
        }
        return null;
    }

    @Override
    public Void updateClient(ClientDetailRequest request) throws BizException {
        checkClientDetailIsExist(request.getClientId());
        try {
            ClientDetail clientDetail = new ClientDetail();
            org.springframework.beans.BeanUtils.copyProperties(request, clientDetail);
            clientDetail.setClientSecret(StringUtils.isNotBlank(request.getSecret()) ? passwordEncoder.encode(request.getSecret()) : null);
            mapper.updateByPrimaryKeySelective(clientDetail);
            BizOperateLogUtils.sendUpdateBizOperateLog(request.getClientId(), BizTypeEnums.OAUTH应用.getCode(), "更新客户端", request);
        } catch (Exception e) {
            log.error("update Client error:{}", e.getMessage());
            throw new BizException("update client error: " + e.getMessage());
        }
        return null;
    }

    @Override
    public Void resetSecret(String clientId, String clientSecret) {
        checkClientDetailIsExist(clientId);
        try {
            ClientDetail clientDetail = new ClientDetail();
            clientDetail.setClientId(clientId);
            clientDetail.setClientSecret(StringUtils.isNotBlank(clientSecret) ? passwordEncoder.encode(clientSecret) : null);
            mapper.updateByPrimaryKeySelective(clientDetail);
            BizOperateLogUtils.sendUpdateBizOperateLog(clientId, BizTypeEnums.OAUTH应用.getCode(), "重置密钥", clientSecret);
        } catch (Exception e) {
            log.error("reset secret error:{}", e.getMessage());
            throw new BizException("reset secret error: " + e.getMessage());
        }
        return null;
    }

    @Override
    public int removeClinet(String clientId) throws BizException {
        checkClientDetailIsExist(clientId);
        try {
            BizOperateLogUtils.sendRemoveBizOperateLog(clientId, BizTypeEnums.OAUTH应用.getCode(), "删除应用", clientId);
            return mapper.deleteByPrimaryKey(clientId);
        } catch (Exception e) {
            log.error("deleteClinet error:{}", e.getMessage());
            throw new BizException("delete client error: " + e.getMessage());
        }
    }

    /**
     * 校验客户端ID和客户端秘钥是否匹配
     *
     * @param clientId     客户端ID
     * @param clientSecret 客户端秘钥
     * @return 是否匹配
     */
    @Override
    public boolean validateClientSecret(String clientId, String clientSecret) {
        ClientDetail client = getClient(clientId);
        return client.getClientSecret().equals(passwordEncoder.encode(clientSecret));
    }

    private void checkClientDetailIsExist(String clientId) {
        ClientDetail checkClientDetail = mapper.selectByPrimaryKey(clientId);
        if (null == checkClientDetail) {
            throw new BizException("客户端不存在！");
        }
    }

}
