package com.yeelight.service.user.server.service.dubbo.impl;

import cn.hutool.http.useragent.*;
import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.enums.RegionEnum;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.constant.UserConstant;
import com.yeelight.service.user.client.domain.UserMonitoringProperty;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.domain.LoginTokenInfo;
import com.yeelight.service.user.client.dto.CheckedToken;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.constant.TokenConstants;
import com.yeelight.service.user.server.custom.NewCustomRedisAuthorizationService;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.TokenUtils;
import com.yeelight.service.user.server.utils.UserAgentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Method;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2TokenType;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新版Token服务实现
 * 基于Spring Authorization Server实现
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@DubboService(timeout = 5000, methods = {@Method(name = "getAuthenticationByToken", timeout = 2000)})
public class NewTokenServiceImpl implements TokenService {

    @Resource
    private NewCustomRedisAuthorizationService authorizationService;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    /**
     * 获取指定用户的所有登录令牌信息。
     *
     * @param username 用户名，用于查找该用户相关的访问令牌。
     * @return 返回一个登录令牌信息列表。每个登录令牌信息包含令牌的基本信息和认证详情。
     */
    @Override
    public List<LoginTokenInfo> getTokenInfos(String username) {
        // 根据用户名查找所有的OAuth2访问令牌
        List<YeelightOAuth2AccessToken> auth2AccessTokens = findTokensByUserName(username);
        return auth2AccessTokens.stream().filter(Objects::nonNull).map(auth2AccessToken -> {
            // 根据访问令牌值获取认证信息
            OAuth2AuthenticationDto authenticationDto = getAuthenticationByToken(auth2AccessToken.getValue());
            // 对认证信息进行安全处理
            TokenUtils.safeOauth2AuthenticationDto(authenticationDto);
            // 根据访问令牌和认证信息生成登录令牌信息
            return generateLoginTokenInfo(auth2AccessToken, authenticationDto);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取指定用户和供应商的登录令牌信息列表。
     *
     * @param username 用户名，用于查找与之相关的访问令牌。
     * @param vendor 供应商名称，用于过滤出指定供应商的令牌信息。
     * @return 返回一个登录令牌信息列表，仅包含指定用户和供应商的令牌信息。
     */
    @Override
    public List<LoginTokenInfo> getTokenInfos(String username, String vendor) {
        // 根据用户名查找所有的OAuth2访问令牌
        List<YeelightOAuth2AccessToken> auth2AccessTokens = findTokensByUserName(username);

        // 流式处理所有找到的访问令牌
        return auth2AccessTokens.stream().filter(Objects::nonNull).map(auth2AccessToken -> {
            // 根据令牌值获取认证信息
            OAuth2AuthenticationDto authenticationDto = getAuthenticationByToken(auth2AccessToken.getValue());

            // 如果认证信息或请求参数不存在，则跳过当前令牌处理
            if (Objects.isNull(authenticationDto) || Objects.isNull(authenticationDto.getStoredRequest()) || Objects.isNull(authenticationDto.getStoredRequest().getRequestParameters())) {
                return null;
            }
            // 对认证信息进行安全处理
            TokenUtils.safeOauth2AuthenticationDto(authenticationDto);

            // 获取令牌关联的供应商名称
            final String vendorName = authenticationDto.getStoredRequest().getRequestParameters().getOrDefault(TokenConstants.ADDITIONAL_INFORMATION_VENDOR, UserVendorEnum.DEFAULT.getCode());

            // 如果令牌关联的供应商与指定供应商不匹配，则跳过当前令牌处理
            if (!vendorName.equalsIgnoreCase(vendor)) {
                return null;
            }

            // 生成并返回登录令牌信息
            return generateLoginTokenInfo(auth2AccessToken, authenticationDto);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Result<CheckedToken> checkToken(String token) {
        Result<CheckedToken> result = new Result<>();
        result.setSuccess(false);
        // 检查令牌字符串是否为空
        if (StringUtils.isBlank(token)) {
            result.setMsg("Token was blank");
            result.setErrorMsg("Token was blank");
            return result;
        }
        // 从令牌存储中读取令牌
        YeelightOAuth2AccessToken oAuth2AccessToken = authorizationService.findAccessTokenByValue(token);
        // 检查读取的令牌是否为空，或者是否没有额外信息
        if (null == oAuth2AccessToken || null == oAuth2AccessToken.getAdditionalInformation()) {
            result.setMsg("Token was not recognised");
            result.setErrorMsg("Token was not recognised");
            return result;
        }
        // 检查令牌是否已过期
        if (oAuth2AccessToken.isExpired()) {
            result.setSuccess(false);
            result.setMsg("Token has expired");
            result.setErrorMsg("Token has expired");
            return result;
        }
        // 如果令牌通过所有检查，则创建CheckedToken对象并填充详细信息
        CheckedToken checkedToken = new CheckedToken();
        Map<String, Object> additionalInformation = oAuth2AccessToken.getAdditionalInformation();
        String tokenRegion;
        // 确定令牌所属的地区
        Object region = additionalInformation.get(TokenConstants.ADDITIONAL_INFORMATION_REGION);
        if (region instanceof String) {
            tokenRegion = (String) region;
        } else if (region instanceof RegionEnum) {
            tokenRegion = ((RegionEnum) region).getCode();
        } else {
            tokenRegion = RegionEnum.CN.getCode();
        }
        // 填充CheckedToken对象
        checkedToken.setId(Long.parseLong(additionalInformation.get(TokenConstants.ADDITIONAL_INFORMATION_ID).toString()));
        checkedToken.setUsername(additionalInformation.get(TokenConstants.ADDITIONAL_INFORMATION_USERNAME).toString());
        checkedToken.setRegion(tokenRegion);
        checkedToken.setDevice(additionalInformation.get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE).toString());
        result.setSuccess(true);
        result.setData(checkedToken);
        return result;
    }

    /**
     * 校验令牌（token）的有效性。
     *
     * @param token 需要校验的令牌字符串。不可为空或空白字符串。
     * @throws BizException 如果token为空，或在系统中不存在，或已过期，则抛出业务异常。
     */
    @Override
    public void checkValidToken(String token) throws BizException {
        // 确保token不为空或空白字符串
        Assert.notBlank(token, "token not allow blank");
        // 从token存储中读取相应的OAuth2访问令牌
        YeelightOAuth2AccessToken oAuth2AccessToken = authorizationService.findAccessTokenByValue(token);
        // 确保读取到的令牌对象不为空
        Assert.notNull(oAuth2AccessToken, "token not exist");
        // 检查令牌是否已过期
        Assert.isTrue(!oAuth2AccessToken.isExpired(), "token has expired");
    }

    /**
     * 使用token进行登录验证。
     *
     * @param token 用户的认证token，用于验证用户身份。
     * @param request 用户的请求对象，用于获取和设置会话信息。
     * @return boolean 登录成功返回true，失败返回false。
     */
    @Override
    public boolean loginByToken(String token, HttpServletRequest request) {
        // 检查token是否为空
        if (StringUtils.isBlank(token)) {
            return false;
        }
        // 通过token从tokenStore中读取认证信息
        OAuth2AuthenticationDto authenticationDto = getAuthenticationByToken(token);
        // 检查读取到的认证信息及其用户认证信息是否为空
        if (Objects.isNull(authenticationDto) || Objects.isNull(authenticationDto.getUserAuthentication())) {
            return false;
        }
        // 设置安全上下文
        SecurityContext context = SecurityContextHolder.getContext();
        context.setAuthentication(authenticationDto.getUserAuthentication());
        // 将安全上下文存储到会话中
        request.getSession().setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, context);
        // 发送业务操作日志
        BizOperateLogUtils.sendSimpleBizOperateLog(authenticationDto.getName(), BizTypeEnums.OAUTH.getCode(), "使用token登录", token);
        return true;
    }

    @Override
    public boolean expireAccessToken(String token) {
        // 检查令牌是否为空
        if (StringUtils.isBlank(token)) {
            return false;
        }
        try {
            // 从tokenStore中读取对应的认证信息
            OAuth2AuthenticationDto authentication = getAuthenticationByToken(token);
            // 如果认证信息或用户认证为空，则认为令牌无效
            if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
                return false;
            }
            // 通过删除授权来实现token过期
            OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
            if (authorization != null) {
                authorizationService.remove(authorization);
            }
            // 发送操作日志
            BizOperateLogUtils.sendRemoveBizOperateLog(authentication.getName(), BizTypeEnums.OAUTH.getCode(), "设置token过期", token);
            log.info("设置token 过期:{}", token);
            return true;
        } catch (Exception e) {
            // 记录异常信息，并返回失败
            log.error("设置token 过期异常,{}", ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 设置访问令牌的过期时间。
     * warning: 仅支持立即过期（seconds=0），自定义时间由于新版限制暂不支持
     * @param token 需要设置过期时间的访问令牌字符串。如果为空或blank，则方法直接返回false。
     * @param seconds 令牌的过期时间，单位为秒。一旦设置，令牌将在该时间后失效。
     * @return 如果令牌成功设置为过期，返回true；如果因为任何原因（包括令牌不存在、用户认证信息缺失等）设置失败，则返回false。
     */
    @Override
    public boolean expireAccessTokenTime(String token, int seconds) {
        // 检查token是否为空或blank
        if (StringUtils.isBlank(token)) {
            return false;
        }

        // 从tokenStore中读取对应的访问令牌和认证信息
        OAuth2AuthenticationDto authentication = getAuthenticationByToken(token);

        // 检查认证信息是否存在且包含用户认证信息
        if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
            return false;
        }

        try {
            // 在新版中，如果seconds为0，立即过期（删除token）
            if (seconds == 0) {
                OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
                if (authorization != null) {
                    authorizationService.remove(authorization);
                }
            } else {
                // 对于非零的过期时间，新版实现较为复杂，暂时不支持
                log.warn("Setting custom expire time ({} seconds) is not fully supported in new version", seconds);
                return false;
            }

            // 记录操作日志
            BizOperateLogUtils.sendRemoveBizOperateLog(authentication.getName(), BizTypeEnums.OAUTH.getCode(), "设置token过期时间", token);
            log.info("设置token过期时间:{}", token);
            return true;
        } catch (Exception e) {
            // 记录异常信息
            log.error("设置token过期时间异常,{}", ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 根据用户名撤销用户的令牌。
     * 这个方法会尝试调用Token存储机制中的revokeTokenByUserName方法，来使指定用户的令牌失效。
     *
     * @param username 要撤销令牌的用户名。如果用户名为空或blank，则方法直接返回false。
     * @return boolean 如果撤销操作成功执行，则返回true；如果遇到异常或用户名为空，则返回false。
     */
    @Override
    public boolean revokeTokenByUserName(String username) {
        // 验证用户名是否为空
        if (StringUtils.isBlank(username)) {
            return false;
        }
        try {
            // 通过自定义方法撤销指定用户的所有令牌
            List<YeelightOAuth2AccessToken> tokens = findTokensByUserName(username);
            for (YeelightOAuth2AccessToken token : tokens) {
                OAuth2Authorization authorization = authorizationService.findByToken(token.getValue(), OAuth2TokenType.ACCESS_TOKEN);
                if (authorization != null) {
                    authorizationService.remove(authorization);
                }
            }
            log.info("设置用户[{}]下token过期", username);

            // 发送业务操作日志，记录撤销令牌的操作
            BizOperateLogUtils.sendRemoveBizOperateLog(username, BizTypeEnums.OAUTH.getCode(), "根据用户名登出用户下所有token", username);
            return true;
        } catch (Exception e) {
            // 捕获异常，并记录日志
            log.error("设置用户[{}]下token过期异常,{}", username, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 登出指定的访问令牌。
     *
     * @param token 需要登出的访问令牌字符串。如果为空或blank，则直接返回false。
     * @return 如果登出操作成功执行，则返回true；如果令牌不存在或任何异常发生，则返回false。
     */
    @Override
    public boolean logoutCurrentToken(String token) {
        // 检查token是否为空或blank
        if (StringUtils.isBlank(token)) {
            return false;
        }
        try {
            // 尝试从authorizationService读取对应的访问令牌
            YeelightOAuth2AccessToken oAuth2AccessToken = authorizationService.findAccessTokenByValue(token);
            if (oAuth2AccessToken == null) {
                // 如果令牌不存在，记录日志并返回false
                log.info("登出Token操作时, Token {} 不存在", token);
                return false;
            } else {
                // 令牌存在时，移除对应的授权信息
                OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
                if (authorization != null) {
                    authorizationService.remove(authorization);
                }
                // 发送操作日志，记录登出操作
                BizOperateLogUtils.sendRemoveBizOperateLog(oAuth2AccessToken.getAdditionalInformation().getOrDefault(TokenConstants.ADDITIONAL_INFORMATION_USERNAME, token), BizTypeEnums.OAUTH.getCode(), "登出指定token", token);
                log.info("登出Token:{}成功, OAuth2AccessToken:{}", token, oAuth2AccessToken);
            }
            return true;
        } catch (Exception e) {
            // 捕获异常，记录日志，并返回false
            log.error("登出Token[{}], 异常:{}", token, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 登出当前应用。
     * 该方法通过提供的token来查找对应的认证信息，如果找到，就针对该用户和客户端ID撤销令牌。
     * 成功撤销令牌后，会记录相关操作日志。
     *
     * @param token 用户的访问令牌，用于登出应用。
     * @return boolean 返回登出操作是否成功。成功返回true，失败返回false。
     */
    @Override
    public boolean logoutCurrentApplication(String token) {
        // 检查token是否为空
        if (StringUtils.isBlank(token)) {
            return false;
        }
        try {
            // 通过token读取认证信息
            OAuth2AuthenticationDto authentication = getAuthenticationByToken(token);
            // 检查认证信息和用户认证信息是否为空
            if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
                log.warn("登出应用时, 获取不到token[{}]对应的用户信息", token);
                return false;
            }
            // 获取用户名称和客户端ID
            String userName = authentication.getUserAuthentication().getName();
            String clientId = authentication.getStoredRequest().getClientId();
            // 撤销指定客户端ID和用户名下的所有令牌
            List<YeelightOAuth2AccessToken> tokens = findTokensByUserName(userName);
            for (YeelightOAuth2AccessToken accessToken : tokens) {
                OAuth2Authorization authorization = authorizationService.findByToken(accessToken.getValue(), OAuth2TokenType.ACCESS_TOKEN);
                if (authorization != null && clientId.equals(authorization.getRegisteredClientId())) {
                    authorizationService.remove(authorization);
                }
            }
            // 发送操作日志
            BizOperateLogUtils.sendRemoveBizOperateLog(userName, BizTypeEnums.OAUTH.getCode(), "登出应用下所有用户", token);
            log.info("登出应用成功, token:{}, OAuth2Authentication:{}", token, authentication);
            return true;
        } catch (Exception e) {
            // 捕获异常，记录错误信息
            log.error("登出应用失败, token:[{}], errMsg:{}", token, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 登出当前用户。
     * 通过传入的token来识别用户，并注销该用户的所有认证token。
     *
     * @param token 用户的认证token，用于识别用户。
     * @return boolean 返回操作是否成功。成功返回true，失败返回false。
     */
    @Override
    public boolean logoutCurrentUser(String token) {
        // 如果token为空或blank，则直接返回失败
        if (StringUtils.isBlank(token)) {
            return false;
        }
        try {
            // 通过token读取认证信息
            OAuth2AuthenticationDto authentication = getAuthenticationByToken(token);
            // 如果读取的认证信息或用户认证为空，则返回失败
            if (Objects.isNull(authentication) || Objects.isNull(authentication.getUserAuthentication())) {
                log.error("登出用户token时, 获取不到token[{}]对应的用户信息", token);
                return false;
            }
            // 获取用户名
            String userName = authentication.getUserAuthentication().getName();
            // 根据用户名撤销所有token
            revokeTokenByUserName(userName);
            // 发送操作日志
            BizOperateLogUtils.sendRemoveBizOperateLog(userName, BizTypeEnums.OAUTH.getCode(), "根据token登出对应用户下所有token", token);
            log.info("登出用户token成功, token[{}], OAuth2Authentication:{}", token, authentication);
            return true;
        } catch (Exception e) {
            // 捕获异常并记录日志
            log.error("登出用户token失败, token:[{}], errMsg:{}", token, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 设置用户监控属性。
     * 该方法用于根据传入的用户监控属性对象来配置或删除用户的监控信息。
     * 如果传入的监控属性对象或用户ID为空，则不进行任何操作。
     * 对于非空的监控属性对象，根据其remove属性来决定是删除监控配置还是设置监控配置。
     * 如果remove属性为true，则删除对应的监控配置；否则，配置监控属性，包括每分钟限制次数。
     *
     * @param monitoringProperty 用户监控属性对象，包含用户ID、是否移除监控、每分钟限制次数等属性。
     * @throws BizException 业务异常，如果操作Redis过程中发生错误则可能抛出此异常。
     */
    @Override
    public void setUserMonitoring(UserMonitoringProperty monitoringProperty) throws BizException {
        // 检查monitoringProperty或其userId是否为空，若为空则直接返回
        if (Objects.isNull(monitoringProperty) || Objects.isNull(monitoringProperty.getUserId())) {
            return;
        }
        // 构造Redis中监控配置的键名
        String key = UserConstant.BLACK_USER_MONITORING + monitoringProperty.getUserId();
        // 判断是否需要删除监控配置
        if (Objects.nonNull(monitoringProperty.getRemove()) && monitoringProperty.getRemove()) {
            // 删除监控配置
            authorizationService.getRedisManager().redisTemplate().delete(key);
        } else {
            // 配置监控配置
            if (Objects.isNull(monitoringProperty.getLimitPerMinute())) {
                // 如果未设置每分钟限制次数，则默认为0
                monitoringProperty = UserMonitoringProperty.builder().limitPerMinute(0L).build();
            }
            // 更新监控属性的最后设置时间，并将监控属性对象转换为JSON字符串后存储到Redis中
            monitoringProperty.setLastSetTime(System.currentTimeMillis() / 1000);
            authorizationService.getRedisManager().redisTemplate().opsForValue().set(key, JSON.toJSONString(monitoringProperty));
        }
    }

    /**
     * 获取所有用户监控属性列表。
     * 该方法从Redis中检索以特定前缀开头的键值对，并将它们转换为UserMonitoringProperty对象的列表。
     * 注意：此方法不接受任何参数，而是从Redis数据库中直接获取数据。
     *
     * @return List<UserMonitoringProperty> 用户监控属性列表。如果不存在匹配的监控属性，则返回空列表。
     */
    @Override
    public List<UserMonitoringProperty> getAllUserMonitoring() {
        // 初始化存储键名的列表
        List<String> keys = new ArrayList<>();
        // 使用Redis的SCAN命令以匹配特定前缀的键，分批获取最多100个键
        Cursor<byte[]> cursor = Objects.requireNonNull(authorizationService.getRedisManager().redisTemplate().getConnectionFactory()).getConnection().keyCommands().scan(ScanOptions.scanOptions().match(UserConstant.BLACK_USER_MONITORING + "*").count(100).build());
        while (cursor.hasNext()) {
            // 将键名从字节数组转换为字符串，并添加到keys列表中
            keys.add(new String(cursor.next()));
        }
        // 如果没有找到匹配的键，则直接返回空列表
        if (keys.isEmpty()) {
            return new ArrayList<>();
        }

        // 从Redis中批量获取键对应的值
        List<Object> objectList = authorizationService.getRedisManager().redisTemplate().opsForValue().multiGet(keys);
        List<String> userMonitoringProperties = null;
        if (objectList != null) {
            userMonitoringProperties = objectList.stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());
        }
        // 如果获取的值为空，则返回空列表
        if (CollectionUtils.isEmpty(userMonitoringProperties)) {
            return new ArrayList<>();
        }

        // 过滤非空的值，将其转换为UserMonitoringProperty对象，按最后一次设置时间降序排序后返回
        return userMonitoringProperties.stream()
                // 过滤掉空字符串
                .filter(StringUtils::isNotBlank)
                // 将字符串转换为对象
                .map(userMonitoringProperty -> JSON.parseObject(userMonitoringProperty, UserMonitoringProperty.class))
                // 过滤掉转换失败的null对象
                .filter(Objects::nonNull)
                // 按最后一次设置时间降序排序
                .sorted(Comparator.comparingLong(UserMonitoringProperty::getLastSetTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取用户监控属性。
     *
     * @param userId 用户的ID，不能为空。
     * @return 如果找到对应的用户监控属性，则返回UserMonitoringProperty对象；如果未找到或参数为空，则返回null。
     */
    @Override
    public UserMonitoringProperty getUserMonitoring(Long userId) {
        // 检查用户ID是否为空
        if (Objects.isNull(userId)) {
            return null;
        }
        // 构造Redis中存储的键名
        String key = UserConstant.BLACK_USER_MONITORING + userId;
        // 从Redis中获取用户监控属性的JSON字符串
        String userMonitoringProperty = (String) authorizationService.getRedisManager().redisTemplate().opsForValue().get(key);
        // 如果Redis中没有存储该用户的监控属性，则返回null
        if (StringUtils.isBlank(userMonitoringProperty)) {
            return null;
        }
        // 将JSON字符串反序列化为UserMonitoringProperty对象并返回
        return JSON.parseObject(userMonitoringProperty, UserMonitoringProperty.class);
    }

    /**
     * 根据令牌获取OAuth2认证信息的DTO（数据传输对象）。
     *
     * @param token 用户的访问令牌，用于查询认证信息。
     * @return 如果令牌有效且存在对应的认证信息，则返回相应的OAuth2AuthenticationDto对象；否则返回null。
     */
    @Override
    public OAuth2AuthenticationDto getAuthenticationByToken(String token) {
        // 检查令牌是否为空
        if (StringUtils.isBlank(token)) {
            return null;
        }
        // 从令牌存储中读取认证信息
        OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
        // 将认证信息转换为DTO格式
        OAuth2AuthenticationDto authenticationDto = TokenUtils.authorizationToDto(authorization);
        // 移除DTO中的用户认证信息，通常是为了保护敏感信息
        TokenUtils.authenticationWithoutUserAuthenticationDto(authenticationDto);
        return authenticationDto;
    }

    /**
     * 通过token获取用户名。
     *
     * @param token 用户的认证token，用于识别用户身份。
     * @return 返回用户名，如果token为空、无效或者无法找到对应的用户认证信息，则返回null。
     */
    @Override
    public String getUserNameByToken(String token) {
        // 检查token是否为空或空白
        if (StringUtils.isBlank(token)) {
            return null;
        }
        // 从token存储中读取对应的OAuth2认证信息
        OAuth2Authorization authorization = authorizationService.findByToken(token, OAuth2TokenType.ACCESS_TOKEN);
        // 检查认证信息是否为空，或者用户认证信息是否为空
        if (Objects.isNull(authorization) || Objects.isNull(authorization.getPrincipalName())) {
            return null;
        }
        // 返回用户的用户名
        return authorization.getPrincipalName();
    }

    /**
     * 通过用户名查找所有的OAuth2访问令牌。
     *
     * @param userName 需要查找访问令牌的用户名，不能为空或blank。
     * @return 返回与用户名相关联的所有OAuth2AccessToken列表。如果没有找到任何令牌，将返回一个空列表。
     */
    @Override
    public List<YeelightOAuth2AccessToken> findTokensByUserName(String userName) {
        List<YeelightOAuth2AccessToken> tokens = new ArrayList<>();
        // 如果用户名为空或blank，直接返回空的token列表
        if (StringUtils.isBlank(userName)) {
            return tokens;
        }
        try {
            // 通过自定义方法查找用户的所有访问令牌
            Collection<YeelightOAuth2AccessToken> auth2AccessTokens = authorizationService.findTokensByPrincipalName(userName);
            // 如果没有找到任何令牌，直接返回空的token列表
            if (CollectionUtils.isEmpty(auth2AccessTokens)) {
                return tokens;
            }
            // 过滤掉null值，并收集到tokens列表中，然后反转列表顺序
            tokens = auth2AccessTokens.stream().filter(Objects::nonNull).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            Collections.reverse(tokens);
            return tokens;
        } catch (Exception e) {
            // 记录调用异常信息
            log.error("获取用户[{}]下所有token异常,{}", userName, ExceptionUtils.getStackTrace(e));
        }
        // 如果出现异常或其他情况，确保返回一个空的token列表
        return new ArrayList<>();
    }

    /**
     * 根据Yeelight用户ID模拟认证流程。
     *
     * @param clientId 应用的唯一标识符，不允许为空。
     * @param yeelightId Yeelight用户的ID，不能为空。
     * @param extendParameters 扩展参数，可选，用于认证过程中的额外信息传递。
     * @return OAuth2Token 认证成功后返回的OAuth2令牌。
     * @throws BizException 如果用户不存在或者认证过程出错，则抛出业务异常。
     */
    @Override
    public OAuth2Token mockAuthenticationByYeelightId(String clientId, Long yeelightId, Map<String, String> extendParameters) throws BizException {
        // 校验clientId不为空
        Assert.notBlank(clientId, "应用ID不允许为空");
        // 校验yeelightId不为空，并通过国际化工具获取用户不存在的错误信息
        Assert.notNull(yeelightId, I18nUtil.message("User.Exception.user.notExist", yeelightId));

        // 根据yeelightId查询用户信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserById(yeelightId);
        // 确保查询到的用户信息不为空
        Assert.notNull(yeelightUserDto, I18nUtil.message("User.Exception.user.notExist", yeelightId));

        // 发送业务操作日志，记录用户模拟登录的行为
        BizOperateLogUtils.sendSimpleBizOperateLog(yeelightId, BizTypeEnums.OAUTH.getCode(), "根据用户ID模拟登陆", extendParameters);
        // 调用内部方法，完成模拟认证流程，并返回OAuth2令牌
        return mockAuthentication(clientId, yeelightUserDto, extendParameters);
    }

    /**
     * 根据用户名模拟认证流程。
     * <p>此方法用于通过用户名而不是实际的认证流程来获取OAuth2令牌。它首先验证用户名和应用ID的有效性，然后查询用户信息，
     * 并记录相关操作日志，最后调用内部方法完成模拟认证。</p>
     *
     * @param clientId 应用的唯一标识符，不允许为空。
     * @param username 用户的用户名，不允许为空。
     * @param extendParameters 扩展参数映射，可用于传递额外的信息，可选。
     * @return OAuth2Token 认证成功后返回的OAuth2令牌对象。
     * @throws BizException 如果用户名不存在或用户信息查询失败，则抛出业务异常。
     */
    @Override
    public OAuth2Token mockAuthenticationByUserName(String clientId, String username, Map<String, String> extendParameters) throws BizException {
        // 确保clientId和username不为空
        Assert.notBlank(clientId, "应用ID不允许为空");
        Assert.notBlank(username, I18nUtil.message("User.Exception.user.notExist", username));

        // 根据用户名查询用户信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByUsername(username);
        // 确保查询到的用户信息不为空
        Assert.notNull(yeelightUserDto, I18nUtil.message("User.Exception.user.notExist", username));

        // 记录操作日志
        BizOperateLogUtils.sendSimpleBizOperateLog(username, BizTypeEnums.OAUTH.getCode(), "根据用户名模拟登陆", extendParameters);
        // 调用内部方法完成模拟认证并返回令牌
        return mockAuthentication(clientId, yeelightUserDto, extendParameters);
    }

    /**
     * 通过授权码方式获取OAuth2Token。
     *
     * @param clientId 应用ID，不允许为空。
     * @param code 授权码，用于获取访问令牌。
     * @param extendParameters 扩展参数，可包含任意额外信息，在令牌创建过程中可能会使用到。
     * @return OAuth2Token 认证成功的OAuth2令牌对象。
     * @throws BizException 业务异常，可能在应用不存在、授权范围验证失败等情况下抛出。
     */
    @Override
    public OAuth2Token authenticationByCode(String clientId, String code, Map<String, String> extendParameters) throws BizException {
        // 确保clientId不为空，并向extendParameters中添加必要的参数
        Assert.notBlank(clientId, "应用ID不允许为空");
        extendParameters.putIfAbsent(OAuth2Utils.CLIENT_ID, clientId);
        extendParameters.putIfAbsent("code", code);
        extendParameters.putIfAbsent(OAuth2Utils.GRANT_TYPE, "authorization_code");
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_VENDOR, UserVendorEnum.DEFAULT.name());
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_REGION, UserVendorEnum.DEFAULT.getRegion().getCode());
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME, String.valueOf(System.currentTimeMillis()));

        try {
            // 在新版Spring Authorization Server中，我们需要通过授权码查找已存在的授权
            // 注意：OAuth2TokenType没有AUTHORIZATION_CODE，我们使用自定义方法
            OAuth2Authorization authorization = authorizationService.findByAuthorizationCode(code);

            if (authorization == null) {
                throw new BizException("Invalid authorization code: " + code);
            }

            // 检查授权码是否已过期（这里简化处理，实际可以检查授权时间）
            // 新版中授权码的过期检查由框架内部处理

            // 创建访问令牌
            YeelightOAuth2AccessToken accessToken = createAccessTokenFromAuthorization(authorization, extendParameters);

            // 将访问令牌转换为OAuth2Token对象
            OAuth2Token oAuth2Token = TokenUtils.accessTokenToAuth2Token(accessToken);

            // 如果转换成功，则记录业务操作日志
            if (Objects.nonNull(oAuth2Token)) {
                BizOperateLogUtils.sendSimpleBizOperateLog(oAuth2Token.getUsername(), BizTypeEnums.OAUTH.getCode(), "根据授权码模拟登陆", extendParameters);
            }

            return oAuth2Token;
        } catch (Exception e) {
            log.error("Error in authentication by code for clientId: {}, code: {}", clientId, code, e);
            throw new BizException("Authentication by code failed: " + e.getMessage());
        }
    }

    /**
     * 模拟认证过程，生成OAuth2令牌。
     *
     * @param clientId 应用客户端ID，用于标识发起认证请求的客户端。
     * @param yeelightUserDto 用户数据传输对象，包含用户详细信息。
     * @param extendParameters 扩展参数映射，可用于传递额外的认证参数。
     * @return OAuth2Token 认证成功返回包含访问令牌的OAuth2Token对象，如果输入的yeelightUserDto为null，则返回null。
     */
    private OAuth2Token mockAuthentication(String clientId, YeelightUserDto yeelightUserDto, Map<String, String> extendParameters) {
        // 如果用户信息为空，则直接返回null
        if (Objects.isNull(yeelightUserDto)) {
            return null;
        }

        // 设置授权类型为密码模式
        String grantType = "password";
        // 将用户DTO转换为实体类
        YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);

        // 向扩展参数中添加必要的认证信息
        extendParameters.putIfAbsent(com.yeelight.service.user.client.utils.OAuth2Utils.CLIENT_ID, clientId);
        extendParameters.putIfAbsent(com.yeelight.service.user.client.utils.OAuth2Utils.GRANT_TYPE, grantType);
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_ID, String.valueOf(yeelightUser.getId()));
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_USERNAME, yeelightUser.getUsername());
        // 根据用户ID确定用户供应商，并添加到扩展参数中
        UserVendorEnum userVendorEnum = Arrays.stream(UserVendorEnum.values())
            .filter(vendor -> vendor.userIdRange().contains(yeelightUserDto.getId()))
            .findFirst()
            .orElse(UserVendorEnum.DEFAULT);
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_VENDOR, userVendorEnum.name());
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_REGION, userVendorEnum.getRegion().getCode());
        // 添加创建时间到扩展参数
        extendParameters.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME, String.valueOf(System.currentTimeMillis()));

        try {
            // 创建用户认证
            UsernamePasswordAuthenticationToken userAuthentication = new UsernamePasswordAuthenticationToken(
                yeelightUser, extendParameters, yeelightUser.getAuthorities());

            // 构建YeelightOAuth2Request
            YeelightOAuth2Request oAuth2Request = YeelightOAuth2Request.builder()
                .clientId(clientId)
                .grantType(grantType)
                .scope(Set.of("read", "write"))
                .approved(true)
                .requestParameters(extendParameters)
                .build();

            // 创建YeelightOAuth2Authentication
            YeelightOAuth2Authentication oAuth2Authentication = YeelightOAuth2Authentication.builder()
                .oAuth2Request(oAuth2Request)
                .userAuthentication(userAuthentication)
                .authenticated(true)
                .build();

            // 创建访问令牌
            YeelightOAuth2AccessToken accessToken = createMockAccessToken(oAuth2Authentication, extendParameters);

            // 将访问令牌转换为OAuth2Token对象
            return TokenUtils.accessTokenToAuth2Token(accessToken);
        } catch (Exception e) {
            log.error("Error in mock authentication for user: {}", yeelightUserDto.getUsername(), e);
            return null;
        }
    }

    /**
     * 创建模拟访问令牌
     *
     * @param oAuth2Authentication OAuth2认证对象
     * @param extendParameters 扩展参数
     * @return YeelightOAuth2AccessToken 访问令牌
     */
    private YeelightOAuth2AccessToken createMockAccessToken(YeelightOAuth2Authentication oAuth2Authentication, Map<String, String> extendParameters) {
        // 生成唯一的token值
        String tokenValue = "yeelight_access_token_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "");

        // 创建访问令牌
        YeelightOAuth2AccessToken accessToken = new YeelightOAuth2AccessToken(tokenValue);

        // 设置过期时间（2小时）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, 2);
        accessToken.setExpiration(calendar.getTime());

        // 设置scope
        accessToken.setScope(oAuth2Authentication.getOAuth2Request().getScope());

        // 设置additionalInformation
        Map<String, Object> additionalInfo = new HashMap<>(extendParameters);
        accessToken.setAdditionalInformation(additionalInfo);

        return accessToken;
    }

    /**
     * 从授权对象创建访问令牌
     *
     * @param authorization OAuth2授权对象
     * @param extendParameters 扩展参数
     * @return YeelightOAuth2AccessToken 访问令牌
     */
    private YeelightOAuth2AccessToken createAccessTokenFromAuthorization(OAuth2Authorization authorization, Map<String, String> extendParameters) {
        // 生成唯一的token值
        String tokenValue = "yeelight_access_token_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "");

        // 创建访问令牌
        YeelightOAuth2AccessToken accessToken = new YeelightOAuth2AccessToken(tokenValue);

        // 设置过期时间（2小时）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, 2);
        accessToken.setExpiration(calendar.getTime());

        // 设置scope
        accessToken.setScope(authorization.getAuthorizedScopes());

        // 设置additionalInformation
        Map<String, Object> additionalInfo = new HashMap<>(extendParameters);
        additionalInfo.put(TokenConstants.ADDITIONAL_INFORMATION_USERNAME, authorization.getPrincipalName());
        additionalInfo.put(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_ID, authorization.getRegisteredClientId());
        accessToken.setAdditionalInformation(additionalInfo);

        return accessToken;
    }

    private static LoginTokenInfo generateLoginTokenInfo(YeelightOAuth2AccessToken auth2AccessToken, OAuth2AuthenticationDto authenticationDto) {
        // 检查输入参数是否为null
        if (Objects.isNull(authenticationDto) || Objects.isNull(authenticationDto.getPrincipal()) || Objects.isNull(auth2AccessToken)  || Objects.isNull(auth2AccessToken.getAdditionalInformation())) {
            return null;
        }

        // 尝试将认证信息中的主体转换为YeelightUser对象
        YeelightUser userDetail;
        Object principal = authenticationDto.getPrincipal();
        if (principal instanceof YeelightUser) {
            userDetail = (YeelightUser) principal;
        } else {
            userDetail = null;
        }

        // 转换和清理认证信息，以安全地使用
        TokenUtils.safeOauth2AuthenticationDto(authenticationDto);
        YeelightOAuth2Authentication authentication = TokenUtils.dtoToSafeYeelightOauth2Authentication(authenticationDto);

        // 计算令牌的过期时间和创建时间
        Instant expirationTime = Objects.nonNull(auth2AccessToken.getExpiration()) ? auth2AccessToken.getExpiration().toInstant() : null;
        Instant creationTime = Objects.nonNull(auth2AccessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME)) ? Instant.ofEpochMilli(Long.parseLong(auth2AccessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME).toString())) : null;
        Duration maxInactiveInterval = auth2AccessToken.getExpiresIn() > 0 ? Duration.ofSeconds(auth2AccessToken.getExpiresIn()) : null;
        if (Objects.nonNull(expirationTime) && Objects.nonNull(maxInactiveInterval) && Objects.isNull(creationTime)) {
            // 如果创建时间未设置，则根据过期时间和有效期计算创建时间
            creationTime = Instant.now().minus(maxInactiveInterval);
        }

        return toLoginTokenInfo(auth2AccessToken, authenticationDto, maxInactiveInterval, creationTime, expirationTime, userDetail, authentication);
    }

    @NotNull
    private static LoginTokenInfo toLoginTokenInfo(YeelightOAuth2AccessToken auth2AccessToken, OAuth2AuthenticationDto authenticationDto, Duration maxInactiveInterval, Instant creationTime, Instant expirationTime, YeelightUser userDetail, YeelightOAuth2Authentication authentication) {
        // 构建LoginTokenInfo对象，并设置其属性
        LoginTokenInfo loginTokenInfo = LoginTokenInfo.builder()
                .accessToken(auth2AccessToken.getValue())
                .vendor(UserVendorEnum.getVendorByCode((String) auth2AccessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_VENDOR)))
                .expired(auth2AccessToken.isExpired())
                .maxInactiveInterval(maxInactiveInterval)
                .lastAccessedTime(Instant.now())
                .creationTime(creationTime)
                .expirationTime(expirationTime)
                .authentication(authenticationDto)
                .user(userDetail)
                .userAgent((String) auth2AccessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_USER_AGENT))
                .locale(Locale.forLanguageTag((String) auth2AccessToken.getAdditionalInformation().getOrDefault(TokenConstants.ADDITIONAL_INFORMATION_LOCALE, Locale.CHINA.toLanguageTag())))
                .ip((String) auth2AccessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_IP))
                .build();
        loginTokenInfo.attachAuthentication(authentication);

        // 如果设置了用户代理，则解析用户代理信息，并更新LoginTokenInfo对象
        if (StringUtils.isNotBlank(loginTokenInfo.getUserAgent())) {
            UserAgent ua = UserAgentUtil.parse(loginTokenInfo.getUserAgent());
            // 解析并设置浏览器相关信息
            if (Objects.nonNull(ua.getBrowser()) && Browser.Unknown != ua.getBrowser()) {
                loginTokenInfo.setBrowserName(ua.getBrowser().getName());
                loginTokenInfo.setBrowserVersion(ua.getVersion());
            }
            // 解析并设置浏览器引擎相关信息
            if (Objects.nonNull(ua.getEngine()) && Engine.Unknown != ua.getEngine()) {
                loginTokenInfo.setBrowserEngine(ua.getEngine().getName());
                loginTokenInfo.setBrowserEngineVersion(ua.getEngineVersion());
            }
            // 解析并设置操作系统相关信息
            if (Objects.nonNull(ua.getPlatform()) && Platform.Unknown != ua.getPlatform()) {
                loginTokenInfo.setPlatform(ua.getPlatform().getName());
                loginTokenInfo.setIPhoneOrIPod(ua.getPlatform().isIPhoneOrIPod());
                loginTokenInfo.setIPad(ua.getPlatform().isIPad());
                loginTokenInfo.setIos(ua.getPlatform().isIos());
                loginTokenInfo.setAndroid(ua.getPlatform().isAndroid());
            }
            // 解析并设置操作系统名称和版本
            if (Objects.nonNull(ua.getOs()) && OS.Unknown != ua.getOs()) {
                loginTokenInfo.setOsName(ua.getOs().getName());
                loginTokenInfo.setOsVersion(ua.getOsVersion());
            }
            // 标记是否为移动设备
            loginTokenInfo.setMobile(ua.isMobile());
        }
        return loginTokenInfo;
    }

}
