package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.user.client.domain.ProxyInfo;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.service.TokenProxyService;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.config.RedisManager;
import org.apache.dubbo.config.annotation.DubboService;

import jakarta.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Token代理服务
 * <AUTHOR>
 * @date 2020-02-17 14:50
 */
@DubboService
public class TokenProxyServiceImpl implements TokenProxyService {
    @Resource
    private TokenService tokenService;
    @Resource
    private RedisManager redisManager;
    private static final int TIMEOUT_KEY_STORE = 7*24*60;
    private static final TimeUnit TIMEOUT_UNIT_KEY_STORE = TimeUnit.MINUTES;
    private static final String KEY_PREF = "USER:UP_";

    @Override
    public OAuth2AuthenticationDto getAuthentication(String token) {
        return tokenService.getAuthenticationByToken(token);
    }

    /**
     * 绑定代理用户和代理信息到Redis。
     *
     * @param proxyUserId 代理用户的ID，作为Redis键的一部分。
     * @param proxyInfo 包含代理详细信息的对象，将被存储为Redis值。
     * 该方法通过将代理用户ID和代理信息存储在Redis中来实现绑定，
     * 使用的键是基于代理用户ID构建的，数据存储带有超时设置。
     */
    @Override
    public void bindProxy(long proxyUserId, ProxyInfo proxyInfo) {
        // 使用Redis模板为给定的代理用户ID设置代理信息，同时设置超时时间
        redisManager.redisTemplate().opsForValue().set(buildKey(proxyUserId), proxyInfo, TIMEOUT_KEY_STORE, TIMEOUT_UNIT_KEY_STORE);
    }


    private String buildKey(long userId) {
        return KEY_PREF + userId;
    }

    /**
     * 根据代理用户ID获取代理信息。
     *
     * @param proxyUserId 代理用户的ID，用于构建Redis中的键。
     * @return 返回与代理用户ID对应的ProxyInfo对象，如果不存在，则返回null。
     */
    @Override
    public ProxyInfo getProxyInfo(long proxyUserId) {
        // 从Redis中获取代理信息，使用代理用户ID构建的键来检索
        return (ProxyInfo) redisManager.redisTemplate().opsForValue().get(buildKey(proxyUserId));
    }

    /**
     * 解除代理关系，通过删除特定的Redis键来实现。
     *
     * @param proxyUserId 需要解除代理关系的用户的ID，类型为长整型。
     *                    这个ID用于构建Redis键，进而删除对应的键值对。
     */
    @Override
    public void unbindProxy(long proxyUserId) {
        // 使用代理用户ID构建键名，并通过Redis模板删除该键，实现解除代理关系的操作
        redisManager.redisTemplate().delete(buildKey(proxyUserId));
    }

}
