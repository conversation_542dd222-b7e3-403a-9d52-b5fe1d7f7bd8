package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.framework.util.SnowFlake;
import com.yeelight.service.user.client.domain.UserEarnings;
import com.yeelight.service.user.client.domain.YeelightUserAccount;
import com.yeelight.service.user.client.domain.YeelightUserAccountExample;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import com.yeelight.service.user.client.query.UserEarningsQuery;
import com.yeelight.service.user.client.service.UserEarningsService;
import com.yeelight.service.user.client.service.YeelightUserAccountService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.mapper.user.UserEarningsMapper;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import com.yeelight.service.user.server.engine.UserEarningsRuleEngine;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class UserEarningsServiceImpl implements UserEarningsService {

    @Resource
    private UserEarningsMapper userEarningsMapper;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserAccountService yeelightUserAccountService;

    @Resource
    private UserEarningsRuleEngine userEarningsRuleEngine;

    private Weekend<UserEarnings> exportWeekend(UserEarningsQuery query) {
        Weekend<UserEarnings> weekend = Weekend.of(UserEarnings.class);
        WeekendCriteria<UserEarnings, Object> criteria = weekend.weekendCriteria();

        if (Objects.nonNull(query.getYeelightUserId())) {
            criteria.andEqualTo(UserEarnings::getYeelightUserId, query.getYeelightUserId());
        }
        if (StringUtils.isNotBlank(query.getEarningType())) {
            criteria.andEqualTo(UserEarnings::getEarningType, query.getEarningType());
        }
        if (StringUtils.isNotBlank(query.getEarningFrom())) {
            criteria.andEqualTo(UserEarnings::getEarningFrom, query.getEarningFrom());
        }
        if (Objects.nonNull(query.getFromId())) {
            criteria.andEqualTo(UserEarnings::getFromId, query.getFromId());
        }
        if (Objects.nonNull(query.getIsCredit())) {
            criteria.andEqualTo(UserEarnings::getIsCredit, query.getIsCredit());
        }
        if (StringUtils.isNotBlank(query.getEarningNo())) {
            criteria.andEqualTo(UserEarnings::getEarningNo, query.getEarningNo());
        }
        if (StringUtils.isNotBlank(query.getRemark())) {
            criteria.andLike(UserEarnings::getRemark, "%" + query.getRemark() + "%");
        }
        if (Objects.nonNull(query.getAuditUid())) {
            criteria.andEqualTo(UserEarnings::getAuditUid, query.getAuditUid());
        }
        if (StringUtils.isNotBlank(query.getAuditName())) {
            criteria.andEqualTo(UserEarnings::getAuditName, query.getAuditName());
        }

        exportWeekendTime(query, criteria);
        return weekend;
    }

    private static void exportWeekendTime(UserEarningsQuery query, WeekendCriteria<UserEarnings, Object> criteria) {
        if (Objects.nonNull(query.getAuditTimeStart())) {
            criteria.andGreaterThanOrEqualTo(UserEarnings::getAuditTime, query.getAuditTimeStart());
        }
        if (Objects.nonNull(query.getAuditTimeEnd())) {
            criteria.andLessThanOrEqualTo(UserEarnings::getAuditTime, query.getAuditTimeEnd());
        }

        if (Objects.nonNull(query.getEarningTimeStart())) {
            criteria.andGreaterThanOrEqualTo(UserEarnings::getEarningTime, query.getEarningTimeStart());
        }
        if (Objects.nonNull(query.getEarningTimeEnd())) {
            criteria.andLessThanOrEqualTo(UserEarnings::getEarningTime, query.getEarningTimeEnd());
        }
        if (Objects.nonNull(query.getIsInvalid())) {
            criteria.andEqualTo(UserEarnings::getIsInvalid, query.getIsInvalid());
        }
        if (Objects.nonNull(query.getInvalidTimeStart())) {
            criteria.andGreaterThanOrEqualTo(UserEarnings::getInvalidTime, query.getInvalidTimeStart());
        }
        if (Objects.nonNull(query.getInvalidTimeEnd())) {
            criteria.andLessThanOrEqualTo(UserEarnings::getInvalidTime, query.getInvalidTimeEnd());
        }
    }

    /**
     * 更新用户收入信息。
     * 该方法首先会校验用户是否存在，然后根据用户和用户等级类型更新用户的收入信息，并触发相关的业务规则计算。
     * 这个过程会涉及到用户收入记录的生成和相关状态的更新。
     *
     * @param userEarnings 用户收入信息对象，包含用户ID和收入详情等信息。
     * @param userLevelType 用户等级类型，用于可能的收入计算规则影响。
     * @throws BizException 如果业务逻辑执行过程中出现错误，则抛出此异常。
     */
    @Override
    @Transactional(rollbackFor=Exception.class)
    public void updateUserEarning(UserEarnings userEarnings, UserLevelTypes userLevelType) throws BizException {
        // 根据用户ID查询用户信息，确保用户存在
        YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(userEarnings.getYeelightUserId());
        Assert.notNull(yeelightUser, I18nUtil.message("User.Exception.user.notExist", userEarnings.getYeelightUserId()));

        // 查询或初始化用户的账户信息
        YeelightUserAccount yeelightUserAccount = yeelightUserAccountService.selectOne(YeelightUserAccountExample.builder().yeelightUserId(userEarnings.getYeelightUserId()).build());

        // 生成唯一的收入编号，并设置收入和审核时间
        SnowFlake snowFlake = new SnowFlake(1, 4);
        userEarnings.setEarningNo(String.valueOf(snowFlake.nextId()));
        userEarnings.setEarningTime(DateUtils.getCurrentSecond());
        userEarnings.setAuditTime(DateUtils.getCurrentSecond());

        // 使用新的规则引擎替代Drools，完全实现原有业务逻辑
        // 根据用户状态、用户的账户状态、收入的信息计算可否获取/获取多少收入
        userEarningsRuleEngine.executeUpdateUserEarningRules(
            yeelightUser,
            Optional.ofNullable(yeelightUserAccount).orElse(new YeelightUserAccount()),
            userEarnings
        );
    }

    /**
     * 标记用户收益为无效。
     * 该操作会根据用户收益的类型（信用或实际），更新对应的用户账户余额，并将该收益标记为无效。
     *
     * @param userEarningsQuery 用户收益查询条件，用于指定需要标记为无效的用户收益。
     * @throws BizException 业务异常，如果在更新用户账户余额或标记收益无效时遇到问题则抛出。
     */
    @Override
    @Transactional(rollbackFor=Exception.class)
    public void invalidUserEarning(UserEarningsQuery userEarningsQuery) throws BizException {
        // 根据查询条件选择用户收益列表
        List<UserEarnings> userEarningsList = userEarningsMapper.selectByExample(exportWeekend(userEarningsQuery));
        // 如果存在需要标记为无效的用户收益，则进行处理
        if (!userEarningsList.isEmpty()) {
            // 遍历用户收益列表
            userEarningsList.forEach(userEarnings -> {
                // 根据用户收益的yeelightUserId查询对应的用户账户
                YeelightUserAccount yeelightUserAccount = yeelightUserAccountService.selectOne(YeelightUserAccountExample.builder().yeelightUserId(userEarnings.getYeelightUserId()).build());
                // 确保用户账户存在
                Assert.notNull(yeelightUserAccount, I18nUtil.message("User.Exception.userAccount.notExist", userEarnings.getYeelightUserId()));
                // 准备更新用户账户信息
                YeelightUserAccount yeelightUserAccountUpdate = new YeelightUserAccount();
                // 根据用户收益的类型，更新账户余额
                if (userEarnings.getIsCredit().equals(0)) {
                    // 保证用户账户预估余额充足
                    Assert.isNotTrue(yeelightUserAccount.getEstimateWithdrawableAmount().compareTo(userEarnings.getEarningMoney()) >= 0, "用户账户预估余额不足: " + userEarnings.getYeelightUserId());
                    yeelightUserAccountUpdate.setId(yeelightUserAccount.getId());
                    yeelightUserAccountUpdate.setEstimateWithdrawableAmount(yeelightUserAccount.getEstimateWithdrawableAmount().subtract(userEarnings.getEarningMoney()));
                } else {
                    // 保证用户账户实际余额充足
                    Assert.isNotTrue(yeelightUserAccount.getActualWithdrawableAmount().compareTo(userEarnings.getEarningMoney()) >= 0, "用户账户实际余额不足: " + userEarnings.getYeelightUserId());
                    yeelightUserAccountUpdate.setId(yeelightUserAccount.getId());
                    yeelightUserAccountUpdate.setActualWithdrawableAmount(yeelightUserAccount.getActualWithdrawableAmount().subtract(userEarnings.getEarningMoney()));
                }
                // 更新用户账户信息
                yeelightUserAccountService.updateByPrimaryKey(yeelightUserAccountUpdate);
            });
            // 更新用户收益为无效状态，并记录无效时间
            UserEarnings userEarningsUpdate = new UserEarnings();
            userEarningsUpdate.setIsInvalid(1);
            userEarningsUpdate.setInvalidTime(DateUtils.getCurrentSecond());
            userEarningsMapper.updateByExampleSelective(userEarningsUpdate, exportWeekend(userEarningsQuery));
        }
    }

    @Override
    public void removeUserEarningByUserId(Long userId) throws BizException {
        Assert.notNull(userId, I18nUtil.message("User.Exception.userId.notExist"));
        this.userEarningsMapper.deleteByExample(exportWeekend(UserEarningsQuery.builder().yeelightUserId(userId).build()));
    }
}
