package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.user.client.domain.UserLevelLog;
import com.yeelight.service.user.client.query.UserLevelLogQuery;
import com.yeelight.service.user.client.service.UserLevelLogService;
import com.yeelight.service.user.server.mapper.user.UserLevelLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;
import com.yeelight.service.framework.service.impl.BaseServiceImpl;

import jakarta.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class UserLevelLogServiceImpl extends BaseServiceImpl<UserLevelLogMapper, UserLevelLog, UserLevelLogQuery> implements UserLevelLogService {

    @Resource
    private UserLevelLogMapper userLevelLogMapper;


    @Override
    protected Weekend<UserLevelLog> exportWeekend(UserLevelLogQuery query) {
        Weekend<UserLevelLog> weekend = Weekend.of(UserLevelLog.class);
        WeekendCriteria<UserLevelLog, Object> criteria = weekend.weekendCriteria();
        if (StringUtils.isNotBlank(query.getUserName())) {
            criteria.andEqualTo(UserLevelLog::getUserName, query.getUserName());
        }
        if (StringUtils.isNotBlank(query.getLevelType())) {
            criteria.andEqualTo(UserLevelLog::getLevelType, query.getLevelType());
        }
        if (Objects.nonNull(query.getYeelightUserId())) {
            criteria.andEqualTo(UserLevelLog::getYeelightUserId, query.getYeelightUserId());
        }
        if (Objects.nonNull(query.getUpdateUid())) {
            criteria.andEqualTo(UserLevelLog::getUpdateUid, query.getUpdateUid());
        }
        if (Objects.nonNull(query.getCreateUid())) {
            criteria.andEqualTo(UserLevelLog::getCreateUid, query.getCreateUid());
        }
        return weekend;
    }
}
