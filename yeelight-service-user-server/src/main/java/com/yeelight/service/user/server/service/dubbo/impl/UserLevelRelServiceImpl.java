package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.user.client.domain.UserLevelRel;
import com.yeelight.service.user.client.query.UserLevelRelQuery;
import com.yeelight.service.user.client.service.UserLevelRelService;
import com.yeelight.service.user.server.mapper.user.UserLevelRelMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;
import com.yeelight.service.framework.service.impl.BaseServiceImpl;

import jakarta.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class UserLevelRelServiceImpl extends BaseServiceImpl<UserLevelRelMapper, UserLevelRel, UserLevelRelQuery> implements UserLevelRelService {

    @Resource
    private UserLevelRelMapper userLevelRelMapper;

    @Override
    protected Weekend<UserLevelRel> exportWeekend(UserLevelRelQuery query) {
        Weekend<UserLevelRel> weekend = Weekend.of(UserLevelRel.class);
        WeekendCriteria<UserLevelRel, Object> criteria = weekend.weekendCriteria();
        if (StringUtils.isNotBlank(query.getLevelName())) {
            criteria.andEqualTo(UserLevelRel::getLevelName, query.getLevelName());
        }

        if (StringUtils.isNotBlank(query.getLevelType())) {
            criteria.andEqualTo(UserLevelRel::getLevelType, query.getLevelType());
        }
        if (Objects.nonNull(query.getYeelightUserId())) {
            criteria.andEqualTo(UserLevelRel::getYeelightUserId, query.getYeelightUserId());
        }
        if (Objects.nonNull(query.getLevelId())) {
            criteria.andEqualTo(UserLevelRel::getLevelId, query.getYeelightUserId());
        }
        return weekend;
    }

    /**
     * 替换用户等级关系信息。
     * 该方法通过调用 userLevelRelMapper 的 replaceLevelRel 方法，来更新数据库中的用户等级关系记录。
     *
     * @param userLevelRel 用户等级关系对象，包含需要更新的用户等级关系信息。
     */
    @Override
    public void replaceLevelRel(UserLevelRel userLevelRel) {
        // 调用mapper层执行更新操作
        userLevelRelMapper.replaceLevelRel(userLevelRel);
    }

    @Override
    public UserLevelRel getUserLevel(Long userId) {
        return selectOne(UserLevelRelQuery.builder().yeelightUserId(userId).build());
    }

    @Override
    public UserLevelRel getUserLevelByLevelType(Long userId, String levelType) {
        return selectOne(UserLevelRelQuery.builder().yeelightUserId(userId).levelType(levelType).build());
    }
}
