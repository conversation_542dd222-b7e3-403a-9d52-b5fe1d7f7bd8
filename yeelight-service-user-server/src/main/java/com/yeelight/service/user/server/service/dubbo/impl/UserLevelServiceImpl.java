package com.yeelight.service.user.server.service.dubbo.impl;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.service.impl.BaseServiceImpl;
import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.user.client.domain.*;
import com.yeelight.service.user.client.dto.UserLevelAndRulesData;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import com.yeelight.service.user.client.enums.UserStatus;
import com.yeelight.service.user.client.query.UserLevelQuery;
import com.yeelight.service.user.client.query.UserLevelRelQuery;
import com.yeelight.service.user.client.request.AddUserGrowthRequest;
import com.yeelight.service.user.client.request.UserLevelUpgradeRule;
import com.yeelight.service.user.client.service.*;
import com.yeelight.service.user.server.mapper.user.UserLevelMapper;
import com.yeelight.service.user.client.service.UserLevelLogService;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import com.yeelight.service.user.server.engine.UserLevelUpgradeRuleEngine;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class UserLevelServiceImpl extends BaseServiceImpl<UserLevelMapper, UserLevel, UserLevelQuery> implements UserLevelService {

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private UserLevelRelService userLevelRelService;

    @Resource
    private UserLevelLogService userLevelLogService;

    @Resource
    private YeelightUsersExtendService userExtendService;

    @Resource
    private UserLevelUpgradeRuleEngine userLevelUpgradeRuleEngine;

    @Override
    protected Weekend<UserLevel> exportWeekend(UserLevelQuery query) {
        Weekend<UserLevel> weekend = Weekend.of(UserLevel.class);
        WeekendCriteria<UserLevel, Object> criteria = weekend.weekendCriteria();
        if (StringUtils.isNotBlank(query.getLevelName())) {
            criteria.andEqualTo(UserLevel::getLevelName, query.getLevelName());
        }

        if (StringUtils.isNotBlank(query.getLevelType())) {
            criteria.andEqualTo(UserLevel::getLevelType, query.getLevelType());
        }

        if (StringUtils.isNotBlank(query.getLevelDesc())) {
            criteria.andLike(UserLevel::getLevelDesc, "%" + query.getLevelDesc() + "%");
        }
        return weekend;
    }

    /**
     * 增加用户成长值。
     *
     * @param addUserGrowthRequest 包含增加成长值所需信息的请求对象，如用户ID、成长值等。
     * @throws BizException 业务异常，可能抛出当用户不存在、用户被禁用等情况。
     */
    @Override
    @Transactional(rollbackFor=Exception.class)
    public void addGrowth(AddUserGrowthRequest addUserGrowthRequest) throws BizException {
        // 根据请求查找用户
        YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(addUserGrowthRequest.getYeelightUserId());
        // 断言用户存在
        Assert.notNull(yeelightUser, I18nUtil.message("User.Exception.user.notExist", addUserGrowthRequest.getYeelightUserId()));
        // 断言用户未被禁用
        Assert.isNotTrue(UserStatus.ENABLED.getCode().equals(yeelightUser.getStatus()), I18nUtil.getMessage("User.Exception.user.disabled"));

        // 查询用户当前等级关系，不存在则初始化
        UserLevelRel userLevelRel = userLevelRelService.selectOne(
                UserLevelRelQuery.builder()
                        .yeelightUserId(yeelightUser.getId())
                        .levelType(addUserGrowthRequest.getUserLevelType().getCode())
                        .build()
        );
        // 初始化用户等级关系
        if (Objects.isNull(userLevelRel)) {
            userLevelRel = new UserLevelRel();
            userLevelRel.setLevelType(addUserGrowthRequest.getUserLevelType().getCode());
            userLevelRel.setYeelightUserId(yeelightUser.getId());
            userLevelRel.setGrowth(0);
            userLevelRel.setLevelId(0L);
            userLevelRel.setLevelName("无");
        }

        // 构建用户等级日志对象
        UserLevelLog userLevelLog = UserLevelLog.builder()
                .yeelightUserId(yeelightUser.getId())
                .userName(yeelightUser.getName())
                .levelType(addUserGrowthRequest.getUserLevelType().getCode())
                .createUid(addUserGrowthRequest.getOperateUid())
                .updateUid(addUserGrowthRequest.getOperateUid())
                .createTime(DateUtils.getCurrentSecond())
                .updateTime(DateUtils.getCurrentSecond())
                .build();

        // 初始化用户等级升级规则
        UserLevelUpgradeRule userLevelUpgradeRule = new UserLevelUpgradeRule();
        userLevelUpgradeRule.setIncreasedGrowth(addUserGrowthRequest.getIncreasedGrowth());
        userLevelUpgradeRule.setYeelightUser(yeelightUser);
        // 查询并排序适用的用户等级列表
        List<UserLevel> userLevelList = selectByExample(UserLevelQuery.builder().levelType(addUserGrowthRequest.getUserLevelType().getCode()).build());
        userLevelList.sort(Comparator.comparing(UserLevel::getRequiredGrowth));
        userLevelUpgradeRule.setUserLevelList(userLevelList);

        // 使用新的规则引擎替代Drools，完全实现原有业务逻辑
        // 触发用户等级升级规则计算
        userLevelUpgradeRuleEngine.executeUserLevelUpgradeRules(
            userLevelUpgradeRule,
            userLevelRel,
            userLevelLog
        );
    }

    @Override
    public UserLevel.CommissionRule getCommissionRule(Long levelId) {
        UserLevel userLevel = selectByPrimaryKey(levelId);
        if (Objects.nonNull(userLevel)) {
            return JSON.parseObject(userLevel.getCommissionRules(), UserLevel.CommissionRule.class);
        }
        return null;
    }

    @Override
    public UserLevel.GrowthRule getGrowthRule(Long levelId) {
        UserLevel userLevel = selectByPrimaryKey(levelId);
        if (Objects.nonNull(userLevel)) {
            return JSON.parseObject(userLevel.getGrowthRules(), UserLevel.GrowthRule.class);
        }
        return null;
    }

    /**
     * 获取用户等级和规则数据。
     *
     * @param yeelightUserId 用户的Yeelight用户ID，不可为null。
     * @param userLevelType 用户的等级类型，不可为null。
     * @param money 用户当前的金额，用于计算成长值和佣金，不可为null。
     * @return UserLevelAndRulesData 包含用户等级、规则及相关计算数据的对象。
     * @throws BizException 如果用户不存在、等级关系不存在、等级规则数据缺失或非法时抛出。
     */
    @Override
    public UserLevelAndRulesData getLevelAndRules(Long yeelightUserId, UserLevelTypes userLevelType, BigDecimal money) throws BizException {
        // 根据ID查找用户
        YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(yeelightUserId);
        Assert.notNull(yeelightUser, I18nUtil.message("User.Exception.user.notExist", yeelightUserId));

        // 根据用户ID和等级类型查找用户等级关系
        UserLevelRel userLevelRel = userLevelRelService.selectOne(UserLevelRelQuery.builder()
                .levelType(userLevelType.getCode())
                .yeelightUserId(yeelightUserId)
                .build());
        Assert.notNull(userLevelRel, I18nUtil.message("User.Exception.user.notExist", userLevelType.getCode()));

        // 根据等级ID查找用户等级详情
        UserLevel userLevel = selectByPrimaryKey(userLevelRel.getLevelId());
        Assert.notNull(userLevel, I18nUtil.message("User.Exception.user.notExist", userLevelRel.getLevelId()));

        // 查找用户的扩展信息
        YeelightUserExtend userExtend = userExtendService.selectOne(YeelightUserExtendExample.builder().yeelightUserId(yeelightUserId).build());
        Assert.notNull(userExtend, I18nUtil.message("User.Exception.user.notExist", yeelightUserId));

        // 解析等级的佣金和成长规则
        UserLevel.CommissionRule commissionRule = JSON.parseObject(userLevel.getCommissionRules(), UserLevel.CommissionRule.class);
        UserLevel.GrowthRule growthRule = JSON.parseObject(userLevel.getGrowthRules(), UserLevel.GrowthRule.class);
        Assert.notNull(commissionRule, I18nUtil.message("User.Exception.level.commissionRule.notExist", yeelightUserId));
        Assert.notNull(growthRule, I18nUtil.message("User.Exception.level.growthRule.notExist", yeelightUserId));
        // 验证成长规则的合法性
        Assert.isNotTrue(growthRule.getMoneyRequiredPerGrowth().compareTo(BigDecimal.ZERO) > 0, I18nUtil.getMessage("User.Exception.level.growthRule.illegal"));

        // 构建并返回用户等级和规则数据
        return UserLevelAndRulesData.builder()
                .yeelightUserId(yeelightUserId)
                .userName(userExtend.getRealName())
                .userMobile(yeelightUser.getPhoneNumber())
                .userCurrentGrowth(userLevelRel.getGrowth())
                .userCurrentLevelId(userLevelRel.getLevelId())
                .commissionRate(commissionRule.getCommissionRate())
                .commissionMoney(money)
                .expectedMoney(money.multiply(commissionRule.getCommissionRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))
                .growth(money.divideToIntegralValue(growthRule.getMoneyRequiredPerGrowth()).intValue())
                .build();
    }
}
