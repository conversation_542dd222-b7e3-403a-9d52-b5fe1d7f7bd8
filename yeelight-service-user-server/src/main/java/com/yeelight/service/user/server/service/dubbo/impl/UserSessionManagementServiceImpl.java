/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service.impl
 * Description: 会话管理服务实现
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-11-27 11:18:11:18
 * UpdatedTime: 2024-12-21 (升级到Spring Boot 3.x)
 */
package com.yeelight.service.user.server.service.dubbo.impl;

import cn.hutool.http.useragent.UserAgent;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.constant.UserConstant;
import com.yeelight.service.user.client.domain.LoginSessionInfo;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.utils.TokenUtils;
import com.yeelight.service.user.server.utils.UserAgentUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.session.Session;
import org.springframework.session.SessionRepository;
import org.springframework.session.FindByIndexNameSessionRepository;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Desc: 会话管理服务实现
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-11-27 11:18:11:18
 */
@Slf4j
@DubboService(timeout = 3000)
public class UserSessionManagementServiceImpl implements UserSessionManagementService {
    @Resource
    private SessionRepository<? extends Session> sessionRepository;

    /**
     * 注销指定用户的指定厂商会话。
     * 该方法会遍历所有与给定用户名匹配的会话，并根据厂商信息判断哪些会话需要被注销。
     * 对于需要注销的会话，将删除其会话记录，并生成相应的操作日志。
     *
     * @param username 需要注销会话的用户名称。
     * @param vendor 需要注销的会话所属的厂商。
     * @throws BizException 业务异常，如果会话操作失败可能会抛出此异常。
     */
    @Override
    public void expireUserSessions(String username, String vendor) throws BizException {
        // 获取与指定用户名匹配的所有会话
        Map<String, ? extends Session> sessions = findSessionsByUsername(username);
        if (sessions != null && !sessions.isEmpty()) {
            log.info("用户{}的会话数为{}, 强制登出指定厂商下的会话", username, sessions.size());
            // 遍历所有会话，检查并逐个注销符合条件的会话
            for (Session session : sessions.values()) {
                invalidSession(username, vendor, session);
            }
        }
    }

    private void invalidSession(String username, String vendor, Session session) {
        // 尝试从会话中获取安全上下文
        SecurityContext securityContext = session.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
        if (Objects.nonNull(securityContext)) {
            // 验证会话是否属于目标用户且所属厂商匹配
            Authentication authentication = securityContext.getAuthentication();
            Object principal = authentication.getPrincipal();
            if (principal instanceof YeelightUser userDetails) {
                // 判断用户ID是否在指定厂商的范围内，是则注销该会话
                if (userDetails.getUsername().equals(username) && UserVendorEnum.getVendorByCode(vendor).userIdRange().contains(userDetails.getId())) {
                    sessionRepository.deleteById(session.getId());
                }
            }
        } else {
            // 如果会话没有安全上下文，也直接注销
            sessionRepository.deleteById(session.getId());
        }
        // 获取并发送会话删除操作的日志
        LoginSessionInfo sessionInfo = getSessionInfo(session.getId());
        if (Objects.nonNull(sessionInfo)) {
            BizOperateLogUtils.sendRemoveBizOperateLog(sessionInfo.getUsername(), BizTypeEnums.会话.getCode(), "注销指定厂商用户会话", sessionInfo);
        }
    }

    /**
     * 强制用户下线所有会话
     *
     * @param username 需要强制下线的用户名称
     * @throws BizException 业务异常，可能在操作会话或日志记录时抛出
     */
    @Override
    public void expireUserSessions(String username) throws BizException {
        // 获取指定用户的所有会话
        Map<String, ? extends Session> sessions = findSessionsByUsername(username);
        if (sessions != null && !sessions.isEmpty()) {
            log.info("用户{}的会话数为{}, 全部登出", username, sessions.size());
            // 遍历并逐个终止所有会话
            for (Session session : sessions.values()) {
                // 尝试获取会话信息，为后续的会话无效化做准备
                LoginSessionInfo sessionInfo = getSessionInfo(session.getId());
                if (Objects.nonNull(sessionInfo)) {
                    // 从会话存储中删除会话
                    sessionRepository.deleteById(session.getId());
                    // 发送删除操作的日志
                    BizOperateLogUtils.sendRemoveBizOperateLog(sessionInfo.getUsername(), BizTypeEnums.会话.getCode(), "注销用户会话", sessionInfo);
                }
            }
        }
    }

    /**
     * 强制用户下线指定会话
     *
     * @param sessionId 会话的唯一标识符
     * @throws BizException 业务异常，如果操作过程中出现错误
     */
    @Override
    public void expireSession(String sessionId)  throws BizException{
        // 检查会话ID是否为空，若为空则直接返回
        if (StringUtils.isBlank(sessionId)) {
            return;
        }
        // 根据会话ID获取会话信息
        LoginSessionInfo sessionInfo = getSessionInfo(sessionId);
        // 如果找到对应的会话信息，则进行会话删除操作并记录操作日志
        if (Objects.nonNull(sessionInfo)) {
            // 发送删除操作日志
            BizOperateLogUtils.sendRemoveBizOperateLog(sessionInfo.getUsername(), BizTypeEnums.会话.getCode(), "退出会话", sessionInfo);
            // 从会话存储中删除指定ID的会话
            sessionRepository.deleteById(sessionId);
        }
    }

    /**
     * 根据会话ID获取登录会话信息。
     *
     * @param sessionId 会话的唯一标识符。
     * @return LoginSessionInfo 对象，包含与登录相关的会话信息。
     */
    @Override
    public LoginSessionInfo getSessionInfo(String sessionId) {
        // 通过会话ID从会话仓库中查找会话，并将其转换为登录会话信息对象
        return sessionToLoginSessionInfo(sessionRepository.findById(sessionId));
    }

    /**
     * 获取指定用户的所有登录会话信息，并按创建时间倒序排列。
     *
     * @param username 用户名，用于查询该用户的登录会话信息。
     * @return 返回一个登录会话信息列表，列表中的元素按创建时间从新到旧排序。
     */
    @Override
    public List<LoginSessionInfo> getSessionInfos(String username) {
        // 通过用户名查询所有会话
        Map<String, ? extends Session> sessions = findSessionsByUsername(username);
        // 过滤掉空值，将Session对象转换为LoginSessionInfo对象，再过滤掉转换后的空值
        return sessions.values().stream().filter(Objects::nonNull).map(this::sessionToLoginSessionInfo).filter(Objects::nonNull)
                // 按照LoginSessionInfo的创建时间倒序排列
                .sorted(Comparator.comparing(LoginSessionInfo::getCreationTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取指定用户和供应商的登录会话信息列表。
     *
     * @param username 用户名。
     * @param vendor 供应商名称，用于过滤出属于指定供应商的用户会话。
     * @return 返回一个登录会话信息列表，列表中的元素按照创建时间倒序排列。
     */
    @Override
    public List<LoginSessionInfo> getSessionInfos(String username, String vendor) {
        // 通过用户名查询所有的会话
        Map<String, ? extends Session> sessions = findSessionsByUsername(username);
        // 过滤出非空会话，并进一步筛选出符合条件的会话（即用户会话且属于指定供应商）
        return sessions.values().stream().filter(Objects::nonNull).filter(session -> {
            SecurityContext securityContext = session.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
            if (Objects.nonNull(securityContext)) {
                Authentication authentication = securityContext.getAuthentication();
                Object principal = authentication.getPrincipal();
                if (principal instanceof YeelightUser userDetails) {
                    // 确保用户名称匹配且用户ID属于指定供应商的范围
                    return userDetails.getUsername().equals(username) && UserVendorEnum.valueOf(vendor).userIdRange().contains(userDetails.getId());
                }
            } else {
                // 如果securityContext为空，也认为该会话符合条件
                return true;
            }
            return false;
        }).map(this::sessionToLoginSessionInfo).filter(Objects::nonNull)
            // 按照LoginSessionInfo的创建时间倒序排列
            .sorted(Comparator.comparing(LoginSessionInfo::getCreationTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 根据用户名查找会话的辅助方法
     * 兼容新版Spring Session API
     */
    private Map<String, ? extends Session> findSessionsByUsername(String username) {
        if (sessionRepository instanceof FindByIndexNameSessionRepository<? extends Session> indexedRepository) {
            return indexedRepository.findByIndexNameAndIndexValue(
                FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME, username);
        } else {
            // 如果不支持索引查找，返回空Map
            log.warn("SessionRepository does not support finding sessions by principal name");
            return new HashMap<>(0);
        }
    }

    /**
     * 将Session对象转换为LoginSessionInfo对象。
     * 这个方法主要用于将HTTP会话中的信息，包括认证、用户详情等，转换为一个更便于前端使用的格式。
     *
     * @param session HttpSession对象，代表一个用户会话。
     * @return LoginSessionInfo对象，包含了会话的各种信息，如果输入的Session为null，则返回null。
     */
    private LoginSessionInfo sessionToLoginSessionInfo(Session session) {
        if (Objects.isNull(session)) {
            return null;
        }
        LoginSessionInfo loginSessionInfo = new LoginSessionInfo();
        // 基本会话信息设置
        loginSessionInfo.setSessionId(session.getId());
        loginSessionInfo.setExpired(session.isExpired());
        loginSessionInfo.setMaxInactiveInterval(session.getMaxInactiveInterval());
        loginSessionInfo.setLastAccessedTime(session.getLastAccessedTime());
        loginSessionInfo.setCreationTime(session.getCreationTime());
        // 计算过期时间
        loginSessionInfo.setExpirationTime(session.getCreationTime().plusSeconds(session.getMaxInactiveInterval().getSeconds()));
        // 安全认证信息设置
        attachUserInfoToSession(session, loginSessionInfo);
        // 会话环境信息设置（如用户代理、地区、IP等）
        loginSessionInfo.setUserAgent(session.getAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_UA));
        loginSessionInfo.setLocale(session.getAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_LOCALE));
        loginSessionInfo.setIp(session.getAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_IP));
        loginSessionInfo.setServiceIp(session.getAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_SERVER_IP));
        loginSessionInfo.setServiceName(session.getAttribute(UserConstant.USER_LOGIN_ENVIRONMENT_SERVER_NAME));

        attachUserAgentInfoToSession(loginSessionInfo);
        return loginSessionInfo;
    }

    private static void attachUserAgentInfoToSession(LoginSessionInfo loginSessionInfo) {
        // 如果用户代理不为空，则解析用户代理以获取更详细的环境信息
        if (StringUtils.isNotBlank(loginSessionInfo.getUserAgent())) {
            UserAgent ua = UserAgentUtil.parse(loginSessionInfo.getUserAgent());
            // 如果用户代理不为空，则解析用户代理以获取更详细的环境信息
            if (ua != null) {
                // 浏览器信息设置
                attachUaBrowser(loginSessionInfo, ua);
                // 浏览器引擎信息设置
                attachUaEngine(loginSessionInfo, ua);
                // 平台与操作系统信息设置
                attachUaPlatform(loginSessionInfo, ua);
                // 操作系统信息设置
                attachUaOs(loginSessionInfo, ua);
                // 标记是否为移动设备
                loginSessionInfo.setMobile(ua.isMobile());
            }
        }
    }

    private static void attachUaOs(LoginSessionInfo loginSessionInfo, UserAgent ua) {
        if (Objects.nonNull(ua.getOs())) {
            loginSessionInfo.setOsName(ua.getOs().getName());
            loginSessionInfo.setOsVersion(ua.getOsVersion());
        }
    }

    private static void attachUaEngine(LoginSessionInfo loginSessionInfo, UserAgent ua) {
        if (Objects.nonNull(ua.getEngine())) {
            loginSessionInfo.setBrowserEngine(ua.getEngine().getName());
            loginSessionInfo.setBrowserEngineVersion(ua.getEngineVersion());
        }
    }

    private static void attachUaBrowser(LoginSessionInfo loginSessionInfo, UserAgent ua) {
        if (Objects.nonNull(ua.getBrowser())) {
            loginSessionInfo.setBrowserName(ua.getBrowser().getName());
            loginSessionInfo.setBrowserVersion(ua.getVersion());
        }
    }

    private static void attachUaPlatform(LoginSessionInfo loginSessionInfo, UserAgent ua) {
        if (Objects.nonNull(ua.getPlatform())) {
            loginSessionInfo.setPlatform(ua.getPlatform().getName());
            loginSessionInfo.setIPhoneOrIPod(ua.getPlatform().isIPhoneOrIPod());
            loginSessionInfo.setIPad(ua.getPlatform().isIPad());
            loginSessionInfo.setIos(ua.getPlatform().isIos());
            loginSessionInfo.setAndroid(ua.getPlatform().isAndroid());
        }
    }

    private static void attachUserInfoToSession(Session session, LoginSessionInfo loginSessionInfo) {
        SecurityContext securityContext = session.getAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY);
        if (Objects.nonNull(securityContext)) {
            Authentication authentication = securityContext.getAuthentication();
            Object principal = authentication.getPrincipal();
            if (principal instanceof YeelightUser userDetails) {
                // 将认证信息转换为DTO，并进行安全处理
                OAuth2AuthenticationDto authenticationDto = TokenUtils.authenticationToDto(authentication);
                TokenUtils.safeOauth2AuthenticationDto(authenticationDto);
                loginSessionInfo.setAuthentication(authenticationDto);
                loginSessionInfo.setUser(userDetails);
                // 根据用户ID确定供应商
                UserVendorEnum userVendorEnum = Arrays.stream(UserVendorEnum.values()).filter(vendor -> vendor.userIdRange().contains(userDetails.getId())).findFirst().orElse(UserVendorEnum.DEFAULT);
                loginSessionInfo.setVendor(userVendorEnum);
                // 关联认证信息
                loginSessionInfo.attachAuthentication(authentication);
            }
        }
    }
}
