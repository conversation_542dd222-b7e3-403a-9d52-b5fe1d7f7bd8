package com.yeelight.service.user.server.service.dubbo.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.framework.util.SnowFlake;
import com.yeelight.service.user.client.domain.*;
import com.yeelight.service.user.client.dto.UserWithdrawDetailDto;
import com.yeelight.service.user.client.enums.UserWithdrawStatus;
import com.yeelight.service.user.client.query.UserWithdrawsDetailQuery;
import com.yeelight.service.user.client.query.UserWithdrawsQuery;
import com.yeelight.service.user.client.request.ApplyWithdrawRequest;
import com.yeelight.service.user.client.request.AuditWithdrawRequest;
import com.yeelight.service.user.client.service.UserWithdrawsService;
import com.yeelight.service.user.server.mapper.user.UserWithdrawsMapper;
import com.yeelight.service.framework.service.impl.BaseServiceImpl;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class UserWithdrawsServiceImpl extends BaseServiceImpl<UserWithdrawsMapper, UserWithdraws, UserWithdrawsQuery> implements UserWithdrawsService {

    @Resource
    private UserWithdrawsMapper userWithdrawsMapper;

    @Resource
    private YeelightUserAccountServiceImpl userAccountService;

    @Resource
    private YeelightUserBankServiceImpl userBankService;

    @Override
    protected Weekend<UserWithdraws> exportWeekend(UserWithdrawsQuery query) {
        Weekend<UserWithdraws> weekend = Weekend.of(UserWithdraws.class);
        WeekendCriteria<UserWithdraws, Object> criteria = weekend.weekendCriteria();

        if (Objects.nonNull(query.getYeelightUserId())) {
            criteria.andEqualTo(UserWithdraws::getYeelightUserId, query.getYeelightUserId());
        }
        if (StringUtils.isNotBlank(query.getWithdrawType())) {
            criteria.andEqualTo(UserWithdraws::getWithdrawType, query.getWithdrawType());
        }
        if (StringUtils.isNotBlank(query.getWithdrawNo())) {
            criteria.andEqualTo(UserWithdraws::getWithdrawNo, query.getWithdrawNo());
        }
        if (Objects.nonNull(query.getStatus())) {
            criteria.andEqualTo(UserWithdraws::getStatus, query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getBankUserName())) {
            criteria.andEqualTo(UserWithdraws::getBankUserName, query.getBankUserName());
        }
        if (StringUtils.isNotBlank(query.getBankName())) {
            criteria.andEqualTo(UserWithdraws::getBankName, query.getBankName());
        }
        if (StringUtils.isNotBlank(query.getBankCard())) {
            criteria.andEqualTo(UserWithdraws::getBankCard, query.getBankCard());
        }
        if (StringUtils.isNotBlank(query.getBankLocation())) {
            criteria.andLike(UserWithdraws::getBankLocation, "%" + query.getBankLocation() + "%");
        }
        if (StringUtils.isNotBlank(query.getRemark())) {
            criteria.andLike(UserWithdraws::getRemark, "%" + query.getRemark() + "%");
        }
        if (Objects.nonNull(query.getAuditUid())) {
            criteria.andEqualTo(UserWithdraws::getAuditUid, query.getAuditUid());
        }
        if (StringUtils.isNotBlank(query.getAuditName())) {
            criteria.andEqualTo(UserWithdraws::getAuditName, query.getAuditName());
        }

        exportWeekendTime(query, criteria);
        return weekend;
    }

    private static void exportWeekendTime(UserWithdrawsQuery query, WeekendCriteria<UserWithdraws, Object> criteria) {
        if (Objects.nonNull(query.getAuditTimeStart())) {
            criteria.andGreaterThanOrEqualTo(UserWithdraws::getAuditTime, query.getAuditTimeStart());
        }
        if (Objects.nonNull(query.getAuditTimeEnd())) {
            criteria.andLessThanOrEqualTo(UserWithdraws::getAuditTime, query.getAuditTimeEnd());
        }

        if (Objects.nonNull(query.getApplyTimeStart())) {
            criteria.andGreaterThanOrEqualTo(UserWithdraws::getApplyTime, query.getApplyTimeStart());
        }
        if (Objects.nonNull(query.getApplyTimeEnd())) {
            criteria.andLessThanOrEqualTo(UserWithdraws::getApplyTime, query.getApplyTimeEnd());
        }
    }

    /**
     * 处理用户提现申请的业务逻辑。
     *
     * @param applyWithdrawRequest 包含提现申请信息的请求对象，其中包括用户ID、提现金额、提现类型等信息。
     * @throws BizException 业务异常，可能会抛出此异常来处理一些特定的业务规则违反情况，比如用户账户余额不足等。
     */
    @Override
    @Transactional(rollbackFor=Exception.class)
    public void applyWithdraw(ApplyWithdrawRequest applyWithdrawRequest) throws BizException {
        // 根据用户ID查询用户账户信息
        YeelightUserAccount yeelightUserAccount = userAccountService.selectOne(YeelightUserAccountExample.builder()
                .yeelightUserId(applyWithdrawRequest.getYeelightUserId())
                .build());
        // 确保用户账户存在
        Assert.notNull(yeelightUserAccount, I18nUtil.message("User.Exception.userAccount.empty", applyWithdrawRequest.getYeelightUserId()));
        // 确保用户可提现金额大于申请提现的金额
        Assert.isNotTrue(yeelightUserAccount.getActualWithdrawableAmount().compareTo(applyWithdrawRequest.getApplyMoney()) >= 0, I18nUtil.getMessage("User.Exception.userAccount.moneyLess"));

        // 根据用户ID和提现类型查询用户银行账户信息
        YeelightUserBank yeelightUserBank = userBankService.selectOne(YeelightUserBankExample.builder()
                .yeelightUserId(applyWithdrawRequest.getYeelightUserId())
                .type(applyWithdrawRequest.getWithdrawType().getCode())
                .build());
        // 确保用户存在对应的银行账户
        Assert.notNull(yeelightUserBank, I18nUtil.message("User.Exception.userAccount.empty", applyWithdrawRequest.getYeelightUserId()));

        // 冻结用户账户提现金额
        yeelightUserAccount.setActualWithdrawableAmount(yeelightUserAccount.getActualWithdrawableAmount().subtract(applyWithdrawRequest.getApplyMoney()));
        userAccountService.updateByPrimaryKey(yeelightUserAccount);

        // 生成提现记录编号
        SnowFlake snowFlake = new SnowFlake(1, 5);
        // 写入提现记录到数据库
        insertSelective(UserWithdraws.builder()
                .yeelightUserId(applyWithdrawRequest.getYeelightUserId())
                .withdrawType(applyWithdrawRequest.getWithdrawType().getCode())
                .withdrawNo(String.valueOf(snowFlake.nextId()))
                .applyMoney(applyWithdrawRequest.getApplyMoney())
                .accountMoney(yeelightUserAccount.getActualWithdrawableAmount())
                .status(UserWithdrawStatus.待审核.getCode())
                .applyTime(DateUtils.getCurrentSecond())
                .bankUserName(yeelightUserBank.getUserName())
                .bankLocation(yeelightUserBank.getLocation())
                .bankName(yeelightUserBank.getBankName())
                .bankCard(yeelightUserBank.getCardNo())
                .build());
    }

    /**
     * 审核用户提现申请。
     *
     * @param auditWithdrawRequest 包含审核信息和提现申请ID的请求对象。
     * @throws BizException 业务异常，可能在用户提现记录为空、用户账号为空或审核过程中发生错误时抛出。
     */
    @Override
    @Transactional(rollbackFor=Exception.class)
    public void auditWithdraw(AuditWithdrawRequest auditWithdrawRequest) throws BizException {
        // 根据提现ID查询提现记录
        UserWithdraws userWithdraws = selectByPrimaryKey(auditWithdrawRequest.getWithdrawId());
        // 确保提现记录不为空
        Assert.notNull(userWithdraws, I18nUtil.message("User.Exception.userWithdraws.empty", auditWithdrawRequest.getWithdrawId()));
        // 根据用户ID查询用户账号信息
        YeelightUserAccount yeelightUserAccount = userAccountService.selectOne(YeelightUserAccountExample.builder()
                .yeelightUserId(userWithdraws.getYeelightUserId())
                .build());
        // 确保用户账号不为空
        Assert.notNull(yeelightUserAccount, I18nUtil.message("User.Exception.userAccount.empty", userWithdraws.getYeelightUserId()));
        // 更新提现记录的状态和其他相关信息
        updateByPrimaryKeySelective(UserWithdraws.builder()
                .id(auditWithdrawRequest.getWithdrawId())
                .status(auditWithdrawRequest.getStatus().getCode())
                .auditUid(auditWithdrawRequest.getAuditId())
                .auditName(auditWithdrawRequest.getAuditName())
                .auditTime(DateUtils.getCurrentSecond())
                .applyTime(DateUtils.getCurrentSecond())
                .remark(auditWithdrawRequest.getRemark())
                .build());
        // 根据审核结果更新用户账号的累计提现金额或恢复冻结金额
        if (auditWithdrawRequest.getStatus().equals(UserWithdrawStatus.审核通过)) {
            // 审核通过：增加累计提现金额
            yeelightUserAccount.setAccumulativeWithdrawnAmount(yeelightUserAccount.getAccumulativeWithdrawnAmount().add(userWithdraws.getApplyMoney()));
        } else {
            // 审核不通过：恢复冻结金额
            yeelightUserAccount.setActualWithdrawableAmount(yeelightUserAccount.getActualWithdrawableAmount().add(userWithdraws.getApplyMoney()));
        }
        // 更新用户账号信息
        userAccountService.updateByPrimaryKey(yeelightUserAccount);
    }

    @Override
    public PageResultSet<UserWithdrawDetailDto> pageWithdrawsDetailList(UserWithdrawsDetailQuery query) {
        Page<UserWithdrawDetailDto> page = PageHelper.startPage(query.getPageNo(), query.getPageSize());
        PageResultSet<UserWithdrawDetailDto> pageResultSet = new PageResultSet<>();
        List<UserWithdrawDetailDto> list = userWithdrawsMapper.pageWithdrawsDetail(query);
        pageResultSet.setTotal(page.getTotal());
        pageResultSet.setRows(list);
        return pageResultSet;
    }

    @Override
    public List<UserWithdrawDetailDto> exportWithdrawsDetailList(UserWithdrawsDetailQuery query) {
        return userWithdrawsMapper.pageWithdrawsDetail(query);
    }
}
