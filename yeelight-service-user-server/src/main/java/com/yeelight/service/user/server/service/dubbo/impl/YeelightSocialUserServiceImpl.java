/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service.dubbo.impl
 * Description: 用户三方绑定服务实现类
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-06 15:53:15:53
 */
package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.dto.AuthToken;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.exception.NotLoginException;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightSocialUserService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.annotation.UserHintSharding;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.domain.SocialUser;
import com.yeelight.service.user.server.domain.SocialUserAuth;
import com.yeelight.service.user.server.mapper.user.SocialUserAuthMapper;
import com.yeelight.service.user.server.mapper.user.SocialUserMapper;
import com.yeelight.service.user.server.support.UserAssembler;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.support.UserJudge;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.NickNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Desc: 用户三方绑定服务实现类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-05-06 15:53:15:53
 */
@Slf4j
@DubboService(timeout = 5000)
@UserHintSharding
public class YeelightSocialUserServiceImpl implements YeelightSocialUserService {
    @Resource
    private SocialUserMapper socialUserMapper;

    @Resource
    private SocialUserAuthMapper socialUserAuthMapper;

    @Resource
    private TokenService tokenService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @Value("${server.env.region:cn}")
    private String region;

    /**
     * 更新社交用户Token信息。
     * 该方法会根据提供的社交用户ID和认证Token更新数据库中的相关记录。
     *
     * @param socialUserId 社交用户的ID，不能为空。
     * @param authToken 认证Token信息，不能为空。
     * @throws BizException 业务异常，如果更新过程中出现错误则抛出。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialUserTokenInfo(Long socialUserId, AuthToken authToken) throws BizException {
        // 校验参数不能为空
        Assert.notNull(socialUserId, "三方ID不能为空");
        Assert.notNull(authToken, "token信息不能为空");

        // 创建一个新的SocialUser对象，并从authToken复制属性
        SocialUser socialUserSave = new SocialUser();
        BeanUtils.copyPropertiesIgnoreNull(authToken, socialUserSave);

        // 设置ID和更新时间
        socialUserSave.setId(socialUserId);
        socialUserSave.setUpdatedTime((int) Instant.now().getEpochSecond());
        socialUserSave.setLastTokenTime((int) Instant.now().getEpochSecond());

        // 更新数据库中相应记录的选择性属性
        socialUserMapper.updateByPrimaryKeySelective(socialUserSave);

        // 发送业务操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(socialUserId, BizTypeEnums.三方集成.getCode(), "根据三方ID更新三方token信息", socialUserSave);
    }

    /**
     * 更新社交用户Token信息。
     * 该方法用于根据来源（source）和唯一标识符（uuid）更新用户的授权令牌（authToken）信息。
     * 如果对应的社交用户存在，则更新其令牌信息，并记录操作日志。
     *
     * @param source 来源标识，标识用户来自哪个社交平台，不能为空。
     * @param uuid 用户在社交平台的唯一标识符，不能为空。
     * @param authToken 用户的授权令牌信息，不能为空。
     * @throws BizException 业务异常，如果在更新过程中出现错误则抛出。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialUserTokenInfo(String source, String uuid, AuthToken authToken) throws BizException {
        // 将source转换为大写
        source = source.toUpperCase();
        // 参数校验
        Assert.notBlank(source, "三方类型不能为空");
        Assert.notBlank(uuid, "三方UUID不能为空");
        Assert.notNull(authToken, "token信息不能为空");

        // 检查社交用户是否存在
        SocialUser socialUserExist = socialUserMapper.selectSocialUserOne(uuid, source);
        if (Objects.nonNull(socialUserExist)) {
            // 更新社交用户令牌信息
            updateSocialUserTokenInfo(socialUserExist.getId(), authToken);
            // 发送更新业务操作日志
            BizOperateLogUtils.sendUpdateBizOperateLog(uuid, BizTypeEnums.三方集成.getCode(), "根据三方UUID更新三方token信息", authToken);
        }
    }

    /**
     * 根据yeelightId和source查找社交用户。
     *
     * @param yeelightId 用户的yeelightId，用于标识用户。
     * @param source 用户来源平台的标识。
     * @return 如果找到符合条件的社交用户，则返回对应的SocialUserDto对象；否则返回null。
     */
    @Override
    public SocialUserDto findSocialUserBySource(Long yeelightId, String source) {
        // 验证输入参数的有效性
        if (StringUtils.isBlank(source) || Objects.isNull(yeelightId) || !UserJudge.isValidYeelightId(yeelightId)) {
            return null;
        }
        // 将source转换为大写
        source = source.toUpperCase();
        // 根据yeelightId和source查询数据库中的社交用户信息
        SocialUser socialUser = socialUserMapper.selectSocialUserOneByYeelightIdAndSource(yeelightId, source);
        // 将查询到的社交用户信息转换为DTO对象并返回
        return UserConverter.socialUserToDto(yeelightId, socialUser);
    }

    /**
     * 根据来源和UUID查找社交用户。
     *
     * @param uuid 用户的唯一标识符。
     * @param source 用户来源的标识。
     * @return 如果找到符合条件的社交用户，返回其DTO（数据传输对象）；否则返回null。
     */
    @Override
    public SocialUserDto findSocialUserBySourceAndUuid(String uuid, String source) {
        // 验证传入的UUID和source是否为空
        if (StringUtils.isBlank(source) || StringUtils.isBlank(uuid)) {
            return null;
        }
        // 将source转换为大写
        source = source.toUpperCase();
        // 根据UUID和source从数据库中查询社交用户
        SocialUser socialUser = socialUserMapper.selectSocialUserOne(uuid, source);
        if (Objects.isNull(socialUser)) {
            return null;
        }
        // 查询该社交用户对应的用户认证信息
        SocialUserAuth socialUserAuth = socialUserAuthMapper.selectSocialUserAuthOne(socialUser.getId());
        if (Objects.isNull(socialUserAuth)) {
            // 如果没有找到认证信息，只转换社交用户信息为DTO
            return UserConverter.socialUserToDto(null, socialUser);
        }
        // 如果找到了认证信息，将社交用户和认证信息转换为DTO
        return UserConverter.socialUserToDto(socialUserAuth.getUserId(), socialUser);
    }

    @Override
    public List<SocialUserDto> findSocialUsers(Long yeelightId) {
        if (Objects.isNull(yeelightId) || !UserJudge.isValidYeelightId(yeelightId)) {
            return new ArrayList<>();
        }
        // 根据yeelightId查询数据库中的社交用户信息
        return socialUserMapper.selectSocialUsersByYeelightId(yeelightId).stream().filter(Objects::nonNull).map(socialUser -> UserConverter.socialUserToDto(yeelightId, socialUser)).collect(Collectors.toList());
    }

    /**
     * 解绑用户的社交账号。
     * @param yeelightId 用户的yeelightId，用于标识唯一用户。
     * @param source 需要解绑的社交平台来源标识。
     * @throws BizException 业务异常，如果操作失败可能会抛出此异常。
     * @Transactional 注解指明此方法是一个事务方法，任何异常都将回滚。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBindSocialUser(Long yeelightId, String source) throws BizException {
        log.info("解绑三方账号, yeelightId[{}], source:{}", yeelightId, source);
        // 验证输入参数的有效性
        if (StringUtils.isBlank(source) || Objects.isNull(yeelightId) || !UserJudge.isValidYeelightId(yeelightId)) {
            return;
        }
        // 将source转换为大写
        source = source.toUpperCase();
        // 根据yeelightId和source查询对应的社交用户信息
        SocialUser socialUser = socialUserMapper.selectSocialUserOneByYeelightIdAndSource(yeelightId, source);
        // 如果查询到社交用户信息，则进行解绑操作
        if (Objects.nonNull(socialUser)) {
            socialUserMapper.deleteByPrimaryKey(socialUser.getId());
            socialUserAuthMapper.deleteSocialUserAuthOne(yeelightId, socialUser.getId());
            // 发送解绑操作的日志
            BizOperateLogUtils.sendRemoveBizOperateLog(yeelightId, BizTypeEnums.三方集成.getCode(), "解绑三方账号", source);
        }
    }

    /**
     * 根据来源源和令牌查找社交用户。
     *
     * @param token 用户的令牌，用于验证和获取用户信息。
     * @param source 用户的来源，标识不同的社交平台。
     * @return SocialUserDto 社交用户的数据传输对象，包含用户的社交平台信息。
     * @throws BizException 业务异常，可能由于用户验证失败或其他业务规则触发。
     * @throws NotLoginException 用户未登录异常，当用户未在系统中登录时抛出。
     */
    @Override
    public SocialUserDto findSocialUserBySource(String token, String source) throws BizException, NotLoginException {
        // 将source转换为大写
        source = source.toUpperCase();
        // 通过token获取用户的信息
        YeelightUserDto yeelightUser = getYeelightUserDtoByToken(token);
        // 根据用户ID和来源查找社交用户
        return findSocialUserBySource(yeelightUser.getId(), source);
    }

    /**
     * 根据提供的token查找社交用户列表。
     * <p>
     * 这个方法首先会使用提供的token获取一个YeelightUserDto实例，然后使用该用户的id查找并返回社交用户的列表。
     *
     * @param token 用于验证用户身份的token。
     * @return 返回一个包含社交用户信息的列表。
     * @throws BizException 如果业务操作失败则抛出此异常。
     * @throws NotLoginException 如果用户未登录则抛出此异常。
     */
    @Override
    public List<SocialUserDto> findSocialUsers(String token) throws BizException, NotLoginException {
        // 通过token获取用户信息
        YeelightUserDto yeelightUser = getYeelightUserDtoByToken(token);
        // 根据用户id查找社交用户列表
        return findSocialUsers(yeelightUser.getId());
    }

    /**
     * 根据供应商和令牌查找社交用户。
     *
     * @param vendor 供应商代码，表示要查询的社交平台类型。
     * @param token 用于在社交平台上进行身份验证的令牌。
     * @return 返回一个社交用户列表，这些用户属于指定的供应商。
     * @throws BizException 业务异常，可能由于内部业务逻辑错误引发。
     * @throws NotLoginException 用户未登录异常，可能在无法验证用户身份时抛出。
     */
    @Override
    public List<SocialUserDto> findSocialUsersByVendor(String vendor, String token) throws BizException, NotLoginException {
        // 将供应商字符串转换为枚举类型
        UserVendorEnum vendorEnum = UserVendorEnum.getVendorByCode(vendor);

        // 如果是默认供应商，则进行特定的筛选逻辑
        if (UserVendorEnum.DEFAULT.getCode().equals(vendorEnum.getCode())) {
            // 筛选出Yeelight ID有效且来源为默认供应商的社交用户
            return findSocialUsers(token).stream().filter(socialUserDto -> {
                UserVendorEnum userVendorEnum = UserVendorEnum.getVendorByCode(UserVendorHolder.getSimpleVendor(socialUserDto.getSource()));
                return UserVendorEnum.DEFAULT.getCode().equals(userVendorEnum.getCode()) && UserJudge.isValidYeelightId(socialUserDto.getYeelightId());
            }).collect(Collectors.toList());
        }
        // 对于非默认供应商，筛选出来源包含指定供应商代码且Yeelight ID有效的社交用户
        return findSocialUsers(token).stream().filter(socialUserDto -> socialUserDto.getSource().contains(UserVendorHolder.getSimpleVendor(vendorEnum.getCode())) && UserJudge.isValidYeelightId(socialUserDto.getYeelightId())).collect(Collectors.toList());
    }

    /**
     * 根据供应商和用户ID查找社交用户列表。
     *
     * @param vendor 供应商代码，表示要查询的社交平台类型。
     * @param yeelightUserId Yeelight用户ID，用于标识请求的用户。
     * @return 返回一个社交用户列表，这些用户属于指定的供应商。
     * @throws BizException 业务异常，可能由于数据问题或其他业务规则触发。
     * @throws NotLoginException 用户未登录异常，表示用户在尝试操作前未登录。
     */
    @Override
    public List<SocialUserDto> findSocialUsersByVendor(String vendor, Long yeelightUserId) throws BizException, NotLoginException {
        // 将供应商字符串转换为枚举类型
        UserVendorEnum vendorEnum = UserVendorEnum.getVendorByCode(vendor);

        // 如果是默认供应商，则进行特殊处理
        if (UserVendorEnum.DEFAULT.getCode().equals(vendorEnum.getCode())) {
            // 查找所有社交用户，然后筛选出属于默认供应商且Yeelight ID有效的用户
            return findSocialUsers(yeelightUserId).stream().filter(socialUserDto -> {
                UserVendorEnum userVendorEnum = UserVendorEnum.getVendorByCode(UserVendorHolder.getSimpleVendor(socialUserDto.getSource()));
                return UserVendorEnum.DEFAULT.getCode().equals(userVendorEnum.getCode()) && UserJudge.isValidYeelightId(socialUserDto.getYeelightId());
            }).collect(Collectors.toList());
        }
        // 对于非默认供应商，直接筛选出属于该供应商且Yeelight ID有效的用户
        return findSocialUsers(yeelightUserId).stream().filter(socialUserDto -> socialUserDto.getSource().contains(UserVendorHolder.getSimpleVendor(vendorEnum.getCode())) && UserJudge.isValidYeelightId(socialUserDto.getYeelightId())).collect(Collectors.toList());
    }

    /**
     * 解绑社交用户。
     * 该方法通过提供的token找到对应的用户，并解绑指定来源的社交账号。
     *
     * @param token 用户的认证token，用于识别用户身份。
     * @param source 需要解绑的社交平台来源标识。
     * @throws BizException 业务异常，可能由于用户不存在或token无效等原因抛出。
     * @throws NotLoginException 用户未登录异常，当用户未登录时抛出。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBindSocialUser(String token, String source) throws BizException, NotLoginException {
        // 将source转换为大写
        source = source.toUpperCase();
        // 通过token获取用户信息
        YeelightUserDto yeelightUser = getYeelightUserDtoByToken(token);
        // 调用解绑方法，传入用户ID和社交平台来源标识
        unBindSocialUser(yeelightUser.getId(), source);
    }

    /**
     * 自动注册社交用户。
     * 该方法用于处理社交用户（如微信、QQ等）的自动注册或绑定逻辑。根据当前是否登录的用户以及社交用户是否已绑定过用户，
     * 实现注册新用户或绑定已有用户的功能，并处理相应的业务逻辑。
     * Lock key example: service_user:auto_register_social_user:105935915457451364100_GOOGLE
     * Related tables: `yeelight_users` (用户主表) `social_user` (三方用户表) `social_user_auth` (关联表：主表 + 三方用户表)
     *
     * @param authUser 认证用户信息，包含社交平台的用户标识和来源等信息。这允许方法识别和处理特定于社交平台的用户数据。
     * @param currentLoginUserId 当前登录的用户ID，如果用户未登录，则为null。此参数用于区分是新用户注册还是现有用户绑定社交账号。
     * @return 返回绑定或注册后的用户信息。这包括但不限于用户的基本信息，以便于调用方进一步处理。
     * @throws BizException 业务异常，可能由于用户验证或其他业务规则失败导致。这表明在处理过程中遇到了特定的业务错误。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto autoRegisterSocialUser(AuthUser authUser, Long currentLoginUserId) throws BizException {
        log.info("绑定三方账号, authUser[{}], currentLoginUserId:{}", authUser, currentLoginUserId);

        // 检查传入的认证用户信息是否为空
        if (Objects.isNull(authUser)) {
            return null;
        }

        // 构建唯一标识的锁键，用于并发控制
        String lockKey = Constants.AUTO_REGISTER_SOCIAL_USER_LOCK_PREFIX.concat(UserVendorHolder.getVendor()).concat(authUser.getUuid().concat("_").concat(authUser.getSource()));
        RLock rLock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取分布式锁，以确保数据一致性和避免并发问题
            rLock.tryLock(3, 3, TimeUnit.SECONDS);

            // 保存或更新社交用户的信息
            SocialUser socialUser = this.saveSocialUser(authUser);
            Long socialUserId = socialUser.getId();

            // 检查该社交用户是否已被绑定到某个用户
            SocialUserAuth socialUserAuth = socialUserAuthMapper.selectSocialUserAuthOne(socialUserId);
            if (Objects.nonNull(socialUserAuth)) {
                // 如果发现已存在的绑定关系，需先检查绑定的用户是否有效
                YeelightUserDto socialYeelightUser = yeelightUserReadService.findUserById(socialUserAuth.getUserId());
                if (Objects.isNull(socialYeelightUser)) {
                    // 如果绑定的用户无效，则删除这个绑定关系
                    socialUserAuthMapper.deleteByPrimaryKey(socialUserAuth.getId());
                    socialUserAuth = null;
                }
            }

            // 根据用户是否已登录，选择不同的处理逻辑
            if (Objects.nonNull(currentLoginUserId)) {
                // 如果用户已登录，则处理绑定逻辑
                return handleLoggedInUser(currentLoginUserId, socialUser, socialUserAuth);
            } else {
                // 如果用户未登录，则处理注册逻辑
                return handleNotLoggedInUser(authUser, socialUser, socialUserAuth);
            }
        } catch (BizException e) {
            // 捕获并重新抛出业务异常，便于上层处理
            log.error("autoRegisterSocialUser: {} BizException: {}", authUser, ExceptionUtils.getStackTrace(e));
            throw new BizException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            // 捕获其他异常，并抛出业务异常，以统一异常处理
            log.error("autoRegisterSocialUser: {} Exception: {}", authUser, ExceptionUtils.getStackTrace(e));
            throw new BizException(I18nUtil.getMessage("User.Exception.data.verifyFail"));
        } finally {
            // 无论如何都释放锁，确保锁的正确释放，避免死锁
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }


    /**
     * 根据token获取Yeelight用户信息。
     * <p>
     * 通过token从tokenService获取用户名，然后查询yeelightUserReadService以获取用户详细信息。
     * 如果用户不存在或token无效，则抛出相应异常。
     *
     * @param token 用户的token，用于验证用户身份和获取用户名。
     * @return YeelightUserDto 用户的信息对象。
     * @throws BizException 业务异常，可能由于内部逻辑错误引发。
     * @throws NotLoginException 用户未登录异常，当无法根据token获取用户名时抛出。
     */
    private YeelightUserDto getYeelightUserDtoByToken(String token) throws BizException, NotLoginException {
        // 通过token获取用户名
        String userName = tokenService.getUserNameByToken(token);
        if (StringUtils.isBlank(userName)) {
            log.warn("根据token获取用户信息时, 获取不到token[{}]对应的用户信息", token);
            throw new NotLoginException();
        }
        // 根据用户名查询用户信息
        YeelightUserDto yeelightUser = yeelightUserReadService.findUserByUsername(userName);
        Assert.notNull(yeelightUser, "[{}]对应的用户不存在", userName);
        // 检查用户ID的有效性
        UserJudge.checkYeelightId(yeelightUser.getId());
        return yeelightUser;
    }

    /**
     * 保存或更新社交用户信息。
     *
     * @param authUser 认证用户信息，包含用户的唯一标识和来源。
     * @return 返回已保存或更新的社交用户信息。
     */
    private SocialUser saveSocialUser(AuthUser authUser) {
        // 根据用户UUID和来源查询社交用户信息
        SocialUser socialUser = socialUserMapper.selectSocialUserOne(authUser.getUuid(), authUser.getSource());
        // 将认证用户信息转换为社交用户信息
        SocialUser socialUserSave = UserAssembler.authUserToSocialUser(authUser);
        if (Objects.nonNull(socialUser)) {
            // 如果社交用户信息存在，则更新社交用户信息
            BeanUtils.copyPropertiesIgnoreNull(socialUserSave, socialUser);
            // 如果更新了访问令牌，则记录令牌更新时间
            if (StringUtils.isNotBlank(socialUserSave.getAccessToken())) {
                socialUserSave.setLastTokenTime((int) Instant.now().getEpochSecond());
            }
            socialUserMapper.updateByPrimaryKeySelective(socialUser);
            return socialUser;
        } else {
            // 如果社交用户信息不存在，则插入新的社交用户信息
            socialUserSave.setCreatedTime((int) Instant.now().getEpochSecond());
            socialUserSave.setLastTokenTime((int) Instant.now().getEpochSecond());
            socialUserMapper.insertSelective(socialUserSave);
            return socialUserSave;
        }
    }

    /**
     * 处理已登录用户与社交用户认证信息的逻辑。
     *
     * @param currentLoginUserId 当前登录的用户ID。
     * @param socialUser 社交平台的用户信息。
     * @param socialUserAuth 社交用户的认证信息。
     * @return YeelightUserDto 用户数据传输对象，包含用户详细信息。
     * @throws BizException 业务异常。
     */
    private YeelightUserDto handleLoggedInUser(Long currentLoginUserId, SocialUser socialUser, SocialUserAuth socialUserAuth) throws BizException {
        // 检查用户yeelight ID的有效性
        UserJudge.checkYeelightId(currentLoginUserId);

        if (Objects.nonNull(socialUserAuth)) {
            // 社交用户认证信息不为空时的处理逻辑
            if (!currentLoginUserId.equals(socialUserAuth.getUserId())) {
                // 当前登录用户ID与社交用户认证信息中的用户ID不一致时的处理
                YeelightUserDto yeelightUserDto = voteImportantYeelightUser(currentLoginUserId, socialUserAuth.getUserId());
                socialUserAuthMapper.deleteSocialUserAuth(socialUser.getId());
                if (!socialUserAuth.getUserId().equals(yeelightUserDto.getId())) {
                    // 如果新的用户ID与旧的不一致，则重新插入社交用户认证信息
                    this.insertSocialUserAuth(socialUser, yeelightUserDto.getId());
                }
                // 添加业务操作日志
                BizOperateLogUtils.sendAddBizOperateLog(currentLoginUserId, BizTypeEnums.三方集成.getCode(), "已登录用户，已绑定三方，删除原绑定记录，并重新绑定", socialUser);
                return yeelightUserDto;
            } else {
                // 当前登录用户ID与社交用户认证信息中的用户ID一致时的处理
                YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(currentLoginUserId);
                Assert.notNull(yeelightUser, "当前登录的账号[{}]不存在", currentLoginUserId);
                // 添加业务操作日志
                BizOperateLogUtils.sendAddBizOperateLog(currentLoginUserId, BizTypeEnums.三方集成.getCode(), "已登录用户，已绑定三方，且绑定的是当前登录用户", socialUser);
                return yeelightUser;
            }
        } else {
            // 社交用户认证信息为空时的处理逻辑
            YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(currentLoginUserId);
            Assert.notNull(yeelightUser, "当前登录的账号[{}]不存在", currentLoginUserId);
            // 插入新的社交用户认证信息
            this.insertSocialUserAuth(socialUser, currentLoginUserId);
            // 添加业务操作日志
            BizOperateLogUtils.sendAddBizOperateLog(currentLoginUserId, BizTypeEnums.三方集成.getCode(), "已登录用户，未绑定三方，进行三方绑定", socialUser);
            return yeelightUser;
        }
    }

    /**
     * 处理未登录用户的身份绑定逻辑。
     *
     * @param authUser 认证用户信息，包含用户认证相关的数据。
     * @param socialUser 社交用户信息，例如从微信、QQ等社交平台获取的用户信息。
     * @param socialUserAuth 社交用户认证信息，具体是哪个社交平台的认证信息。
     * @return 返回绑定后的用户信息，可能是新创建的也可能是已存在的用户信息。
     * @throws BizException 业务异常，处理过程中可能会抛出的异常。
     */
    private YeelightUserDto handleNotLoggedInUser(AuthUser authUser, SocialUser socialUser, SocialUserAuth socialUserAuth) throws BizException {
        // 判断社交用户认证信息是否存在
        if (Objects.nonNull(socialUserAuth)) {
            // 根据社交用户认证信息查找已存在的用户
            YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(socialUserAuth.getUserId());
            // 用户不存在时，创建新用户并绑定社交账号
            if (Objects.isNull(yeelightUser)) {
                YeelightUserDto newYeelightUserDto = this.createOrGetYeelightUser(authUser);
                socialUserAuthMapper.deleteSocialUserAuthOne(socialUserAuth.getUserId(), socialUser.getId());
                this.insertSocialUserAuth(socialUser, newYeelightUserDto.getId());
                // 记录操作日志
                BizOperateLogUtils.sendAddBizOperateLog(null, BizTypeEnums.三方集成.getCode(), "未登录用户，三方已绑定，但用户不存在", socialUser);
                return newYeelightUserDto;
            } else {
                // 用户已存在时，检查手机号是否变化，若变化则重新绑定
                if (StringUtils.isNotBlank(authUser.getPhone()) && !StringUtils.equals(authUser.getPhone(), yeelightUser.getPhoneNumber())) {
                    YeelightUserDto existYeelightUserDto = this.createOrGetYeelightUser(authUser);
                    socialUserAuthMapper.deleteSocialUserAuthOne(socialUserAuth.getUserId(), socialUser.getId());
                    this.insertSocialUserAuth(socialUser, existYeelightUserDto.getId());
                    // 记录操作日志，特殊逻辑处理微信小程序商城端手机号更改
                    BizOperateLogUtils.sendAddBizOperateLog(null, BizTypeEnums.三方集成.getCode(), "未登录用户，三方已绑定，兼容 微信小程序商城端手机号更改的特殊逻辑", socialUser);
                    return existYeelightUserDto;
                }
                return yeelightUser;
            }
        } else {
            // 社交用户认证信息不存在时，直接创建新用户并绑定社交账号
            YeelightUserDto yeelightUserDto = this.createOrGetYeelightUser(authUser);
            this.insertSocialUserAuth(socialUser, yeelightUserDto.getId());
            // 记录操作日志
            BizOperateLogUtils.sendAddBizOperateLog(null, BizTypeEnums.三方集成.getCode(), "未登录用户，三方未绑定，执行创建用户并绑定三方操作", socialUser);
            return yeelightUserDto;
        }
    }

    /**
     * 获取Yeelight用户ID
     * <p>
     * 根据提供的AuthUser对象，尝试通过邮箱、电话号码或第三方平台联合ID查找或创建Yeelight用户。
     * 如果找到已存在的有效用户，返回该用户；否则，创建新用户并返回。
     * `username` example: 105935915457451364100_GOOGLE
     * @param authUser 认证用户对象，包含用户认证所需的信息，如邮箱、电话号码、第三方平台信息等。
     * @return 返回Yeelight用户信息DTO，包含用户的详细信息。
     */
    private YeelightUserDto createOrGetYeelightUser(AuthUser authUser) {
        // 邮箱存在的情况
        if (StringUtils.isNotBlank(authUser.getEmail())) {
            YeelightUserDto yeelightUser = yeelightUserReadService.findUserByEmail(authUser.getEmail());
            if (Objects.nonNull(yeelightUser) && UserJudge.isValidYeelightId(yeelightUser.getId())) {
                return yeelightUser;
            }
        }
        // 电话号码存在的情况
        if (StringUtils.isNotBlank(authUser.getPhone())) {
            YeelightUserDto yeelightUser = yeelightUserReadService.findUserByPhoneNumber(authUser.getPhone());
            if (Objects.nonNull(yeelightUser) && UserJudge.isValidYeelightId(yeelightUser.getId())) {
                return yeelightUser;
            }
        }
        // 联合ID存在的情况
        if (Objects.nonNull(authUser.getToken()) && StringUtils.isNotBlank(authUser.getToken().getUnionId())) {
            SocialUser relationSocialUser = socialUserMapper.selectSocialUserOneByUnionId(authUser.getToken().getUnionId());
            if (Objects.nonNull(relationSocialUser) && !StringUtils.equals(relationSocialUser.getUuid(), authUser.getToken().getOpenId())) {
                SocialUserAuth relationSocialUserAuth = socialUserAuthMapper.selectSocialUserAuthOne(relationSocialUser.getId());
                if (Objects.nonNull(relationSocialUserAuth)) {
                    YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(relationSocialUserAuth.getUserId());
                    if (Objects.nonNull(yeelightUser) && UserJudge.isValidYeelightId(yeelightUser.getId())) {
                        return yeelightUser;
                    }
                }
            }
        }

        // 创建新用户
        String username = UserUtils.randomStr(6).concat("_").concat(authUser.getSource());
        String password = UserUtils.passwordEncoder().encode(UserUtils.saltEncoder());
        String nickname = StringUtils.isNotBlank(authUser.getNickname()) ? authUser.getNickname() : NickNameGenerator.generate(region, UserVendorHolder.getVendorEnum());
        String avatar = StringUtils.isNotBlank(authUser.getAvatar()) ? authUser.getAvatar() : Constants.DEFAULT_AVATAR;
        String email = StringUtils.isNotBlank(authUser.getEmail()) ? authUser.getEmail() : null;
        String phoneNumber = StringUtils.isNotBlank(authUser.getPhone()) ? authUser.getPhone() : null;

        // 构建创建用户请求
        CreateUserRequest createUserRequest = CreateUserRequest.builder()
                .username(username)
                .password(password)
                .name(nickname)
                .avatar(avatar)
                .email(email)
                .phoneNumber(phoneNumber)
                .build();
        // 创建新用户
        Long yeelightId = yeelightUserWriteService.create(createUserRequest);
        UserJudge.checkYeelightId(yeelightId);

        // 返回新创建的用户
        return yeelightUserReadService.findUserById(yeelightId);
    }

    /**
     * 将社交用户认证信息插入到数据库中。
     * 如果该认证信息已经存在，则更新原有信息；如果不存在，则插入新信息。
     *
     * @param socialUser 社交用户信息，包含用户的认证详情。
     * @param yeelightUserId 用户在Yeelight平台的用户ID。
     */
    private void insertSocialUserAuth(SocialUser socialUser, Long yeelightUserId) {
        // 验证Yeelight用户ID的有效性
        UserJudge.checkYeelightId(yeelightUserId);

        // 如果社交用户信息为null，则直接返回，不执行后续操作
        if (Objects.isNull(socialUser)) {
            return;
        }

        // 检查数据库中是否已经存在相同的社交来源认证信息
        SocialUserAuth socialUserAuthExist = socialUserAuthMapper.selectSocialUserAuthsWithYeelightIdAndSource(yeelightUserId, socialUser.getSource());

        // 如果已经存在，则更新该认证信息；如果不存在，则插入新认证信息
        if (Objects.nonNull(socialUserAuthExist)) {
            socialUserAuthExist.setSocialUserId(socialUser.getId());
            socialUserAuthMapper.updateByPrimaryKeySelective(socialUserAuthExist);
        } else {
            // 构建新的社交用户认证信息，并插入到数据库中
            SocialUserAuth socialUserAuthInsert = SocialUserAuth.builder()
                    .socialUserId(socialUser.getId())
                    .userId(yeelightUserId)
                    .build();
            socialUserAuthMapper.insertSelective(socialUserAuthInsert);
        }

    }

    /**
     * 对重要的Yeelight用户进行投票选择。
     *
     * @param currentLoginUserId 当前登录用户的ID，如果为null，则只进行查询不进行比较。
     * @param userId 要投票的用户ID。
     * @return YeelightUserDto 被选中的用户信息。
     * @throws BizException 如果用户ID为空，用户不存在，或比较逻辑出现错误时抛出。
     */
    private YeelightUserDto voteImportantYeelightUser(Long currentLoginUserId, Long userId) throws BizException {
        // 确保被投票用户的ID不为空
        Assert.notNull(userId, "三方关联的用户ID不能为空");
        // 检查用户ID的有效性（例如是否为合法的Yeelight用户ID）
        UserJudge.checkYeelightId(userId);

        if (Objects.isNull(currentLoginUserId)) {
            // 如果没有登录用户，只查询并返回被投票的用户信息
            YeelightUserDto yeelightUser = yeelightUserReadService.findUserById(userId);
            Assert.notNull(yeelightUser, "三方关联的用户[{}]不存在", userId);
            return yeelightUser;
        } else {
            // 如果有登录用户，查询并比较登录用户和被投票用户的完成度分数
            YeelightUserDto currentYeelightUser = yeelightUserReadService.findUserById(currentLoginUserId);
            Assert.notNull(currentYeelightUser, "当前登录的账号[{}]不存在", currentLoginUserId);
            YeelightUserDto otherYeelightUser = yeelightUserReadService.findUserById(userId);
            Assert.notNull(otherYeelightUser, "三方关联的用户[{}]不存在", userId);

            // 根据完成度分数决定返回哪个用户的信息
            if (UserConverter.getUserFinishScore(currentYeelightUser) >= UserConverter.getUserFinishScore(otherYeelightUser)) {
                return currentYeelightUser;
            } else {
                return otherYeelightUser;
            }
        }
    }
}
