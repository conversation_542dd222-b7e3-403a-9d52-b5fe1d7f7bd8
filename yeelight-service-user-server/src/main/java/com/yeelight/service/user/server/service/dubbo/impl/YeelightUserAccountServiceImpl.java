package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.framework.service.impl.BaseServiceImpl;
import com.yeelight.service.user.client.domain.YeelightUserAccount;
import com.yeelight.service.user.client.domain.YeelightUserAccountExample;
import com.yeelight.service.user.client.service.YeelightUserAccountService;
import com.yeelight.service.user.server.mapper.user.YeelightUserAccountMapper;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

/**
 * <AUTHOR>
 */
@DubboService
public class YeelightUserAccountServiceImpl extends BaseServiceImpl<YeelightUserAccountMapper, YeelightUserAccount, YeelightUserAccountExample> implements YeelightUserAccountService {
    @Override
    protected Weekend<YeelightUserAccount> exportWeekend(YeelightUserAccountExample example) {
        Weekend<YeelightUserAccount> weekend = Weekend.of(YeelightUserAccount.class);
        WeekendCriteria<YeelightUserAccount, Object> criteria = weekend.weekendCriteria();
        if (null == example) {
            return weekend;
        }
        if (null != example.getYeelightUserId()) {
            criteria.andEqualTo(YeelightUserAccount::getYeelightUserId, example.getYeelightUserId());
        }
        return weekend;
    }

}
