package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.user.client.domain.YeelightUserBank;
import com.yeelight.service.user.client.domain.YeelightUserBankExample;
import com.yeelight.service.user.client.service.YeelightUserBankService;
import com.yeelight.service.user.server.mapper.user.YeelightUserBankMapper;
import com.yeelight.service.framework.service.impl.BaseServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class YeelightUserBankServiceImpl extends BaseServiceImpl<YeelightUserBankMapper, YeelightUserBank, YeelightUserBankExample> implements YeelightUserBankService {

    @Resource
    private YeelightUserBankMapper mapper;

    /**
     * 根据提供的银行卡ID列表，删除对应的银行卡记录。
     *
     * @param bankCardIds 银行卡ID的列表，类型为List<Long>。这是要删除的银行卡记录的ID。
     *                    如果列表为空或null，则不执行任何删除操作。
     */
    @Override
    public void deleteByIds(List<Long> bankCardIds) {
        // 当银行卡ID列表非空时，遍历列表并为每个ID调用删除操作
        if (CollectionUtils.isNotEmpty(bankCardIds)) {
            bankCardIds.forEach(bankCardId -> mapper.deleteByPrimaryKey(bankCardId));
        }
    }


    @Override
    protected Weekend<YeelightUserBank> exportWeekend(YeelightUserBankExample example) {
        Weekend<YeelightUserBank> weekend = Weekend.of(YeelightUserBank.class);
        WeekendCriteria<YeelightUserBank, Object> criteria = weekend.weekendCriteria();
        if (null == example) {
            return weekend;
        }
        if (null != example.getYeelightUserId()) {
            criteria.andEqualTo(YeelightUserBank::getYeelightUserId, example.getYeelightUserId());
        }
        if (null != example.getIsDefault()) {
            criteria.andEqualTo(YeelightUserBank::getIsDefault, example.getIsDefault());
        }
        if (null != example.getType()) {
            criteria.andEqualTo(YeelightUserBank::getType, example.getType());
        }
        if (StringUtils.isNotBlank(example.getBankName())) {
            criteria.andLike(YeelightUserBank::getBankName, '%' + example.getBankName() + '%');
        }
        if (StringUtils.isNotBlank(example.getUserName())) {
            criteria.andLike(YeelightUserBank::getUserName, '%' + example.getUserName() + '%');
        }
        if (StringUtils.isNotBlank(example.getCardNo())) {
            criteria.andEqualTo(YeelightUserBank::getCardNo, example.getCardNo());
        }
        if (CollectionUtils.isNotEmpty(example.getIds())) {
            criteria.andIn(YeelightUserBank::getId, example.getIds());
        }
        return weekend;
    }

}
