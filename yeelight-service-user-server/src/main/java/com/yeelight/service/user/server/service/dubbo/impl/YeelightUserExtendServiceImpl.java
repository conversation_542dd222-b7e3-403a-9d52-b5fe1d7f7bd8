package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.user.client.domain.YeelightUserExtend;
import com.yeelight.service.user.client.domain.YeelightUserExtendExample;
import com.yeelight.service.user.client.dto.YeelightUserExtendDto;
import com.yeelight.service.user.client.service.YeelightUsersExtendService;
import com.yeelight.service.user.server.mapper.user.YeelightUserExtendMapper;
import com.yeelight.service.framework.service.impl.BaseServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DubboService
public class YeelightUserExtendServiceImpl extends BaseServiceImpl<YeelightUserExtendMapper, YeelightUserExtend, YeelightUserExtendExample> implements YeelightUsersExtendService {

    @Resource
    private YeelightUserExtendMapper yeelightUsersExtendMapper;

    @Override
    public List<YeelightUserExtendDto> selectByExample(YeelightUserExtendDto example) {
        List<YeelightUserExtend> yeelightUsersExtend = yeelightUsersExtendMapper.selectByExample(exportWeekend(example));
        return yeelightUsersExtend.stream().map(userExtend -> {
            YeelightUserExtendDto yeelightUserExtendDto = new YeelightUserExtendDto();
            BeanUtils.copyProperties(userExtend, yeelightUserExtendDto);
            return yeelightUserExtendDto;
        }).collect(Collectors.toList());
    }

    @Override
    public YeelightUserExtendDto selectOneByExample(YeelightUserExtendExample example) {
        YeelightUserExtend userExtend = selectOne(example);
        if (Objects.isNull(userExtend)) {
            return null;
        }
        YeelightUserExtendDto userExtendDto = new YeelightUserExtendDto();
        BeanUtils.copyProperties(userExtend, userExtendDto);
        return userExtendDto;
    }

    /**
     * 插入一个新的用户扩展信息到数据库。
     *
     * @param userExtendDto 用户扩展信息的数据传输对象，包含将要插入的数据。
     *                      如果传入的对象为null，则不执行插入操作。
     */
    @Override
    public void insert(YeelightUserExtendDto userExtendDto) {
        // 创建一个用户扩展信息实例
        YeelightUserExtend usersExtend = new YeelightUserExtend();
        // 判断传入的DTO是否为空，不为空则将DTO的属性复制到实体类中
        if (userExtendDto != null) {
            BeanUtils.copyProperties(userExtendDto, usersExtend);
        }
        // 调用mapper层执行插入操作
        yeelightUsersExtendMapper.insert(usersExtend);
    }

    private Weekend<YeelightUserExtend> exportWeekend(YeelightUserExtendDto example) {
        Weekend<YeelightUserExtend> weekend = Weekend.of(YeelightUserExtend.class);
        WeekendCriteria<YeelightUserExtend, Object> criteria = weekend.weekendCriteria();
        if (example.getId() != null) {
            criteria.andEqualTo(YeelightUserExtend::getId, example.getId());
        }
        if (example.getYeelightUserId() != null) {
            criteria.andEqualTo(YeelightUserExtend::getYeelightUserId, example.getYeelightUserId());
        }
        return weekend;
    }

    /**
     * 根据unionId更新用户扩展信息。
     *
     * @param extend 用户扩展信息对象，包含需要更新的数据。
     * @param unionId 用户的唯一标识符。
     * @return 总是返回null，表示没有具体的返回值。
     */
    @Override
    public Void updateByUnionId(YeelightUserExtend extend, Long unionId) {
        // 当传入的扩展信息对象和unionId都不为null时，执行更新操作
        if (null != extend && null != unionId) {
            // 构造查询条件并更新满足条件的所有记录
            yeelightUsersExtendMapper.updateByExampleSelective(extend, exportWeekend(YeelightUserExtendExample.builder().yeelightUserId(unionId).build()));
        }
        return null;
    }

    @Override
    protected Weekend<YeelightUserExtend> exportWeekend(YeelightUserExtendExample example) {
        Weekend<YeelightUserExtend> weekend = Weekend.of(YeelightUserExtend.class);
        WeekendCriteria<YeelightUserExtend, Object> criteria = weekend.weekendCriteria();
        if (null != example.getId()) {
            criteria.andEqualTo(YeelightUserExtend::getId, example.getId());
        }
        if (null != example.getYeelightUserId()) {
            criteria.andEqualTo(YeelightUserExtend::getYeelightUserId, example.getYeelightUserId());
        }
        if (StringUtils.isNotBlank(example.getBirthday())) {
            criteria.andEqualTo(YeelightUserExtend::getBirthday, example.getBirthday());
        }
        if (StringUtils.isNotBlank(example.getProvinceId())) {
            criteria.andEqualTo(YeelightUserExtend::getProvinceId, example.getProvinceId());
        }
        if (StringUtils.isNotBlank(example.getCityId())) {
            criteria.andEqualTo(YeelightUserExtend::getCityId, example.getCityId());
        }
        if (StringUtils.isNotBlank(example.getRegionId())) {
            criteria.andEqualTo(YeelightUserExtend::getRegionId, example.getRegionId());
        }
        if (StringUtils.isNotBlank(example.getIdCard())) {
            criteria.andLike(YeelightUserExtend::getIdCard, '%' + example.getIdCard() + '%');
        }
        if (StringUtils.isNotBlank(example.getProvinceName())) {
            criteria.andLike(YeelightUserExtend::getProvinceName, '%' + example.getProvinceName() + '%');
        }
        if (StringUtils.isNotBlank(example.getCityName())) {
            criteria.andLike(YeelightUserExtend::getCityName, '%' + example.getCityName() + '%');
        }
        if (StringUtils.isNotBlank(example.getRegionName())) {
            criteria.andLike(YeelightUserExtend::getRegionName, '%' + example.getRegionName() + '%');
        }
        if (StringUtils.isNotBlank(example.getIdCard())) {
            criteria.andEqualTo(YeelightUserExtend::getIdCard, example.getIdCard());
        }
        if (null != example.getNotEqualUnionId()) {
            criteria.andNotEqualTo(YeelightUserExtend::getYeelightUserId, example.getNotEqualUnionId());
        }
        return weekend;
    }

}
