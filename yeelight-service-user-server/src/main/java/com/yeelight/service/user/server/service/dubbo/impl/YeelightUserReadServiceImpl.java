/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service.dubbo.impl
 * Description: 用户读服务
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-29 16:36:16:36
 */
package com.yeelight.service.user.server.service.dubbo.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.framework.util.PartitionTemplate;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.UserMonitoringProperty;
import com.yeelight.service.user.client.domain.YeelightUserEntity;
import com.yeelight.service.user.client.domain.YeelightUserExample;
import com.yeelight.service.user.client.dto.MonitoringYeelightUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.query.YeelightUserQuery;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.annotation.UserHintSharding;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.domain.YeelightUser;
import com.yeelight.service.user.server.mapper.user.YeelightUserMapper;
import com.yeelight.service.user.server.support.UserJudge;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Desc: 用户读服务
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-29 16:36:16:36
 */
@Slf4j
@DubboService(timeout = 5000)
@UserHintSharding
public class YeelightUserReadServiceImpl implements YeelightUserReadService {
    @Resource
    private YeelightUserMapper yeelightUserMapper;

    @Resource
    private TokenService tokenService;

    /**
     * 分页查询Yeelight用户信息。
     *
     * @param query 分页查询条件
     * @return 返回分页查询结果集，包含总数和当前页的数据列表。
     */
    @Override
    public PageResultSet<YeelightUserDto> page(YeelightUserQuery query) {
        // 初始化分页结果集对象
        PageResultSet<YeelightUserDto> dtoPageResultSet = new PageResultSet<>();
        // 使用PageHelper进行分页，设置当前页和每页大小
        Page page = PageHelper.startPage(query.getPageNo(), query.getPageSize());
        // 根据查询条件执行分页查询
        List<YeelightUser> userList = yeelightUserMapper.selectByExample(exportExample(query));
        // 设置查询结果的总条数
        dtoPageResultSet.setTotal(page.getTotal());
        // 如果查询结果为空，直接返回空的分页结果集
        if (CollectionUtils.isEmpty(userList)) {
            return dtoPageResultSet;
        }
        // 将用户实体列表转换为用户DTO列表，并设置到分页结果集中
        dtoPageResultSet.setRows(BeanUtils.listToBean(userList, YeelightUserDto.class));
        return dtoPageResultSet;
    }


    /**
     * 获取所有有效的Yeelight用户列表。
     * 此方法通过查询数据库中的所有用户，然后筛选出具有有效Yeelight ID的用户，将这些用户转换为YeelightUserDto对象的列表返回。
     *
     * @return 返回一个包含所有有效Yeelight用户信息的列表，列表中的每个元素都是一个 YeelightUserDto 对象。
     */
    @Override
    public List<YeelightUserDto> list() {
        // 从数据库中选择所有用户，过滤出具有有效Yeelight ID的用户，然后将这些用户转换为YeelightUserDto列表
        return BeanUtils.listToBean(yeelightUserMapper.selectAll().stream().filter(user -> UserJudge.isValidYeelightId(user.getId())).collect(Collectors.toList()), YeelightUserDto.class);
    }

    /**
     * 根据用户名查找用户信息。
     *
     * @param username 要查找的用户名。
     * @return YeelightUserDto 用户信息的DTO（数据传输对象）实例，如果找到匹配的用户。
     */
    @Override
    public YeelightUserDto findUserByUsername(String username) {
        // 通过用户名从数据库中查找用户，然后将用户对象转换为DTO
        return UserConverter.yeelightUserToDto(yeelightUserMapper.findUserByUsername(username));
    }

    /**
     * 根据用户ID查找用户信息。
     *
     * @param id 用户的唯一标识符。
     * @return 返回转换后的用户信息DTO（数据传输对象）。
     */
    @Override
    public YeelightUserDto findUserById(Long id) {
        // 通过ID查找用户，并将用户对象转换为DTO
        return UserConverter.yeelightUserToDto(yeelightUserMapper.findUserById(id));
    }

    /**
     * 根据ID列表查找用户信息。
     *
     * @param ids 用户ID的列表，类型为Long。这些ID被认为是有效的Yeelight用户ID。
     * @return 返回一个 YeelightUserDto 的列表，对应传入ID的用户信息。如果传入ID列表为空或无效ID，则返回空列表。
     */
    @Override
    public List<YeelightUserDto> findUserByIds(List<Long> ids) {
        // 检查传入的ID列表是否为空，如果为空则直接返回空列表
        if(CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        // 过滤掉无效的Yeelight用户ID
        ids = ids.stream().filter(UserJudge::isValidYeelightId).collect(Collectors.toList());
        // 如果过滤后的ID列表为空，则返回空列表
        if(CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        // 分批处理ID列表，以不超过200个ID为一批，查询用户信息
        List<YeelightUser> yeelightUsers = PartitionTemplate.partitionRead(ids, oneBatchIds -> yeelightUserMapper.findUserByIds(oneBatchIds), 200);
        // 将查询到的用户信息转换为DTO格式，并收集到列表中返回
        return yeelightUsers.stream().map(UserConverter::yeelightUserToDto).collect(Collectors.toList());
    }


    /**
     * 根据电话号码查找用户。
     *
     * @param phoneNumber 用户的电话号码。
     * @return YeelightUserDto 用户的数据传输对象，如果找到匹配的用户。
     */
    @Override
    public YeelightUserDto findUserByPhoneNumber(String phoneNumber) {
        // 通过电话号码查找用户，并将用户实体转换为Dto
        return UserConverter.yeelightUserToDto(yeelightUserMapper.findUserByPhoneNumber(phoneNumber));
    }

    /**
     * 根据电子邮件地址查找用户。
     *
     * @param email 要查找的用户的电子邮件地址，不能为空。
     * @return 如果找到匹配的用户，则返回对应的用户信息Dto；如果没有找到，则返回null。
     */
    @Override
    public YeelightUserDto findUserByEmail(String email) {
        // 通过电子邮件地址从数据库中查找用户
        YeelightUser yeelightUser = yeelightUserMapper.findUserByEmail(email);
        // 将查找到的用户实体转换为用户信息Dto
        return UserConverter.yeelightUserToDto(yeelightUser);
    }

    /**
     * 根据账户信息查询用户。该方法首先尝试将账户信息解析为邮箱、电话号码、用户名或用户ID，
     * 然后分别尝试查找对应的用户信息。
     *
     * @param account 账户信息，可以是邮箱、电话号码、用户名或用户ID。
     * @return 返回匹配的用户信息，如果没有找到匹配的用户，则返回null。
     */
    @Override
    public YeelightUserDto findUserByAccount(String account) {
        YeelightUserDto yeelightUser = null;

        // 尝试通过邮箱查找用户
        if (UserUtils.isEmail(account)) {
            yeelightUser = findUserByEmail(account);
        }
        // 尝试通过电话号码查找用户
        if (UserUtils.isPhoneNumber(account)) {
            yeelightUser = findUserByPhoneNumber(account);
        }
        if (Objects.isNull(yeelightUser)) {
            // 尝试通过id查找用户
            if (UserUtils.isNumber(account)) {
                yeelightUser = findUserById(Long.valueOf(account));
            }
            if (Objects.isNull(yeelightUser)) {
                // 尝试通过用户名查找用户
                yeelightUser = findUserByUsername(account);
            }
        }
        return yeelightUser;
    }

    /**
     * 获取无效的ID集合。
     * 该方法会从提供的ID集合中，去除掉有效的ID，返回剩下的无效ID集合。
     *
     * @param ids 需要验证的ID集合，类型为Long的Set集合。
     * @return 返回一个Set集合，包含不在有效ID列表中的所有ID。
     */
    @Override
    public Set<Long> notValidIds(Set<Long> ids) {
        // 初始化一个集合，用于存放所有提供的ID
        Set<Long> notValidIds = new HashSet<>(ids);

        // 获取有效的ID集合
        Set<Long> validIds = yeelightUserMapper.getValidIds(ids.stream().filter(UserJudge::isValidYeelightId).collect(Collectors.toSet()));

        // 从原始ID集合中去除有效的ID，得到无效的ID集合
        notValidIds.removeAll(validIds);

        return notValidIds;
    }

    /**
     * 根据给定的例子查找用户。
     *
     * @param example 用于查询条件的示例对象，封装了多种可能的查询条件。
     * @return 返回一个用户实体列表，这些实体满足查询条件并且具有有效的Yeelight ID。
     */
    @Override
    public List<YeelightUserEntity> findUser(YeelightUserExample example) {
        // 从数据库中查询满足条件的所有用户，包括扩展信息
        List<YeelightUserEntity> users = yeelightUserMapper.findUserAndExtend(example);

        // 过滤出具有有效Yeelight ID的用户，并收集到一个新的列表中
        return users.stream().filter(user -> UserJudge.isValidYeelightId(user.getId())).collect(Collectors.toList());
    }

    /**
     * 获取所有监控用户的列表。
     * 该方法首先从tokenService中获取所有用户的监控属性列表，然后根据这些属性去查询用户信息。
     * 最后，将用户信息和监控属性组合成 MonitoringYeelightUserDto 并返回其列表。
     *
     * @return List<MonitoringYeelightUserDto> 包含所有监控用户及其属性的列表。
     */
    @Override
    public List<MonitoringYeelightUserDto> getAllMonitoringUser() {
        // 从token服务获取所有用户的监控属性
        List<UserMonitoringProperty> userMonitoringProperties = tokenService.getAllUserMonitoring();

        // 如果没有监控属性，则直接返回空列表
        if (CollectionUtils.isEmpty(userMonitoringProperties)) {
            return new ArrayList<>();
        }

        // 根据监控属性中的用户ID列表查询用户信息
        List<YeelightUserDto> userDtos = findUserByIds(userMonitoringProperties.stream().map(UserMonitoringProperty::getUserId).collect(Collectors.toList()));

        // 将用户信息和监控属性映射并转换成 MonitoringYeelightUserDto 对象
        return userDtos.stream().map(user -> {
            MonitoringYeelightUserDto monitoringYeelightUserDto = new MonitoringYeelightUserDto();
            // 复制用户信息到监控用户DTO，忽略空值
            BeanUtils.copyPropertiesIgnoreNull(user, monitoringYeelightUserDto);
            // 查找当前用户对应的监控属性，并设置到DTO中
            UserMonitoringProperty userMonitoringProperty = userMonitoringProperties.stream().filter(property -> property.getUserId().equals(user.getId())).findFirst().orElse(null);
            if (Objects.nonNull(userMonitoringProperty)) {
                // 设置最后设置时间
                monitoringYeelightUserDto.setLastSetTime(userMonitoringProperty.getLastSetTime());
                // 设置每分钟限制
                monitoringYeelightUserDto.setLimitPerMinute(userMonitoringProperty.getLimitPerMinute());
            }
            return monitoringYeelightUserDto;
        }).collect(Collectors.toList());
    }

    private Weekend<YeelightUser> exportExample(YeelightUserQuery query) {
        Weekend<YeelightUser> weekend = Weekend.of(YeelightUser.class);
        WeekendCriteria<YeelightUser, Object> criteria = weekend.weekendCriteria();
        if (null == query) {
            return weekend;
        }
        if (Objects.nonNull(query.getId())) {
            criteria.andEqualTo(YeelightUser::getId, query.getId());
        }
        if (StringUtils.isNotBlank(query.getUsername())) {
            criteria.andEqualTo(YeelightUser::getUsername, query.getUsername());
        }
        if (StringUtils.isNotBlank(query.getPhoneNumber())) {
            criteria.andEqualTo(YeelightUser::getPhoneNumber, query.getPhoneNumber());
        }
        if (StringUtils.isNotBlank(query.getEmail())) {
            criteria.andEqualTo(YeelightUser::getEmail, query.getEmail());
        }
        if (StringUtils.isNotBlank(query.getName())) {
            criteria.andEqualTo(YeelightUser::getName, query.getName());
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            criteria.andEqualTo(YeelightUser::getStatus, query.getStatus());
        }
        if (StringUtils.isNotBlank(query.getUsernameLike())) {
            criteria.andLike(YeelightUser::getUsername, '%' + query.getUsernameLike() + '%');
        }
        if (StringUtils.isNotBlank(query.getPhoneNumberLike())) {
            criteria.andLike(YeelightUser::getPhoneNumber, '%' + query.getPhoneNumberLike() + '%');
        }
        if (StringUtils.isNotBlank(query.getEmailLike())) {
            criteria.andLike(YeelightUser::getEmail, '%' + query.getEmailLike() + '%');
        }
        if (StringUtils.isNotBlank(query.getNameLike())) {
            criteria.andLike(YeelightUser::getName, '%' + query.getNameLike() + '%');
        }
        return weekend;
    }
}
