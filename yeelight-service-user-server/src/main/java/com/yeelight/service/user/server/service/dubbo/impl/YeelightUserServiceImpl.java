package com.yeelight.service.user.server.service.dubbo.impl;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.YeelightUserEntity;
import com.yeelight.service.user.client.domain.YeelightUserExample;
import com.yeelight.service.user.client.dto.*;
import com.yeelight.service.user.client.exception.NotLoginException;
import com.yeelight.service.user.client.query.YeelightUserQuery;
import com.yeelight.service.user.client.service.*;
import com.yeelight.service.user.server.annotation.UserHintSharding;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.support.UserJudge;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * @program: yeelight-service-user
 * @description: 用户服务实现类
 * @author: Sheldon
 * @create: 2019-06-20 17:48
 **/
@Slf4j
@DubboService(timeout = 2000)
@UserHintSharding
@Deprecated
public class YeelightUserServiceImpl implements YeelightUserService {
    @Resource
    private TokenService tokenService;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private YeelightUserWriteService yeelightUserWriteService;

    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private JustAuthUserDetailsService justAuthUserDetailsService;

    @Override
    public PageResultSet<YeelightUserDto> page(YeelightUserQuery query) {
        return yeelightUserReadService.page(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result insert(YeelightUserDto yeelightUserDto)  throws BizException {
        try {
            yeelightUserWriteService.create(UserConverter.dtoToAddRequest(yeelightUserDto));
            return Result.success();
        } catch (BizException e) {
            return Result.failure(e.getCode(), e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result update(YeelightUserDto yeelightUserDto) throws BizException {
        try {
            this.updateThrows(yeelightUserDto);
            return Result.success();
        } catch (BizException e) {
            return Result.failure(e.getCode(), e.getMessage());
        }
    }

    /**
     * 更新用户信息。该方法会检查待更新的用户信息是否为空，用户是否存在，并根据传入的用户信息更新用户名、电话号码、
     * 邮箱、姓名和头像。如果相应的信息没有变化，则不会进行更新操作。
     *
     * @param yeelightUserDto 包含待更新用户信息的对象，不能为空。
     * @throws BizException 如果更新过程中出现业务错误，会抛出此异常。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateThrows(YeelightUserDto yeelightUserDto) throws BizException {
        // 确保传入的用户信息对象和ID不能为空
        Assert.notNull(yeelightUserDto, "用户信息不能为空");
        Assert.notNull(yeelightUserDto.getId(), "用户ID不能为空");

        // 根据ID查找用户，确保用户存在
        YeelightUserDto user = yeelightUserReadService.findUserById(yeelightUserDto.getId());
        UserJudge.checkUserExist(user);

        // 如果用户名有变化，则更新用户名
        if (StringUtils.isNotBlank(yeelightUserDto.getUsername()) && !StringUtils.equals(user.getUsername(), yeelightUserDto.getUsername()) ) {
            yeelightUserWriteService.updateUsername(yeelightUserDto.getId(), yeelightUserDto.getUsername());
        }

        // 如果电话号码有变化，则更新电话号码
        if (StringUtils.isNotBlank(yeelightUserDto.getPhoneNumber()) && !StringUtils.equals(user.getPhoneNumber(), yeelightUserDto.getPhoneNumber()) ) {
            yeelightUserWriteService.updatePhoneNumber(yeelightUserDto.getId(), yeelightUserDto.getPhoneNumber());
        }

        // 如果邮箱有变化，则更新邮箱
        if (StringUtils.isNotBlank(yeelightUserDto.getEmail()) && !StringUtils.equals(user.getEmail(), yeelightUserDto.getEmail())) {
            yeelightUserWriteService.updateEmail(yeelightUserDto.getId(), yeelightUserDto.getEmail());
        }

        // 如果姓名有变化，则更新姓名
        if (StringUtils.isNotBlank(yeelightUserDto.getName()) && !StringUtils.equals(user.getName(), yeelightUserDto.getName()) ) {
            yeelightUserWriteService.updateName(yeelightUserDto.getId(), yeelightUserDto.getName());
        }

        // 如果头像有变化，则更新头像
        if (StringUtils.isNotBlank(yeelightUserDto.getAvatar()) && !StringUtils.equals(user.getAvatar(), yeelightUserDto.getAvatar()) ) {
            yeelightUserWriteService.updateAvatar(yeelightUserDto.getId(), yeelightUserDto.getAvatar());
        }
    }

    @Override
    public List<YeelightUserDto> list() {
        return yeelightUserReadService.list();
    }

    @Override
    public void doResetPassword(Long id) throws BizException {
        yeelightUserWriteService.resetPassword(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto doDisable(Long id) throws BizException {
        yeelightUserWriteService.disable(id);
        return yeelightUserReadService.findUserById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto doEnable(Long id) throws BizException {
        yeelightUserWriteService.enable(id);
        return yeelightUserReadService.findUserById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto doLock(Long id) throws BizException {
        yeelightUserWriteService.lock(id);
        return yeelightUserReadService.findUserById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto doUnlock(Long id) throws BizException {
        yeelightUserWriteService.unlock(id);
        return yeelightUserReadService.findUserById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String oldPassword, String newPassword) throws BizException {
        yeelightUserWriteService.updatePassword(id, oldPassword, newPassword);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String newPassword) throws BizException {
        yeelightUserWriteService.updatePassword(id, newPassword);
    }

    @Override
    public List<YeelightUserEntity> findUser(YeelightUserExample example) {
        return yeelightUserReadService.findUser(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto autoRegisterSocialUser(AuthUser authUser, Long currentLoginUserId) throws BizException {
        return justAuthUserDetailsService.autoRegisterSocialUser(authUser, currentLoginUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialUserTokenInfo(Long socialUserId, AuthToken authToken) throws BizException {
        yeelightSocialUserService.updateSocialUserTokenInfo(socialUserId, authToken);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSocialUserTokenInfo(String source, String uuid, AuthToken authToken) throws BizException {
        yeelightSocialUserService.updateSocialUserTokenInfo(source, uuid, authToken);
    }

    @Override
    public SocialUserDto findSocialUserBySource(Long yeelightId, String source) {
        return yeelightSocialUserService.findSocialUserBySource(yeelightId, source);
    }

    @Override
    public SocialUserDto findSocialUserBySourceAndUuid(String uuid, String source) {
        return yeelightSocialUserService.findSocialUserBySourceAndUuid(uuid, source);
    }

    @Override
    public List<SocialUserDto> findSocialUsers(Long yeelightId) {
        return yeelightSocialUserService.findSocialUsers(yeelightId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBindSocialUser(Long yeelightId, String source) throws BizException {
        yeelightSocialUserService.unBindSocialUser(yeelightId, source);
    }

    @Override
    public SocialUserDto findSocialUserBySource(String token, String source) throws BizException, NotLoginException {
        return yeelightSocialUserService.findSocialUserBySource(token, source);
    }

    @Override
    public List<SocialUserDto> findSocialUsers(String token) throws BizException, NotLoginException {
        return yeelightSocialUserService.findSocialUsers(token);
    }

    @Override
    public List<SocialUserDto> findSocialUsersByVendor(String vendor, String token) throws BizException, NotLoginException {
        return yeelightSocialUserService.findSocialUsersByVendor(vendor, token);
    }

    @Override
    public List<SocialUserDto> findSocialUsersByVendor(String vendor, Long yeelightUserId) throws BizException, NotLoginException {
        return yeelightSocialUserService.findSocialUsersByVendor(vendor, yeelightUserId);
    }

    @Override
    public void unBindSocialUser(String token, String source) throws BizException, NotLoginException {
        yeelightSocialUserService.unBindSocialUser(token, source);
    }

    @Override
    public List<MonitoringYeelightUserDto> getAllMonitoringUser() {
        return yeelightUserReadService.getAllMonitoringUser();
    }

    @Override
    public YeelightUserDto findUserByUsername(String username) {
        return yeelightUserReadService.findUserByUsername(username);
    }

    @Override
    public YeelightUserDto findUserById(Long id) {
        return yeelightUserReadService.findUserById(id);
    }

    @Override
    public List<YeelightUserDto> findUserByIds(List<Long> ids) {
        return yeelightUserReadService.findUserByIds(ids);
    }

    @Override
    public YeelightUserDto findUserByPhoneNumber(String phoneNumber) {
        return yeelightUserReadService.findUserByPhoneNumber(phoneNumber);
    }

    @Override
    public YeelightUserDto findUserByEmail(String email) {
        return yeelightUserReadService.findUserByEmail(email);
    }

    @Override
    public YeelightUserDto findUserByAccount(String account) {
        return yeelightUserReadService.findUserByAccount(account);
    }

    @Override
    public int deleteUser(Long recordId) throws BizException {
        yeelightUserWriteService.removeUserByUserId(recordId);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto createUserByPhoneIfNotExist(String phone, String password) throws BizException {
        Long yeelightId = yeelightUserWriteService.getUserIdOrCreateByPhoneOrEmail(phone, password);
        return findUserById(yeelightId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YeelightUserDto createUserIfAbsent(YeelightUserDto userDto) throws BizException {
        return createUserByPhoneIfNotExist(userDto.getPhoneNumber(), userDto.getPassword());
    }

    @Override
    public boolean revokeTokenByUserName(String username) {
        return tokenService.revokeTokenByUserName(username);
    }

    @Override
    public boolean logoutByUserName(String username) {
        return yeelightUserWriteService.logoutByUserName(username);
    }

    @Override
    public boolean logoutCurrentToken(String token) {
        return tokenService.logoutCurrentToken(token);
    }

    @Override
    public boolean logoutCurrentApplication(String token) {
        return tokenService.logoutCurrentApplication(token);
    }

    @Override
    public boolean logoutCurrentUser(String token) {
        return tokenService.logoutCurrentUser(token);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeUser(String userName) throws BizException {
        try {
            yeelightUserWriteService.removeUserByUserName(userName);
            return true;
        } catch (BizException e) {
            log.error("removeUser: {} BizException: {}", userName, e.getMessage());
            throw new BizException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("removeUser: {} Exception: {}", userName, ExceptionUtils.getStackTrace(e));
            throw new BizException(I18nUtil.getMessage("User.Exception.data.verifyFail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUsername(YeelightUserDto userDto, YeelightUserDto user) {
        yeelightUserWriteService.updateUsername(userDto.getId(), user.getUsername());
    }

    @Override
    public Set<Long> notValidIds(Set<Long> ids) {
        return yeelightUserReadService.notValidIds(ids);
    }
}
