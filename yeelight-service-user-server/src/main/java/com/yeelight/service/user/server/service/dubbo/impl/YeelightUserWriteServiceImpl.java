/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service.dubbo.impl
 * Description: 用户写服务实现
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-29 16:38:16:38
 */
package com.yeelight.service.user.server.service.dubbo.impl;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.lock.RedisService;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.YeelightUserExtend;
import com.yeelight.service.user.client.domain.YeelightUserExtendExample;
import com.yeelight.service.user.client.dto.YeelightUserEvent;
import com.yeelight.service.user.client.dto.YeelightUserEventDto;
import com.yeelight.service.user.client.dto.YeelightUserExtendDto;
import com.yeelight.service.user.client.enums.UserStatus;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import com.yeelight.service.user.client.service.YeelightUserWriteService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.annotation.UserHintSharding;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.config.AppConfig;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.domain.SocialUserAuth;
import com.yeelight.service.user.server.domain.YeelightUser;
import com.yeelight.service.user.server.mapper.user.SocialUserAuthMapper;
import com.yeelight.service.user.server.mapper.user.SocialUserMapper;
import com.yeelight.service.user.server.mapper.user.YeelightUserMapper;
import com.yeelight.service.user.server.support.UserAssembler;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.support.UserJudge;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import java.util.*;
import java.time.LocalDateTime;

/**
 * Desc: 用户写服务实现
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-29 16:38:16:38
 */
@Slf4j
@DubboService(timeout = 5000)
@UserHintSharding
public class YeelightUserWriteServiceImpl implements YeelightUserWriteService {
    @Resource
    private Validator validator;

    @Resource
    private YeelightUserMapper yeelightUserMapper;

    @Resource
    private SocialUserMapper socialUserMapper;

    @Resource
    private SocialUserAuthMapper socialUserAuthMapper;

    @Resource
    private AppConfig appConfig;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private RedisService redisService;

    @Resource
    private UserSessionManagementService userSessionManagementService;

    @Resource
    private TokenService tokenService;

    @Resource
    private YeelightUserExtendServiceImpl yeelightUsersExtendService;

    @Value("${server.env.region:cn}")
    private String region;

    /**
     * 创建用户。
     *
     * @param request 包含创建用户所需信息的请求对象。
     * @return 新创建用户的身份标识符。
     * @throws BizException 业务异常，如果创建过程中出现任何问题。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(CreateUserRequest request)  throws BizException {
        log.info("开始插入用户, 入参:{}", request);
        YeelightUser yeelightUser = UserAssembler.addRequestToYeelightUser(request);
        yeelightUser.setUsername(UserUtils.usernameEncoder(StringUtils.EMPTY));

        // 进行用户创建前的检验
        UserJudge.checkUser(yeelightUser, yeelightUserMapper, validator);

        // 对用户进行默认的装饰
        UserAssembler.defaultDecorateYeelightUser(yeelightUser, region);

        // 将用户插入数据库
        yeelightUserMapper.insertSelective(yeelightUser);

        // 记录创建用户的操作日志
        BizOperateLogUtils.sendAddBizOperateLog(yeelightUser.getId(), BizTypeEnums.用户.getCode(), "创建用户", UserConverter.yeelightUserToDto(yeelightUser));
        UserJudge.checkYeelightId(yeelightUser.getId());

        // 发送用户创建成功的事件
        sendEvent(YeelightUserEvent.EventTypeEnum.CREATED, yeelightUser);

        return yeelightUser.getId();
    }

    /**
     * 根据手机号/邮箱和密码创建或获取用户ID。
     * 如果用户不存在，则使用提供的手机号和密码创建新用户，并返回新用户的ID。
     * 如果用户已存在，则返回该用户的ID。
     *
     * @param phoneOrEmail 用户的手机号/邮箱，不能为空。
     * @param password 用户的密码，如果为空，则使用手机号作为密码。
     * @return 用户的ID，如果创建了新用户，则返回新用户的ID；如果用户已存在，则返回已存在的用户ID。
     * @throws BizException 业务异常，可能抛出当手机号格式不正确或其他业务规则被违反时。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long getUserIdOrCreateByPhoneOrEmail(String phoneOrEmail, String password) throws BizException {
        // 验证手机号不能为空
        Assert.notBlank(phoneOrEmail, I18nUtil.getMessage("User.Exception.phoneNumber.notBlank"));
        // 如果密码为空，则使用手机号作为密码
        password = Optional.ofNullable(password).orElse(phoneOrEmail);
        log.info("根据手机号/邮箱和密码创建/获取用户开始, 手机号/邮箱:{}, 密码:{}", phoneOrEmail, password);

        String phone;
        String email;
        // 判断是手机号还是邮箱, 并赋值给phone和email
        if (UserUtils.isEmail(phoneOrEmail)) {
            email = phoneOrEmail;
            phone = null;
        } else {
            phone = phoneOrEmail;
            email = null;
        }

        // 构造Redis锁的key，防止并发创建用户的问题
        String lockKey = Constants.YEELIGHT_USER_CREATE_USER_BY_PHONE_OR_EMAIL_IF_NOT_EXIST + ":" + UserVendorHolder.getVendor() + ":" + phoneOrEmail;

        try {
            // 获取分布式锁，确保并发安全
            redisService.lock(lockKey, appConfig.getRedisLockExpire(), appConfig.getRedisLockLoopInterval(), appConfig.getRedisLockLoopNum());

            // 查询数据库中是否存在该手机号/邮箱的用户
            YeelightUser yeelightUser = UserUtils.isEmail(phoneOrEmail) ? yeelightUserMapper.findUserByEmail(phoneOrEmail) : yeelightUserMapper.findUserByPhoneNumber(phoneOrEmail);

            if (Objects.isNull(yeelightUser)) {
                // 如果用户不存在，则创建新用户
                CreateUserRequest createUserRequest = CreateUserRequest.builder().phoneNumber(phone).email(email).password(password).build();
                Long yeelightId = create(createUserRequest);
                // 记录业务操作日志
                BizOperateLogUtils.sendAddBizOperateLog(yeelightId, BizTypeEnums.用户.getCode(), "根据手机号/邮箱和密码创建用户", createUserRequest);
                log.info("根据手机号/邮箱和密码创建/获取用户, 未获取到用户,执行新增用户操作, YeelightUser:{}", yeelightId);
                return yeelightId;
            } else {
                // 如果用户已存在，进行用户ID的合法性检查
                UserJudge.checkYeelightId(yeelightUser.getId());
                return yeelightUser.getId();
            }
        } finally {
            // 无论如何，最后都要释放分布式锁
            redisService.unlock(lockKey);
        }
    }

    /**
     * 更新用户用户名
     *
     * @param id 用户ID
     * @param userName 新的用户名
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUsername(Long id, String userName) {
        log.info("开始更新用户名, id:{}, userName:{}", id, userName);

        // 校验用户ID的有效性
        UserJudge.checkYeelightId(id);
        // 根据ID查询用户信息
        YeelightUser user = yeelightUserMapper.findUserById(id);

        // 检查是否允许修改用户名
        UserJudge.checkUpdateUserName(user, userName);
        // 确保新用户名未被其他用户使用
        Assert.isNotTrue(UserJudge.checkUsername(userName, yeelightUserMapper), I18nUtil.getMessage("User.Exception.account.alreadyExist"));

        // 更新用户名修改次数
        refreshUsernameUpdateTime(user);

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "更新用户名", UserConverter.yeelightUserToDto(user), user.getUsername(), userName);

        // 更新用户用户名和修改时间
        user.setUsername(userName);
        user.setUpdateTime(LocalDateTime.now());

        // 登出使用该用户名的所有会话
        logoutByUserName(user.getUsername());
        // 执行用户信息更新操作
        yeelightUserMapper.updateByPrimaryKeySelective(user);
        // 发送用户更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_USERNAME, user);
    }


    /**
     * 更新用户手机号码。
     * @param id 用户ID，不能为空。
     * @param phoneNumber 新的手机号码，格式需符合电话号码规范。
     * @throws BizException 如果更新过程中出现业务错误，比如用户不存在、手机号码已存在等。
     */
    @Override
    public void updatePhoneNumber(Long id, String phoneNumber) throws BizException {
        log.info("开始更新用户手机号, id:{}, phoneNumber:{}", id, phoneNumber);
        // 校验ID的有效性
        UserJudge.checkYeelightId(id);
        // 根据ID查找用户
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 检查是否允许更新手机号
        UserJudge.checkUpdatePhoneNumber(user, phoneNumber);
        // 确保要更新的手机号码未被其他用户使用
        Assert.isNotTrue(UserJudge.checkPhoneNumber(phoneNumber, yeelightUserMapper), I18nUtil.getMessage("User.Exception.phoneNumber.alreadyExist"));

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "更新用户手机号", UserConverter.yeelightUserToDto(user), user.getPhoneNumber(), phoneNumber);

        // 更新用户手机号和更新时间
        user.setPhoneNumber(phoneNumber);
        user.setUpdateTime(LocalDateTime.now());

        // 执行用户信息更新
        yeelightUserMapper.updateByPrimaryKeySelective(user);
        // 发送用户名称更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_PHONE_NUMBER, user);
        // 发送用户更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED, user);
    }


    /**
     * 更新用户邮箱
     *
     * @param id 用户ID
     * @param email 新的邮箱地址
     * @throws BizException 业务异常，如果更新过程中出现错误（如邮箱已存在）会抛出此异常
     */
    @Override
    public void updateEmail(Long id, String email) throws BizException {
        log.info("开始更新用户邮箱, id:{}, email:{}", id, email);
        // 检查用户ID的有效性
        UserJudge.checkYeelightId(id);
        // 根据ID查询用户信息
        YeelightUser user = yeelightUserMapper.findUserById(id);

        // 检验是否可以修改邮箱
        UserJudge.checkUpdateEmail(user, email);

        // 验证邮箱是否唯一
        Assert.isNotTrue(UserJudge.checkEmail(email, yeelightUserMapper), I18nUtil.getMessage("User.Exception.email.alreadyExist"));

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "更新用户邮箱", UserConverter.yeelightUserToDto(user), user.getEmail(), email);

        // 更新用户邮箱信息
        user.setEmail(email);
        user.setUpdateTime(LocalDateTime.now());

        // 执行数据库更新操作
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 发送用户邮箱更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_EMAIL, user);
        // 发送用户信息更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED, user);
    }

    /**
     * 更新用户的名字。
     * @param id 用户的唯一标识符。
     * @param name 需要更新的新名字。
     * @throws BizException 如果更新过程中出现业务逻辑错误。
     */
    @Override
    public void updateName(Long id, String name) throws BizException {
        log.info("开始更新用户昵称, id:{}, name:{}", id, name);

        // 校验用户ID的有效性
        UserJudge.checkYeelightId(id);

        // 根据ID查找用户
        YeelightUser user = yeelightUserMapper.findUserById(id);

        // 检查是否具有修改属性的权限
        UserJudge.checkUpdateProperty(user, name);

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "更新用户昵称", UserConverter.yeelightUserToDto(user), user.getName(), name);

        // 更新用户的名字和更新时间
        user.setName(name);
        user.setUpdateTime(LocalDateTime.now());

        // 执行用户信息更新操作
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 发送用户名字更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_NAME, user);
        // 发送用户更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED, user);
    }


    /**
     * 更新用户头像
     *
     * @param id 用户ID
     * @param avatar 新的头像标识
     * @throws BizException 业务异常，可能由于不合法的操作或系统错误引发
     */
    @Override
    public void updateAvatar(Long id, String avatar) throws BizException {
        log.info("开始更新用户头像, id:{}, avatar:{}", id, avatar);
        // 校验用户ID的有效性
        UserJudge.checkYeelightId(id);
        // 根据ID查找用户
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 检查是否具有修改权限
        UserJudge.checkUpdateProperty(user, avatar);

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "更新用户头像", UserConverter.yeelightUserToDto(user), user.getAvatar(), avatar);

        // 更新用户头像和更新时间
        user.setAvatar(avatar);
        user.setUpdateTime(LocalDateTime.now());

        // 执行用户信息更新
        yeelightUserMapper.updateByPrimaryKeySelective(user);
        // 发送头像更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_AVATAR, user);
        // 发送用户更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED, user);
    }


    /**
     * 重置用户密码。
     *
     * @param id 用户ID，不能为空。
     * @throws BizException 业务异常，可能抛出当用户ID为空、用户ID无效、用户不存在等情况。
     */
    @Override
    public void resetPassword(Long id) throws BizException {
        log.info("开始重置密码, id:{}", id);

        // 校验ID不能为空
        Assert.notNull(id, I18nUtil.getMessage("User.Exception.id.notNull"));
        // 检查ID是否为有效的Yeelight用户ID
        UserJudge.checkYeelightId(id);
        // 根据ID查询用户信息
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 校验用户是否存在
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));

        // 生成新的随机密码并加密
        String newPassword = UserUtils.randomStr(32);
        String newEncryptedPassword = UserUtils.passwordEncoder().encode(newPassword);
        user.setPassword(newEncryptedPassword);
        user.setSalt(UserUtils.saltEncoder());
        user.setUpdateTime(LocalDateTime.now());
        log.info("实际执行重置密码, 参数:{}", user);

        // 强制用户登出
        logoutByUserName(user.getUsername());

        // 更新用户密码及相关信息到数据库
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 记录密码重置操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "重置密码", newPassword, UserConverter.yeelightUserToDto(user), UserConverter.yeelightUserToDto(user));
        // 发送密码更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_PASSWORD, user);
    }


    /**
     * 更新用户密码，需要提供旧密码和新密码。
     * 对应的用户ID、旧密码和新密码作为参数。
     * 此方法会检查旧密码是否正确，并将新密码加密后更新到数据库。
     * 如果更新成功，会记录操作日志并触发密码更新事件。
     *
     * @param id 用户ID，不能为空。
     * @param oldPassword 旧密码，不能为空。
     * @param newPassword 新密码，不能为空。
     * @throws BizException 如果操作失败，可能会抛出业务异常。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String oldPassword, String newPassword) throws BizException {
        log.info("开始修改密码(带老密码方式), id:{}, oldPassword:{}, newPassword:{}", id, oldPassword, newPassword);

        // 校验用户ID的有效性
        UserJudge.checkYeelightId(id);

        // 确保旧密码和新密码不为空
        Assert.isNotTrue(!StringUtils.isEmpty(oldPassword), I18nUtil.getMessage("User.Exception.oldPassword.notBlank"));
        Assert.isNotTrue(!StringUtils.isEmpty(newPassword), I18nUtil.getMessage("User.Exception.newPassword.notBlank"));

        // 根据ID查询用户，确保用户存在
        YeelightUser user = yeelightUserMapper.findUserById(id);
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));

        // 验证旧密码是否正确
        Assert.isNotTrue(UserUtils.passwordEncoder().matches(oldPassword, user.getPassword()), I18nUtil.getMessage("User.Exception.oldPassword.wrong"));

        // 更新密码，设置新密码和更新时间
        String newEncryptedPassword = UserUtils.passwordEncoder().encode(newPassword);
        user.setPassword(newEncryptedPassword);
        user.setSalt(UserUtils.saltEncoder());
        user.setUpdateTime(LocalDateTime.now());
        log.info("实际执行修改密码(带老密码方式), 参数:{}", user);

        // 登出用户，确保安全
        logoutByUserName(user.getUsername());

        // 更新用户信息到数据库
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 记录业务操作日志并发送密码更新事件
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "修改密码(带老密码方式)", UserConverter.yeelightUserToDto(user));
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_PASSWORD, user);
    }

    /**
     * 更新用户密码，无需提供旧密码。此操作会强制用户登出，确保密码安全更新。
     *
     * @param id 用户ID，不能为空。
     * @param newPassword 新密码，不能为空且必须符合密码强度要求。
     * @throws BizException 业务异常，可能抛出当用户ID不存在、新密码为空或用户不存在等情况下。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String newPassword) throws BizException {
        log.info("开始修改密码(无老密码方式), id:{}, newPassword:{}", id, newPassword);

        // 校验用户ID的有效性
        UserJudge.checkYeelightId(id);
        // 确保新密码不为空
        Assert.isNotTrue(!StringUtils.isEmpty(newPassword), I18nUtil.getMessage("User.Exception.newPassword.notBlank"));

        // 根据ID查询用户，确保用户存在
        YeelightUser user = yeelightUserMapper.findUserById(id);
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));

        // 加密新密码并更新用户信息
        String newEncryptedPassword = UserUtils.passwordEncoder().encode(newPassword);
        user.setPassword(newEncryptedPassword);
        user.setSalt(UserUtils.saltEncoder());
        user.setUpdateTime(LocalDateTime.now());
        log.info("实际执行修改密码(无老密码方式), 参数:{}", user);

        // 强制用户登出
        logoutByUserName(user.getUsername());

        // 更新用户信息到数据库
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "修改密码", newPassword, UserConverter.yeelightUserToDto(user), UserConverter.yeelightUserToDto(user));
        // 发送用户密码更新事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UPDATED_PASSWORD, user);
    }

    /**
     * 禁用用户。
     *
     * @param id 用户ID。
     * @throws BizException 业务异常，可能由于用户不存在、用户已是禁用状态等原因抛出。
     * @see UserJudge#checkYeelightId(Long) 校验用户ID的有效性。
     * @see YeelightUserMapper#findUserById(Long) 根据ID查询用户信息。
     * @see Assert#notNull(Object, String) 校验对象不为null，否则抛出异常。
     * @see UserStatus#ENABLED 获取启用状态的代码。
     * @see Assert#isNotTrue(boolean, String) 校验条件是否为假，否则抛出异常。
     * @see #sendEvent(YeelightUserEvent.EventTypeEnum, YeelightUser) 发送用户事件。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disable(Long id) throws BizException {
        // 校验用户ID格式是否正确。
        UserJudge.checkYeelightId(id);
        // 根据ID查找用户。
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 确保用户存在。
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));

        // 校验用户当前状态是否为启用状态。
        Assert.isNotTrue(UserStatus.ENABLED.getCode().equals(user.getStatus()), I18nUtil.getMessage("User.Exception.user.notEnable"));

        // 更新用户状态为禁用，并记录修改时间。
        user.setStatus(UserStatus.DISABLED.getCode());
        user.setUpdateTime(LocalDateTime.now());
        log.info("禁用用户:{}", user);

        // 使用户登出。
        logoutByUserName(user.getUsername());

        // 更新数据库中的用户状态。
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 记录禁用用户的业务操作日志。
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "禁用用户", UserConverter.yeelightUserToDto(user));
        // 发送用户禁用事件。
        sendEvent(YeelightUserEvent.EventTypeEnum.DISABLED, user);
    }

    /**
     * 启用用户。
     *
     * @param id 用户ID。
     * @throws BizException 业务异常。
     * <p>
     * 本方法主要步骤包括：
     * 1. 校验用户ID的有效性；
     * 2. 查询用户信息，确保用户存在；
     * 3. 检查用户状态，确保用户不是已启用状态；
     * 4. 更新用户状态为启用，并记录更新时间；
     * 5. 登出与该用户关联的所有会话和Token；
     * 6. 发送用户启用事件；
     * 7. 记录操作日志。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enable(Long id) throws BizException {
        // 校验用户ID格式合法性
        UserJudge.checkYeelightId(id);
        // 查询用户信息
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 确保用户存在
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));
        // 检查用户是否已经是启用状态
        Assert.isNotTrue(!UserStatus.ENABLED.getCode().equals(user.getStatus()), I18nUtil.getMessage("User.Exception.user.enabled"));

        // 设置用户状态为启用
        user.setStatus(UserStatus.ENABLED.getCode());
        // 更新用户信息时间
        user.setUpdateTime(LocalDateTime.now());
        // 记录日志
        log.info("启用用户:{}", user);

        // 更新用户信息
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 登出与该用户关联的所有会话和Token，以便用户能够以启用状态重新登录
        logoutByUserName(user.getUsername());

        // 发送用户启用事件
        sendEvent(YeelightUserEvent.EventTypeEnum.ENABLED, user);

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "启用用户", (user));
    }

    /**
     * 锁定用户。
     *
     * @param id 用户ID。
     * @throws BizException 业务异常。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lock(Long id) throws BizException {
        // 校验用户ID的有效性
        UserJudge.checkYeelightId(id);
        // 根据ID查找用户
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 确保用户存在
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));

        // 检查用户状态，确保用户不是已经被禁用的状态
        Assert.isNotTrue(UserStatus.ENABLED.getCode().equals(user.getStatus()), I18nUtil.getMessage("User.Exception.user.notEnable"));

        // 更新用户状态为锁定，并记录修改时间
        user.setStatus(UserStatus.LOCKED.getCode());
        user.setUpdateTime(LocalDateTime.now());
        log.info("锁定用户:{}", user);

        // 执行用户登出操作
        logoutByUserName(user.getUsername());

        // 更新用户信息
        yeelightUserMapper.updateByPrimaryKeySelective(user);
        // 记录业务操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "锁定用户", UserConverter.yeelightUserToDto(user));
        // 发送用户锁定事件
        sendEvent(YeelightUserEvent.EventTypeEnum.LOCKED, user);
    }

    /**
     * 解锁用户。
     *
     * @param id 用户ID。
     * @throws BizException 业务异常。
     * <p>
     * 本方法首先验证用户ID的有效性，然后检查用户是否处于锁定状态，
     * 如果是，则将用户状态更新为启用，并记录相关操作日志，
     * 同时登出该用户的所有会话和Token，以允许用户以启用状态重新登录。
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unlock(Long id) throws BizException {
        // 验证用户ID的有效性
        UserJudge.checkYeelightId(id);
        // 根据ID查找用户
        YeelightUser user = yeelightUserMapper.findUserById(id);
        // 确保用户存在
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist", id));

        // 检查用户是否处于锁定状态
        Assert.isNotTrue(UserStatus.LOCKED.getCode().equals(user.getStatus()), I18nUtil.getMessage("User.Exception.user.notLocked"));

        // 更新用户状态为启用，并记录修改时间
        user.setStatus(UserStatus.ENABLED.getCode());
        user.setUpdateTime(LocalDateTime.now());
        log.info("解锁用户:{}", user);

        // 更新用户信息
        yeelightUserMapper.updateByPrimaryKeySelective(user);

        // 登出用户的所有会话和Token
        logoutByUserName(user.getUsername());

        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(id, BizTypeEnums.用户.getCode(), "解锁用户", UserConverter.yeelightUserToDto(user));
        // 发送解锁事件
        sendEvent(YeelightUserEvent.EventTypeEnum.UNLOCKED, user);
    }


    /**
     * 根据用户名登出用户的所有会话并撤销所有相关token。
     *
     * @param username 用户名，用于登出操作。
     * @return boolean 返回登出操作是否成功。如果用户名为空或无效，或用户不存在，返回false；否则成功登出并返回true。
     */
    @Override
    public boolean logoutByUserName(String username) {
        // 检查用户名是否为空
        if (StringUtils.isBlank(username)) {
            return false;
        }
        // 根据用户名查找用户
        YeelightUser user = yeelightUserMapper.findUserByUsername(username);
        // 如果用户不存在，则返回false
        if (Objects.isNull(user)) {
            return false;
        }

        // 确定用户类型，以便于后续操作
        UserVendorEnum userVendorEnum = Arrays.stream(UserVendorEnum.values()).filter(vendor -> vendor.userIdRange().contains(user.getId())).findFirst().orElse(UserVendorEnum.DEFAULT);
        // 结束所有相关会话
        userSessionManagementService.expireUserSessions(username, userVendorEnum.getCode());
        // 撤销所有与该用户名相关的token
        tokenService.revokeTokenByUserName(username);
        // 记录操作日志
        BizOperateLogUtils.sendUpdateBizOperateLog(user.getId(), BizTypeEnums.用户.getCode(), "登出所有会话和token", UserConverter.yeelightUserToDto(user));
        return true;
    }


    /**
     * 根据用户名删除用户。
     * 该操作是事务性的，如果处理过程中发生异常，会回滚事务。
     *
     * @param userName 要删除的用户的用户名
     * @throws BizException 业务处理过程中的异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeUserByUserName(String userName) throws BizException {
        // 通过用户名查找用户
        YeelightUser yeelightUser = yeelightUserMapper.findUserByUsername(userName);
        // 删除找到的用户
        removeUser(yeelightUser);
    }

    /**
     * 根据用户ID移除用户。
     * <p>此方法通过用户ID查找对应的用户，然后调用{@code removeUser}方法进行移除操作。</p>
     *
     * @param userId 用户ID，用于查找并移除特定用户。
     * @throws BizException 如果移除用户过程中出现业务错误，则抛出此异常。
     */
    @Override
    public void removeUserByUserId(Long userId) throws BizException {
        // 根据用户ID查找用户
        YeelightUser yeelightUser = yeelightUserMapper.findUserById(userId);
        // 移除找到的用户
        removeUser(yeelightUser);
    }


    /**
     * 移除用户，包括解绑第三方账号、登出、清除用户信息，并发送相关事件与操作日志。
     *
     * @param yeelightUser 要移除的Yeelight用户对象，不可为null。
     */
    private void removeUser(YeelightUser yeelightUser) {
        Assert.notNull(yeelightUser, "用户不存在");
        UserJudge.checkYeelightId(yeelightUser.getId());

        // 清除用户与第三方账号的绑定信息
        List<SocialUserAuth> socialUserAuthList = socialUserAuthMapper.selectSocialUserAuthsWithYeelightId(yeelightUser.getId());
        if (!CollectionUtils.isEmpty(socialUserAuthList)) {
            socialUserAuthList.forEach(socialUserAuth -> {
                socialUserMapper.deleteByPrimaryKey(socialUserAuth.getSocialUserId());
                socialUserAuthMapper.deleteByPrimaryKey(socialUserAuth.getId());
            });
        }

        // 执行用户登出操作
        logoutByUserName(yeelightUser.getUsername());

        // 清除指定用户名的用户信息
        yeelightUserMapper.removeUserByUsername(yeelightUser.getUsername());

        // 发送用户被移除的事件
        sendEvent(YeelightUserEvent.EventTypeEnum.REMOVED, yeelightUser);

        // 发送删除用户操作的日志
        BizOperateLogUtils.sendRemoveBizOperateLog(yeelightUser.getId(), BizTypeEnums.用户.getCode(), "注销用户", yeelightUser.getUsername());

        log.info("注销用户成功, userName[{}]", yeelightUser.getUsername());
    }

    /**
     * 刷新用户用户名的更新次数
     * 该方法会检查给定的用户是否存在对应的扩展信息，如果存在，则更新用户名的修改次数；如果不存在，则插入新的用户扩展信息。
     *
     * @param user 需要刷新用户名次数的用户对象。该对象不应为null。
     */
    private void refreshUsernameUpdateTime(YeelightUser user) {
        if (Objects.isNull(user)) {
            return;
        }
        // 根据用户ID查询其扩展信息
        YeelightUserExtend yeelightUserExtend = yeelightUsersExtendService.selectOne(YeelightUserExtendExample.builder().yeelightUserId(user.getId()).build());
        if (Objects.nonNull(yeelightUserExtend)) {
            // 如果扩展信息存在，则更新用户名的修改次数
            yeelightUserExtend.setUsernameModifyNum(Optional.ofNullable(yeelightUserExtend.getUsernameModifyNum()).orElse(0) + 1);
            yeelightUsersExtendService.updateByPrimaryKey(yeelightUserExtend);
        } else {
            // 如果扩展信息不存在，则插入新的用户扩展信息，设置用户名修改次数为1
            yeelightUsersExtendService.insert(YeelightUserExtendDto.builder().yeelightUserId(user.getId()).usernameModifyNum(1).build());
        }
    }

    /**
     * 发送用户事件到Kafka主题。
     *
     * @param eventTypeEnum 用户事件的类型枚举。
     * @param yeelightUser 用户实体，包含用户详细信息。
     * <p>
     * 该方法首先检查用户对象及其ID是否为空，然后尝试从应用上下文中获取Kafka模板，
     * 使用该模板将用户事件数据发送到对应的Kafka主题。如果在应用上下文中找不到对应的Kafka模板，
     * 则记录日志信息，表示该服务节点未开启Kafka用户信息消息服务。
     */
    private void sendEvent(YeelightUserEvent.EventTypeEnum eventTypeEnum, YeelightUser yeelightUser) {
        // 检查用户对象及其ID是否为空
        if (Objects.isNull(yeelightUser) || Objects.isNull(yeelightUser.getId())) {
            return;
        }
        try {
            // 尝试从应用上下文中获取Kafka模板
            KafkaTemplate<String, String> kafkaTemplate = applicationContext.getBean("userEventKafkaTemplate", KafkaTemplate.class);
            YeelightUserEventDto eventDto = new YeelightUserEventDto();
            // 复制用户信息到事件DTO中
            BeanUtils.copyProperties(yeelightUser, eventDto);
            // 设置事件的供应商信息
            eventDto.setVendor(UserVendorHolder.getVendor());
            // 使用Kafka模板发送事件数据到主题
            kafkaTemplate.send(YeelightUserEvent.TOPIC_NAME + appConfig.getKafkaTopicSuffix(), String.valueOf(yeelightUser.getId()), JSON.toJSONString(new YeelightUserEvent<>(eventTypeEnum, eventDto)));

        } catch (NoSuchBeanDefinitionException e) {
            // 记录无法找到Kafka模板的日志信息
            log.info("该服务节点未开启kafka用户信息消息服务: eventTypeEnum {}, user:{}", eventTypeEnum.name(), yeelightUser);
        }
    }
}
