package com.yeelight.service.user.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.yeelight.service.framework.domain.HttpResult;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.EnvironmentPropertyUtils;
import com.yeelight.service.framework.util.HttpUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.request.AuthCodeRequest;
import com.yeelight.service.user.server.request.auth.AuthAmazonAlexaRequest;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import com.yeelight.service.user.server.service.AmazonAlexaService;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import okhttp3.Headers;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * @program: yeelight-oauth-api
 * @description:
 * @author: lixiaodong
 * @create: 2022-02-10 09:30
 **/
@Service
@Slf4j
public class AmazonAlexaServiceImpl implements AmazonAlexaService {
    @Resource
    private OauthKnifeService oauthKnifeService;

    @Resource
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;

    @Resource
    private AuthStateCache authStateCache;

    @Resource
    private JwtAuthService jwtAuthService;

    private static final String CONFIG_PREFIX = "justauth.extend.config";

    /**
     * 获取LWA（Login with Amazon）的回退URL。
     * 该方法用于生成一个用于账户链接的回退URL，主要涉及从配置中读取客户端ID和Alexa LWA重定向URI，
     * 生成一个授权状态，并将该状态与请求相关联，最后构造并返回LWA的授权URL。
     *
     * @param source 用于标识配置源的参数，用于生成对应的客户端ID和重定向URI的配置键。
     * @param request 用户的请求对象，用于在JWT服务中缓存token。
     * @return 返回构建的LWA授权页面的URL。
     * @throws BizException 如果无法从配置中读取到客户端ID或Alexa LWA重定向URI时抛出。
     */
    @Override
    public String getLwaFallbackUrl(String source, HttpServletRequest request) throws BizException {
        // 从配置中读取客户端ID和Alexa LWA重定向URI
        String clientId = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "client-id")).orElseThrow(() -> new BizException("Could not find the correct client-id"));
        String alexaLwaRedirectUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "alexa-lwa-redirect-uri")).orElseThrow(() -> new BizException("Could not find the correct alexa-lwa-redirect-uri"));

        // 生成一个授权状态，并将其缓存
        String state = AuthStateUtils.createState();
        authStateCache.cache(state, state);

        // 为第三方缓存token
        jwtAuthService.cacheTokenForThirdParty(state, request);

        // 构造并返回LWA授权URL
        return "https://www.amazon.com/ap/oa?client_id="+clientId+"&scope=alexa::skills:account_linking&response_type=code&redirect_uri="+alexaLwaRedirectUri+"&state="+state;
    }


    /**
     * 生成Alexa应用的URL，用于第三方账户链接。
     *
     * @param source 用于标识属性来源的源信息，用于构造客户端ID和重定向URI的属性键。
     * @param request HttpServletRequest对象，用于JWT服务缓存token。
     * @return 返回构建的用于Alexa账户链接的URL字符串。
     * @throws BizException 如果无法从环境属性中找到客户端ID或Alexa重定向URI时抛出。
     */
    @Override
    public String getAlexaAppUrl(String source, HttpServletRequest request) throws BizException {
        // 从环境属性中获取客户端ID和Alexa重定向URI，如果不存在则抛出BizException异常
        String clientId = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "client-id")).orElseThrow(() -> new BizException("Could not find the correct client-id"));
        String alexaRedirectUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "alexa-redirect-uri")).orElseThrow(() -> new BizException("Could not find the correct alexa-redirect-uri"));

        // 创建一个授权状态，并将其缓存
        String state = AuthStateUtils.createState();
        authStateCache.cache(state, state);

        // 使用请求信息缓存JWT令牌，为第三方服务准备
        jwtAuthService.cacheTokenForThirdParty(state, request);

        // 构建并返回Alexa账户链接的URL
        return "https://alexa.amazon.com/spa/skill-account-linking-consent?fragment=skill-account-linking-consent&client_id="+clientId+"&scope=alexa::skills:account_linking&skill_stage=live&response_type=code&redirect_uri="+alexaRedirectUri+"&state="+state;
    }


    /**
     * 查询Alexa认证令牌。
     * 该方法用于通过给定的源、代码和状态，从Alexa服务获取认证令牌。
     *
     * @param source 源标识，用于确定从哪个源请求认证令牌。
     * @param code 验证码，用于认证过程中的身份验证。
     * @param state 状态码，用于保持请求和回调之间的状态。
     * @return AuthToken 认证令牌，用于后续的API调用身份验证。
     * @throws BizException 如果获取认证令牌的过程中出现错误，则抛出业务异常。
     */
    @Override
    public AuthToken queryAlexaAuthToken(String source, String code, String state) throws BizException {
        // 从环境属性中获取Alexa启用URI和Yeelight重定向URI
        String alexaEnablementUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "alexa-enablement-uri")).orElseThrow(() -> new BizException("Could not find the correct alexa-enablement-uri"));
        String yeelightRedirectUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "yeelight-redirect-uri")).orElseThrow(() -> new BizException("Could not find the correct yeelight-redirect-uri"));

        // 根据源创建Alexa认证请求
        AuthRequest alexaAuthRequest = yeelightAuthRequestFactory.get(source);

        // 构建认证回调对象
        AuthCallback authCallback = AuthCallback.builder()
                .code(code)
                .state(state)
                .build();

        // 执行认证请求，获取认证响应
        AuthResponse<?> alexaAuthResponse = alexaAuthRequest.login(authCallback);
        log.info("queryAlexaAuthToken response:{}", alexaAuthResponse);

        // 校验认证响应状态
        Assert.isNotTrue(AuthResponseStatus.SUCCESS.getCode() == alexaAuthResponse.getCode(), alexaAuthResponse.getMsg());

        // 从认证响应中提取用户信息，并返回认证令牌
        AuthUser alexaAuthUser = (AuthUser) alexaAuthResponse.getData();
        return alexaAuthUser.getToken();
    }


    /**
     * 生成Yeelight授权码。
     * 该方法用于根据提供的源信息、Yeelight令牌和状态生成一个授权码。它首先从环境属性中获取Yeelight的客户端ID和重定向URI，
     * 然后缓存状态，并创建一个授权码请求对象，最后通过OAuthKnifeService生成并返回授权码。
     *
     * @param source 源标识，用于查找对应的Yeelight配置信息。
     * @param yeelightToken Yeelight令牌，用于授权过程。
     * @param state 用于保持授权状态的字符串，防止CSRF攻击。
     * @return 返回生成的授权码。
     * @throws BizException 如果找不到对应的yeelight-client-id或yeelight-redirect-uri环境属性时抛出。
     */
    @Override
    public String generateYeelightAuthCode(String source, String yeelightToken, String state) throws BizException {
        // 从环境变量中获取Yeelight的客户端ID和重定向URI
        String yeelightClientId = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "yeelight-client-id")).orElseThrow(() -> new BizException("Could not find the correct yeelight-client-id"));
        String yeelightRedirectUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "yeelight-redirect-uri")).orElseThrow(() -> new BizException("Could not find the correct yeelight-redirect-uri"));

        // 缓存授权状态，以备后续验证
        authStateCache.cache(state, state);

        // 创建授权码请求对象，并设置相关参数
        AuthCodeRequest authCodeRequest = new AuthCodeRequest();
        authCodeRequest.setToken(yeelightToken);
        authCodeRequest.setClientId(yeelightClientId);
        authCodeRequest.setRedirectUri(yeelightRedirectUri);
        authCodeRequest.setState(state);
        authCodeRequest.setScope("read write");

        // 调用服务生成授权码
        return oauthKnifeService.generateAuthCode(authCodeRequest);
    }


    /**
     * 查询Alexa API的端点信息。
     * @param source 表示数据来源，用于追踪和记录。
     * @param token 用户的授权令牌，用于访问Alexa API。
     * @return 返回一个包含端点信息的字符串列表。
     * @throws BizException 如果查询过程中出现业务错误，则抛出此异常。
     */
    @Override
    public List<String> queryAlexaApiEndpoints(String source, String token) throws BizException {
        // 构建请求头部，包含Authorization信息
        Headers headers = new Headers.Builder().add("Authorization", "Bearer " + token).build();
        // 发起GET请求获取Alexa API端点信息
        HttpResult response = HttpUtils.get(AuthAmazonAlexaRequest.ALEXA_API_ENDPOINT, headers);
        // 解析响应数据
        JSONObject jsonObject = JSONObject.parseObject(response.getData());
        // 从响应JSON中提取并返回endpoints列表
        return jsonObject.getObject("endpoints", new TypeReference<List<String>>(){});
    }


    /**
     * 查询技能和账户链接状态。
     * 该方法通过提供的源信息和Alexa令牌，查询指定源的Alexa技能和账户链接状态。
     *
     * @param source 源标识，用于确定查询的环境或配置。
     * @param alexaToken Alexa的令牌，用于授权访问Alexa API。
     * @return Boolean 返回账户是否已链接的状态。如果账户已链接，则返回Boolean.TRUE；否则返回Boolean.FALSE。
     * @throws BizException 如果无法找到对应的alexa-enablement-uri或查询过程中发生错误，则抛出BizException。
     */
    @Override
    public Boolean querySkillAndLinkAccountStatus(String source, String alexaToken) throws BizException {
        // 从配置中获取指定源的alexa-enablement-uri
        String alexaEnablementUri = EnvironmentPropertyUtils.getProperty(getPropertyKey(source, "alexa-enablement-uri")).orElseThrow(() -> new BizException("Could not find the correct alexa-enablement-uri"));
        // 通过Alexa令牌查询可用的API端点
        List<String> endpoints = queryAlexaApiEndpoints(source, alexaToken);
        // 确保端点列表不为空
        Assert.notEmpty(endpoints, "Could not find the correct endpoint for the user");
        log.info("AlexaApiEndpoints:{}", JSON.toJSONString(endpoints));

        // 构建请求头，包含Authorization信息
        Headers headers = new Headers.Builder().add("Authorization", "Bearer " + alexaToken).build();

        // 遍历所有获取到的端点，查询账户链接状态
        for (String endpoint : endpoints) {
            // 向指定端点发送GET请求
            HttpResult response = HttpUtils.get("https://" + endpoint + alexaEnablementUri, headers);
            // 解析响应数据
            JSONObject jsonObject = JSONObject.parseObject(response.getData());
            log.info("querySkillAndLinkAccountStatus response:{}", response.getData());

            // 从响应数据中获取账户链接状态
            JSONObject linked = jsonObject.getJSONObject("accountLink");
            if (Objects.nonNull(linked) && "LINKED".equals(linked.getString("status"))) {
                // 如果账户状态为已链接，则返回true
                return Boolean.TRUE;
            }
        }
        // 如果遍历所有端点后都未找到已链接的状态，则返回false
        return Boolean.FALSE;
    }

    /**
     * 构建属性键的完整字符串。
     * 此方法用于将配置前缀、源信息和键名组合成一个完整的属性键。
     *
     * @param source 源信息，可以是一个模块名或其它标识符，为空时不会添加到键中。
     * @param key 配置的键名，为空时不会添加到键中。
     * @return 返回构建好的属性键字符串。
     */
    private String getPropertyKey(String source, String key) {
        // 以配置前缀开始构建属性键
        StringBuilder sb = new StringBuilder(CONFIG_PREFIX);
        // 如果source非空，将其转换为大写并添加到属性键中
        if(StringUtils.isNotBlank(source)) {
            sb.append(".").append(source.toUpperCase());
        }
        // 如果key非空，将其添加到属性键中
        if(StringUtils.isNotBlank(key)) {
            sb.append(".").append(key);
        }
        // 返回构建好的属性键字符串
        return sb.toString();
    }

}
