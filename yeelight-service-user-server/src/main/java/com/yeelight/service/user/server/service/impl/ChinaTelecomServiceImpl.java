/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service.impl
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-07-25 17:43:17:43
 */
package com.yeelight.service.user.server.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.domain.HttpResult;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.Assert;
import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.framework.util.HttpUtils;
import com.yeelight.service.user.server.request.ChinaTelecomTokenNotifyRequest;
import com.yeelight.service.user.server.request.ChinaTelecomVerifyRequest;
import com.yeelight.service.user.server.service.ChinaTelecomService;
import com.yeelight.service.user.server.utils.chinatelecom.*;
import com.yeelight.service.user.server.vo.ChinaTelecomToken;
import com.yeelight.service.user.server.vo.ChinaTelecomUserInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-07-25 17:43:17:43
 */
@Service
@Slf4j
public class ChinaTelecomServiceImpl implements ChinaTelecomService {
    /**
     * TokenCode 有效时间
     */
    private static final Integer TOKEN_EXPIRE_MINUTES = 2;

    @Override
    public ChinaTelecomVerifyRequest extractTokenCode(ChinaTelecomTokenNotifyRequest tokenNotifyRequest) throws BizException {
        // 验证PartnerId是否正确
        Assert.equals(tokenNotifyRequest.getPartnerId(), Constants.PARTNER_ID, "中国电信：PartnerId不正确");
        // 校验 MD5(sequenceNo+encryptParam+ partnerSecret) 是否和signature相同
        String signature = EncryptUtil.md5String(tokenNotifyRequest.getSequenceNo() + tokenNotifyRequest.getEncryptParam() + Constants.PARTNER_SECRET);
        Assert.notBlank(signature, "中国电信：签名生成失败");
        if (signature != null) {
            Assert.equals(tokenNotifyRequest.getSignature(), signature.toLowerCase(), "中国电信：签名不正确");
        }
        // 从序列号中提取日期部分，并校验是否过期
        String sequenceNo = tokenNotifyRequest.getSequenceNo().substring(0, tokenNotifyRequest.getSequenceNo().length() - 4);
        // sequenceNo 转为日期 YYYYMMDDHHMMSS
        Date date = DateUtil.parse(sequenceNo, "yyyyMMddHHmmss");
        // 和当前时间比较
        if (DateUtils.addMinutes(date, TOKEN_EXPIRE_MINUTES).before(new Date())) {
            Assert.throwException("中国电信TokenCode已过期");
        }
        // 解密encryptParam并验证解密结果
        try {
            String platJson = EncryptUtil.decryptAesEcbPkcs5Padding(tokenNotifyRequest.getEncryptParam(), Constants.SECRET_KEY);
            Assert.notBlank(platJson, "中国电信：TokenCode解密失败");
            ChinaTelecomVerifyRequest verifyRequest = JSON.parseObject(platJson, ChinaTelecomVerifyRequest.class);
            Assert.notNull(verifyRequest, "中国电信：TokenCode解密失败");
            return verifyRequest;
        } catch (Exception e) {
            Assert.throwException("中国电信：TokenCode解密失败");
        }
        // 虽然理论上这行代码到不了，但为了满足语法完整性，这里返回null
        return null;
    }

    @Override
    public ChinaTelecomToken getTokenByTokenCode(String tokenCode) throws BizException {
        Headers headers = new Headers.Builder()
                .add(com.xkcoding.http.constants.Constants.CONTENT_TYPE, "application/x-www-form-urlencoded;charset=UTF-8")
                .build();
        LinkedHashMap<String, String> param = new LinkedHashMap<>();
        // 开发者在注册应用的时候由综合平台分发的接入方ID
        param.put("appId", Constants.APP_ID);
        // 客户端类型，统一为：20100
        param.put("clientType", "20100");
        // 目前仅支持json格式，redirect用于重定向接口的显示说明
        param.put("format", "json");
        // 调用的接口版本号，如v1.5 　v2.0
        param.put("version", "v2.0");
        /*
         * 使用appSecret对所有传入参数采用XXTea加密，并且按照接口详细规范中定义的参数(除appId、clientType、format、version、sign)拼接，不要求参数顺序。
         * 例如：paras = XXTea((a=value1&b=value2&…),appSecret)
         */
        param.put("paras", ChinaTelecomTeaUtil.encrypt("tokenCode=" + tokenCode, Constants.APP_SECRET));
        /*
         * 签名算法：
         * sign=HMAC-SHA1(appId+clientType+format+version+paras, appSecret)
         * 备注：注意保持签名的各字段顺序正确
         */
        // param 值拼接后，使用appSecret进行HMAC-SHA1签名
        log.info(String.join("", param.values()));
        param.put("sign", EncryptUtil.calculateHmacSha1(String.join("", param.values()), Constants.APP_SECRET));
        log.info("中国电信：获取Token请求参数: {}", param);
        RequestBody requestBody = HttpUtils.buildFormRequestBody(param);
        HttpResult response = HttpUtils.post(Constants.GET_TOKEN_BY_TOKEN_CODE_API, headers, requestBody);
        Assert.notNull(response, "中国电信：获取Token失败, 接口请求结果为空");
        Assert.notBlank(response.getData(), "中国电信：获取Token失败, 接口请求结果为空");
        log.info("中国电信获取Token响应: {}", response.getData());
        ChinaTelecomToken telecomToken = JSONObject.parseObject(response.getData(), ChinaTelecomToken.class);
        Assert.notNull(telecomToken, "中国电信：Token解析失败");
        if(0 != telecomToken.getResult()) {
            throw new BizException(telecomToken.getResult().toString(), telecomToken.getMsg());
        }
        return telecomToken;
    }

    @Override
    public ChinaTelecomUserInfo getUserInfo(String accessToken) throws BizException {
        String timeStamp = String.valueOf(System.currentTimeMillis());
        String format = "json";
        Map<String, String> generalParamsMap = new HashMap<>(0);
        Map<String, String> businessParamsMap = new HashMap<>(0);
        businessParamsMap.put("accessToken", accessToken);

        String params = RequestUtil.generateParams(businessParamsMap, Constants.APP_SECRET);
        generalParamsMap.put("clientId", Constants.APP_ID);
        generalParamsMap.put("timeStamp", timeStamp);
        generalParamsMap.put("format", format);
        generalParamsMap.put("params", params);
        try {
            String sign = RequestUtil.generateSign(generalParamsMap, Constants.RSA_PRIVATE_KEY);
            generalParamsMap.put("sign", sign);

            log.info("中国电信：获取用户信息请求参数: {}", generalParamsMap);
            String resp = HttpUtil.doHttpPost(Constants.GET_USER_INFO_API, generalParamsMap);
            Assert.notBlank(resp, "中国电信：获取用户信息失败, 接口请求结果为空");
            log.info("中国电信：获取用户信息响应: {}", resp);
            //取号结果需要RSA解密
            JSONObject respObj = JSONObject.parseObject(resp);
            Assert.notNull(respObj, "中国电信：获取用户信息对象解析失败");
            if (0 != respObj.getInteger(com.yeelight.service.user.server.constant.Constants.RESULT_KEY)) {
                throw new BizException(respObj.getString(com.yeelight.service.user.server.constant.Constants.RESULT_KEY), respObj.getString(com.yeelight.service.user.server.constant.Constants.MSG_KEY));
            }
            String encData = respObj.getString("data");
            //使用RSA私钥解密encData
            String finalData = RsaUtils.decryptByPrivateKeyForLongStr(encData, Constants.RSA_PRIVATE_KEY);
            log.info("中国电信：获取用户信息解析数据: {}", finalData);
            ChinaTelecomUserInfo telecomUserInfo = JSONObject.parseObject(finalData, ChinaTelecomUserInfo.class);
            Assert.notNull(telecomUserInfo, "中国电信：获取用户信息解析失败");
            return telecomUserInfo;
        } catch (Exception e) {
            Assert.throwException("中国电信：获取用户信息异常: " + e.getMessage());
        }
        return null;
    }
}
