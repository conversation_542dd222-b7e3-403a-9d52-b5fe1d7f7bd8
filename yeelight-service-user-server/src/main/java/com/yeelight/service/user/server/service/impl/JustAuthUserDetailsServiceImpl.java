package com.yeelight.service.user.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.YeelightSocialUserService;
import com.yeelight.service.user.client.token.JustAuthAuthenticationToken;
import com.yeelight.service.user.server.bizlog.enums.BizTypeEnums;
import com.yeelight.service.user.server.bizlog.utils.BizOperateLogUtils;
import com.yeelight.service.user.server.config.RedisManager;
import com.yeelight.service.user.server.request.auth.AuthAppleRequest;
import com.yeelight.service.user.server.request.auth.YeelightAuthRequestFactory;
import com.yeelight.service.user.server.service.JustAuthUserDetailsService;
import com.yeelight.service.user.server.support.UserConverter;
import com.yeelight.service.user.server.utils.Assert;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.enums.AuthResponseStatus;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.request.AuthRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;

/**
 * JustAuthUserDetailsService Implementation
 * @description: JustAuthUserDetailsService实现类
 * <AUTHOR> Yu
 * @date 9/13/21 5:56 PM
 */
@Slf4j
@Service
public class JustAuthUserDetailsServiceImpl implements JustAuthUserDetailsService {
    @Resource
    private YeelightSocialUserService yeelightSocialUserService;

    @Resource
    private YeelightAuthRequestFactory yeelightAuthRequestFactory;

    @Resource
    private RedisManager redisManager;

    @Resource
    private HttpServletRequest httpServletRequest;

    /**
     * 根据token加载用户信息。
     * 该方法用于在用户通过第三方认证登录时，加载或创建用户信息，并设置其权限。
     *
     * @param authAuthenticationToken 认证令牌，包含用户认证信息。
     * @return YeelightUser 用户对象，包含用户详细信息及权限。
     * @throws UsernameNotFoundException 当用户不存在时抛出此异常。
     */
    @Override
    public YeelightUser loadUserByToken(JustAuthAuthenticationToken authAuthenticationToken) throws UsernameNotFoundException {
        // 尝试从认证令牌中获取用户信息
        AuthUser authUser = (AuthUser) authAuthenticationToken.getPrincipal();
        String state = httpServletRequest.getParameter(OAuth2Utils.STATE);
        log.info("JustAuthUser: {}", JSON.toJSONString(authUser));

        // 判断是否获取到了用户信息
        if (Objects.isNull(authUser)) {
            return null;
        }

        // 尝试从SecurityContext中获取当前登录用户，如果未登录或不是预期用户类型，则尝试从Redis中获取
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        Long currentLoginUserId = null;
        if (Objects.nonNull(auth) && auth.isAuthenticated() && auth.getPrincipal() instanceof YeelightUser) {
            YeelightUser yeelightUser = (YeelightUser) auth.getPrincipal();
            currentLoginUserId = yeelightUser.getId();
        } else {
            // 从Redis中获取用户信息，基于state参数
            if (StringUtils.isNotBlank(state)) {
                String yeelightUserJson = (String) redisManager.redisTemplate().opsForValue().get(AuthAppleRequest.APPLE_BINDING_USER + state);
                if (StringUtils.isNotBlank(yeelightUserJson)) {
                    YeelightUser yeelightUser = JSON.parseObject(yeelightUserJson, YeelightUser.class);
                    if (Objects.nonNull(yeelightUser)) {
                        currentLoginUserId = yeelightUser.getId();
                    }
                }
            }
        }

        // 自动注册或获取社交用户信息，并设置用户权限
        YeelightUserDto yeelightUserDto = autoRegisterSocialUser(authUser, currentLoginUserId);
        if (Objects.nonNull(yeelightUserDto)) {
            Collection<GrantedAuthority> authorities = new ArrayList<>();
            authorities.add(new CustomGrantedAuthority("ROLE_USER"));

            // 将用户信息从DTO拷贝到实体类，并设置权限
            YeelightUser yeelightUser = UserConverter.dtoToClientYeelightUser(yeelightUserDto);
            yeelightUser.setGrantedAuthorities(authorities);

            return yeelightUser;
        } else {
            // 当用户信息无法获取时，抛出用户名不存在异常
            throw new UsernameNotFoundException(I18nUtil.getMessage("ResultCode.Common.PARAMS_MISS"));
        }
    }

    /**
     * 根据三方平台ID和平台来源获取社交用户信息，并自动刷新令牌。
     *
     * @param yeelightId 用户的Yeelight ID，不能为空。
     * @param source 三方平台的类型标识，比如微信、微博，不能为空且必须是合法的OAuth类型。
     * @return SocialUserDto 社交用户数据传输对象，包含了用户在三方平台的信息及自动刷新后的令牌。
     * @throws BizException 如果用户ID为空、三方类型为空或不合法时，抛出业务异常。
     */
    @Override
    public SocialUserDto getSocialUser(Long yeelightId, String source) throws BizException {
        // 校验用户ID和来源参数不能为空
        Assert.notNull(yeelightId, "用户ID参数不能为空");
        Assert.notBlank(source, "三方类型不能为空");
        // 校验来源是否为合法的OAuth类型
        Assert.isNotTrue(yeelightAuthRequestFactory.oauthList().contains(source), "三方类型[{}]不合法", source);
        // 根据用户ID和来源查找社交用户信息
        SocialUserDto socialUser = yeelightSocialUserService.findSocialUserBySource(yeelightId, source);
        // 对找到的社交用户信息进行令牌自动刷新处理
        return autoRefreshToken(socialUser);
    }

    /**
     * 根据三方平台TOKEN和平台来源获取社交用户信息，并自动刷新令牌。
     *
     * @param yeelightToken 用户的yeelight令牌，不能为空。
     * @param source 三方平台来源标识，比如微信、QQ等，不能为空且必须是合法的OAuth来源。
     * @return SocialUserDto 社交用户数据传输对象，包含用户基本信息和令牌信息。
     * @throws BizException 如果输入参数不合法或查询过程中出现业务错误，则抛出BizException。
     */
    @Override
    public SocialUserDto getSocialUser(String yeelightToken, String source) throws BizException {
        // 校验yeelightToken和source参数不能为空
        Assert.notBlank(yeelightToken, "用户TOKEN参数不能为空");
        Assert.notBlank(source, "三方类型不能为空");

        // 校验source是否为合法的OAuth来源
        Assert.isNotTrue(yeelightAuthRequestFactory.oauthList().contains(source), "三方类型[{}]不合法", source);

        // 根据yeelightToken和source查询社交用户信息
        SocialUserDto socialUser = yeelightSocialUserService.findSocialUserBySource(yeelightToken, source);

        // 对查询到的社交用户信息进行令牌自动刷新
        return autoRefreshToken(socialUser);
    }


    /**
     * 自动注册社交用户。
     * 该方法用于处理社交用户（如微信、QQ等）的自动注册或绑定逻辑。根据当前是否登录的用户以及社交用户是否已绑定过用户，
     * 实现注册新用户或绑定已有用户的功能，并处理相应的业务逻辑。
     * Lock key example: service_user:auto_register_social_user:105935915457451364100_GOOGLE
     * Related tables: `yeelight_users` (用户主表) `social_user` (三方用户表) `social_user_auth` (关联表：主表 + 三方用户表)
     *
     * @param authUser 认证用户信息，包含社交平台的用户标识和来源等信息。这允许方法识别和处理特定于社交平台的用户数据。
     * @param currentLoginUserId 当前登录的用户ID，如果用户未登录，则为null。此参数用于区分是新用户注册还是现有用户绑定社交账号。
     * @return 返回绑定或注册后的用户信息。这包括但不限于用户的基本信息，以便于调用方进一步处理。
     * @throws BizException 业务异常，可能由于用户验证或其他业务规则失败导致。这表明在处理过程中遇到了特定的业务错误。
     */
    @Override
    public YeelightUserDto autoRegisterSocialUser(AuthUser authUser, Long currentLoginUserId) throws BizException {
        return yeelightSocialUserService.autoRegisterSocialUser(authUser, currentLoginUserId);
    }

    /**
     * 自动刷新用户社交平台的访问令牌。
     * 如果用户的访问令牌已过期且存在有效的刷新令牌，则尝试使用刷新令牌获取新的访问令牌。
     *
     * @param socialUser 包含用户社交平台信息的DTO，如访问令牌、刷新令牌等。
     * @return 返回更新后的SocialUserDto对象，可能包含了新的访问令牌。
     * @throws BizException 业务异常，如果刷新令牌失败或更新用户令牌信息失败时抛出。
     */
    private SocialUserDto autoRefreshToken(SocialUserDto socialUser) throws BizException {
        // 检查用户对象非空，访问令牌已过期且存在有效的刷新令牌
        if (Objects.nonNull(socialUser) && socialUser.isAccessTokenExpired() && StringUtils.isNotBlank(socialUser.getRefreshToken())) {
            // 根据社交平台来源获取对应的认证请求对象
            AuthRequest authRequest = yeelightAuthRequestFactory.get(socialUser.getSource());
            // 使用刷新令牌刷新访问令牌
            AuthResponse<?> authResponse = authRequest.refresh(AuthToken.builder().refreshToken(socialUser.getRefreshToken()).build());

            // 检查刷新访问令牌的响应状态
            if (AuthResponseStatus.SUCCESS.getCode() == authResponse.getCode()) {
                // 将刷新后的令牌信息更新到用户社交服务端
                AuthToken originalAuthToken = (AuthToken) authResponse.getData();
                com.yeelight.service.user.client.dto.AuthToken authToken = BeanUtils.objToBean(originalAuthToken, com.yeelight.service.user.client.dto.AuthToken.class);
                yeelightSocialUserService.updateSocialUserTokenInfo(socialUser.getId(), authToken);

                // 更新SocialUserDto对象的令牌信息
                BeanUtils.copyPropertiesIgnoreNull(authToken, socialUser);

                // 记录刷新三方令牌的操作日志
                BizOperateLogUtils.sendSimpleBizOperateLog(socialUser.getYeelightId(), BizTypeEnums.三方集成.getCode(), "自动刷新三方token", socialUser);
            }
        }
        // 返回可能已更新的用户社交平台信息DTO
        return socialUser;
    }
}
