/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.service.impl
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-25 11:09:11:09
 */
package com.yeelight.service.user.server.service.impl;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.domain.ClientDetail;
import com.yeelight.service.user.client.domain.LoginSessionInfo;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.ClientDetailDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.service.ClientDetailService;
import com.yeelight.service.user.client.service.UserSessionManagementService;
import com.yeelight.service.user.client.service.YeelightUserReadService;
import com.yeelight.service.user.server.config.JwtAuthProperties;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.service.JwtAuthService;
import com.yeelight.service.user.server.utils.Assert;
import com.yeelight.service.user.server.utils.AuthUtils;
import com.yeelight.service.user.server.vo.JwtAuthGrantInfoVo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.MacAlgorithm;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.cache.AuthStateCache;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-25 11:09:11:09
 */
@Slf4j
@Service
public class JwtAuthServiceImpl implements JwtAuthService {
    private static final MacAlgorithm SIGNATURE_ALGORITHM = Jwts.SIG.HS512;

    @Resource
    private JwtAuthProperties jwtAuthProperties;

    @Resource
    private ClientDetailService clientDetailService;

    @Resource
    private UserSessionManagementService userSessionManagementService;

    @Resource
    private YeelightUserReadService yeelightUserReadService;

    @Resource
    private AuthStateCache authStateCache;

    /**
     * 生成用于认证的JWT Token。
     *
     * @param vendor 厂商名称，将通过UserVendorHolder转换为简单的厂商名。
     * @param username 用户名。
     * @param sessionId 会话ID，用于标识当前用户的会话。
     * @return 生成的JWT Token字符串。
     */
    @Override
    public String generateToken(String vendor, String username, String sessionId) {
        // 转换复杂的厂商名称为简单的格式
        vendor = UserVendorHolder.getSimpleVendor(vendor);

        // 获取指定用户的所有会话信息
        List<LoginSessionInfo> sessionInfos = userSessionManagementService.getSessionInfos(vendor, username);

        // 如果用户的会话数量超过最大允许值，并且当前会话ID不在其中，则踢掉最早的会话
        if (sessionInfos.size() >= jwtAuthProperties.getMaxTokens() && sessionInfos.stream().noneMatch(sessionInfo -> StringUtils.equals(sessionInfo.getSessionId(), sessionId))) {
            Optional<LoginSessionInfo> earliestSession = sessionInfos.stream().min(Comparator.comparing(LoginSessionInfo::getCreationTime));
            // 删除最早的会话
            earliestSession.ifPresent(loginSessionInfo -> userSessionManagementService.expireSession(loginSessionInfo.getSessionId()));
        }

        // 构建JWT Token，设置各种声明
        return Jwts.builder()
                // 签发人
                .issuer(vendor)
                // 主题
                .subject(username)
                // 受众
                .audience().add(jwtAuthProperties.getClaims().getAudience()).and()
                // 签发时间
                .issuedAt(new Date())
                // 生效时间
                .notBefore(generateBeforeDate())
                // 过期时间
                .expiration(generateExpirationDate())
                // 编号
                .id(sessionId)
                // Token类型
                .claim("type", jwtAuthProperties.getType().name())
                // 签名密钥
                .signWith(getSecretKey())
                .compact();
    }

    /**
     * 验证令牌的有效性，并返回相应的用户ID。
     *
     * @param vendor 提供令牌的供应商名称。
     * @param token 需要验证的令牌字符串。
     * @return 如果令牌有效，返回与令牌关联的用户ID；否则抛出BizException异常。
     * @throws BizException 如果验证过程中出现错误，比如令牌无效或供应商不存在。
     */
    @Override
    public String validateToken(String vendor, String token) throws BizException {
        // 通过供应商名称和令牌获取所有的声明信息
        Claims claims = getAllClaims(vendor, token);
        // 返回声明中的ID字段，即用户ID
        return claims.getId();
    }


    /**
     * 根据令牌获取登录会话信息。
     *
     * @param vendor 提供商名称，用于指定令牌所属的提供商。
     * @param token 用户登录成功后返回的令牌。
     * @return LoginSessionInfo 登录会话信息对象，包含用户会话的详细信息。
     * @throws BizException 业务异常，如果在获取会话信息过程中出现错误。
     */
    @Override
    public LoginSessionInfo getLoginSessionByToken(String vendor, String token) throws BizException {
        // 通过供应商和令牌获取所有的声明信息
        Claims claims = getAllClaims(vendor, token);
        // 根据声明中的ID获取会话信息
        return userSessionManagementService.getSessionInfo(claims.getId());
    }


    /**
     * 从HTTP请求中获取认证token。
     * 首先尝试从请求直接获取JWT认证token，如果未获取到且存在有效的state参数，
     * 则尝试从三方认证状态缓存中根据state参数获取token。
     *
     * @param request HttpServletRequest对象，用于获取请求参数和信息。
     * @return 认证token，如果无法获取到则返回null。
     */
    @Override
    public String getToken(HttpServletRequest request) {
        // 检查请求对象是否为空
        if (Objects.isNull(request)) {
            return null;
        }
        // 尝试从请求直接获取JWT认证token
        String token = AuthUtils.getJwtAuthToken(request);
        // 如果token为空，且存在state参数，则尝试从缓存中获取token
        if (StringUtils.isBlank(token)) {
            String state = request.getParameter(OAuth2Utils.STATE);
            if (StringUtils.isNotBlank(state)) {
                // 从三方认证状态缓存中根据state参数获取token
                token = authStateCache.get(SecurityConstants.THIRD_PARTY_AUTH_TOKEN_PREFIX + state);
            }
        }
        return token;
    }


    /**
     * 从请求中获取token，如果token为空或为空白字符串，则抛出BizException异常。
     *
     * @param request HttpServletRequest对象，用于获取请求中的token。
     * @return String 返回从请求中提取到的非空token。
     * @throws BizException 如果获取到的token为空或空白字符串，抛出此异常。
     */
    @Override
    public String getTokenThrowException(HttpServletRequest request) throws BizException {
        // 从请求中获取token
        String token = getToken(request);
        // 确保token不为空或空白字符串，否则抛出异常
        Assert.notBlank(token, I18nUtil.getMessage("ResultCode.Common.UNAUTHORIZED"));
        return token;
    }

    /**
     * 根据令牌获取用户信息.
     *
     * @param vendor 提供者名称，用于标识用户来源。
     * @param token 用户的令牌，用于验证用户身份。
     * @return YeelightUserDto 用户信息的封装对象，包含用户的详细信息。
     * @throws BizException 如果用户不存在或令牌无效，则抛出业务异常。
     */
    @Override
    public YeelightUserDto getUserInfoByToken(String vendor, String token) throws BizException {
        // 通过令牌获取用户名
        String username = getUserNameByToken(vendor, token);

        // 根据用户名查询用户信息
        YeelightUserDto yeelightUserDto = yeelightUserReadService.findUserByUsername(username);

        // 确保用户信息不为空，否则抛出用户不存在异常
        Assert.notNull(yeelightUserDto, I18nUtil.message("User.Exception.user.notExist", ""));

        return yeelightUserDto;
    }


    @Override
    public YeelightUserDto getCurrentUserInfoByToken(String vendor, String token) throws BizException {
        YeelightUserDto yeelightUserDto;

        // 尝试从SecurityContextHolder中获取认证信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (Objects.isNull(auth) || Objects.isNull(auth.getPrincipal()) || !(auth.getPrincipal() instanceof YeelightUser yeelightUser)) {
            // 如果认证信息不存在或不是预期的类型，则通过token从外部服务获取用户信息
            yeelightUserDto = getUserInfoByToken(vendor, token);
        } else {
            // 如果存在有效的认证信息，则直接从认证主体中获取用户ID
            yeelightUserDto = yeelightUserReadService.findUserById(yeelightUser.getId());
        }

        // 确保获取到的用户信息不为空，否则抛出异常
        Assert.notNull(yeelightUserDto, I18nUtil.message("User.Exception.user.notExist", ""));

        return yeelightUserDto;
    }

    /**
     * 根据令牌获取授权信息。
     *
     * @param vendor 厂商标识，用于指定令牌所属的厂商。
     * @param token 用户的访问令牌。
     * @param clientId 客户端ID，用于识别请求的客户端。
     * @param scope 请求的权限范围。
     * @return JwtAuthGrantInfoVo 包含授权信息的对象，如用户ID、用户名、用户头像、客户端ID、客户端名称等。
     * @throws BizException 业务异常，可能在客户端信息查询或用户信息查询过程中抛出。
     */
    @Override
    public JwtAuthGrantInfoVo getAuthGrantInfoByToken(String vendor, String token, String clientId, String scope) throws BizException {
        JwtAuthGrantInfoVo jwtAuthGrantInfoVo = new JwtAuthGrantInfoVo();
        // 确保clientId不为空
        Assert.notNull(clientId, "client_id is required");
        // 通过clientId加载客户端详情
        ClientDetail client = clientDetailService.getClient(clientId);
        // 确保找到对应的客户端详情
        Assert.notNull(client, "client not found");

        ClientDetailDto clientDetails = new ClientDetailDto(
                client.getClientId(),
                client.getClientSecret(),
                client.getResourceIds(),
                client.getScope(),
                client.getAuthorizedGrantTypes(),
                client.getWebServerRedirectUri(),
                client.getAuthorities(),
                client.getAdditionalInformation(),
                client.getAutoApprove(),
                client.getAccessTokenValidity(),
                client.getRefreshTokenValidity()
        );

        // 解析并过滤权限范围，确保只有客户端允许的权限被授权
        Set<String> scopes = OAuth2Utils.parseParameterList(scope);
        Set<String> authorizedScopes;
        if (CollectionUtils.isNotEmpty(scopes)) {
            authorizedScopes = scopes.stream()
                    .filter(s -> clientDetails.getScope().contains(s))
                    .collect(Collectors.toSet());
        } else {
            authorizedScopes = clientDetails.getScope();
        }

        // 从客户端详情中获取额外信息，并设置到返回对象中
        Map<String, Object> additionalInformation = clientDetails.getAdditionalInformation();
        jwtAuthGrantInfoVo.setScopes(authorizedScopes);
        jwtAuthGrantInfoVo.setClientId(clientId);
        if (additionalInformation != null) {
            jwtAuthGrantInfoVo.setClientName(Objects.nonNull(additionalInformation.get("name"))?additionalInformation.get("name").toString():null);
            jwtAuthGrantInfoVo.setClientWarning(Objects.nonNull(additionalInformation.get("warning"))?additionalInformation.get("warning").toString():null);
        }

        // 通过令牌获取用户信息，并设置到返回对象中
        YeelightUserDto userDto = getUserInfoByToken(vendor, token);
        jwtAuthGrantInfoVo.setUserId(userDto.getId());
        jwtAuthGrantInfoVo.setUserNickName(userDto.getName());
        jwtAuthGrantInfoVo.setUserAvatar(userDto.getAvatar());

        return jwtAuthGrantInfoVo;
    }

    /**
     * 实现退出登录的功能。
     * 通过给定的供应商和令牌，找到对应的会话ID，并使该会话失效。
     *
     * @param vendor 供应商名称，用于指定用户登录的供应商。
     * @param token 用户登录时获取的令牌，用于验证用户身份和获取会话ID。
     * @throws BizException 如果过程中出现业务异常，则抛出BizException。
     */
    @Override
    public void logout(String vendor, String token) throws BizException {
        // 根据供应商和令牌获取会话ID
        String sessionId = getSessionIdByToken(vendor, token);
        // 利用会话ID使对应的会话失效
        userSessionManagementService.expireSession(sessionId);
    }


    /**
     * 缓存第三方授权token。
     * 该方法会根据提供的state和请求对象request，提取token并将其缓存起来。
     * 注意：如果state为空或token为空，则不会进行缓存操作。
     *
     * @param state 用于标识缓存token的唯一状态码。
     * @param request 请求对象，用于从中提取token。
     */
    @Override
    public void cacheTokenForThirdParty(String state, HttpServletRequest request) {
        // 如果state为空，则直接返回，不进行后续操作
        if (StringUtils.isBlank(state)) {
            return;
        }
        // 从请求中提取token
        String token = getToken(request);
        // 如果提取到的token为空，则直接返回，不进行缓存
        if (StringUtils.isBlank(token)) {
            return;
        }
        // 将token缓存起来，使用state作为key，token作为value
        authStateCache.cache(SecurityConstants.THIRD_PARTY_AUTH_TOKEN_PREFIX + state, token);
    }


    /**
     * 根据供应商和令牌获取用户名称。
     * @param vendor 供应商名称，用于指定令牌所属的供应商。
     * @param token 用于身份验证的令牌。
     * @return 返回通过令牌验证的用户的名称。
     * @throws BizException 如果验证过程中出现业务错误，则抛出此异常。
     */
    private String getUserNameByToken(String vendor, String token) throws BizException {
        // 通过供应商和令牌获取所有的声明信息
        Claims claims = getAllClaims(vendor, token);
        // 返回声明中的主题（用户名称）
        return claims.getSubject();
    }


    /**
     * 根据供应商和令牌获取会话ID。
     *
     * @param vendor 供应商名称，用于指定令牌所属的供应商。
     * @param token 用于验证和识别用户会话的令牌。
     * @return 返回从令牌中解析出的会话ID。
     * @throws BizException 如果在获取会话ID过程中出现业务错误，则抛出此异常。
     */
    private String getSessionIdByToken(String vendor, String token) throws BizException {
        // 通过供应商和令牌获取所有的声明信息
        Claims claims = getAllClaims(vendor, token);
        // 返回声明中的ID字段，即会话ID
        return claims.getId();
    }

    /**
     * 获取所有的声明信息。
     * 该方法用于解析给定的token，从中提取出声明（claims）信息。
     *
     * @param vendor 厂商名称，用于验证token的发行者。
     * @param token 用户的token，用于验证和提取声明信息。
     * @return Claims 声明信息对象，包含token中的所有声明。
     * @throws BizException 如果解析token过程中发生异常，则抛出业务异常。
     */
    private Claims getAllClaims(String vendor, String token) throws BizException {
        // 根据厂商名称获取简化的厂商标识
        vendor = UserVendorHolder.getSimpleVendor(vendor);
        Claims claims;
        try {
            // 构建JWT解析器，并设置要求的Issuer、Audience、type等信息，以及签名的密钥
            claims = Jwts.parser()
                    // 验证token的发行者
                    .requireIssuer(vendor)
                    // 注释掉的代码用于验证token的主题
                    //.requireSubject(properties.getJwt().getClaims().getSubject())
                    // 验证token的接收者
                    .requireAudience(jwtAuthProperties.getClaims().getAudience())
                    // 验证token的类型
                    .require("type", jwtAuthProperties.getType().name())
                    // 设置签名密钥
                    .verifyWith(getSecretKey())
                    .build().parseSignedClaims(token).getPayload();
        } catch (Exception e) {
            // 如果解析过程中出现异常，则抛出业务异常，异常信息为解析异常的message
            throw new BizException(e.getMessage());
        }
        return claims;
    }

    /**
     * 生成过期时间
     *
     * @return 过期时间
     */
    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + jwtAuthProperties.getClaims().getExpirationTimeDays() * 86400 * 1000);
    }

    /**
     * 生成生效时间
     *
     * @return 生效时间
     */
    private Date generateBeforeDate() {
        return new Date(System.currentTimeMillis() - 300 * 1000);
    }

    /**
     * 获取用于签名的密钥。
     * <p>
     * 此方法解码JWT认证属性中指定的密钥，并返回一个密钥对象，可用于JWT的签名和验证过程。
     * </p>
     *
     * @return 返回解码后的密钥对象，用于JWT的签名算法。
     */
    private SecretKey getSecretKey() {
        // 解码JWT认证属性中的密钥
        byte[] decodedKey = Decoders.BASE64.decode(jwtAuthProperties.getSecret());
        // 创建并返回一个密钥对象，用于签名算法
        return new SecretKeySpec(decodedKey, SIGNATURE_ALGORITHM.getId());
    }

}
