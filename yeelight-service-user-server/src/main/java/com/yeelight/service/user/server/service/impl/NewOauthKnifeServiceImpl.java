package com.yeelight.service.user.server.service.impl;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.server.custom.NewCustomRedisAuthorizationService;
import com.yeelight.service.user.server.request.AuthCodeRequest;
import com.yeelight.service.user.server.service.OauthKnifeService;
import com.yeelight.service.user.server.utils.Assert;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.server.authorization.OAuth2Authorization;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationCode;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 新版OAuth刀具服务实现类
 * 基于新版Spring Authorization Server实现
 * 
 * <AUTHOR>
 * @since 2025-01
 */
@Slf4j
@Service
public class NewOauthKnifeServiceImpl implements OauthKnifeService {

    @Resource
    private NewCustomRedisAuthorizationService authorizationService;

    @Resource
    private RegisteredClientRepository registeredClientRepository;

    @Resource
    private TokenService tokenService;

    /**
     * 生成授权码。
     * 根据提供的认证请求，验证令牌的有效性，并生成相应的授权码。
     *
     * @param request 包含认证请求详情的对象，如令牌、客户端ID、范围、状态和重定向URI。
     * @return 生成的授权码。
     * @throws BizException 如果验证过程中出现问题，如令牌不存在或已过期。
     */
    @Override
    public String generateAuthCode(AuthCodeRequest request) throws BizException {
        // 验证请求中的令牌是否为空
        Assert.notBlank(request.getToken(), I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));
        // 根据令牌获取认证信息
        OAuth2AuthenticationDto authentication = tokenService.getAuthenticationByToken(request.getToken());
        // 确保认证信息不为空
        Assert.notNull(authentication, I18nUtil.getMessage("ResultCode.Common.TOKEN_NOT_EXIST_OR_EXPIRED"));

        try {
            // 获取注册的客户端信息
            RegisteredClient registeredClient = registeredClientRepository.findByClientId(request.getClientId());
            if (registeredClient == null) {
                throw new BizException("Client not found: " + request.getClientId());
            }

            // 创建用户认证
            UsernamePasswordAuthenticationToken userAuthentication = 
                (UsernamePasswordAuthenticationToken) authentication.getUserAuthentication();

            // 构建授权参数 - 保持与原版相同的逻辑
            Map<String, Object> attributes = new HashMap<>(8);

            // 从原版authentication中保留重要信息，确保与原版逻辑一致
            if (authentication.getStoredRequest() != null) {
                // 保留原始请求参数
                attributes.put("original_request_parameters", authentication.getStoredRequest().getRequestParameters());
                attributes.put("original_scope", authentication.getStoredRequest().getScope());
                attributes.put("original_resource_ids", authentication.getStoredRequest().getResourceIds());
                attributes.put("original_response_types", authentication.getStoredRequest().getResponseTypes());
                attributes.put("original_extensions", authentication.getStoredRequest().getExtensions());
                attributes.put("original_authorities", authentication.getAuthorities());
            }

            // 添加当前请求的参数
            attributes.put(OAuth2Utils.SCOPE, request.getScope());
            attributes.put(OAuth2Utils.STATE, request.getState());
            attributes.put(OAuth2Utils.REDIRECT_URI, request.getRedirectUri());
            attributes.put(OAuth2Utils.RESPONSE_TYPE, "code");
            attributes.put(OAuth2Utils.CLIENT_ID, request.getClientId());
            attributes.put("approved", true);

            // 生成授权码
            String authorizationCode = "yeelight_auth_code_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "");

            // 创建OAuth2AuthorizationCode
            Instant issuedAt = Instant.now();
            // 10分钟过期
            Instant expiresAt = issuedAt.plus(10, ChronoUnit.MINUTES);
            OAuth2AuthorizationCode authCode = new OAuth2AuthorizationCode(authorizationCode, issuedAt, expiresAt);

            // 构建OAuth2Authorization - 保持与原版逻辑一致
            OAuth2Authorization.Builder authorizationBuilder = OAuth2Authorization.withRegisteredClient(registeredClient)
                .principalName(authentication.getName())
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .attributes(attrs -> attrs.putAll(attributes));

            // 设置授权范围 - 优先使用原版authentication中的scope，然后是请求中的scope
            Set<String> authorizedScopes = new HashSet<>();
            if (authentication.getStoredRequest() != null && authentication.getStoredRequest().getScope() != null) {
                authorizedScopes.addAll(authentication.getStoredRequest().getScope());
            }
            if (request.getScope() != null && !request.getScope().trim().isEmpty()) {
                authorizedScopes.addAll(Set.of(request.getScope().split("\\s+")));
            }
            if (!authorizedScopes.isEmpty()) {
                authorizationBuilder.authorizedScopes(authorizedScopes);
            }

            // 添加授权码token
            authorizationBuilder.token(authCode);

            // 保存授权信息
            OAuth2Authorization authorization = authorizationBuilder.build();
            authorizationService.save(authorization);

            log.debug("Generated authorization code for client: {}, user: {}", request.getClientId(), authentication.getName());
            return authorizationCode;

        } catch (Exception e) {
            log.error("Error generating authorization code for client: {}", request.getClientId(), e);
            throw new BizException("Failed to generate authorization code: " + e.getMessage());
        }
    }

    /**
     * 快速创建访问令牌。
     * 该方法用于根据用户信息和客户端ID快速生成OAuth2令牌，不需要进行密码认证等流程。
     *
     * @param clientId 客户端ID，标识请求令牌的客户端。
     * @param yeelightUser 用户对象，必须不为空且账户处于启用状态。
     * @param extendParameters 扩展参数，可选，用于在生成令牌时附加额外的信息。
     * @return OAuth2Token 返回生成的OAuth2令牌对象。
     * @throws BizException 如果用户不存在或已禁用，抛出业务异常。
     */
    @Override
    public OAuth2Token fastCreateAccessToken(String clientId, YeelightUser yeelightUser, Map<String, String> extendParameters) throws BizException {
        // 确保用户对象不为空
        Assert.notNull(yeelightUser, I18nUtil.message("User.Exception.user.notExist", ""));
        // 检查用户是否被禁用
        if (!yeelightUser.isEnabled()) {
            throw new DisabledException(I18nUtil.getMessage("ResultCode.账户已禁用"));
        }
        // 通过用户名和客户端ID模拟认证并生成令牌
        return tokenService.mockAuthenticationByUserName(clientId, yeelightUser.getUsername(), extendParameters);
    }
}
