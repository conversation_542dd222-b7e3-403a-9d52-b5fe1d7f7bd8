package com.yeelight.service.user.server.service.impl;

import com.yeelight.basic.platform.rpc.dto.FileSystemConfig;
import com.yeelight.basic.platform.rpc.utils.FileSystemUtils;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.Assert;
import com.yeelight.service.framework.util.DateUtils;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.dto.YeelightUserExtendDto;
import com.yeelight.service.user.client.service.YeelightUserService;
import com.yeelight.service.user.client.service.YeelightUsersExtendService;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.request.UpdatePasswordRequest;
import com.yeelight.service.user.server.request.UpdateUserNameRequest;
import com.yeelight.service.user.server.request.UploadUserAvatarRequest;
import com.yeelight.service.user.server.service.PersonalInfoManageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Deprecated
public class PersonalInfoManageServiceImpl implements PersonalInfoManageService {

    @Resource
    private YeelightUserService yeelightUserService;

    @Resource
    private YeelightUsersExtendService yeelightUsersExtendService;

    @Override
    public void updateUsername(UpdateUserNameRequest request) throws BizException {
        YeelightUserDto user = yeelightUserService.findUserById(request.getId());
        Assert.notNull(user, "用户不存在, id=[{}]", request.getId());

        YeelightUserDto yeelightUser = yeelightUserService.findUserByUsername(request.getUsername());
        if (Objects.nonNull(yeelightUser)) {
            throw new BizException("用户名已存在！");
        }
        if (CollectionUtils.isNotEmpty(yeelightUsersExtendService.selectByExample(YeelightUserExtendDto.builder().yeelightUserId(user.getId()).build()))) {
            throw new BizException("用户名只能修改一次！");
        }

        try {
            YeelightUserDto userDto = new YeelightUserDto();
            BeanUtils.copyProperties(request, userDto);
            yeelightUserService.updateUsername(userDto, user);
        } catch (Exception e) {
            log.error("用户名更新异常 ", e);
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public Result<?> updateName(String name, Long id) throws BizException {
        YeelightUserDto user = yeelightUserService.findUserById(id);
        Assert.notNull(user, "用户不存在, id=[{}]", id);

        try {
            YeelightUserDto userDto = new YeelightUserDto();
            userDto.setId(id);
            userDto.setName(name);
            return yeelightUserService.update(userDto);
        } catch (Exception e) {
            log.error("用户名更新异常 ", e);
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public boolean checkOldPasswordIsCorrect(String oldPassword, Long id) throws BizException {
        YeelightUserDto user = yeelightUserService.findUserById(id);
        Assert.notNull(user, "用户不存在，用户id=[{}]", id);

        return UserUtils.passwordEncoder().matches(oldPassword, user.getPassword());
    }

    @Override
    public String uploadAvatar(UploadUserAvatarRequest uploadOauthAvatar, Long id) throws BizException {
        Assert.notNull(yeelightUserService.findUserById(id), "用户不存在, id=[{}]", id);
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyy-MM-dd"));
        String fileName = "avatars/" + today + "/" + DateUtils.getCurrentSecond() + uploadOauthAvatar.getFileName();

        try {
            FileSystemConfig config = FileSystemUtils.getConfig(FileSystemConfig.ConfigType.YOS_USER);
            String url = FileSystemUtils.upload(config, fileName, uploadOauthAvatar.getBytes());
            YeelightUserDto userDto = new YeelightUserDto();
            userDto.setId(id);
            userDto.setAvatar(url);
            yeelightUserService.update(userDto);
            return url;
        } catch (Exception e) {
            log.error("Upload avatar fail ...... ", e);
            throw new BizException("头像上传失败！");
        } finally {
            uploadOauthAvatar.setBytes(null);
        }
    }

    @Override
    public void changePassword(UpdatePasswordRequest updatePassword) throws BizException {
        YeelightUserDto user = yeelightUserService.findUserById(updatePassword.getId());
        Assert.notNull(user, "用户不存在，用户id=[{}]", updatePassword.getId());

        try {
            yeelightUserService.updatePassword(updatePassword.getId(), updatePassword.getOldPassword(), updatePassword.getNewPassword());
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public YeelightUserDto userDetail(Long id) {
        YeelightUserDto userDto = yeelightUserService.findUserById(id);
        if (null != userDto) {
            userDto.setPassword(Strings.EMPTY);
        }
        if (userDto != null && Objects.nonNull(userDto.getAvatar()) && !userDto.getAvatar().contains(Constants.HTTP_KEY)) {
            userDto.setAvatar(null);
        }
        return userDto;
    }

}
