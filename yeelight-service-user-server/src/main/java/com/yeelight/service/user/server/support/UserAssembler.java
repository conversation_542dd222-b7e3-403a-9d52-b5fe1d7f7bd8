/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.support
 * Description: 用户服务组装
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-06 19:37:19:37
 */
package com.yeelight.service.user.server.support;

import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.dto.AuthToken;
import com.yeelight.service.user.client.dto.AuthUser;
import com.yeelight.service.user.client.enums.UserDeleted;
import com.yeelight.service.user.client.enums.UserStatus;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.client.utils.UserUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.domain.SocialUser;
import com.yeelight.service.user.server.domain.YeelightUser;
import com.yeelight.service.user.server.utils.NickNameGenerator;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * Desc: 用户服务组装
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-05-06 19:37:19:37
 */
public class UserAssembler {
    /**
     * 将创建用户请求转换为Yeelight用户
     *
     * @param request 创建用户请求
     * @return 返回Yeelight用户
     */
    public static YeelightUser addRequestToYeelightUser(CreateUserRequest request) {
        if (request == null) {
            return null;
        }
        return BeanUtils.objToBean(request, YeelightUser.class);
    }



    /**
     * 对YeelightUser对象进行默认装饰操作。
     * 该方法会设置用户的默认属性，如果相应属性已经设置，则不会被覆盖。
     *
     * @param yeelightUser YeelightUser对象，不可为null。
     * @param region 用户的区域信息，如果用户未设置区域，则会用此参数作为默认区域。
     */
    public static void defaultDecorateYeelightUser(YeelightUser yeelightUser, String region) {
        if (yeelightUser == null) {
            return;
        }
        // 设置用户名为经过编码的空字符串
        yeelightUser.setUsername(UserUtils.usernameEncoder(yeelightUser.getUsername()));
        // 设置密码为经过编码的密码
        if (StringUtils.isEmpty(yeelightUser.getPassword())) {
            yeelightUser.setPassword(UserUtils.passwordEncoder().encode(UserUtils.saltEncoder()));
        } else {
            yeelightUser.setPassword(UserUtils.passwordEncoder().encode(yeelightUser.getPassword()));
        }
        // 设置用户状态为启用
        yeelightUser.setStatus(UserStatus.ENABLED.getCode());
        // 如果头像URL为空，则设置为默认头像
        if (StringUtils.isEmpty(yeelightUser.getAvatar())) {
            yeelightUser.setAvatar(UserVendorHolder.replaceDomain(Constants.DEFAULT_AVATAR));
        }
        // 设置用户删除状态为未删除
        yeelightUser.setDeleted(UserDeleted.UNDELETED.getCode());
        // 设置用户盐值
        yeelightUser.setSalt(UserUtils.saltEncoder());
        // 设置用户创建时间和更新时间
        yeelightUser.setCreateTime(LocalDateTime.now());
        yeelightUser.setUpdateTime(LocalDateTime.now());
        // 如果用户未设置区域，则使用传入的region作为默认区域
        yeelightUser.setRegion(Optional.ofNullable(yeelightUser.getRegion()).orElse(region));

        // 如果用户未设置名字，则根据区域和供应商生成昵称
        if (StringUtils.isBlank(yeelightUser.getName())) {
            yeelightUser.setName(NickNameGenerator.generate(region, UserVendorHolder.getVendorEnum()));
        }
    }

    /**
     * 将手机号转换为Yeelight用户
     *
     * @param phone 手机号
     * @param password 密码
     * @param region 区域
     * @return 返回Yeelight用户
     */
    public static YeelightUser phoneToYeelightUser(String phone, String password, String region) {
        if (StringUtils.isEmpty(phone)) {
            return null;
        }
        password = Optional.ofNullable(password).orElse(phone);
        YeelightUser yeelightUser = new YeelightUser();
        yeelightUser.setPassword(password);
        return yeelightUser;
    }

    /**
     * 将授权用户转换为社交用户
     *
     * @param authUser 授权用户
     * @return 返回社交用户
     */
    public static SocialUser authUserToSocialUser(AuthUser authUser) {
        if (authUser == null) {
            return null;
        }
        AuthToken authToken = authUser.getToken();
        if (Objects.isNull(authToken)) {
            authToken = new AuthToken();
        }
        return SocialUser.builder()
                .uuid(authUser.getUuid())
                .source(authUser.getSource())
                .accessToken(authToken.getAccessToken())
                .expireIn(authToken.getExpireIn())
                .refreshToken(authToken.getRefreshToken())
                .openId(authToken.getOpenId())
                .uid(authToken.getUid())
                .accessCode(authToken.getAccessCode())
                .unionId(authToken.getUnionId())
                .scope(authToken.getScope())
                .tokenType(authToken.getTokenType())
                .idToken(authToken.getIdToken())
                .macAlgorithm(authToken.getMacAlgorithm())
                .macKey(authToken.getMacKey())
                .code(authToken.getCode())
                .oauthToken(authToken.getOauthToken())
                .oauthTokenSecret(authToken.getOauthTokenSecret())
                .sessionKey(authToken.getSessionKey())
                .updatedTime((int) Instant.now().getEpochSecond())
                .build();
    }
}
