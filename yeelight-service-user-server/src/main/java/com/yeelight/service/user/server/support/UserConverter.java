/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.converter
 * Description: 用户对象转换类
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-06 16:09:16:09
 */
package com.yeelight.service.user.server.support;

import com.yeelight.service.framework.util.BeanUtils;
import com.yeelight.service.user.client.dto.SocialUserDto;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.client.request.CreateUserRequest;
import com.yeelight.service.user.server.domain.SocialUser;
import com.yeelight.service.user.server.domain.YeelightUser;
import org.apache.commons.lang3.StringUtils;

/**
 * Desc: 用户对象转换类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-05-06 16:09:16:09
 */
public class UserConverter {
    private static final int PHONE_NUMBER_FILLED_SCORE = 50;
    private static final int EMAIL_FILLED_SCORE = 50;
    private static final int USERNAME_IS_PHONE_NUMBER_SCORE = 0;
    private static final int USERNAME_IS_EMAIL_SCORE = 0;

    /**
     * 将 YeelightUser 转换为 YeelightUserDto 的静态方法。
     *
     * @param yeelightUser YeelightUser 实例，表示一个用户信息。
     * @return 返回转换后的 YeelightUserDto 实例，如果输入的用户无效或为null，则返回null。
     */
    public static YeelightUserDto yeelightUserToDto(YeelightUser yeelightUser) {
        // 检查输入的用户对象是否为null
        if (yeelightUser == null) {
            return null;
        }
        // 检查用户的ID是否为有效的Yeelight ID
        if (!UserJudge.isValidYeelightId(yeelightUser.getId())) {
            return null;
        }
        // 将YeelightUser对象转换为YeelightUserDto对象
        return BeanUtils.objToBean(yeelightUser, YeelightUserDto.class);
    }

    /**
     * 将YeelightUserDto对象转换为YeelightUser对象。
     *
     * @param yeelightUserDto 用户数据传输对象，不可为null。
     * @return 转换后的用户业务对象，如果输入为null，则返回null。
     */
    public static YeelightUser dtoToYeelightUser(YeelightUserDto yeelightUserDto) {
        // 检查输入的DTO对象是否为null
        if (yeelightUserDto == null) {
            return null;
        }
        // 使用BeanUtils的objToBean方法进行对象转换
        return BeanUtils.objToBean(yeelightUserDto, YeelightUser.class);
    }

    /**
     * 将YeelightUserDto对象转换为YeelightUser对象。
     *
     * @param yeelightUserDto 用户数据传输对象，不可为null。
     * @return 转换后的用户业务对象，如果输入为null，则返回null。
     */
    public static com.yeelight.service.user.client.domain.YeelightUser dtoToClientYeelightUser(YeelightUserDto yeelightUserDto) {
        // 检查输入的DTO对象是否为null
        if (yeelightUserDto == null) {
            return null;
        }
        // 使用BeanUtils的objToBean方法进行对象转换
        return BeanUtils.objToBean(yeelightUserDto, com.yeelight.service.user.client.domain.YeelightUser.class);
    }

    /**
     * 将社交用户对象转换为社交用户数据传输对象（DTO）。
     *
     * @param yeelightId yeelightId，用于验证是否有效。
     * @param socialUser 社交用户对象，不可为null。
     * @return 返回转换后的社交用户DTO，如果输入无效则返回null。
     */
    public static SocialUserDto socialUserToDto(Long yeelightId, SocialUser socialUser) {
        // 验证社交用户对象是否为null
        if (socialUser == null) {
            return null;
        }
        // 验证yeelightId的有效性
        if (!UserJudge.isValidYeelightId(yeelightId)) {
            return null;
        }
        // 使用BeanUtils将社交用户对象转换为DTO对象
        SocialUserDto socialUserDto = BeanUtils.objToBean(socialUser, SocialUserDto.class);
        // 设置DTO对象的yeelightId
        socialUserDto.setYeelightId(yeelightId);
        return socialUserDto;
    }

    /**
     * 将 YeelightUserDto 对象转换为 CreateUserRequest 对象。
     *
     * @param yeelightUserDto 用户信息的数据传输对象，不可为null。
     * @return 返回转换后的 CreateUserRequest 对象，如果输入为null，则返回null。
     */
    public static CreateUserRequest dtoToAddRequest(YeelightUserDto yeelightUserDto) {
        // 检查输入的yeelightUserDto是否为null
        if (yeelightUserDto == null) {
            return null;
        }
        // 使用BeanUtils将yeelightUserDto对象转换为CreateUserRequest对象
        return BeanUtils.objToBean(yeelightUserDto, CreateUserRequest.class);
    }

    /**
     * 计算用户完成度分数。
     * <p>
     * 根据用户提供的信息，分配相应的分数。如果用户提供了电话号码，则返回固定电话号码分数；
     * 如果提供了电子邮件地址，则返回固定电子邮件分数。如果用户的用户名等于电话号码或电子邮件地址，
     * 分数会有相应的增加。
     *
     * @param yeelightUserDto 包含用户信息的数据传输对象，包括用户名、电话号码和电子邮件。
     * @return 返回用户完成度的总分数。
     */
    public static int getUserFinishScore(YeelightUserDto yeelightUserDto) {
        int score = 0;
        // 如果用户填写了电话号码，直接返回电话号码填写的固定分数
        if (StringUtils.isNotBlank(yeelightUserDto.getPhoneNumber())) {
            return PHONE_NUMBER_FILLED_SCORE;
        }
        // 如果用户填写了电子邮件，直接返回电子邮件填写的固定分数
        if (StringUtils.isNotBlank(yeelightUserDto.getEmail())) {
            return EMAIL_FILLED_SCORE;
        }
        // 如果用户名等于电话号码，增加相应的分数
        if (StringUtils.equals(yeelightUserDto.getUsername(), yeelightUserDto.getPhoneNumber())) {
            score += USERNAME_IS_PHONE_NUMBER_SCORE;
        }
        // 如果用户名等于电子邮件，增加相应的分数
        if (StringUtils.equals(yeelightUserDto.getUsername(), yeelightUserDto.getEmail())) {
            score += USERNAME_IS_EMAIL_SCORE;
        }
        return score;
    }
}
