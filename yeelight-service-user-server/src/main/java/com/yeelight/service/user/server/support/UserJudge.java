/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.support
 * Description: 用户校验类
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-06 16:11:16:11
 */
package com.yeelight.service.user.server.support;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.client.dto.YeelightUserDto;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.domain.YeelightUser;
import com.yeelight.service.user.server.mapper.user.YeelightUserMapper;
import com.yeelight.service.user.server.utils.Assert;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import java.util.HashMap;
import java.util.Set;

/**
 * Desc: 用户校验类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-05-06 16:11:16:11
 */
public class UserJudge {
    /**
     * 验证Yeelight ID的有效性。
     *
     * @param yeelightId 待验证的Yeelight设备ID，为Long类型。
     * @return 返回一个布尔值，如果ID有效，则返回true；如果ID为null或无效，则返回false。
     */
    public static boolean isValidYeelightId(Long yeelightId) {
        // 检查yeelightId是否为null
        if (yeelightId == null) {
            return false;
        }
        // 利用UserVendorHolder中的vendor枚举验证yeelightId的有效性
        return UserVendorHolder.getVendorEnum().contains(yeelightId);
    }

    /**
     * 检查用户是否存在。
     * 该方法会验证传入的用户对象是否为非空，如果为空，则抛出异常。
     *
     * @param user 一个代表用户的对象，不应为空。
     * @throws IllegalArgumentException 如果用户对象为空，抛出此异常。
     */
    public static void checkUserExist(YeelightUserDto user) {
        // 确保用户对象不为空
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist"));
    }

    /**
     * 检查Yeelight ID的有效性。
     * <p>此方法将验证传入的Yeelight ID是否在有效范围内。如果ID无效，即不在允许的范围内，将抛出BizException异常。</p>
     *
     * @param yeelightId 需要检查的Yeelight设备ID。
     * @throws BizException 如果Yeelight ID无效（不在范围内），则抛出此异常。
     */
    public static void checkYeelightId(Long yeelightId) throws BizException {
        // 验证Yeelight ID的有效性，如果无效，则抛出异常
        Assert.isNotTrue(isValidYeelightId(yeelightId), "The user ID is out of range, region:{}, vendor:{}", UserVendorHolder.getVendorEnum().getRegion().getCode(), UserVendorHolder.getSimpleVendor());
    }

    /**
     * 检查并更新用户名称。
     * 该方法用于确保在更新用户名称之前，用户对象存在，更新的用户名不为空且与当前用户名称不同。
     *
     * @param user 用户对象，不可为null。
     * @param userName 需要更新的用户名，不可为空字符串。
     * @throws IllegalArgumentException 如果用户对象为null，用户名为空或用户名已存在，则抛出异常。
     */
    public static void checkUpdateUserName(YeelightUser user, String userName) {
        // 确保用户对象不为空
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist"));
        // 确保用户名不为空白字符串
        Assert.notBlank(userName, I18nUtil.getMessage("User.Exception.userAccount.empty"));
        // 确保新用户名与当前用户名不同
        Assert.isTrue(StringUtils.equals(userName, user.getUsername()), I18nUtil.getMessage("User.Exception.account.alreadyExist"));
    }

    /**
     * 检查并更新用户电话号码。
     * 该方法用于验证用户是否存在，电话号码是否为空，以及电话号码是否与当前用户绑定的电话号码相同。
     * 如果验证失败，将抛出相应的异常。
     *
     * @param user 用户对象，不可为null。
     * @param phoneNumber 需要更新的电话号码，不可为空字符串。
     * @throws IllegalArgumentException 如果用户对象为null或电话号码为空，将抛出此异常。
     * @throws IllegalStateException 如果电话号码与当前用户绑定的电话号码不相同，将抛出此异常。
     */
    public static void checkUpdatePhoneNumber(YeelightUser user, String phoneNumber) {
        // 确保用户对象不为null
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist"));
        // 确保电话号码不为空
        Assert.notBlank(phoneNumber, I18nUtil.getMessage("User.Exception.userAccount.empty"));
        // 确保新电话号码与当前用户绑定的电话号码相同
        Assert.isTrue(StringUtils.equals(phoneNumber, user.getPhoneNumber()), I18nUtil.getMessage("User.Exception.phoneNumber.alreadyExist"));
    }

    /**
     * 检查并更新用户邮箱。
     *
     * @param user 用户对象，不可为null。
     * @param email 需要更新的邮箱地址，不可为空字符串。
     * 该方法不返回任何值，但会通过断言验证用户是否存在，邮箱地址是否为空，以及是否与用户当前邮箱地址一致。
     * 如果任一条件不满足，将抛出异常。
     */
    public static void checkUpdateEmail(YeelightUser user, String email) {
        // 确保用户对象不为null
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist"));
        // 确保邮箱地址不为空字符串
        Assert.notBlank(email, I18nUtil.getMessage("User.Exception.userAccount.empty"));
        // 确保新邮箱地址与用户当前邮箱地址一致
        Assert.isTrue(StringUtils.equals(email, user.getEmail()), I18nUtil.getMessage("User.Exception.email.alreadyExist"));
    }

    /**
     * 检查更新用户属性的合法性。
     * 该方法用于确保在更新用户属性之前，用户对象和属性值是合法且非空的。
     *
     * @param user 表示要更新属性的用户对象，不能为空。
     * @param property 表示要更新的用户属性，不能为空字符串。
     *
     * @exception IllegalArgumentException 如果用户对象或属性值不合法，抛出该异常。
     */
    public static void checkUpdateProperty(YeelightUser user, String property) {
        // 确保用户对象不为空
        Assert.notNull(user, I18nUtil.message("User.Exception.user.notExist"));
        // 确保属性值非空
        Assert.notBlank(property, I18nUtil.getMessage("User.Exception.data.verifyFail"));
    }

    /**
     * 检查用户数据重复项并验证用户数据的合法性。
     * 该方法首先分别检查用户名、电子邮件和电话号码是否已存在，如果存在任何重复，则抛出业务异常。
     * 随后，使用Java Bean验证框架验证用户对象的数据合法性，如果验证失败，同样抛出异常。
     *
     * @param user 包含用户信息的对象，需要进行重复项检查和数据合法性验证。
     * @param yeelightUserMapper  数据库操作接口
     * @param validator   Java Bean验证器，用于验证用户数据的合法性。
     * @throws BizException 如果检查过程中发现用户名、电子邮件或电话号码已存在，或者用户数据不符合验证规则，则抛出此异常。
     */
    public static void checkUser(YeelightUser user, YeelightUserMapper yeelightUserMapper, Validator validator) throws BizException {
        // 检查用户名是否已存在
        boolean checkUsername = checkUsername(user.getUsername(), yeelightUserMapper);
        // 检查电子邮件是否已存在
        boolean checkEmail = checkEmail(user.getEmail(), yeelightUserMapper);
        // 检查电话号码是否已存在
        boolean checkPhoneNumber = checkPhoneNumber(user.getPhoneNumber(), yeelightUserMapper);

        // 对已存在的情况抛出异常
        Assert.isNotTrue(checkUsername, I18nUtil.getMessage("User.Exception.account.alreadyExist"));
        Assert.isNotTrue(checkEmail, I18nUtil.getMessage("User.Exception.email.alreadyExist"));
        Assert.isNotTrue(checkPhoneNumber, I18nUtil.getMessage("User.Exception.phoneNumber.alreadyExist"));

        // 验证用户数据的合法性
        Set<ConstraintViolation<YeelightUser>> constraintViolations = validator.validate(user);
        // 如果存在合法性问题，抛出异常
        constraintViolations.forEach(constraintViolation -> Assert.throwException(I18nUtil.getMessage(constraintViolation.getMessage())));
    }


    /**
     * 检查提供的用户名是否符合要求。
     * 该方法通过调用checkProperty方法，以用户名键（由SecurityConstants.USERNAME_KEY定义）和用户名为参数进行检查。
     *
     * @param userName 待检查用户名
     * @param yeelightUserMapper  数据库操作接口
     * @return boolean 返回true如果用户名有效，否则返回false
     */
    public static boolean checkUsername(String userName, YeelightUserMapper yeelightUserMapper) {
        // 使用checkProperty方法检查用户名
        return checkProperty(SecurityConstants.USERNAME_KEY, userName, yeelightUserMapper);
    }

    /**
     * 检查用户邮箱是否符合要求。
     *
     * @param email 包含用户邮箱信息的对象
     * @param yeelightUserMapper  数据库操作接口
     * @return 返回一个布尔值，如果邮箱符合要求则返回true，否则返回false。
     */
    public static boolean checkEmail(String email, YeelightUserMapper yeelightUserMapper) {
        // 通过调用checkProperty方法来检查邮箱是否符合要求，传入的参数为属性名"email"和用户邮箱。
        return checkProperty("email", email, yeelightUserMapper);
    }

    /**
     * 检查用户提供的电话号码是否符合要求。
     *
     * @param phoneNumber 电话号码。
     * @param yeelightUserMapper  数据库操作接口
     * @return 返回一个布尔值，如果电话号码符合预设规则则为 true，否则为 false。
     */
    public static boolean checkPhoneNumber(String phoneNumber, YeelightUserMapper yeelightUserMapper) {
        // 通过调用 checkProperty 方法来验证电话号码的合法性
        return checkProperty("phone_number", phoneNumber, yeelightUserMapper);
    }

    /**
     * 检查是否有重复属性的数据
     *
     * @param property 要检查的属性名称
     * @param value 属性对应的值
     * @param yeelightUserMapper  数据库操作接口
     * @return 返回是否发现重复属性值。如果值为null或空字符串，认为没有重复，返回true；否则，返回是否在数据库中找到相同属性和值的记录。
     */
    private static boolean checkProperty(String property, String value, YeelightUserMapper yeelightUserMapper) {
        // 首先检查值是否为null或空，如果是，则认为没有重复，直接返回true
        if (value == null || StringUtils.isEmpty(value)) {
            return true;
        }

        // 构建查询条件
        HashMap<String, Object> condition = new HashMap<>(2);
        condition.put("property", property);
        condition.put("value", value);

        // 根据条件查询数据库中该属性值的重复数
        Integer count = yeelightUserMapper.getCountByProperty(condition);

        // 如果重复数为0，说明没有重复，返回true；否则，返回false
        return (count == 0);
    }

}
