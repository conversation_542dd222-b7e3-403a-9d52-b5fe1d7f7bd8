package com.yeelight.service.user.server.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.HttpUtil;
import com.yeelight.service.framework.util.StringUtils;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @program: yeelight-oauth-api
 * @description:
 * @author: lixiaodong
 * @create: 2022-01-25 13:36
 **/
@Slf4j
public class AppleLoginUtils {
    private static final String APPLE_ISSUER_URL = "https://appleid.apple.com";
    private static final String APPLE_AUTH_URL = "https://appleid.apple.com/auth/keys";
    private static final String APPLE_AUD = "com.yeelight.oauth";

    private static final LoadingCache<String, Optional<Map<String, String>>> KEYS_CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .build(new CacheLoader<>() {
                @Override
                public Optional<Map<String, String>> load(@NotNull String s) {
                    return Optional.ofNullable(getAppleKeyByKid(s));
                }
            });

    /**
     * 根据key ID(kid)从Apple服务器获取对应的公钥信息。
     *
     * @see <a href="https://developer.apple.com/documentation/sign_in_with_apple/jwkset/keys">...</a>
     * @param s key ID (kid)，用于查找对应的公钥。
     * @return 返回一个包含公钥信息的Map，如果找不到对应的公钥或发生异常，则返回null。
     *         公钥信息包括：n（公钥的模数）、e（公钥的指数）、kty（密钥类型）。
     */
    private static Map<String, String> getAppleKeyByKid(String s) {
        try {
            // 从Apple授权服务器获取JSON Web Key Set (JWKS)
            String str = HttpUtil.sendGet(APPLE_AUTH_URL);
            JSONObject data = JSONObject.parseObject(str);
            log.info("keys =====> {}", JSONObject.toJSONString(data));
            // 解析JWKS中的公钥信息
            JSONArray jsonArray = data.getJSONArray("keys");
            for (int i = 0; i < jsonArray.size(); i++) {
                Map<String, String> m = new HashMap<>(8);
                // 提取并存储公钥的模数和指数，以及密钥类型
                m.put("n", jsonArray.getJSONObject(i).getString("n"));
                m.put("e", jsonArray.getJSONObject(i).getString("e"));
                m.put("kty", jsonArray.getJSONObject(i).getString("kty"));
                // 查找与给定key ID匹配的公钥
                String kid = jsonArray.getJSONObject(i).getString("kid");
                if (StringUtils.equals(s, kid)) {
                    return m;
                }
            }
        } catch (Exception ex) {
            // 记录获取公钥异常信息
            log.error("获取apple公钥异常 error:{}", ExceptionUtils.getStackTrace(ex));
        }
        // 如果未找到匹配的公钥或发生异常，返回null
        return null;
    }


    /**
     * 获取苹果的公钥
     * @param kid 密钥类型
     * @return PublicKey
     */
    public static PublicKey getPublicKey(String kid) {
        try {
            Optional<Map<String, String>> keyOptional = AppleLoginUtils.KEYS_CACHE.get(kid);
            Map<String, String> key = keyOptional.orElseGet(() -> getAppleKeyByKid(kid));
            Assert.notNull(key, "获取apple公钥失败");
            String n = key.get("n");
            String e = key.get("e");
            String kty = key.get("kty");

            BigInteger modulus = new BigInteger(1, Base64.decodeBase64(n));
            BigInteger publicExponent = new BigInteger(1, Base64.decodeBase64(e));

            RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
            //目前kty均为 "RSA"
            KeyFactory kf = KeyFactory.getInstance(kty);
            return kf.generatePublic(spec);
        } catch (Exception e) {
            log.warn("解析apple公钥异常 kid:{}, error:{}", kid, ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    /**
     * 对前端传来的JWT字符串identityToken的第二部分进行解码
     * 主要获取其中的aud和sub，aud对应ios前端的包名，sub对应当前用户的授权openID
     *
     * @param identityToken 前端传的identityToken
     * @return 返回一个包含identityToken的header和payload部分的Map
     */
    public static Map<String, JSONObject> parserIdentityToken(String identityToken) {
        Map<String, JSONObject> map = new HashMap<>(2);

        String[] arr = identityToken.split("\\.");

        String deHeader = new String(Base64.decodeBase64(arr[0]));
        JSONObject header = JSON.parseObject(deHeader);
        map.put("header", header);

        String dePayload = new String(Base64.decodeBase64(arr[1]));
        JSONObject payload = JSON.parseObject(dePayload);
        map.put("payload", payload);
        return map;
    }

    /**
     * 对前端传来的identityToken进行验证
     *
     * @param identityToken 前端传的identityToken
     * @return openId open id
     */
    public static Map<String, Object> verifyToken(String identityToken) throws BizException {
        try {
            // 1 解析
            Map<String, JSONObject> json = parserIdentityToken(identityToken);
            JSONObject header = json.get("header");
            String kid = json.get("header").getString("kid");
            // 2 生成publicKey
            PublicKey publicKey = getPublicKey(kid);
            Assert.notNull(publicKey, "获取apple验证公钥失败");
            // 3 验证  https://developer.apple.com/documentation/sign_in_with_apple/generate_and_validate_tokens
            JwtParser jwtParser = Jwts.parser()
                    .requireIssuer(APPLE_ISSUER_URL)
                    //.requireAudience(appleAud)
                    .verifyWith(publicKey).build();
            Jws<Claims> claim = jwtParser.parseSignedClaims(identityToken);
            Assert.notNull(claim, "apple登陆授权 idToken 验证失败");
            //sub,即用户的Apple的openId
            String sub = claim.getPayload().get("sub").toString();
            String iss = claim.getPayload().get("iss").toString();
            String aud = claim.getPayload().get("aud").toString();
            //exp is second
            long exp = Long.parseLong(claim.getPayload().get("exp").toString()) * 1000;
            if (APPLE_ISSUER_URL.equals(iss)
                    /*&& APPLE_AUD.equals(aud)*/
                    && System.currentTimeMillis() < exp
            ) {
                Map<String, Object> result = new HashMap<>(3);
                result.put("sub", sub);
                result.put("iss", iss);
                result.put("aud", aud);
                return claim.getPayload();
            } else {
                log.warn("苹果登陆授权 idToken不合法  identityToken:{}", identityToken);
                Assert.throwException("苹果登陆授权 idToken不合法");
            }
        } catch (ExpiredJwtException e) {
            log.warn("苹果登陆授权 idToken 已过期  identityToken:{}, error:{}", identityToken, ExceptionUtils.getStackTrace(e));
            Assert.throwException("苹果登陆授权 idToken 已过期");
        } catch (Exception e) {
            log.warn("非法的苹果登陆授权 idToken identityToken:{}, error:{}", identityToken, ExceptionUtils.getStackTrace(e));
            Assert.throwException("非法的苹果登陆授权 idToken");
        }
        return null;
    }

    public static void main(String[] args) {
        String idToken = "eyJraWQiOiI4NkQ4OEtmIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.C89rWBYD3Gdx91pglXaT75OJlnGE77WqRVtaGXjbnrluPiwxGb4X2H0dSr8fCbYM8y07oLL87NYSSMXi_DWj1Vgalp3i-HHrBvj6Ak2sYMaRSY494-GsiqCctzfGaz5ScC-jc6zz0sKluANebPRKlD9QF1wa9kwkBTuJsv037Kz7cTBKQOLgL3cWw52YaGwzrdUo59Fc3PRqUHqprx_yVVgTSQR_fv8k1Wp0oNZBrzqC_Um_OwPl-wv6gpWAFVQ6Alg-IG61kND0LimxGF8vVB2sGQ1Wmg0YzNP8tWnFlOhzCsCe3H6gxPUONnPz0X_f3-hTVCIG6xtWDr8IOxukrQ";
        Map<String, Object> result = verifyToken(idToken);
        log.info("result:{}", result);
    }
}
