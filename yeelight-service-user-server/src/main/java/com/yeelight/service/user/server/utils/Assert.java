/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-05-11 10:27:10:27
 */
package com.yeelight.service.user.server.utils;

import com.yeelight.service.framework.exception.BizException;
import com.yeelight.service.framework.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * Desc: 断言工具类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-05-11 10:27:10:27
 */
public class Assert extends com.yeelight.service.framework.util.Assert {
    /**
     * 校验参数不为null，如果为null则抛出异常。
     * @param object 待校验的对象。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void notNull(Object object, String code, String message) {
        if (object == null) {
            throwException(code, message);
        }
    }

    /**
     * 校验参数不为null，如果为null则抛出异常，支持格式化消息。
     * @param object 待校验的对象。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因，支持参数替换。
     * @param param 消息格式化时使用的参数。
     */
    public static void notNull(Object object, String code, String message, Object... param) {
        if (object == null) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验参数为null，如果不为null则抛出异常。
     * @param object 待校验的对象。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void isNull(Object object, String code, String message, Object... param) {
        if (object != null) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验参数为null，如果不为null则抛出异常。
     * @param object 待校验的对象。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void isNull(Object object, String code, String message) {
        if (object != null) {
            throwException(code, message);
        }
    }

    /**
     * 校验字符串不为空白，如果为空白则抛出异常。
     * @param str 待校验的字符串。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void notBlank(String str, String code, String message, Object... param) {
        if (StringUtils.isBlank(str)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验字符串不为空白，如果为空白则抛出异常。
     * @param str 待校验的字符串。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void notBlank(String str, String code, String message) {
        if (StringUtils.isBlank(str)) {
            throwException(code, message);
        }
    }

    /**
     * 校验字符串为空白，如果不为空白则抛出异常。
     * @param str 待校验的字符串。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void isBlank(String str, String code, String message, Object... param) {
        if (StringUtils.isNotBlank(str)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验字符串为空白，如果不为空白则抛出异常。
     * @param str 待校验的字符串。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void isBlank(String str, String code, String message) {
        if (StringUtils.isNotBlank(str)) {
            throwException(code, message);
        }
    }

    /**
     * 校验集合不为空，如果为空则抛出异常。
     * @param collection 待校验的集合。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void notEmpty(Collection<?> collection, String code, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throwException(code, message);
        }
    }

    /**
     * 校验集合不为空，如果为空则抛出异常，支持格式化消息。
     * @param collection 待校验的集合。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因，支持参数替换。
     * @param param 消息格式化时使用的参数。
     */
    public static void notEmpty(Collection<?> collection, String code, String message, Object... param) {
        if (CollectionUtils.isEmpty(collection)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验集合为空，如果不为空则抛出异常。
     * @param collection 待校验的集合。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void isEmpty(Collection<?> collection, String code, String message, Object... param) {
        if (CollectionUtils.isNotEmpty(collection)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验集合为空，如果不为空则抛出异常。
     * @param collection 待校验的集合。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void isEmpty(Collection<?> collection, String code, String message) {
        if (CollectionUtils.isNotEmpty(collection)) {
            throwException(code, message);
        }
    }

    /**
     * 校验Map为空，如果为空则抛出异常。
     * @param map 待校验的Map。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void isMapEmpty(Map map, String code, String message, Object... param) {
        if (MapUtils.isEmpty(map)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验Map为空，如果为空则抛出异常。
     * @param map 待校验的Map。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void isMapEmpty(Map map, String code, String message) {
        if (MapUtils.isEmpty(map)) {
            throwException(code, message);
        }
    }

    /**
     * 校验对象是否在给定的集合中，不在集合中则抛出异常。
     * @param object 待校验的对象。
     * @param values 集合，用于对比的对象集合。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param params 消息格式化时使用的参数。
     */
    public static void notNullAndIn(Object object, Collection<Object> values, String code, String message, Object... params) {
        notNull(object, message, params);
        if (!values.contains(object)) {
            throwException(code, message, params);
        }
    }

    /**
     * 校验对象是否在给定的集合中，不在集合中则抛出异常。
     * @param object 待校验的对象。
     * @param values 集合，用于对比的对象集合。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     */
    public static void notNullAndIn(Object object, Collection<Object> values, String code, String message) {
        notNull(object, message);
        if (!values.contains(object)) {
            throwException(code, message);
        }
    }

    /**
     * 校验两个对象是否相等，不相等则抛出异常。
     * @param object1 第一个待校验的对象。
     * @param object2 第二个待校验的对象。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void equal(Object object1, Object object2, String code, String message, Object... param) {
        if (!Objects.equals(object1, object2)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验两个字符串是否相等，不相等则抛出异常。
     * @param str1 第一个待校验的字符串。
     * @param str2 第二个待校验的字符串。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void equal(String str1, String str2, String code, String message, Object... param) {
        if (!StringUtils.equals(str1, str2)) {
            throwException(code, message, param);
        }
    }

    /**
     * 校验两个对象是否相等，不相等则抛出异常。
     * @param object1 第一个待校验的对象。
     * @param object2 第二个待校验的对象。
     * @param code 异常代码，用于标识异常类型。
     * @param message 异常信息，描述校验失败的具体原因。
     * @param param 消息格式化时使用的参数。
     */
    public static void equals(Object object1, Object object2, String code, String message, Object... param) {
        if (!Objects.equals(object1, object2)) {
            throwException(code, message, param);
        }
    }

    /**
     * 检查两个字符串是否相等，如果不相等，则抛出异常。
     *
     * @param str1 第一个字符串
     * @param str2 第二个字符串
     * @param code 异常代码
     * @param message 异常消息，可以包含占位符
     * @param param 消息占位符的参数
     */
    public static void equals(String str1, String str2, String code, String message, Object... param) {
        if (!StringUtils.equals(str1, str2)) {
            throwException(code, message, param);
        }
    }

    /**
     * 检查两个对象是否相等，如果相等，则抛出异常。
     *
     * @param object1 第一个对象
     * @param object2 第二个对象
     * @param code 异常代码
     * @param message 异常消息，可以包含占位符
     * @param param 消息占位符的参数
     */
    public static void isEqual(Object object1, Object object2, String code, String message, Object... param) {
        if (Objects.equals(object1, object2)) {
            throwException(code, message, param);
        }
    }

    /**
     * 检查两个对象是否相等，如果相等，则抛出异常。
     *
     * @param object1 第一个对象
     * @param object2 第二个对象
     * @param code 异常代码
     * @param message 异常消息
     */
    public static void isEqual(Object object1, Object object2, String code, String message) {
        if (Objects.equals(object1, object2)) {
            throwException(code, message);
        }
    }

    /**
     * 检查条件是否为真，如果为真，则抛出异常。
     *
     * @param expression 要检查的条件
     * @param code 异常代码
     * @param message 异常消息，可以包含占位符
     * @param param 消息占位符的参数
     */
    public static void isTrue(boolean expression, String code, String message, Object... param) {
        if (expression) {
            throwException(code, message, param);
        }
    }

    /**
     * 检查条件是否为真，如果为真，则抛出异常。
     *
     * @param expression 要检查的条件
     * @param code 异常代码
     * @param message 异常消息
     */
    public static void isTrue(boolean expression, String code, String message) {
        if (expression) {
            throwException(code, message);
        }
    }

    /**
     * 检查条件是否为假，如果为假，则抛出异常。
     *
     * @param expression 要检查的条件
     * @param code 异常代码
     * @param message 异常消息，可以包含占位符
     * @param params 消息占位符的参数
     */
    public static void isNotTrue(boolean expression, String code, String message, Object... params) {
        if (!expression) {
            throwException(code, message, params);
        }
    }

    /**
     * 检查条件是否为假，如果为假，则抛出异常。
     *
     * @param expression 要检查的条件
     * @param code 异常代码
     * @param message 异常消息
     */
    public static void isNotTrue(boolean expression, String code, String message) {
        if (!expression) {
            throwException(code, message);
        }
    }

    /**
     * 抛出一个业务异常。
     *
     * @param code 异常代码
     * @param message 异常消息，可以是格式化字符串
     * @param param 格式化字符串的参数
     */
    public static void throwException(String code, String message, Object... param) {
        throw new BizException(code, StringUtils.stringFormat(message, param));
    }

}
