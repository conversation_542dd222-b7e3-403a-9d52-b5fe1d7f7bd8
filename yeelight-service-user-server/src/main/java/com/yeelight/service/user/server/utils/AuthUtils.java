/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-18 16:38:16:38
 */
package com.yeelight.service.user.server.utils;

import com.yeelight.service.user.server.constant.SecurityConstants;
import org.apache.commons.codec.binary.Base64;
import org.springframework.util.ObjectUtils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Objects;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-18 16:38:16:38
 */
public class AuthUtils extends com.yeelight.service.user.client.utils.AuthUtils {
    /**
     * 获取JWT认证的Token。
     * 优先从HTTP请求的头中查找指定的Token，如果未找到，则尝试从请求参数中获取，
     * 若还不存在，则从HTTP Cookie中查找。
     *
     * @param request 当前的HTTP请求对象，用于从中提取JWT Token。
     * @return 找到的JWT Token字符串。如果未找到，则返回空字符串。
     */
    public static String getJwtAuthToken(HttpServletRequest request) {
        // 尝试从请求头获取JWT Token
        String jwtToken = request.getHeader(SecurityConstants.JWT_AUTH_TOKEN_KEY);
        if (ObjectUtils.isEmpty(jwtToken)) {
            // 如果请求头中不存在，尝试从请求参数中获取
            jwtToken = request.getParameter(SecurityConstants.JWT_AUTH_TOKEN_KEY);
            // 如果请求参数中也不存在，那么尝试从Cookie中获取
            if (ObjectUtils.isEmpty(jwtToken)) {
                // 从请求的Cookie中查找JWT Token
                Cookie[] cookies = request.getCookies();
                if (!ObjectUtils.isEmpty(cookies)) {
                    for (Cookie cookie : cookies) {
                        // 比对Cookie名称，找到对应的JWT Token
                        if (SecurityConstants.JWT_AUTH_TOKEN_KEY.equals(cookie.getName())) {
                            jwtToken = cookie.getValue();
                            break;
                        }
                    }
                }
            }
        }
        return jwtToken;
    }


    /**
     * 从HttpServletRequest中获取当前会话的ID。
     * <p>此方法不会创建新的会话，只会返回当前存在的会话ID。
     * 如果没有会话存在，则返回null。</p>
     *
     * @param request HttpServletRequest对象，用于获取会话ID。
     * @return 当前会话的ID，如果不存在会话则返回null。
     */
    public static String getSessionId(HttpServletRequest request) {
        // 检查传入的request对象是否为null
        if (Objects.isNull(request)) {
            return null;
        }
        // 尝试获取当前请求的会话，如果不存在则返回null
        if (Objects.isNull(request.getSession(false))) {
            return null;
        }
        // 获取现有会话的ID并返回
        return request.getSession(false).getId();
    }

    /**
     * 生成一个用于OAuth 2.0授权代码流程扩展中的code_challenge模式的CodeVerifier随机字符串。
     * 该字符串由[A-Z]、[a-z]、[0-9]、"-"、"."、"_"、"~"范围内的字符组成，长度在43到128位之间。
     *
     * @return 生成的代码验证字符串。字符串是经过Base64编码的随机字节序列，保证了其在URL中的安全使用。
     */
    public static String generateCodeVerifier() {
        // 生成一个安全随机数实例
        SecureRandom sr = new SecureRandom();
        // 创建一个长度为32的字节数组，用于存储随机生成的字节
        byte[] code = new byte[32];
        // 使用SecureRandom生成的随机字节填充字节数组code
        sr.nextBytes(code);
        // 将字节数组code经过Base64 URL安全编码转换为字符串形式返回
        return Base64.encodeBase64URLSafeString(code);
    }


    /**
     * 生成用于OAuth 2.0授权代码流程扩展中的code_challenge。
     * 根据指定的method，对code_verifier进行处理生成code_challenge。
     * 支持的方法有plain和S256，其中S256方法符合RFC7636标准。
     *
     * @param codeVerifier 验证码，用于生成code_challenge的输入。
     * @param method 生成code_challenge的方法，支持"plain"和"S256"。
     * @return 生成的code_challenge字符串。
     * @see <a href="https://tools.ietf.org/html/rfc7636#section-4.2">RFC7636</a> 对code_challenge的生成方法有详细说明。
     */
    public static String generateCodeChallenge(String codeVerifier, String method) {
        try {
            // 如果指定的方法为S256，则使用SHA-256算法计算codeVerifier的哈希值，并进行BASE64URL编码。
            if (SecurityConstants.CODE_CHALLENGE_METHOD_S256.equalsIgnoreCase(method)) {
                byte[] bytes = codeVerifier.getBytes(StandardCharsets.US_ASCII);
                MessageDigest md = MessageDigest.getInstance("SHA-256");
                md.update(bytes, 0, bytes.length);
                byte[] digest = md.digest();
                return Base64.encodeBase64URLSafeString(digest);
            }
            // 如果指定的方法为plain，则直接返回codeVerifier。
            else if (SecurityConstants.CODE_CHALLENGE_METHOD_PLAIN.equalsIgnoreCase(method)){
                return codeVerifier;
            }
            // 如果指定了不支持的生成方法，则抛出异常。
            else {
                throw new IllegalArgumentException("Unsupported code_challenge_method: " + method);
            }
        } catch (Exception e) {
            // 在生成code_challenge的过程中发生异常，则抛出IllegalArgumentException。
            throw new IllegalArgumentException("Failed to generate code_challenge", e);
        }
    }


    /**
     * 生成用于存储code_challenge的缓存key。
     * 在OAuth 2.0授权流程中，PKCE（Proof Key for Code Exchange）是一种增强安全性的机制。
     * 该方法通过将代码（code）与一个预定义的前缀相结合，来生成一个唯一的缓存key，
     * 用于存储code_challenge，以便后续的代码验证。
     *
     * @param code 随机生成的代码，用于PKCE流程中的code_exchange步骤。
     * @return 缓存key，用于存储与给定code关联的code_challenge。
     */
    public static String generateCodeChallengeCacheKey(String code) {
        // 生成缓存key：将固定前缀与传入的code拼接
        return SecurityConstants.PKCE_MODE_CACHE_PREFIX + code;
    }

    /**
     * 从HttpServletRequest中获取Cookie的Path。
     * 如果请求的上下文路径不为空，则返回上下文路径，否则返回根路径。
     *
     * @param request HttpServletRequest对象，用于获取请求的上下文路径。
     * @return Cookie的Path路径。
     */
    public static String getCookiePath(HttpServletRequest request) {
        String contextPath = request.getContextPath();
        return !contextPath.isEmpty() ? contextPath : "/";
    }

    public static void clearCookie(HttpServletRequest request, HttpServletResponse response) {
        // 清除浏览器中的无效的 JSESSIONID
        Cookie cookie = new Cookie("OAUTH_SESSION_ID", null);
        cookie.setPath(AuthUtils.getCookiePath(request));
        cookie.setMaxAge(0);
        response.addCookie(cookie);
        Cookie cookie1 = new Cookie("JSESSIONID", null);
        cookie1.setPath(AuthUtils.getCookiePath(request));
        cookie1.setMaxAge(0);
        response.addCookie(cookie1);
    }
}
