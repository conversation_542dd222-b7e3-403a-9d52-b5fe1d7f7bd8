package com.yeelight.service.user.server.utils;

import com.yeelight.service.framework.i18n.I18nUtil;
import com.yeelight.service.framework.util.ResultCodeEnum;
import com.yeelight.service.user.client.dto.YeelightUserDto;

import java.time.Instant;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ContextHolder {

    private static final ThreadLocal<YeelightUserDto> USER = new ThreadLocal<>();

    private static final ThreadLocal<Integer> TIME = new ThreadLocal<>();

    public static void setUser(YeelightUserDto user) {
        USER.remove();
        USER.set(user);
    }

    public static YeelightUserDto getUser() {
        return USER.get();
    }

    /**
     * 获取非空的用户对象。
     * 此方法会尝试获取一个用户对象，并确保该对象不为空。如果用户对象为空，则会抛出未授权异常。
     *
     * @return YeelightUserDto 非空的用户对象。
     */
    public static YeelightUserDto getNotNullUser() {
        // 尝试获取用户对象
        YeelightUserDto yeelightUserDto = getUser();
        // 确保用户对象不为空，否则抛出异常
        Assert.notNull(yeelightUserDto, I18nUtil.message("User.Exception.user.notExist", ""));
        // 额外的空检查，确保不会返回空的用户对象
        if (Objects.isNull(yeelightUserDto)) {
            // 如果用户对象为空，抛出未授权异常
            Assert.throwException(ResultCodeEnum.UNAUTHORIZED.getCode(), ResultCodeEnum.UNAUTHORIZED.getMsg());
        }
        // 返回非空的用户对象
        return yeelightUserDto;
    }

    public static void now(Integer timestamp) {
        TIME.remove();
        TIME.set(Objects.nonNull(timestamp) ? timestamp : (int) Instant.now().getEpochSecond());
    }

    public static Integer now() {
        return Objects.nonNull(TIME.get()) ? TIME.get() : (int) Instant.now().getEpochSecond();
    }

    public static void clear() {
        USER.remove();
        TIME.remove();
    }
}
