package com.yeelight.service.user.server.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: yeelight-service-station
 * @description:
 * @author: yeelight
 * @create: 2022-12-13 11:13
 **/
public class DiffUtil extends com.yeelight.service.framework.util.DiffUtil {
    public DiffUtil() {
    }

    /**
     * 对比新旧两个对象之间的差异，并返回差异结果。
     *
     * @param newObj 新对象，可以是任意类型的对象。
     * @param oldObj 旧对象，可以是任意类型的对象。
     * @return 返回一个包含新旧对象差异的Map。其中，"before"键对应的值为旧对象的差异结果，
     *         "after"键对应的值为新对象的差异结果。如果对象为空，会将其转换为空Map处理。
     */
    public static Map<String, Map<String, Object>> diff(Object newObj, Object oldObj) {
        // 将新旧对象转换为Map格式
        Map<String, Object> newJsonObject;
        Map<String, Object> oldJsonObject;
        if(Objects.isNull(newObj)) {
            newJsonObject = Maps.newHashMap();
        } else {
            try {
                newJsonObject = JSON.parseObject(JSON.toJSONString(newObj), new TypeReference<>() {
                });
            } catch (Exception e) {
                newJsonObject = Maps.newHashMap();
            }
        }
        if(Objects.isNull(oldObj)) {
            oldJsonObject = Maps.newHashMap();
        } else {
            try {
                oldJsonObject = JSON.parseObject(JSON.toJSONString(oldObj), new TypeReference<>() {
                });
            } catch (Exception e) {
                oldJsonObject = Maps.newHashMap();
            }
        }

        // 如果新旧对象之一为空，则直接返回包含两个空Map的结果
        if (MapUtils.isEmpty(newJsonObject) || MapUtils.isEmpty(oldJsonObject)) {
            return ImmutableMap.of("before", oldJsonObject, "after", newJsonObject);
        }

        // 通过递归调用对比新旧对象，获取差异结果
        Map<String, Object> after = diff(newJsonObject, oldJsonObject);
        Map<String, Object> before = diff(oldJsonObject, newJsonObject);

        // 返回包含新旧对象差异的结果
        return ImmutableMap.of("before", before, "after", after);
    }

    /**
     * 对比两个Map对象，找出第一个Map中存在但第二个Map中不存在的键值对，以及键值对相同但值不同的项。
     * 如果第一个Map为空，返回一个空的Map；如果第二个Map为空，则返回第一个Map本身。
     * 对于值为Map类型的键值对，会递归对比其内部元素。
     * 对于值为Collection类型的键值对，会找出第一个Map中包含但第二个Map中不包含的元素。
     *
     * @param leftMap 第一个Map对象，作为基准对象。
     * @param rightMap 第二个Map对象，与基准对象进行对比。
     * @return 一个Map，包含第一个Map中存在但第二个Map中不存在的键值对，以及值不同的键值对。
     */
    private static Map<String, Object> diff(Map<String, Object> leftMap, Map<String, Object> rightMap) {
        // 当基准Map为空时，返回一个空Map
        if(MapUtils.isEmpty(leftMap)) {
            return Maps.newHashMap();
        }
        // 当对比Map为空时，返回基准Map本身
        if(MapUtils.isEmpty(rightMap)) {
            return leftMap;
        }
        Map<String, Object> diff = Maps.newHashMap();
        // 计算两个Map之间的差异
        MapDifference<String, Object> mapDifference = Maps.difference(leftMap, rightMap);
        // 处理仅在基准Map中存在的键值对
        Map<String, Object> entriesOnlyOnLeft = mapDifference.entriesOnlyOnLeft();
        diff.putAll(entriesOnlyOnLeft);
        // 处理值不同的键值对
        Map<String, MapDifference.ValueDifference<Object>> entriesDiffering = mapDifference.entriesDiffering();
        for(String key : entriesDiffering.keySet()) {
            diffItem(key, entriesDiffering, diff);
        }
        return diff;
    }

    private static void diffItem(String key, Map<String, MapDifference.ValueDifference<Object>> entriesDiffering, Map<String, Object> diff) {
        Object left = entriesDiffering.get(key).leftValue();
        Object right = entriesDiffering.get(key).rightValue();
        // 递归对比值为Map类型的键值对
        if(left instanceof Map && right instanceof Map) {
            Map<String, Object> leftChildMap = (Map<String, Object>) left;
            Map<String, Object> rightChildMap = (Map<String, Object>) right;
            diff.put(key, diff(leftChildMap, rightChildMap));
            // 对比值为Collection类型的键值对
        } else if(left instanceof Collection && right instanceof Collection) {
            Collection<Object> leftCollection = (Collection<Object>) left;
            Collection<Object> rightCollection = (Collection<Object>) right;
            List<String> rightList = Lists.newArrayList();
            List<Object> leftDiff = Lists.newArrayList();
            for(Object ele : rightCollection) {
                rightList.add(JSON.toJSONString(ele));
            }
            for(Object ele : leftCollection) {
                String eleString = JSON.toJSONString(ele);
                if(!rightList.contains(eleString)) {
                    leftDiff.add(ele);
                }
            }
            diff.put(key, leftDiff);
        } else {
            // 对于其他类型的差异，直接添加到结果Map中
            diff.put(key, left);
        }
    }
}
