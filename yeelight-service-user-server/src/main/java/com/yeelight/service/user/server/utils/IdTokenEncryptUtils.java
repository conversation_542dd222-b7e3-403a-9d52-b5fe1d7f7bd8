package com.yeelight.service.user.server.utils;

import com.github.mervick.aes_everywhere.Aes256;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class IdTokenEncryptUtils {
    private static final String ENCRYPT_KEY = "UtL8PRVi$Udt%mvIV9b0CDETJ2RT8M";
    public static final String CLIENT_ID_KEY = "clientId";
    public static final String YEELIGHT_ID_KEY = "yeelightId";
    public static final String YEELIGHT_ID_SIGN_KEY = "yeelightIdSign";
    public static final String YEELIGHT_ACCOUNT_KEY = "yeelightAccount";
    private static final int EXPIRE_TIME = 300000;

    /**
     * 使用AES256算法对明文进行加密。
     *
     * @param plaintext 需要加密的明文字符串。
     * @param key 加密使用的密钥字符串。
     * @return 返回加密后的密文字符串。如果加密过程中发生异常，则返回null。
     */
    public static String encrypt(String plaintext, String key) {
        try {
            // 尝试使用AES256算法加密明文
            return Aes256.encrypt(plaintext, key);
        } catch (Exception e) {
            // 记录加密过程中出现的异常信息
            log.info("IdTokenEncryptUtils encrypt error :{}", e.getMessage());
            return null;
        }
    }

    /**
     * 使用AES256算法解密字符串。
     *
     * @param encryptStr 需要解密的字符串。
     * @param key 解密使用的密钥。
     * @return 解密后的字符串。如果解密过程中发生异常，则返回null。
     */
    public static String decrypt(String encryptStr, String key) {
        try {
            // 尝试使用AES256算法解密传入的字符串
            return Aes256.decrypt(encryptStr, key);
        } catch (Exception e) {
            // 记录解密过程中的异常信息
            log.info("IdTokenEncryptUtils decrypt error :{}", e.getMessage());
            return null;
        }
    }

    /**
     * 解密ID令牌并解析成参数映射。
     *
     * @param idToken 加密的ID令牌字符串。
     * @param key 解密令牌的密钥。
     * @return 解析出的参数映射。
     * @throws Exception 如果解密过程出错或令牌无效，则抛出异常。
     */
    public static Map<String, String> decryptIdToken(String idToken, String key) throws Exception {
        // 解密idToken并解析成参数映射
        // 1. 对idToken进行URL解码
        String urlDecodedEncryptStr = URLDecoder.decode(idToken, StandardCharsets.UTF_8);
        log.info("idToken is :{}", urlDecodedEncryptStr);
        // 2. 使用密钥对解码后的字符串进行解密
        String plaintext = decrypt(urlDecodedEncryptStr, key);
        log.info("plaintext is :{}", plaintext);
        // 3. 将解密后的字符串按参数键值对分割并映射
        Map<String, String> paramMap = parsePaamMap(plaintext);
        log.info("paramMap is :{}", paramMap);

        if (paramMap == null) {
            throw new Exception("idToken decrypt wrong, decrypt result null");
        }

        // 4. 验证令牌的时间戳，确保令牌未过期
        // 令牌有效期为5分钟，若当前时间与时间戳差异超过5分钟，则认为令牌无效
        if (System.currentTimeMillis() - Long.parseLong(paramMap.get(Constants.TIMESTAMP_KEY)) > EXPIRE_TIME) {
            throw new Exception("time diffrence(5 mintues) too large");
        }
        return paramMap;
    }

    /**
     * 验证yeelightId是否有效。
     *
     * @param yeelightId 需要验证的yeelightId。
     * @param yeelightIdSign yeelightId的签名。
     * @return 如果yeelightId有效，则返回true；否则返回false。
     */
    public static boolean verifyYeelightId(Long yeelightId, String yeelightIdSign) {
        if (yeelightId == null || yeelightIdSign == null) {
            return false;
        }
        return StringUtils.equals(yeelightId.toString(), decrypt(yeelightIdSign, ENCRYPT_KEY));
    }

    /**
     * 使用AES256算法对yeelightId进行加密。
     *
     * @param yeelightId 需要加密的yeelightId。
     * @return 返回加密后的yeelightId字符串。
     */
    public static String encryptYeelightId(Long yeelightId) {
        if (yeelightId == null) {
            return null;
        }
        return encrypt(yeelightId.toString(), ENCRYPT_KEY);
    }


    private static Map<String, String> parsePaamMap(String plaintext) {
        Map<String, String> paramMap = null;
        if (plaintext != null) {
            paramMap = Arrays.stream(plaintext.split("&"))
                    .map(s -> s.split("="))
                    .collect(Collectors.toMap(paramArr -> paramArr[0], strings -> strings.length == 2 ? strings[1] : ""));
        }
        return paramMap;
    }

    public static void main(String[] args) throws Exception {
        // 定义密钥
        String key = "123456";
        log.info("密钥：{}", key);
        // 加密流程˚
        log.info("开始 加密流程");
        // 定义待加密参数， 格式 key1=value1&key2=value2&key3=value3...
        // yeelightId 用于识别用户
        // timestamp 用于时间验证，防止token重用。每个token有效期 5 分钟
        log.info("以格式 key1=value1&key2=value2&key3=value3... 定义待加密参数数据");
        log.info("待加密参数 yeelightAccount:{}", "<EMAIL>");
        log.info("待加密参数 timestamp:{}", System.currentTimeMillis());
        String data = "yeelightId=122433&yeelightIdSign=U2FsdGVkX1+DN5XWAfYhyXrEfEN77a2EYNzfRztJfUA=&clientId=dev&yeelightAccount=***********&timestamp=" + System.currentTimeMillis();
        log.info("完整待加密参数数据为:{}", data);
        // 执行加密
        log.info("完整待加密参数数据为:{}, 密钥为:{}", data, key);
        String encryptStr = encrypt(data, key);
        log.info("加密后数据为:{}", encryptStr);
        // 进行url编码
        log.info("对加密后的数据进行url编码，待编码数据为:{}", encryptStr);
        String urlEncodedEncryptStr = null;
        if (encryptStr != null) {
            urlEncodedEncryptStr = URLEncoder.encode(encryptStr, StandardCharsets.UTF_8);
        }
        log.info("进行url编码后的数据为:{}", urlEncodedEncryptStr);
        log.info("加密流程 结束");


        log.info("开始 解密流程");
        // 解密流程
        // url解码
        log.info("开始 解密流程");
        log.info("首先进行 url解码， 待url解码数据为:{}", urlEncodedEncryptStr);
        String urlDecodedEncryptStr = null;
        if (urlEncodedEncryptStr != null) {
            urlDecodedEncryptStr = URLDecoder.decode(urlEncodedEncryptStr, StandardCharsets.UTF_8);
        }
        log.info("url解码后的数据为:{}", urlDecodedEncryptStr);
        // 执行解密
        log.info("执行解密， 待解密的数据为:{},解密密钥为:{}", urlDecodedEncryptStr, key);
        String plaintext = decrypt(urlDecodedEncryptStr, key);
        log.info("解密后的数据为:{}", plaintext);
        // 取参数
        log.info("对解密后的数据[{}]进行解析", plaintext);
        Map<String, String> paramMap = parsePaamMap(plaintext);
        log.info("解析后得到Map:{}", paramMap);

        // 校验时间
        if (paramMap != null && Long.parseLong(paramMap.get(Constants.TIMESTAMP_KEY)) - System.currentTimeMillis() > EXPIRE_TIME) {
            throw new Exception("time diffrence(5 mintues) too large");
        }
    }
}