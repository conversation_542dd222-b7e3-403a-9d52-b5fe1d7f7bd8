/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description: JWT工具类 - 兼容Spring Boot 3.x
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-03-12 11:15:11:15
 * UpdatedTime: 2024-12-21 (升级到Spring Boot 3.x)
 */
package com.yeelight.service.user.server.utils;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;

/**
 * JWT工具类 - 兼容Spring Boot 3.x
 * <p>
 * 注意：此类已升级以兼容Spring Boot 3.x的新JWT实现
 * 旧的spring-security-jwt依赖已被移除，使用新的spring-security-oauth2-jose
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-03-12 11:15:11:15
 */
@Slf4j
public class JwtUtil {

    /**
     * -- SETTER --
     *  设置JWT解码器（由Spring容器注入）
     */
    @Setter
    private static JwtDecoder jwtDecoder;

    /**
     * 解析JWT Token（使用新的JwtDecoder）
     *
     * @param token 待解析的JWT Token字符串
     * @param jwtDecoder JWT解码器
     * @return 解析后的Token信息，以Map形式返回，如果解析失败则返回null
     */
    public static Map<String, Object> parseToken(String token, JwtDecoder jwtDecoder) {
        try {
            if (StringUtils.isBlank(token)) {
                return null;
            }
            
            // 使用新的JwtDecoder解析token
            Jwt jwt = jwtDecoder.decode(token);
            if (jwt == null) {
                return null;
            }
            
            // 返回JWT的所有声明
            return jwt.getClaims();
            
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("JwtUtil parseToken error: {}, token: {}", e.getMessage(), token);
            return null;
        } catch (Exception e) {
            log.warn("JwtUtil parseToken unexpected error: {}, token: {}", e.getMessage(), token);
            return null;
        }
    }
    
    /**
     * 解析JWT令牌，但不进行验证。
     * 兼容旧版本的parseToken方法
     * 
     * @param token 待解析的JWT Token字符串
     * @return 解析后的Token信息，以Map形式返回，如果解析失败则返回null
     * @deprecated 使用 parseToken(String, JwtDecoder) 替代
     */
    @Deprecated
    public static Map<String, Object> parseToken(String token) {
        log.warn("使用了已废弃的parseToken方法，建议升级到新的JwtDecoder实现");
        
        if (jwtDecoder != null) {
            return parseToken(token, jwtDecoder);
        }
        
        // 降级到不验证的解析方式
        return parseTokenWithoutVerify(token);
    }

    /**
     * 解析JWT令牌，但不进行签名验证（仅解析payload）
     * 注意：此方法不验证JWT签名，仅用于获取payload信息，存在安全风险
     *
     * @param token 待解析的JWT令牌字符串
     * @return 如果解析成功，返回包含JWT声明的Map对象；如果解析失败或令牌无效，返回null
     */
    public static Map<String, Object> parseTokenWithoutVerify(String token) {
        try {
            if (StringUtils.isBlank(token)) {
                return null;
            }
            
            // JWT格式：header.payload.signature
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                log.warn("Invalid JWT format: expected 3 parts, got {}", parts.length);
                return null;
            }
            
            // 解码payload部分（第二部分）
            String payload = parts[1];
            
            // Base64URL解码
            byte[] decodedBytes = Base64.getUrlDecoder().decode(payload);
            String claimsStr = new String(decodedBytes, StandardCharsets.UTF_8);
            
            if (StringUtils.isBlank(claimsStr)) {
                return null;
            }

            // 将声明字符串解析为Map对象
            return JSON.parseObject(claimsStr);
            
        } catch (IllegalArgumentException e) {
            log.warn("JwtUtil parseTokenWithoutVerify Base64 decode error: {}, token: {}", e.getMessage(), token);
            return null;
        } catch (Exception e) {
            log.warn("JwtUtil parseTokenWithoutVerify error: {}, token: {}", e.getMessage(), token);
            return null;
        }
    }

    /**
     * 从JWT令牌中解析客户端ID（使用JwtDecoder验证）
     *
     * @param token 用于解析的JWT令牌字符串
     * @param jwtDecoder JWT解码器
     * @return 解析出的客户端ID，如果无法解析则返回null
     */
    public static String parseClientIdFromToken(String token, JwtDecoder jwtDecoder) {
        Map<String, Object> claims = parseToken(token, jwtDecoder);
        if (claims != null) {
            Object clientId = claims.get("client_id");
            return clientId != null ? clientId.toString() : null;
        }
        return null;
    }
    
    /**
     * 兼容旧版本的parseClientIdFromToken方法
     * 
     * @param token 用于解析的JWT令牌字符串
     * @return 解析出的客户端ID，如果无法解析则返回null
     * @deprecated 使用 parseClientIdFromToken(String, JwtDecoder) 替代
     */
    @Deprecated
    public static String parseClientIdFromToken(String token) {
        log.warn("使用了已废弃的parseClientIdFromToken方法，建议升级到新的JwtDecoder实现");
        
        if (jwtDecoder != null) {
            return parseClientIdFromToken(token, jwtDecoder);
        }
        
        // 降级到不验证的解析方式
        return parseClientIdFromTokenWithoutVerify(token);
    }

    /**
     * 从令牌中解析客户端ID，而不进行签名验证
     * 注意：此方法不验证JWT签名，存在安全风险，仅在必要时使用
     *
     * @param token 待解析的令牌字符串
     * @return 客户端ID。如果解析失败或令牌不包含客户端ID，则返回预定义的无效刷新令牌字符串
     */
    public static String parseClientIdFromTokenWithoutVerify(String token) {
        try {
            Map<String, Object> claims = parseTokenWithoutVerify(token);
            if (claims != null) {
                Object clientId = claims.get("client_id");
                if (clientId != null) {
                    return clientId.toString();
                }
            }
        } catch (Exception e) {
            log.warn("Failed to parse client_id from token without verify: {}", e.getMessage());
        }
        
        // 默认返回无效刷新令牌字符串
        return Constants.INVALID_REFRESH_TOKEN;
    }
    
    /**
     * 从JWT令牌中解析用户ID
     *
     * @param token 待解析的JWT令牌字符串
     * @param jwtDecoder JWT解码器
     * @return 用户ID，如果无法解析则返回null
     */
    public static String parseUserIdFromToken(String token, JwtDecoder jwtDecoder) {
        Map<String, Object> claims = parseToken(token, jwtDecoder);
        if (claims != null) {
            Object userId = claims.get("user_id");
            if (userId == null) {
                // 兼容旧版本的id字段
                userId = claims.get("id");
            }
            return userId != null ? userId.toString() : null;
        }
        return null;
    }
    
    /**
     * 从JWT令牌中解析用户名
     *
     * @param token 待解析的JWT令牌字符串
     * @param jwtDecoder JWT解码器
     * @return 用户名，如果无法解析则返回null
     */
    public static String parseUsernameFromToken(String token, JwtDecoder jwtDecoder) {
        Map<String, Object> claims = parseToken(token, jwtDecoder);
        if (claims != null) {
            Object username = claims.get("username");
            if (username == null) {
                // 兼容标准的subject字段
                username = claims.get("sub");
            }
            return username != null ? username.toString() : null;
        }
        return null;
    }
    
    /**
     * 检查JWT令牌是否已过期
     *
     * @param token 待检查的JWT令牌字符串
     * @param jwtDecoder JWT解码器
     * @return true如果令牌已过期，false如果令牌仍有效，null如果无法解析
     */
    public static Boolean isTokenExpired(String token, JwtDecoder jwtDecoder) {
        try {
            Jwt jwt = jwtDecoder.decode(token);
            if (jwt != null && jwt.getExpiresAt() != null) {
                return jwt.getExpiresAt().isBefore(java.time.Instant.now());
            }
        } catch (JwtException e) {
            log.warn("Failed to check token expiration: {}", e.getMessage());
            // 如果解析失败，认为令牌已过期
            return true;
        }
        return null;
    }
}