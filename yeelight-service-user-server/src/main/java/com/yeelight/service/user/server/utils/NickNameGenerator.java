/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-04-26 14:57:14:57
 */
package com.yeelight.service.user.server.utils;

import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Desc: 昵称自动生成器
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-04-26 14:57:14:57
 */
@Slf4j
public class NickNameGenerator {
    private static final String NAME_WORDS_PATH = "/name_words";

    /**
     * 根据指定的区域和用户供应商枚举生成一个用户名。
     *
     * @param region 用户所在的区域，不同的区域可能导致用户名的生成规则不同。
     * @param userVendorEnum 用户所属的供应商，不同供应商可能有不同的用户名前缀。
     * @return 生成的用户名。如果指定了区域，则返回一个包含随机字符串的用户名；如果未指定区域，根据供应商和区域随机生成一个形容词+名词组合的用户名。
     */
    public static String generate(String region, UserVendorEnum userVendorEnum) {
        // 根据用户供应商确定用户名前缀
        String prefix = UserVendorEnum.DEFAULT.getCode().equals(userVendorEnum.getCode()) ? Constants.DEFAULT_USER_NICKNAME_PREFIX : Constants.SMARTLICHT_USER_NICKNAME_PREFIX;
        if (StringUtils.isNotBlank(region)) {
            // 按照pm要求用户名随机，如果指定了区域，返回前缀加上随机UUID的前6位
            return prefix + UUID.randomUUID().toString().substring(0, 6);
        }
        String adjectivesPath = NAME_WORDS_PATH + "/adjectives.txt";
        String nounsPath = NAME_WORDS_PATH + "/nouns.txt";
        if (Constants.DEFAULT_REGION.equals(region)) {
            // 如果区域为默认区域，改变形容词和名词的文件路径以使用中文词汇
            adjectivesPath = NAME_WORDS_PATH + "/adjectives_cn.txt";
            nounsPath = NAME_WORDS_PATH + "/nouns_cn.txt";
        }

        try {
            // 从资源文件读取形容词和名词列表
            String[] adjectivesArray = readWords(adjectivesPath);
            String[] nounsArray = readWords(nounsPath);

            // 随机选择一个形容词和名词，组合成用户名
            return adjectivesArray[(int) (Math.random() * adjectivesArray.length)] + (Constants.DEFAULT_REGION.equals(region) ? "" : " ") + nounsArray[(int) (Math.random() * nounsArray.length)];
        } catch (IOException e) {
            // 记录读取词汇文件失败的错误日志
            log.error("generate nickname error, adjectivesPath={}, nounsPath={}, error={}", adjectivesPath, nounsPath, ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    /**
     * 从指定路径读取单词。
     *
     * @param path 文件路径，使用classpath中的路径
     * @return String数组，包含文件中读取到的所有单词
     * @throws IOException 如果读取文件发生错误
     */
    private static String[] readWords(String path) throws IOException {
        List<String> words = new ArrayList<>();
        // 使用ClassPathResource加载指定路径的文件资源
        ClassPathResource classPathResource = new ClassPathResource(path);
        InputStream inputStream = classPathResource.getInputStream();

        // 使用BufferedReader逐行读取文件内容
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        String line;
        while ((line = reader.readLine()) != null) {
            words.add(line);
        }

        // 将读取到的词存入数组中并返回
        return words.toArray(new String[0]);
    }


    public static void main(String[] args) throws IOException {
        System.out.println(generate("en", UserVendorEnum.DEFAULT));
        System.out.println(generate("cn", UserVendorEnum.DEFAULT));
    }
}
