package com.yeelight.service.user.server.utils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RedisKeyUtils {

    public static final String USER_LOCK_STATUS = "LOCK";

    public static final String KEY_USER_LOCK_STATUS_PREFIX = "login:status:%s";

    public static final String KEY_USER_ERROR_COUNT_PREFIX = "login:errors:%s";

    public static final String KEY_SCAN_LOGIN_QRCODE_STATUS_PREFIX = "login:scan:qrcode:status:%s";

    public static final String AMAZON_ALEXA_SKILL_EVENT_PUBLISH_PREFIX = "amazon:alexa:skill:event:publish:%s";

    public static final String KEY_MOBILE_CAPTCHA_PREFIX = "public:mobile:captcha:key:%s";

    /**
     * 生成用户锁定状态键的字符串。
     * <p>此方法用于根据yeelightId生成一个特定格式的键名，用于标识用户的锁定状态。</p>
     *
     * @param yeelightId Yeelight设备的唯一标识符，类型为Long。
     * @return 返回格式化后的键名字符串，该字符串以KEY_USER_LOCK_STATUS_PREFIX为前缀，并包含yeelightId。
     */
    public static String getUserLockStatusKey(Long yeelightId) {
        // 使用String.format方法将KEY_USER_LOCK_STATUS_PREFIX和yeelightId格式化成一个键名字符串
        return String.format(KEY_USER_LOCK_STATUS_PREFIX, yeelightId);
    }

    /**
     * 生成用户错误计数的键名。
     * <p>此方法用于根据yeelightId生成一个特定格式的键名，用于存储用户错误计数信息。</p>
     *
     * @param yeelightId yeelight设备的唯一标识符，不能为空。
     * @return 返回格式化后的键名字符串，格式为"KEY_USER_ERROR_COUNT_PREFIX" + yeelightId。
     */
    public static String getUserErrorCountKey(Long yeelightId) {
        // 格式化生成键名
        return String.format(KEY_USER_ERROR_COUNT_PREFIX, yeelightId);
    }

    /**
     * 生成扫描登录二维码状态的键名。
     * <p>此函数用于根据二维码ID生成一个特定格式的键名，用于存储与扫描登录二维码相关的状态信息。</p>
     *
     * @param qrCodeId 二维码的唯一标识符。这是一个字符串值，不能为null。
     * @return 返回格式化后的键名字符串。键名的格式由常量KEY_SCAN_LOGIN_QRCODE_STATUS_PREFIX和传入的qrCodeId拼接而成。
     */
    public static String getScanLoginQrCodeStatusKey(String qrCodeId) {
        // 格式化生成特定于二维码状态的键名
        return String.format(KEY_SCAN_LOGIN_QRCODE_STATUS_PREFIX, qrCodeId);
    }

    /**
     * 生成亚马逊Alexa技能事件发布的密钥。
     *
     * @param userId 用户的唯一标识符。
     * @return 返回格式化后的亚马逊Alexa技能事件发布密钥字符串。
     */
    public static String getAmazonAlexaSkillEventPublishKey(String userId) {
        // 使用指定格式和用户ID生成Alexa技能事件发布密钥
        return String.format(AMAZON_ALEXA_SKILL_EVENT_PUBLISH_PREFIX, userId);
    }

    /**
     * 生成手机验证码的键名。
     *
     * @param key 用户自定义的键名部分。
     * @return 格式化后的完整验证码键名，将用户定义的键名部分与预定义的前缀结合。
     */
    public static String getMobileCaptchaKey(String key) {
        // 使用String.format方法将预定义的键名前缀与传入的key拼接，生成完整的验证码键名
        return String.format(KEY_MOBILE_CAPTCHA_PREFIX, key);
    }

    /**
     * 生成给定值的MD5加密键。
     * @param values 用于生成键的值的映射。这些值将被转换为字符串并进行MD5加密。
     * @return 返回一个32位的十六进制字符串，表示MD5加密的结果。
     * @throws IllegalStateException 如果MD5算法不可用（这在标准JDK中应该是可用的）。
     */
    public static String generateKey(Map<String, Object> values) {
        MessageDigest digest;
        try {
            // 尝试获取MD5消息摘要实例
            digest = MessageDigest.getInstance("MD5");
            // 对values的字符串表示进行MD5加密
            byte[] bytes = digest.digest(values.toString().getBytes(StandardCharsets.UTF_8));
            // 将加密后的字节转换为32位十六进制字符串
            return String.format("%032x", new BigInteger(1, bytes));
        } catch (NoSuchAlgorithmException nsae) {
            // 如果无法获取MD5算法实例，则抛出异常
            throw new IllegalStateException("MD5 algorithm not available.  Fatal (should be in the JDK).", nsae);
        }
    }
}
