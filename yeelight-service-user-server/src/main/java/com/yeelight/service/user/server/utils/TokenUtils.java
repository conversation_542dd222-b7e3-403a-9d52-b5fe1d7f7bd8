/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-11-27 16:23:16:23
 */
package com.yeelight.service.user.server.utils;

import com.yeelight.service.framework.enums.RegionEnum;
import com.yeelight.service.framework.enums.UserVendorEnum;
import com.yeelight.service.framework.request.utils.UserVendorHolder;
import com.yeelight.service.framework.util.IPUtils;
import com.yeelight.service.user.client.domain.CustomGrantedAuthority;
import com.yeelight.service.user.client.domain.YeelightUser;
import com.yeelight.service.user.client.dto.OAuth2AuthenticationDto;
import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2AccessToken;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Authentication;
import com.yeelight.service.user.client.oauth2.YeelightOAuth2Request;
import com.yeelight.service.user.server.constant.SecurityConstants;
import com.yeelight.service.user.server.constant.TokenConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2ErrorCodes;
import com.yeelight.service.user.client.utils.OAuth2Utils;
import com.yeelight.service.user.client.oauth2.YeelightClientDetails;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-11-27 16:23:16:23
 */
@Slf4j
public class TokenUtils {
    /**
     * 将Authentication对象转换为OAuth2AuthenticationDto对象。
     *
     * @param authentication 表示用户认证信息的Authentication对象。
     * @return 转换后的OAuth2AuthenticationDto对象，如果输入的authentication为null，则返回null。
     */
    public static OAuth2AuthenticationDto authenticationToDto(Authentication authentication) {
        if (Objects.isNull(authentication)) {
            return null;
        }
        OAuth2AuthenticationDto authenticationDto = new OAuth2AuthenticationDto();
        // 基本信息转换
        authenticationDto.setAuthenticated(authentication.isAuthenticated());
        authenticationDto.setCredentials(authentication.getCredentials());
        authenticationDto.setDetails(authentication.getDetails());
        authenticationDto.setName(authentication.getName());
        authenticationDto.setPrincipal(authentication.getPrincipal());

        // OAuth2认证信息特殊处理
        if (authentication instanceof YeelightOAuth2Authentication oAuth2Authentication) {
            YeelightOAuth2Request authenticationRequest = oAuth2Authentication.getOAuth2Request();
            // 创建新的OAuth2Request对象，以包含自定义权限
            YeelightOAuth2Request oAuth2Request = new YeelightOAuth2Request(
                    authenticationRequest.getRequestParameters(),
                    authenticationRequest.getClientId(),
                    authenticationRequest.getAuthorities(),
                    authenticationRequest.isApproved(),
                    authenticationRequest.getScope(),
                    authenticationRequest.getResourceIds(),
                    authenticationRequest.getRedirectUri(),
                    authenticationRequest.getResponseTypes(),
                    authenticationRequest.getExtensions()
            );
            // 设置额外的OAuth2认证信息
            authenticationDto.setAuthorities(authenticationRequest.getAuthorities().stream().map(CustomGrantedAuthority::new).collect(Collectors.toSet()));
            authenticationDto.setClientOnly(oAuth2Authentication.isClientOnly());
            authenticationDto.setUserAuthentication(oAuth2Authentication);
            authenticationDto.setStoredRequest(oAuth2Request);
        } else if (authentication instanceof PreAuthenticatedAuthenticationToken preAuthenticatedAuthenticationToken) {
            // 用户名密码认证信息特殊处理
            authenticationDto.setAuthorities(preAuthenticatedAuthenticationToken.getAuthorities().stream().map(authority -> new CustomGrantedAuthority(authority.getAuthority())).collect(Collectors.toSet()));
            authenticationDto.setDetails(preAuthenticatedAuthenticationToken.getDetails());
            authenticationDto.setPrincipal(preAuthenticatedAuthenticationToken.getPrincipal());
            authenticationDto.setCredentials(preAuthenticatedAuthenticationToken.getCredentials());
            authenticationDto.setUserAuthentication(new UsernamePasswordAuthenticationToken(
                    preAuthenticatedAuthenticationToken.getPrincipal(),
                    preAuthenticatedAuthenticationToken.getCredentials())
            );
        }
        log.info("authenticationDto {}", authenticationDto);
        return authenticationDto;
    }

    /**
     * 对OAuth2认证信息进行处理，将用户认证信息转换为UsernamePasswordAuthenticationToken。
     * 这个方法主要用于在不需要UserAuthenticationDto的情况下进行认证信息的处理。
     *
     * @param oAuth2AuthenticationDto 包含OAuth2认证信息的数据对象，其中可能包含用户认证信息和存储的请求信息。
     *                                 如果不为null，并且包含有效的用户认证信息，将基于这些信息创建一个新的UsernamePasswordAuthenticationToken。
     */
    public static void authenticationWithoutUserAuthenticationDto(OAuth2AuthenticationDto oAuth2AuthenticationDto) {
        // 检查传入的OAuth2认证信息是否非空
        if (Objects.nonNull(oAuth2AuthenticationDto)) {
            // 获取用户认证信息
            Authentication userAuthentication = oAuth2AuthenticationDto.getUserAuthentication();
            // 检查用户认证信息是否非空，并且是否为OAuth2认证类型
            if (Objects.nonNull(userAuthentication)) {
                // 检查是否有存储的请求信息，并且权限集合不为空
                if (Objects.nonNull(oAuth2AuthenticationDto.getStoredRequest()) && CollectionUtils.isNotEmpty(oAuth2AuthenticationDto.getStoredRequest().getAuthorities())) {
                    // 使用存储的请求权限创建一个新的UsernamePasswordAuthenticationToken
                    oAuth2AuthenticationDto.setUserAuthentication(new UsernamePasswordAuthenticationToken(
                            userAuthentication.getPrincipal(),
                            userAuthentication.getCredentials(),
                            oAuth2AuthenticationDto.getStoredRequest().getAuthorities().stream().map(CustomGrantedAuthority::new).collect(Collectors.toSet())
                    ));
                } else {
                    // 如果没有存储的请求权限，仅使用用户认证的主体和凭证创建UsernamePasswordAuthenticationToken
                    oAuth2AuthenticationDto.setUserAuthentication(new UsernamePasswordAuthenticationToken(
                            userAuthentication.getPrincipal(),
                            userAuthentication.getCredentials())
                    );
                }
            }
        }
    }

    /**
     * 对OAuth2认证信息进行处理，移除客户端密钥信息，确保安全性。
     * 该方法会检查传入的认证信息对象及其内部请求对象和请求参数是否非空。
     * 如果非空，则将请求参数转换为可变的Map，移除特定的敏感信息，然后更新认证信息对象中的请求参数。
     *
     * @param authenticationDto 包含OAuth2认证请求信息的对象。
     *                          该对象不应为null，并且其内部的storedRequest对象及其requestParameters字段也不应为null。
     */
    public static void safeOauth2AuthenticationDto(OAuth2AuthenticationDto authenticationDto) {
        // 检查authenticationDto及其storedRequest和requestParameters是否非空
        if (Objects.nonNull(authenticationDto) && Objects.nonNull(authenticationDto.getStoredRequest()) && Objects.nonNull(authenticationDto.getStoredRequest().getRequestParameters())) {
            // 将不可变的请求参数map复制到可变的HashMap中
            Map<String, String> requestParameters = new HashMap<>(authenticationDto.getStoredRequest().getRequestParameters());
            // 移除包含客户端密钥的敏感信息
            requestParameters.remove(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_SECRET);
            // 使用更新后的请求参数创建新的OAuth2Request对象，并更新authenticationDto中的storedRequest
            authenticationDto.setStoredRequest(new YeelightOAuth2Request(
                    requestParameters,
                    authenticationDto.getStoredRequest().getClientId(),
                    // 将原有权限转换为自定义权限类型，以保持兼容性
                    authenticationDto.getStoredRequest().getAuthorities(),
                    authenticationDto.getStoredRequest().isApproved(),
                    authenticationDto.getStoredRequest().getScope(),
                    authenticationDto.getStoredRequest().getResourceIds(),
                    authenticationDto.getStoredRequest().getRedirectUri(),
                    authenticationDto.getStoredRequest().getResponseTypes(),
                    authenticationDto.getStoredRequest().getExtensions()
            ));
        }
    }

    /**
     * 将OAuth2AuthenticationDto对象转换为OAuth2Authentication对象。
     *
     * @param authenticationDto 包含认证请求信息和用户认证信息的数据传输对象。
     * @return 转换后的OAuth2Authentication对象，如果输入的authenticationDto或其storedRequest为null，则返回null。
     */
    public static YeelightOAuth2Authentication dtoToOauth2Authentication(OAuth2AuthenticationDto authenticationDto) {
        // 当传入的authenticationDto及其storedRequest非空时，进行转换
        if (Objects.nonNull(authenticationDto) && Objects.nonNull(authenticationDto.getStoredRequest())) {
            // 创建OAuth2Request对象，包含认证请求的参数、客户端ID、权限集合等信息
            YeelightOAuth2Request authenticationRequest = authenticationDto.getStoredRequest();
            YeelightOAuth2Request oAuth2Request = new YeelightOAuth2Request(
                    authenticationRequest.getRequestParameters(),
                    authenticationRequest.getClientId(),
                    // 将原始权限转换为自定义权限
                    authenticationRequest.getAuthorities(),
                    authenticationRequest.isApproved(),
                    authenticationRequest.getScope(),
                    authenticationRequest.getResourceIds(),
                    authenticationRequest.getRedirectUri(),
                    authenticationRequest.getResponseTypes(),
                    authenticationRequest.getExtensions()
            );
            // 创建OAuth2Authentication对象，包含认证请求和用户认证信息
            YeelightOAuth2Authentication authentication = new YeelightOAuth2Authentication(
                    oAuth2Request,
                    authenticationDto.getUserAuthentication()
            );
            // 设置认证状态和详情
            authentication.setAuthenticated(authenticationDto.isAuthenticated());
            authentication.setDetails(authenticationDto.getDetails());
            return authentication;
        }
        return null;
    }

    /**
     * 将OAuth2AuthenticationDto对象转换为安全的OAuth2Authentication对象。
     * 这个转换过程包括将不可变的请求参数转换为可变的，并且从请求参数中移除特定的敏感信息。
     *
     * @param authenticationDto 包含认证请求信息和用户认证信息的DTO对象。
     * @return 转换后的OAuth2Authentication对象，如果输入的DTO对象或其包含的storedRequest为null，则返回null。
     */
    public static YeelightOAuth2Authentication dtoToSafeOauth2Authentication(OAuth2AuthenticationDto authenticationDto) {

        if (Objects.nonNull(authenticationDto) && Objects.nonNull(authenticationDto.getStoredRequest())) {
            YeelightOAuth2Request authenticationRequest = authenticationDto.getStoredRequest();

            // 创建一个可变的请求参数map，并从原始不可变map中移除敏感信息
            Map<String, String> requestParameters = new HashMap<>(4);
            if (Objects.nonNull(authenticationRequest.getRequestParameters())) {
                requestParameters = new HashMap<>(authenticationRequest.getRequestParameters());
                // 移除client_secret信息，避免敏感信息泄露
                requestParameters.remove(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_SECRET);
            }

            // 创建一个新的OAuth2Request对象，包含修改后的请求参数
            YeelightOAuth2Request oAuth2Request = new YeelightOAuth2Request(
                    requestParameters,
                    authenticationRequest.getClientId(),
                    authenticationRequest.getAuthorities(),
                    authenticationRequest.isApproved(),
                    authenticationRequest.getScope(),
                    authenticationRequest.getResourceIds(),
                    authenticationRequest.getRedirectUri(),
                    authenticationRequest.getResponseTypes(),
                    authenticationRequest.getExtensions()
            );

            // 根据转换后的请求信息和用户认证信息，创建新的OAuth2Authentication对象
            YeelightOAuth2Authentication authentication = new YeelightOAuth2Authentication(
                    oAuth2Request,
                    authenticationDto.getUserAuthentication()
            );

            // 设置认证状态和详情
            authentication.setAuthenticated(authenticationDto.isAuthenticated());
            authentication.setDetails(authenticationDto.getDetails());

            return authentication;
        }
        // 如果输入不满足要求，返回null
        return null;
    }

    /**
     * 校验客户端请求的授权类型是否被该客户端允许。
     *
     * @param grantType 需要校验的授权类型。
     * @param clientDetails 客户端的详细信息，包含允许的授权类型列表。
     * @throws OAuth2AuthenticationException 如果校验失败，即授予类型未在客户端详情中授权，则抛出异常。
     */
    public static void validateGrantType(String grantType, YeelightClientDetails clientDetails) {
        // 获取客户端被授权的类型集合
        Collection<String> authorizedGrantTypes = clientDetails.getAuthorizedGrantTypes();
        // 判断授权类型集合不为空且不包含传入的grantType时，抛出异常
        if (authorizedGrantTypes != null && !authorizedGrantTypes.isEmpty()
                && !authorizedGrantTypes.contains(grantType)) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.UNAUTHORIZED_CLIENT), "Unauthorized grant type: " + grantType);
        }
    }

    /**
     * 校验请求中指定的范围（scopes）是否有效。
     * <p>
     * 此方法会检查请求中指定的范围是否都在客户端注册的可允许范围内。如果存在请求中指定的范围不在客户端允许的范围内，
     * 或者请求中没有指定任何范围，则会抛出异常。
     *
     * @param requestScopes 请求中指定的范围集合，由客户端请求访问特定资源时提供。
     * @param clientScopes 客户端注册时被允许的范围集合。如果客户端未指定允许的范围，此参数可为null。
     * @throws OAuth2AuthenticationException 如果请求中指定的任一范围不在客户端允许的范围内，或请求中没有指定任何范围。
     */
    public static void validateScope(Set<String> requestScopes, Set<String> clientScopes) {

        // 验证请求的范围是否在客户端允许的范围内
        if (clientScopes != null && !clientScopes.isEmpty()) {
            for (String scope : requestScopes) {
                if (!clientScopes.contains(scope)) {
                    throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_SCOPE), "Invalid scope: " + scope);
                }
            }
        }

        // 验证请求是否至少包含一个范围
        if (requestScopes.isEmpty()) {
            throw new OAuth2AuthenticationException(new OAuth2Error(OAuth2ErrorCodes.INVALID_SCOPE), "Empty scope (either the client or the user is not allowed the requested scopes)");
        }
    }

    /**
     * 判断请求是否为刷新令牌的请求。
     *
     * @param parameters 请求参数的Map，预期包含grant_type和refresh_token参数。
     * @return 返回true如果请求是刷新令牌的请求，即grant_type参数等于"refresh_token"，且refresh_token参数非空。
     */
    public static boolean isRefreshTokenRequest(Map<String, String> parameters) {
        // 检查grant_type参数是否为"refresh_token"，且refresh_token参数是否非空
        return YeelightOAuth2AccessToken.REFRESH_TOKEN.equals(parameters.get(OAuth2Utils.GRANT_TYPE)) && parameters.get(YeelightOAuth2AccessToken.REFRESH_TOKEN) != null;
    }

    /**
     * 判断传入的参数是否是授权码请求。
     *
     * @param parameters 包含请求参数的Map，期望包含grant_type和code参数。
     * @return 如果请求类型为"authorization_code"且包含非空的"code"参数，则返回true；否则返回false。
     */
    public static boolean isAuthCodeRequest(Map<String, String> parameters) {
        // 检查grant_type是否为"authorization_code"，并且code参数非空
        return "authorization_code".equals(parameters.get(OAuth2Utils.GRANT_TYPE)) && parameters.get("code") != null;
    }

    /**
     * 将OAuth2AccessToken转换为OAuth2Token。
     * 这个方法主要用于将Spring Security OAuth2的AccessToken格式转换为应用内自定义的OAuth2Token格式，
     * 以便于在应用中更方便地使用和管理访问令牌。
     *
     * @param accessToken OAuth2的访问令牌，不可为null。
     * @return 转换后的自定义OAuth2Token对象，如果输入的accessToken为null，则返回null。
     */
    public static OAuth2Token accessTokenToAuth2Token(YeelightOAuth2AccessToken accessToken) {
        if (Objects.nonNull(accessToken)) {
            // 从accessToken中提取区域信息，优先使用额外信息中的区域字段，没有则默认为CN
            String tokenRegion;
            Object region = accessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_REGION);
            if (region instanceof String) {
                tokenRegion = (String) region;
            } else if (region instanceof RegionEnum) {
                tokenRegion = ((RegionEnum) region).getCode();
            } else {
                tokenRegion = RegionEnum.CN.getCode();
            }

            // 根据accessToken构建自定义的OAuth2Token对象
            return OAuth2Token.builder()
                    .accessToken(accessToken.getValue())
                    .refreshToken(accessToken.getRefreshToken().getValue())
                    .tokenType(accessToken.getTokenType())
                    .expiresIn(accessToken.getExpiresIn())
                    .scope(String.join(" ", Optional.ofNullable(accessToken.getScope()).orElse(new HashSet<>())))
                    .id((Long) accessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_ID))
                    .region(tokenRegion)
                    .device((String) accessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE))
                    .clientId((String) accessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_ID))
                    .username((String) accessToken.getAdditionalInformation().get(TokenConstants.ADDITIONAL_INFORMATION_USERNAME))
                    .build();
        }

        return null;
    }

    /**
     * 为访问令牌附加额外信息。
     * 此函数会根据提供的认证信息、OAuth2请求、HTTP请求等，收集并附加额外的信息到additionalInformation映射中。
     * 额外信息包括用户身份识别信息、设备信息、客户端ID、供应商信息、创建时间、用户代理、IP地址和区域设置等。
     *
     * @param additionalInformation 用于收集额外信息的映射，键和值的类型分别为String和Object。
     * @param authentication 用户的认证信息，用于获取用户身份和细节。
     * @param oAuth2Request OAuth2授权请求，包含客户端ID和设备信息等。
     * @param httpServletRequest 当前的HTTP请求，用于获取用户代理、IP地址和区域设置等。
     */
    public static void attachAdditionalInformationForAccessToken(Map<String, Object> additionalInformation, Authentication authentication, YeelightOAuth2Request oAuth2Request, HttpServletRequest httpServletRequest) {
        // 兼容 client_credentials 模式，从认证信息中提取用户ID和用户名
        if (Objects.nonNull(authentication)) {
            YeelightUser user = (YeelightUser) authentication.getPrincipal();
            // 以下是做演示，生产环境绝对不允许在token里放邮箱、电话等敏感信息，只允许放能够识别用户身份的简单信息
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_ID, user.getId());
            //additionalInformation.put("phone_number", user.getPhoneNumber());
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_USERNAME, user.getUsername());
        }

        // 处理设备信息，优先从OAuth2请求参数中获取，其次是认证详情中，最后生成一个随机的UUID
        if (Objects.nonNull(oAuth2Request)) {
            String device = oAuth2Request.getRequestParameters().get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE);
            if (device == null) {
                if (authentication != null) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> details = (Map<String, String>) authentication.getDetails();
                    if (details != null) {
                        device = details.get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE);
                    }
                }
                if (device == null) {
                    device = UUID.randomUUID().toString();
                }
            }
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, device);
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_ID, oAuth2Request.getClientId());
        }

        // 确定用户所属的供应商，以及区域信息，并记录创建时间
        UserVendorEnum userVendorEnum;
        Object principal = authentication.getPrincipal();
        if (principal instanceof YeelightUser userDetail) {
            userVendorEnum = Arrays.stream(UserVendorEnum.values()).filter(vendor -> vendor.userIdRange().contains(userDetail.getId())).findFirst().orElse(UserVendorEnum.DEFAULT);
        } else {
            userVendorEnum = UserVendorHolder.getVendorEnum();
        }
        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_VENDOR, userVendorEnum.name());
        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_REGION, userVendorEnum.getRegion());
        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME, System.currentTimeMillis());

        // 尝试从请求上下文中获取HttpServletRequest，用于提取User-Agent、IP地址和区域设置
        if (Objects.isNull(httpServletRequest)) {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (Objects.nonNull(servletRequestAttributes)) {
                httpServletRequest = servletRequestAttributes.getRequest();
            }
        }
        if (Objects.nonNull(httpServletRequest)) {
            // 获取User-Agent信息并放入additionalInformation
            String userAgent = httpServletRequest.getHeader("User-Agent");
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_USER_AGENT, userAgent);
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_IP, IPUtils.getRealIp(httpServletRequest));
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_LOCALE, Optional.ofNullable(httpServletRequest.getLocale()).orElse(Locale.CHINA).toLanguageTag());
        }
    }

    /**
     * 为令牌请求附加额外信息。
     * 此函数会根据提供的认证信息、OAuth2请求、HTTP请求等，收集并附加额外的信息到additionalInformation映射中。
     * 额外信息包括用户身份识别信息、设备信息、客户端ID、供应商信息、地域信息、创建时间、用户代理、IP地址和语言环境等。
     *
     * @param additionalInformation 用于收集额外信息的映射，键和值为字符串类型。
     * @param authentication 用户的认证信息，可用于获取用户身份和设备信息。
     * @param oAuth2Request OAuth2授权请求，包含客户端ID和设备信息等。
     * @param httpServletRequest HTTP请求，用于获取用户代理、IP地址和语言环境等信息。
     */
    public static void attachAdditionalInformationForTokenRequest(Map<String, String> additionalInformation, Authentication authentication, YeelightOAuth2Request oAuth2Request, HttpServletRequest httpServletRequest) {
        if (Objects.nonNull(authentication)) {
            // 兼容 client_credentials 模式，收集用户身份信息
            YeelightUser user = (YeelightUser) authentication.getPrincipal();
            // 收集用户ID和用户名，但不允许邮箱、电话等敏感信息，只允许放能够识别用户身份的简单信息
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_ID, String.valueOf(user.getId()));
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_USERNAME, user.getUsername());
        }

        if (Objects.nonNull(oAuth2Request)) {
            // 尝试从OAuth2请求参数中获取设备信息，未找到时从认证详情中获取，若仍无则生成一个唯一标识
            if (oAuth2Request.getRequestParameters().get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE) != null) {
                additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, oAuth2Request.getRequestParameters().get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE));
            } else {
                if (Objects.nonNull(authentication)) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> details = (Map<String, String>) authentication.getDetails();
                    if (details != null && details.get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE) != null) {
                        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, details.get(TokenConstants.ADDITIONAL_INFORMATION_DEVICE));
                    } else {
                        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, UUID.randomUUID().toString());
                    }
                } else {
                    additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_DEVICE, UUID.randomUUID().toString());
                }
            }
            // 收集客户端ID
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_CLIENT_ID, oAuth2Request.getClientId());
        }

        // 根据用户类型确定供应商信息，并收集地域代码和创建时间
        UserVendorEnum userVendorEnum = UserVendorHolder.getVendorEnum();
        if (Objects.nonNull(authentication)) {
            Object principal = authentication.getPrincipal();
            if (Objects.nonNull(principal) && principal instanceof YeelightUser userDetail) {
                // 根据用户ID确定供应商
                userVendorEnum = Arrays.stream(UserVendorEnum.values()).filter(vendor -> vendor.userIdRange().contains(userDetail.getId())).findFirst().orElse(UserVendorEnum.DEFAULT);
            }
        }
        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_VENDOR, userVendorEnum.name());
        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_REGION, userVendorEnum.getRegion().getCode());
        additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_CREATE_TIME, String.valueOf(System.currentTimeMillis()));

        // 若未提供HTTP请求，则尝试从请求上下文中获取
        if (Objects.isNull(httpServletRequest)) {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (Objects.nonNull(servletRequestAttributes)) {
                httpServletRequest = servletRequestAttributes.getRequest();
            }
        }
        if (Objects.nonNull(httpServletRequest)) {
            // 收集用户代理、IP地址和语言环境信息并放入additionalInformation
            String userAgent = httpServletRequest.getHeader("User-Agent");
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_USER_AGENT, userAgent);
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_IP, IPUtils.getRealIp(httpServletRequest));
            additionalInformation.putIfAbsent(TokenConstants.ADDITIONAL_INFORMATION_LOCALE, Optional.ofNullable(httpServletRequest.getLocale()).orElse(Locale.CHINA).toLanguageTag());
        }
    }

    /**
     * 从HTTP请求中提取认证令牌。
     * 首先尝试从请求头的Authorization字段中以Bearer类型提取令牌，
     * 若未获取到则尝试直接从请求头的Authorization字段中提取令牌。
     * 若仍未获取到，则尝试从URL参数中提取access_token或从Cookie中提取。
     *
     * @param request HttpServletRequest对象，用于获取请求头、参数和Cookie。
     * @return 返回提取到的认证令牌字符串，如果未提取到则返回空字符串。
     */
    public static String extractToken(HttpServletRequest request) {
        // 尝试从请求头的Authorization字段以Bearer类型提取令牌
        String authToken = request.getHeader(HttpHeaders.AUTHORIZATION.toLowerCase());
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getHeader(HttpHeaders.AUTHORIZATION);
        }
        // 移除Bearer类型字符串，仅保留令牌
        if (authToken != null) {
            authToken = authToken.replace(YeelightOAuth2AccessToken.BEARER_TYPE, "").trim();
        }

        // 未从请求头获取到令牌，尝试从URL参数中获取
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getParameter(YeelightOAuth2AccessToken.ACCESS_TOKEN);
        }

        // 仍未获取到令牌，尝试从自定义安全参数中获取
        if (StringUtils.isBlank(authToken)) {
            authToken = request.getParameter(SecurityConstants.TOKEN_KEY);
        }

        // 仍然未获取到令牌，尝试从Cookie中获取
        if (StringUtils.isBlank(authToken)) {
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    // 从Cookie中寻找认证令牌
                    if (YeelightOAuth2AccessToken.ACCESS_TOKEN.equals(cookie.getName())) {
                        authToken = cookie.getValue();
                    }
                }
            }
        }

        return authToken;
    }

    /**
     * 将OAuth2Authorization对象转换为OAuth2AuthenticationDto对象。
     * 这是为新版Spring Authorization Server添加的转换方法，保持与authenticationToDto相同的逻辑。
     *
     * @param authorization 表示OAuth2授权信息的OAuth2Authorization对象。
     * @return 转换后的OAuth2AuthenticationDto对象，如果输入的authorization为null，则返回null。
     */
    public static OAuth2AuthenticationDto authorizationToDto(org.springframework.security.oauth2.server.authorization.OAuth2Authorization authorization) {
        if (Objects.isNull(authorization)) {
            return null;
        }

        OAuth2AuthenticationDto authenticationDto = new OAuth2AuthenticationDto();

        // 基本信息转换
        authenticationDto.setAuthenticated(true);
        authenticationDto.setName(authorization.getPrincipalName());
        authenticationDto.setPrincipal(authorization.getPrincipalName());
        authenticationDto.setClientOnly(authorization.getPrincipalName() == null);

        // 构建YeelightOAuth2Request
        YeelightOAuth2Request oAuth2Request = YeelightOAuth2Request.builder()
            .clientId(authorization.getRegisteredClientId())
            .grantType(authorization.getAuthorizationGrantType().getValue())
            .scope(authorization.getAuthorizedScopes())
            .approved(true)
            .build();

        authenticationDto.setStoredRequest(oAuth2Request);

        // 构建用户认证信息（如果不是客户端模式）
        if (authorization.getPrincipalName() != null) {
            UsernamePasswordAuthenticationToken userAuthentication = new UsernamePasswordAuthenticationToken(
                authorization.getPrincipalName(),
                null,
                null
            );
            authenticationDto.setUserAuthentication(userAuthentication);
        }

        return authenticationDto;
    }

    /**
     * 将OAuth2AuthenticationDto对象转换为YeelightOAuth2Authentication对象。
     * 这是为新版Spring Authorization Server添加的转换方法。
     *
     * @param authenticationDto 表示OAuth2认证信息的DTO对象。
     * @return 转换后的YeelightOAuth2Authentication对象，如果输入的authenticationDto为null，则返回null。
     */
    public static YeelightOAuth2Authentication dtoToSafeYeelightOauth2Authentication(OAuth2AuthenticationDto authenticationDto) {
        if (Objects.isNull(authenticationDto)) {
            return null;
        }

        return YeelightOAuth2Authentication.builder()
            .oAuth2Request(authenticationDto.getStoredRequest())
            .userAuthentication(authenticationDto.getUserAuthentication())
            .authenticated(authenticationDto.isAuthenticated())
            .build();
    }
}
