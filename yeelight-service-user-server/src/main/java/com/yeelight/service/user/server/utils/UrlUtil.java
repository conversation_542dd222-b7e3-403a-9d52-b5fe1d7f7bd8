/*
 * ProjectName: yeelight-service-gateway
 * PackageName: com.yeelight.service.gateway.util
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-10-12 19:50:19:50
 */
package com.yeelight.service.user.server.utils;

import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.constant.Constants;
import com.yeelight.service.user.server.constant.SecurityConstants;
import org.springframework.http.server.reactive.ServerHttpRequest;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-10-12 19:50:19:50
 */
public class UrlUtil {
    private static final String DOT_DELIMITER = ".";
    private static final String SLASH_DELIMITER = "/";
    private static final String LOCALHOST = "localhost";
    private static final String LOCAL_IP = "127.0.0.1";
    private static final String HTTPS_SCHEME_PREFIX = "https://";
    private static final String HTTP_SCHEME_PREFIX = "http://";
    private static final int HTTP_PORT = 80;
    private static final int HTTPS_PORT = 443;

    private final static Set<String> PUBLIC_SUFFIX_SET = new HashSet<>(
            Arrays.asList("com|org|net|gov|edu|co|tv|mobi|info|asia|xxx|onion|cn|com.cn|edu.cn|gov.cn|net.cn|org.cn|jp|kr|tw|com.hk|hk|com.hk|org.hk|se|com.se|org.se"
                    .split("\\|")));

    private static final Pattern IP_PATTERN = Pattern.compile("(\\d{1,3}\\.){3}(\\d{1,3})");

    /**
     * 获取给定URL的顶级域名。
     *
     * @param url 输入的URL对象。
     * @return 该URL的顶级域名字符串。如果输入是IP地址，则返回原IP地址。
     */
    public static String getTopDomainName(URL url) {
        // 获取URL的主机名
        String host = url.getHost();
        // 移除末尾的句点，如果存在
        if (host.endsWith(DOT_DELIMITER)) {
            host = host.substring(0, host.length() - 1);
        }
        // 如果主机名是IP地址，则直接返回
        if (IP_PATTERN.matcher(host).matches()) {
            return host;
        }

        int index = 0;
        String candidate = host;
        // 从后往前遍历域名，查找顶级域名
        while (index >= 0) {
            index = candidate.indexOf('.');
            String subCandidate = candidate.substring(index + 1);
            // 检查子域名是否在公共后缀列表中
            if (PUBLIC_SUFFIX_SET.contains(subCandidate)) {
                return candidate;
            }
            candidate = subCandidate;
        }
        // 如果没有找到公共后缀，返回最后的候选域名
        return candidate;
    }

    /**
     * 获取给定URL的顶级域名。
     *
     * @param url 待处理的URL字符串。
     * @return 该URL的顶级域名字符串。
     * @throws MalformedURLException 如果传入的URL格式不正确。
     */
    public static String getTopDomainName(String url) throws MalformedURLException {
        // 通过URL字符串创建URL对象，然后调用另一个重载方法进行处理
        return getTopDomainName(URI.create(url).toURL());
    }

    /**
     * 判断两个URL是否属于同一个域名。
     *
     * @param url1 第一个URL对象，用于比较域名。
     * @param url2 第二个URL对象，用于比较域名。
     * @return 如果两个URL的顶级域名相同，则返回true；否则返回false。
     */
    public static boolean isSameDomainName(URL url1, URL url2) {
        // 比较两个URL的顶级域名是否相同
        return getTopDomainName(url1).equalsIgnoreCase(getTopDomainName(url2));
    }

    /**
     * 判断两个给定的URL是否属于同一个域名。
     *
     * @param url1 第一个URL字符串。
     * @param url2 第二个URL字符串。
     * @return 如果两个URL的域名相同，则返回true；否则返回false。
     * @throws MalformedURLException 如果给定的URL字符串格式不正确，无法转换为URL对象时抛出。
     */
    public static boolean isSameDomainName(String url1, String url2) throws MalformedURLException {
        return isSameDomainName(URI.create(url1).toURL(), URI.create(url2).toURL());
    }


    /**
     * 构建完整的请求URL。
     *
     * @param scheme 协议类型（如http或https），如果为空则默认为"http"。
     * @param serverName 服务器名称或IP地址，可以包含"http://"或"https://"前缀。
     * @param serverPort 服务器端口号，如果为默认端口（http:80, https:443），则不会在URL中显示端口号。
     * @param requestUri 请求的URI，如果不以"/"开头，将会在前面添加"/"。
     * @param queryString 查询字符串，将附加在URL的"?"后面，如果为null，则不附加查询字符串。
     * @return 返回构建好的完整请求URL字符串。
     */
    public static String buildFullRequestUrl(String scheme, String serverName, int serverPort, String requestUri, String queryString) {

        // 将scheme设置为默认值"http"并转为小写
        scheme = StringUtils.defaultIfBlank(scheme, Constants.HTTP_KEY).toLowerCase();

        StringBuilder url = new StringBuilder();
        // 处理serverName，如果包含"http://"或"https://"则直接使用，否则根据scheme添加协议头
        if (StringUtils.isNotBlank(serverName)) {
            if (serverName.contains("http://") || serverName.contains("https://")) {
                url.append(serverName);
            } else {
                url.append(scheme).append("://").append(serverName);
            }
        }

        // 仅在非默认端口时添加端口号
        buildRequestPort(scheme, serverPort, url);


        // 处理requestUri，确保其以"/"开始，并根据URL是否为本地回环地址处理requestUri
        if (StringUtils.isNotBlank(requestUri)) {
            // Use the requestURI as it is encoded (RFC 3986) and hence suitable for
            // redirects.
            if (!requestUri.startsWith(SLASH_DELIMITER)) {
                url.append(SLASH_DELIMITER);
            }
            // 判断是否 本地回环
            if (url.toString().contains(LOCALHOST) || url.toString().contains(LOCAL_IP)) {
                url.append(requestUri.replace(SecurityConstants.PREFIX_PATH, ""));
            } else {
                url.append(requestUri);
            }
        }

        // 如果存在查询字符串，则将其附加在URL后面
        if (null != queryString) {
            url.append("?").append(queryString);
        }

        return url.toString();
    }

    private static void buildRequestPort(String scheme, int serverPort, StringBuilder url) {
        // Only add port if not default
        if (Constants.HTTP_KEY.equals(scheme)) {
            if (serverPort != -1 && serverPort != HTTP_PORT && serverPort != HTTPS_PORT) {
                url.append(":").append(serverPort);
            }
        } else if (Constants.HTTPS_KEY.equals(scheme)) {
            if (serverPort != -1 && serverPort != HTTP_PORT && serverPort != HTTPS_PORT) {
                url.append(":").append(serverPort);
            }
        }
    }

    /**
     * 将给定URL转换为当前请求的URL。
     * 该方法通过将原始URL中的域名替换为当前请求的域名来实现转换。
     * 注意：此方法专为处理开发、测试和生产环境之间的URL转换而设计。
     *
     * @param url 需要转换的原始URL字符串。
     * @param serverHttpRequest 当前服务器HTTP请求对象，用于获取当前请求的URI信息。
     * @return 转换后的URL字符串。如果转换过程中发生异常，则返回原始URL。
     */
    public static String toCurrentUrl(String url, ServerHttpRequest serverHttpRequest) {
        try {
            // 解析原始URL，并构建原始域名字符串
            URL uri = URI.create(url).toURL();
            String oldDomain = UrlUtil.buildFullRequestUrl(uri.getProtocol(), uri.getHost(), uri.getPort(), "", null);

            // 根据当前请求的URI，构建新的域名字符串，替换开发、测试等环境标识
            String currentDomain = UrlUtil.buildFullRequestUrl(serverHttpRequest.getURI().getScheme(), serverHttpRequest.getURI().getHost()
                    .replace("-dev", "-test").replace("-stage", "").replace("-st", "")
                    , serverHttpRequest.getURI().getPort(), "", null);

            // 使用新域名替换原始URL中的域名，返回转换后的URL
            return url.replace(oldDomain, currentDomain);
        } catch (Exception e) {
            // 如果有任何异常发生，返回原始URL
            return url;
        }
    }

    /**
     * 替换URL中的顶级域名。
     *
     * @param url 待处理的URL字符串。
     * @param topDomain 新的顶级域名。
     * @return 返回替换后的URL字符串。如果URL格式不正确，则返回原始URL。
     */
    public static String replaceTopDomain(String url, String topDomain) {
        try {
            URL uri = URI.create(url).toURL();
            String oldDomain = UrlUtil.buildFullRequestUrl(uri.getProtocol(), uri.getHost(), uri.getPort(), "", null);
            String newDomain = oldDomain.replace(getTopDomainName(oldDomain), topDomain);
            return url.replace(oldDomain, newDomain);
        } catch (Exception e) {
            return url;
        }
    }
}
