/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description:
 * CreateBy: yeelight
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-01-09 16:01:16:01
 */
package com.yeelight.service.user.server.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;

/**
 * Desc:
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-01-09 16:01:16:01
 */
public class UserAgentUtil {

    // 静态初始化块
    static {
        // 定义解析规则，以处理特定格式的User-Agent字符串，例如基于Dart语言的客户端（ua为Dart/3.1 (dart:io)时（flutter））
        // 初始化自定义浏览器规则
        Browser.addCustomBrowser("Mobile App", "Dart", "Dart\\/([\\d\\w\\.\\-]+)");

        // 初始化引擎规则
        Engine.engines.add(new Engine("Dart", "Dart"));

        // 初始化操作系统规则
        OS.addCustomOs("Mobile App", "Dart", "Dart\\/([\\d\\w\\.\\-]+)");

        // 初始化平台规则
        Platform.platforms.add(new Platform("Mobile App", "Dart"));
        Platform.mobilePlatforms.add(new Platform("Mobile App", "Dart"));

        // TODO: 定义其他特定User-Agent字符串的解析规则
    }

    /**
     * 解析User-Agent
     * <p>
     * 此方法用于解析传入的User-Agent字符串，提取其中关于浏览器、引擎、操作系统和平台的信息。
     *
     * @param userAgentString User-Agent字符串。这是用于识别发出HTTP请求的客户端软件的字符串。
     * @return {@link UserAgent} 包含解析后关于浏览器、引擎、操作系统和平台信息的对象。如果输入的User-Agent字符串为空或白名单，则返回null。
     */
    public static UserAgent parse(String userAgentString) {
        // 检查传入的User-Agent字符串是否为空或白名单，如果是则直接返回null
        if(StrUtil.isBlank(userAgentString)){
            return null;
        }
        final UserAgent userAgent = new UserAgent();

        // 解析浏览器信息，并设置到UserAgent对象中
        final Browser browser = parseBrowser(userAgentString);
        userAgent.setBrowser(browser);
        userAgent.setVersion(browser.getVersion(userAgentString));

        // 解析浏览器引擎信息，并设置到UserAgent对象中
        final Engine engine = parseEngine(userAgentString);
        userAgent.setEngine(engine);
        userAgent.setEngineVersion(engine.getVersion(userAgentString));

        // 解析操作系统信息，并设置到UserAgent对象中
        final OS os = parseOs(userAgentString);
        userAgent.setOs(os);
        userAgent.setOsVersion(os.getVersion(userAgentString));

        // 解析平台信息，并设置到UserAgent对象中
        final Platform platform = parsePlatform(userAgentString);
        userAgent.setPlatform(platform);
        // 判断是否为移动设备，基于浏览器和平台的移动性标识
        userAgent.setMobile(platform.isMobile() || browser.isMobile());


        return userAgent;
    }


    /**
     * 解析浏览器类型
     * <p>
     * 这个方法通过检查传入的User-Agent字符串来识别浏览器类型。它会遍历已知的浏览器列表，
     * 并通过比较User-Agent字符串来匹配浏览器。如果找到匹配项，就返回对应的浏览器类型；
     * 如果没有匹配到任何已知浏览器，则返回未知浏览器类型。
     *
     * @param userAgentString User-Agent字符串，这是浏览器发送到服务器以标识自己的字符串。
     * @return 浏览器类型。返回的是Browser枚举的一个实例，表示解析出的浏览器类型。
     */
    private static Browser parseBrowser(String userAgentString) {
        if (StrUtil.isBlank(userAgentString)) {
            return Browser.Unknown;
        }
        if(CollectionUtils.isNotEmpty(Browser.browers)){
            // 遍历所有已知的浏览器类型，尝试匹配User-Agent字符串
            for (Browser browser : Browser.browers) {
                if (Objects.nonNull(browser) && browser.isMatch(userAgentString)) {
                    // 如果找到匹配的浏览器类型，则返回该浏览器类型
                    return browser;
                }
            }
        }
        // 如果没有匹配到任何已知浏览器，则返回未知浏览器类型
        return Browser.Unknown;
    }


    /**
     * 解析传入的User-Agent字符串，以确定其对应的引擎类型。
     *
     * @param userAgentString 待解析的User-Agent字符串。
     * @return 返回匹配到的引擎类型；如果没有匹配到任何已知引擎，则返回未知引擎类型。
     */
    private static Engine parseEngine(String userAgentString) {
        // 遍历所有已知的引擎，尝试匹配传入的User-Agent字符串
        for (Engine engine : Engine.engines) {
            if (engine.isMatch(userAgentString)) {
                // 如果找到匹配的引擎，则返回该引擎类型
                return engine;
            }
        }
        // 如果没有匹配到任何已知引擎，返回未知引擎类型
        return Engine.Unknown;
    }


    /**
     * 解析用户代理字符串以确定操作系统类型。
     * 这个方法通过遍历已知的操作系统列表，匹配User-Agent字符串来识别操作系统。
     *
     * @param userAgentString 用户代理字符串，通常是HTTP请求头中的一部分，用于标识发起请求的客户端。
     * @return OS 枚举类型的实例，代表识别出的操作系统类型。如果无法识别，则返回 OS.Unknown。
     */
    private static OS parseOs(String userAgentString) {
        // 遍历所有已知的操作系统，尝试匹配User-Agent字符串
        for (OS os : OS.oses) {
            if (os.isMatch(userAgentString)) {
                // 如果找到匹配的操作系统，则返回该操作系统的枚举实例
                return os;
            }
        }
        // 如果没有匹配到任何已知操作系统，则返回Unknown
        return OS.Unknown;
    }


    /**
     * 解析用户代理字符串以确定平台类型。
     * 这个方法通过遍历已知的平台类型，逐一匹配User-Agent字符串来识别平台。
     * 如果没有匹配到任何已知平台，则返回未知平台类型。
     *
     * @param userAgentString 用户代理字符串，用于识别客户端的平台和浏览器等信息。
     * @return Platform 返回识别出的平台类型。如果没有匹配到任何已知平台，则返回Unknown平台类型。
     */
    private static Platform parsePlatform(String userAgentString) {
        // 遍历所有已知平台，尝试匹配User-Agent字符串
        for (Platform platform : Platform.platforms) {
            if (platform.isMatch(userAgentString)) {
                // 如果找到匹配的平台，则返回该平台类型
                return platform;
            }
        }
        // 如果没有匹配到任何已知平台，返回Unknown平台类型
        return Platform.Unknown;
    }


    public static void main(String[] args) {
        UserAgent userAgent = parse("Dart/3.1 (dart:io)");
        System.out.println(userAgent);
    }
}
