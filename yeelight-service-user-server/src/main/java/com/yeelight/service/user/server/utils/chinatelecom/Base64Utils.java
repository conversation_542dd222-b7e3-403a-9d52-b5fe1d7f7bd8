package com.yeelight.service.user.server.utils.chinatelecom;

import java.util.Base64;

/**
 * <p>
 * BASE64编码解码工具包
 * </p>
 * <p>
 * 依赖javabase64-1.3.1.jar
 * </p>
 *
 * <AUTHOR>
 * @date 2012-5-19
 * @version 1.0
 */
public class Base64Utils {
    /**
     * 文件读取缓冲区大小
     */
    private static final int CACHE_SIZE = 1024;

/**
     * <p>
     * BASE64字符串解码为二进制数据
     * </p>
     *
     * @param base64 base64字符串
     * @return 二进制数据
 */
    public static byte[] decode(String base64) {
        return Base64.getMimeDecoder().decode(base64);
    }

/**
     * <p>
     * 二进制数据编码为BASE64字符串
     * </p>
     *
     * @param bytes 二进制数据
     * @return BASE64字符串
 */
    public static String encode(byte[] bytes) {
        return Base64.getMimeEncoder().encodeToString(bytes);
    }
}