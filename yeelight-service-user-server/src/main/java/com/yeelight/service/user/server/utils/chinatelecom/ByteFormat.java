package com.yeelight.service.user.server.utils.chinatelecom;

/**
* @author: <PERSON><PERSON><PERSON><PERSON>
* @description: 格式化操作类
* @date: Created in 12:08 2018/7/24
*/
public class ByteFormat {
    private static final char[] HEX = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    public ByteFormat() {
    }

    /**
     * 将字节数组转换为十六进制字符串。
     * @param bArray 输入的字节数组。
     * @return 转换后的十六进制字符串。
     */
    public static String bytesToHexString(byte[] bArray) {
        // 创建StringBuilder以构建十六进制字符串
        StringBuilder sb = new StringBuilder(bArray.length);

        for (byte b : bArray) {
            // 将字节转换为十六进制字符串
            String sTemp = Integer.toHexString(255 & b);

            // 如果生成的十六进制字符串长度小于2，在其前面补0
            if (sTemp.length() < 2) {
                sb.append(0);
            }

            // 将转换后的十六进制字符添加到StringBuilder中
            sb.append(sTemp.toUpperCase());
        }

        // 返回构建完成的十六进制字符串
        return sb.toString();
    }

    /**
     * 将十六进制字符串转换为字节数组。
     *
     * @param str 表示要转换的十六进制字符串。如果为null，则返回null。
     * @return 与给定的十六进制字符串对应的字节数组。如果输入为null，则返回null。
     */
    public static byte[] hexToBytes(String str) {
        // 如果输入字符串为null，直接返回null
        if (str == null) {
            return null;
        } else {
            // 将字符串转换为字符数组
            char[] hex = str.toCharArray();
            // 计算生成的字节数组长度
            int length = hex.length / 2;
            byte[] raw = new byte[length];

            // 遍历字符数组，每两个字符组成一个十六进制数，转换为一个字节
            for (int i = 0; i < length; ++i) {
                // 获取当前字节的高位和低位数字
                int high = Character.digit(hex[i * 2], 16);
                int low = Character.digit(hex[i * 2 + 1], 16);
                // 将高位和低位数字合并为一个整数
                int value = high << 4 | low;
                // 对于大于127的值，进行补码运算，确保结果为byte类型范围内的负数
                if (value > 127) {
                    value -= 256;
                }

                // 将整数值转换为字节并存储到结果数组中
                raw[i] = (byte) value;
            }

            return raw;
        }
    }

    public static void main(String[] args) {
    }
}
