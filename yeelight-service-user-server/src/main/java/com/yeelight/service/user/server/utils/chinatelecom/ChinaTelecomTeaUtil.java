package com.yeelight.service.user.server.utils.chinatelecom;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
* @author: <PERSON><PERSON>yong
* @description: xxtea加解密工具类
* @date: Created in 12:07 2018/7/24
*/
public class ChinaTelecomTeaUtil {
    private final static Logger logger = LoggerFactory.getLogger(ChinaTelecomTeaUtil.class);

    public ChinaTelecomTeaUtil() {
    }

    /**
     * 使用指定的应用密钥对文本进行加密。
     *
     * @param plainText 需要加密的原始文本。
     * @param appSecret 应用密钥，用于加密过程。
     * @return 加密后的文本的十六进制字符串表示。
     */
    public static String encrypt(String plainText, String appSecret) {
        // 将应用密钥转换为字节数组作为加密密钥
        byte[] key = appSecret.getBytes(StandardCharsets.UTF_8);
        // 加密原始文本字节数组，并返回加密后的字节数组
        byte[] encBytes = encrypt(plainText.getBytes(StandardCharsets.UTF_8), key);
        // 将加密后的字节数组转换为十六进制字符串并返回
        return ByteFormat.bytesToHexString(encBytes);
    }

    /**
     * 对给定的明文数据进行加密。
     *
     * @param plainData 明文数据，以字节数组的形式提供。
     * @param key 加密使用的密钥，以字节数组的形式提供。
     * @return 如果输入数据有效，则返回加密后的数据字节数组；如果输入数据无效（如null或空），则返回null。
     */
    public static byte[] encrypt(byte[] plainData, byte[] key) {
        // 将明文数据和密钥转换为整型数组后进行加密，再将加密结果转换回字节数组
        return plainData != null && plainData.length != 0 && key != null ? toByteArray(encrypt(toIntArray(plainData, true), toIntArray(key, false)), false) : null;
    }

    /**
     * 解密函数，用于解密给定的密文数据。
     *
     * @param cipherData 待解密的数据，以字节数组形式提供。
     * @param key 解密使用的密钥，以字节数组形式提供。
     * @return 如果输入有效，则返回解密后的数据；如果输入为null或空，则返回null。
     */
    public static byte[] decrypt(byte[] cipherData, byte[] key) {
        // 检查密文数据和密钥是否非空非零长度，若是，则进行解密操作，否则返回null
        return cipherData != null && cipherData.length != 0 && key != null ? toByteArray(decrypt(toIntArray(cipherData, false), toIntArray(key, false)), true) : null;
    }

    /**
     * 对给定的整型数组进行加密操作。
     *
     * @param v 待加密的整型数组。
     * @param k 加密使用的密钥数组。
     * @return 加密后的整型数组。
     */
    private static int[] encrypt(int[] v, int[] k) {
        int kl = 4;
        // 计算数组v的长度
        int n = v.length - 1;
        if (n < 1) {
            // 如果数组长度小于1，则直接返回原数组
            return v;
        } else {
            // 如果密钥数组长度小于4，则将其扩展至长度为4
            if (k.length < kl) {
                int[] key = new int[kl];
                System.arraycopy(k, 0, key, 0, k.length);
                k = key;
            }
            // 初始化变量z为数组v的最后一个元素
            int z = v[n];
            // 初始化变量y为数组v的第一个元素
            int y = v[0];
            // 初始化变量delta，用于加密过程中更新sum
            int delta = -1640531527;
            // 初始化变量sum，用于加密过程中计算
            int sum = 0;

            int e; // 用于加密过程中计算的变量e
            int p; // 用于加密过程中遍历数组的变量p

            // 主加密循环
            for(int var9 = 6 + 52 / (n + 1); var9-- > 0; z = v[n] += (z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z)) {
                // 更新sum的值
                sum += delta;
                // 根据sum的值计算e的值
                e = sum >>> 2 & 3;

                // 内部循环，遍历数组v的元素进行加密操作
                for(p = 0; p < n; ++p) {
                    // 更新y的值为下一个元素
                    y = v[p + 1];
                    // 对当前元素进行加密操作并更新
                    z = v[p] += (z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
                }
                // 重置y为数组v的第一个元素
                y = v[0];
            }
            // 返回加密后的数组
            return v;
        }
    }


    /**
     * 解密给定的数组。该函数使用了一种基于循环和位操作的解密算法。
     *
     * @param v 待解密的整型数组。
     * @param k 用于解密的密钥数组。
     * @return 解密后的整型数组。
     */
    private static int[] decrypt(int[] v, int[] k) {
        // 计算数组长度并检查是否有效
        int kl = 4;
        int n = v.length - 1;
        if (n < 1) {
            return v;
        } else {
            // 确保密钥数组长度至少为4
            if (k.length < kl) {
                int[] key = new int[kl];
                System.arraycopy(k, 0, key, 0, k.length);
                k = key;
            }

            // 初始化变量
            int var10000 = v[n];
            int y = v[0];
            int delta = -1640531527;
            int q = 6 + 52 / (n + 1);

            // 主循环，进行解密操作
            for(int sum = q * delta; sum != 0; sum -= delta) {
                // 根据sum计算迭代次数
                int e = sum >>> 2 & 3;

                // 遍历数组，更新每个元素的值
                int p;
                int z;
                for(p = n; p > 0; --p) {
                    z = v[p - 1];
                    y = v[p] -= (z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
                }

                // 对数组的第一个元素进行额外的操作
                z = v[n];
                y = v[0] -= (z >>> 5 ^ y << 2) + (y >>> 3 ^ z << 4) ^ (sum ^ y) + (k[p & 3 ^ e] ^ z);
            }
            return v;
        }
    }


    /**
     * 将字节数据转换成整型数组。
     * 此方法能够将字节数据以大端字节序的方式转换为整型数组，每个字节占据整型的四个位。
     * 如果includeLength为true，则在转换结果的最后添加一个整型元素，该元素表示原始字节数据的长度。
     *
     * @param data 需要转换的字节数据数组。
     * @param includeLength 转换结果中是否包含原始字节数据的长度。
     * @return 转换后的整型数组。
     */
    private static int[] toIntArray(byte[] data, boolean includeLength) {
        // 根据字节数据长度计算所需整型数组的大小
        int n = (data.length & 3) == 0 ? data.length >>> 2 : (data.length >>> 2) + 1;
        int[] result;

        // 根据是否包含长度字段初始化结果数组
        if (includeLength) {
            result = new int[n + 1];
            // 在数组最后添加字节数据长度
            result[n] = data.length;
        } else {
            result = new int[n];
        }

        // 按大端字节序将字节数据转换为整型
        n = data.length;
        for(int i = 0; i < n; ++i) {
            // 每四个字节构成一个整型，按位左移并合并
            result[i >>> 2] |= (255 & data[i]) << ((i & 3) << 3);
        }
        return result;
    }

    /**
     * 将整型数组转换为字节型数组。
     * @param data 整型数组，待转换的数据。
     * @param includeLength 是否在结果中包含数据长度。如果为true，则最后一个整数视为数据长度；否则，忽略该整数。
     * @return 转换后的字节型数组。如果指定的数据长度不合法（大于实际数据长度或非正数），则返回null。
     */
    private static byte[] toByteArray(int[] data, boolean includeLength) {
        // 计算整型数组data所需的字节长度
        int n = data.length << 2;
        // 如果需要包含长度，则检查最后一个整数是否为合法的数据长度
        if (includeLength) {
            int m = data[data.length - 1];
            // 数据长度不合法，返回null
            if (m > n || m <= 0) {
                return null;
            }

            // 使用data中指定的长度
            n = m;
        }
        // 创建结果字节数组
        byte[] result = new byte[n];
        // 将整型数据转换为字节型数据
        for(int i = 0; i < n; ++i) {
            result[i] = (byte)(data[i >>> 2] >>> ((i & 3) << 3) & 255);
        }
        return result;
    }

    public static void main(String[] args) {
        String plainText = "a=1&b=2&c=3";
        String appSecret = "N4rg8jiKy8618nY9B5uUKenVLyr0cNwp";
        try {
            //xxtea 加密
            byte[] key = appSecret.getBytes(StandardCharsets.UTF_8);
            byte[]  encBytes = encrypt(plainText.getBytes(StandardCharsets.UTF_8), key);
            String params = ByteFormat.bytesToHexString(encBytes);
            System.out.println(params);

            //xxtea解密
            byte[] pText = decrypt(ByteFormat.hexToBytes(params), key);
            System.out.println(new String(pText));
        } catch (Exception exception) {
            logger.error("加解密失败:{}" , exception.getMessage());
        }
    }
}
