/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.utils
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-07-25 14:15:14:15
 */
package com.yeelight.service.user.server.utils.chinatelecom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.util.StringUtils;
import com.yeelight.service.user.server.request.ChinaTelecomVerifyRequest;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;


/**
 * Desc: 电信加解密工具类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-07-25 14:15:14:15
 */
public class EncryptUtil {
    private static final Logger logger = LoggerFactory.getLogger(EncryptUtil.class);


    /**
     * AES加密
     */
    private static final String ALGORITHM = "AES";

    /**
     * AES加密模式
     */
    private static final String CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

    /**
     * ECB加密模式，无需偏移量
     */
    private static final String ECB = "AES/ECB/PKCS5Padding";

    /**
     * CBC加密模式
     */
    private static final String CBC = "AES/CBC/PKCS5Padding";

    /**
     * MD5加密
     *
     * @param msg 要加密的字符串
     * @return 加密后数据, 返回null表示输入数据有误
     */
    public static String md5String(String msg) {
        logger.info("MD5加密前：{}", msg);

        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        try {
            byte[] strTemp = msg.getBytes();
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(strTemp);
            // 16位加密
            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            String dd = new String(str);

            logger.info("中国电信：MD5加密后：{}", dd);
            return dd;
        } catch (Exception e) {
            logger.error("中国电信：MD5加密错误", e);
            return null;
        }
    }

    /**
     * AES+base64加密
     *
     * @param data 待加密数据
     * @param key  密钥
     * @return 加密数据, 返回null表示输入数据有误
     */
    public static String encryptAes(String data, String key) throws Exception {
        logger.info("中国电信：AES+base64加密前数据：data={}; 加密密钥：key={}", data, key);

        if (StringUtils.isAnyBlank(data, key)) {
            return null;
        }
        // 还原密钥
        Key k = toKey(key);

        // 实例化
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        // 初始化,设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, k);
        // 执行操作
        byte[] bytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        String result = Base64.encodeBase64String(bytes);

        logger.info("中国电信：AES+base64加密后数据：result={}", result);
        return result;
    }

    /***
     * AES/ECB/PKCS5Padding+base64加密
     * @param data  待加密数据
     * @param key   密钥
     * @return 加密数据, 返回null表示输入数据有误
     * @throws Exception 异常
     */
    public static String encryptAesEcbPkcs5Padding(String data, String key) throws Exception {
        logger.info("中国电信：AES/ECB/PKCS5Padding+base64加密前数据：data={}; 加密密钥：key={}", data, key);

        if (StringUtils.isAnyBlank(data, key)) {
            return null;
        }
        // 还原密钥
        Key k = toKey(key);

        // 实例化
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        // 初始化,设置为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, k);
        // 执行操作
        byte[] bytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        String result = Base64.encodeBase64String(bytes);

        logger.info("中国电信：AES/ECB/PKCS5Padding+base64加密后数据：result={}", result);
        return result;
    }


    /**
     * AES+base64解密
     *
     * @param base64data 待解密BASE64编码数据
     * @param key        密钥
     * @return 解密数据, 返回null表示输入数据有误
     */
    public static String decryptAes(String base64data, String key) throws Exception {
        logger.info("中国电信：AES+base64解密前数据：data={}; 解密密钥：key={}", base64data, key);

        if (StringUtils.isAnyBlank(base64data, key)) {
            return null;
        }

        // 还原密钥
        Key k = toKey(key);

        // 实例化
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        // 初始化,设置为解密模式
        cipher.init(Cipher.DECRYPT_MODE, k);
        // 执行操作
        byte[] bytes = cipher.doFinal(Base64.decodeBase64(base64data));
        String result = new String(bytes, StandardCharsets.UTF_8);

        logger.info("中国电信：AES+base64解密后数据：result={}", result);
        return result;
    }


    /**
     * AES/ECB/PKCS5Padding+base64解密
     *
     * @param base64Data 待解密BASE64编码数据
     * @param key        密钥
     * @return 解密数据, 返回null表示输入数据有误
     */
    public static String decryptAesEcbPkcs5Padding(String base64Data, String key) throws Exception {
        logger.info("中国电信：AES/ECB/PKCS5Padding+base64解密前数据：data={}; 解密密钥：key={}", base64Data, key);

        if (StringUtils.isAnyBlank(base64Data, key)) {
            return null;
        }

        // 还原密钥
        Key k = toKey(key);

        // 实例化
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        // 初始化,设置为解密模式
        cipher.init(Cipher.DECRYPT_MODE, k);
        // 执行操作
        byte[] bytes = cipher.doFinal(Base64.decodeBase64(base64Data));
        String result = new String(bytes, StandardCharsets.UTF_8);

        logger.info("中国电信：AES/ECB/PKCS5Padding+base64解密后数据：result={}", result);

        return result;
    }

    /**
     * DES+base64 加密
     *
     * @param datasource byte[]
     * @param password   String
     * @return byte[]
     */
    public static String encryptDes(String datasource, String password) {
        try {
            SecureRandom random = new SecureRandom();
            DESKeySpec desKey = new DESKeySpec(password.getBytes());
            //创建一个密匙工厂，然后用它把DESKeySpec转换成
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            SecretKey securekey = keyFactory.generateSecret(desKey);
            //Cipher对象实际完成加密操作
            Cipher cipher = Cipher.getInstance("DES");
            //用密匙初始化Cipher对象
            cipher.init(Cipher.ENCRYPT_MODE, securekey, random);
            //现在，获取数据并加密
            //正式执行加密操作
            byte[] a = cipher.doFinal(datasource.getBytes(StandardCharsets.UTF_8));
            //return cipher.doFinal(datasource);
            return Base64.encodeBase64String(a);
        } catch (Throwable e) {
            logger.error("中国电信：DES加密出现异常: error={}", e.getMessage());
        }
        return null;
    }

    /**
     * DES+base64 解密
     *
     * @param password String
     * @return byte[]
     */
    public static String decryptDes(String content, String password) {
        try {
            // DES算法要求有一个可信任的随机数源
            SecureRandom random = new SecureRandom();
            // 创建一个DESKeySpec对象
            DESKeySpec desKey = new DESKeySpec(password.getBytes());
            // 创建一个密匙工厂
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            // 将DESKeySpec对象转换成SecretKey对象
            SecretKey securekey = keyFactory.generateSecret(desKey);
            // Cipher对象实际完成解密操作
            Cipher cipher = Cipher.getInstance("DES");
            // 用密匙初始化Cipher对象
            cipher.init(Cipher.DECRYPT_MODE, securekey, random);
            byte[] a = Base64.decodeBase64(content.getBytes(StandardCharsets.UTF_8));
            // 真正开始解密操作
            return new String(cipher.doFinal(a), StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("中国电信：DES解密出现异常: error={}", e.getMessage());
        }
        return null;
    }

    /**
     * 转换密钥
     *
     * @param key 二进制密钥
     * @return Key 密钥
     */
    private static Key toKey(String key) {
        //获取密钥
        byte[] k = Base64.decodeBase64(key);
        // 实例化AES密钥材料
        return new SecretKeySpec(k, ALGORITHM);
    }

    /**
     * SHA256加密
     *
     * @param str 待加密的字符串
     * @return 加密后的报文
     */
    public static String getSha256String(String str) {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            logger.error("中国电信：SHA256加密出现异常: error={}", e.getMessage());
        }
        return encodeStr;
    }


    /**
     * 将byte转为16进制
     *
     * @param bytes 字节
     * @return 16进制字符串
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    /**
     * AES/CBC加密，密钥无需再md5加密
     *
     * @param data 加密内容
     * @param key  md5加密后密钥
     * @param iv   偏移量
     * @return 密文
     */
    public static String directEncode(String data, String key, String iv) {
        return encrypt(data, key, iv, CBC);
    }

    /**
     * AES/CBC解密，密钥无需再md5加密
     *
     * @param data 解密内容
     * @param key  md5加密后密钥
     * @return 明文
     */
    public static String directDecode(String data, String key, String iv) {
        return decrypt(data, key, iv, CBC);
    }

    /**
     * 加密
     *
     * @param data            加密内容
     * @param md5Key          16位md5后的密钥
     * @param iv              初始向量
     * @param cipherAlgorithm 加密模式
     * @return 加密后的密文
     */
    public static String encrypt(String data, String md5Key, String iv, String cipherAlgorithm) {
        logger.info("中国电信：【AES加密】 待加密内容: data={};", data);

        String result = null;
        try {
            // 两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
            SecretKeySpec key = new SecretKeySpec(md5Key.getBytes(), ALGORITHM);

            //实例化加密类，参数为加密方式，要写全
            Cipher cipher = Cipher.getInstance(cipherAlgorithm);

            //初始化，此方法可以采用三种方式，按加密算法要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
            if (iv == null) {
                cipher.init(Cipher.ENCRYPT_MODE, key);
            } else {
                IvParameterSpec zeroIv = new IvParameterSpec(iv.getBytes());
                cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
            }

            //加密操作,返回加密后的字节数组，然后需要编码。主要编解码方式有Base64, HEX, UUE,7bit等等。此处看服务器需要什么编码方式
            byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // 用此方法不带换行符的
            result = Base64.encodeBase64String(encryptedData);

        } catch (Exception e) {
            logger.error("中国电信：【AES加密】 加密出现异常: error={}", e.getMessage());
        } finally {
            logger.info("中国电信：【AES加密】 加密后内容: result={}", result);
        }

        return result;
    }

    /**
     * 解密
     *
     * @param data            解密内容
     * @param strKey          16位密钥
     * @param iv              初始向量
     * @param cipherAlgorithm 加密模式
     * @return 解密后的明文
     */
    private static String decrypt(String data, String strKey, String iv, String cipherAlgorithm) {
        logger.info("中国电信：【AES解密】 待解密内容: data={}", data);

        String result = null;
        try {
            byte[] byteMi = Base64.decodeBase64(data);
            SecretKeySpec key = new SecretKeySpec(strKey.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(cipherAlgorithm);

            //与加密时不同MODE:Cipher.DECRYPT_MODE
            if (iv == null) {
                cipher.init(Cipher.DECRYPT_MODE, key);
            } else {
                IvParameterSpec zeroIv = new IvParameterSpec(iv.getBytes());
                cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);
            }

            byte[] decryptedData = cipher.doFinal(byteMi);
            result = new String(decryptedData, StandardCharsets.UTF_8);

        } catch (Exception e) {
            logger.error("中国电信：【AES解密】 解密出现异常: error={}", e.getMessage());
        } finally {
            logger.info("中国电信：【AES解密】 解密后内容: result={}", result);
        }

        return result;
    }

    /**
     * 加密
     *
     * @param data            加密内容
     * @param md5Key          16位md5后的密钥
     * @param iv              初始向量
     * @param cipherAlgorithm 加密模式
     * @return 加密后的密文
     */
    public static String encrypt(String data, String md5Key, String iv, String cipherAlgorithm, String constant) {
        logger.info("中国电信：【AES加密】 待加密内容: data={};", data);

        String result = null;
        try {
            // 两个参数，第一个为私钥字节数组， 第二个为加密方式 AES或者DES
            SecretKeySpec key = new SecretKeySpec(md5Key.getBytes(), ALGORITHM);
            Security.addProvider(new BouncyCastleProvider());
            //实例化加密类，参数为加密方式，要写全
            Cipher cipher = Cipher.getInstance(cipherAlgorithm, constant);

            //初始化，此方法可以采用三种方式，按加密算法要求来添加。（1）无第三个参数（2）第三个参数为SecureRandom random = new SecureRandom();中random对象，随机数。(AES不可采用这种方法)（3）采用此代码中的IVParameterSpec
            if (iv == null) {
                cipher.init(Cipher.ENCRYPT_MODE, key);
            } else {
                IvParameterSpec zeroIv = new IvParameterSpec(iv.getBytes());
                cipher.init(Cipher.ENCRYPT_MODE, key, zeroIv);
            }

            //加密操作,返回加密后的字节数组，然后需要编码。主要编解码方式有Base64, HEX, UUE,7bit等等。此处看服务器需要什么编码方式
            byte[] encryptedData = cipher.doFinal(data.getBytes());

            // 用此方法不带换行符的
            result = ByteFormat.bytesToHexString(encryptedData).toLowerCase();

        } catch (Exception e) {
            logger.error("中国电信：【AES加密】 加密出现异常: error={}", e.getMessage());
        } finally {
            logger.info("中国电信：【AES加密】 加密后内容: result={}", result);
        }

        return result;
    }

    /**
     * @param dataByte 待加密数据
     * @param secretByte 密钥
     * @return 加密后的数据
     */
    public static byte[] hmacSha256(byte[] dataByte, byte[] secretByte) {

        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secretByte, "HmacSHA256");
            hmacSha256.init(secretKey);

            return hmacSha256.doFinal(dataByte);
        } catch (Exception e) {
            System.out.println("Error");
        }
        return null;
    }


    public static boolean initialized = false;
    public static final String ALGORITHM_CBC_PKC_S7_PADDING = "AES/CBC/PKCS7Padding";
    private static final String PROVIDER_BC = "BC";

    public static void initialize() {
        if (initialized) {
            return;
        }
        Security.addProvider(new BouncyCastleProvider());
        initialized = true;
    }


    /**
     * @param dataByte 待加密数据
     * @param keyByte 密钥
     * @param ivByte 向量
     * @return 加密后的数据
     * @throws Exception 异常
     */
    public static byte[] encryptCbcWithIv(byte[] dataByte, byte[] keyByte, byte[] ivByte) throws Exception {
        initialize();

        //指定算法，模式，填充方式，创建一个Cipher
        Cipher cipher = Cipher.getInstance(ALGORITHM_CBC_PKC_S7_PADDING, PROVIDER_BC);
        //生成Key对象
        Key sKeySpec = new SecretKeySpec(keyByte, "AES");

        //把向量初始化到算法参数
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
        params.init(new IvParameterSpec(ivByte));

        //指定用途，密钥，参数 初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, sKeySpec, params);

        //指定加密

        return cipher.doFinal(dataByte);
    }

    /**
     * @param dataByte 待解密数据
     * @param keyByte 密钥
     * @param ivByte 向量
     * @return 解密后的数据
     * @throws Exception 异常
     */
    public static String decryptCbcWithIv(byte[] dataByte, byte[] keyByte, byte[] ivByte) throws Exception {

        initialize();

        String data;

        //指定算法，模式，填充方法 创建一个Cipher实例
        Cipher cipher = Cipher.getInstance(ALGORITHM_CBC_PKC_S7_PADDING, PROVIDER_BC);

        //生成Key对象
        Key sKeySpec = new SecretKeySpec(keyByte, "AES");

        //把向量初始化到算法参数
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");
        params.init(new IvParameterSpec(ivByte));

        //指定用途，密钥，参数 初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, sKeySpec, params);

        //执行解密
        byte[] result = cipher.doFinal(dataByte);

        //解密后转成字符串
        data = new String(result);

        return data;
    }


    public static String decryptCtrModeNoPadding(final String encryptedStr, String key, String ivKey) {
        if (StringUtils.isNotBlank(encryptedStr)) {
            SecretKeySpec keySpec = new
                    SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            try {
                Cipher encipher = Cipher.getInstance("AES/CTR/NoPadding");
                IvParameterSpec iv = new
                        IvParameterSpec(ivKey.getBytes(StandardCharsets.UTF_8));
                encipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
                //先⽤BASE64解密
                byte[] encryptedBytes = Base64.decodeBase64(encryptedStr);
                //然后再AES解密
                byte[] originalBytes = encipher.doFinal(encryptedBytes);
                //返回字符串
                return new String(originalBytes);
            } catch (Exception e) {
                logger.error("基于CTR⼯作模式的AES解密失败, encryptedStr:{},KEY:{}", encryptedStr, key);
            }
        }
        return null;
    }

    public static String encryptCtrModeNoPadding(final String plainStr, String key, String ivKey) {
        if (StringUtils.isNotBlank(plainStr)) {
            SecretKeySpec keySpec = new
                    SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            try {
                Cipher encipher = Cipher.getInstance("AES/CTR/NoPadding");
                IvParameterSpec iv = new
                        IvParameterSpec(ivKey.getBytes(StandardCharsets.UTF_8));
                encipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);
                //然后再AES加密
                byte[] encryptedBytes = encipher.doFinal(plainStr.getBytes());
                //返回base64之后的字符串
                return new String(Base64.encodeBase64(encryptedBytes));
            } catch (Exception e) {
                logger.error("基于CTR⼯作模式的AES加密失败, plainStr:{},KEY:{}", plainStr, key);
            }
        }
        return null;
    }

    public static String calculateHmacSha1(String message, String key) {
        try {
            // 创建一个 HMAC-SHA1 密钥
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA1");

            // 创建一个 HMAC-SHA1 实例
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(secretKeySpec);

            // 计算消息的 HMAC-SHA1 值
            byte[] hmacBytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));

            // 将结果转换为十六进制字符串
            StringBuilder sb = new StringBuilder();
            for (byte b : hmacBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            logger.error("中国电信：HMAC-SHA1加密异常", e);
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
        ChinaTelecomVerifyRequest verifyRequest = new ChinaTelecomVerifyRequest();
        verifyRequest.setTokenCode("26c42057a76b47c19a53c7a91bfbac5f");
        verifyRequest.setState("7iTLgl3kBS7aPL28p4hbrrMyLxpCftuL34EcuYopRxyERHiwCR56J2tQQo7uOw6G");
        JSONObject extraParam = new JSONObject();
        extraParam.put("clientId", "ChinaTelecomYLPTest");
        verifyRequest.setExtraParam(extraParam);
        logger.info("加密前：{}", JSON.toJSONString(verifyRequest));
        String encryptStr = encryptAesEcbPkcs5Padding(JSON.toJSONString(verifyRequest), Constants.SECRET_KEY);
        logger.info("加密后：{}", encryptStr);

        String decryptStr = decryptAesEcbPkcs5Padding(encryptStr, Constants.SECRET_KEY);
        logger.info("解密后：{}", decryptStr);
    }
}

