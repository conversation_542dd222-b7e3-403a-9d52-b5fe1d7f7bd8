package com.yeelight.service.user.server.utils.chinatelecom;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
* @author: <PERSON><PERSON><PERSON><PERSON>
* @description: 简单发送请求工具类
* @date: Created in 12:21 2018/7/24
*/
@Slf4j
public class HttpUtil {

	public static String doHttpPost(String url, Map<String, String> req) {
		return doHttpPost(url, RequestUtil.buildParams(req));
	}
	
	/**
	 * HTTP POST
	 * 
	 * @param url
	 *            请求地址
	 * @param req
	 *            请求参数，格式：name1=value1&name2=value2...
	 * @return 应答参数
	 */
	public static String doHttpPost(String url, String req) {
		PrintWriter outPrintWriter = null;
		BufferedReader inBufferedReader = null;
		try {
			// 打开和URL之间的连接
			URLConnection urlConnection = new URI(url).toURL().openConnection();
			// 设置通用的请求属性
			urlConnection.setRequestProperty("accept", "*/*");
			urlConnection.setRequestProperty("connection", "Keep-Alive");
			urlConnection.setRequestProperty("user-agent",
					"Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");

			urlConnection.setDoOutput(true);
			urlConnection.setDoInput(true);
			urlConnection.setConnectTimeout(30000);
			urlConnection.setReadTimeout(30000);

			urlConnection.connect();

			outPrintWriter = new PrintWriter(urlConnection.getOutputStream());
			outPrintWriter.print(req);
			outPrintWriter.flush();
			inBufferedReader = new BufferedReader(new InputStreamReader(
					urlConnection.getInputStream(), StandardCharsets.UTF_8));
			String line;
			StringBuilder response = new StringBuilder();
			while ((line = inBufferedReader.readLine()) != null) {
				response.append(line);
			}
			return response.toString();
		} catch (Exception e) {
			log.error("中国电信：接口请求异常", e);
			return "";
		} finally {
			try {
				if (outPrintWriter != null) {
					outPrintWriter.close();
				}
				if (inBufferedReader != null) {
					inBufferedReader.close();
				}
			} catch (IOException e) {
				log.error("中国电信：接口请求IO异常", e);
			}
		}
	}
}