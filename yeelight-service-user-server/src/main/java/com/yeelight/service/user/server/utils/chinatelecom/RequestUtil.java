package com.yeelight.service.user.server.utils.chinatelecom;

import java.nio.charset.StandardCharsets;
import java.security.interfaces.RSAPrivateKey;
import java.util.*;
import java.util.Map.Entry;

/**
* @author: lixiaodong
* @description: 请求工具类
* @date: Created in 18:53 2018/5/8
*/
public class RequestUtil {

    /**
     * 生成签名串
     * @param requestData 除sign外的通用参数
     * @param privateKey rsa私钥
     * @return 签名串
     */
    public static String generateSign(Map<String, String> requestData, String privateKey) throws Exception {
        RSAPrivateKey rsaPrivateKey = RsaUtil.loadPrivateKey(privateKey);
        return generateSign(requestData, rsaPrivateKey);
    }

    /**
     * 生成签名串 所有参数按key的升序排序
     * @param requestData 除sign外的通用参数
     * @param rsaPrivateKey RSA私钥
     * @return 签名串
     * @throws Exception 异常
     */
    public static String generateSign(Map<String, String> requestData, RSAPrivateKey rsaPrivateKey) throws Exception {
        return RsaUtil.sign(generatePlainText(requestData), rsaPrivateKey);
    }

    /**
     * 参数排序
     * @param returnData 业务参数
     * @return 排序后的参数
     */
    private static String generatePlainText(Map<String,String> returnData){
        //排序参数
        List<Entry<String,String>> mappingList;
        mappingList = new ArrayList<>(returnData.entrySet());
        mappingList.sort(Entry.comparingByKey());

        StringBuilder plainText= new StringBuilder();
        for(Entry<String,String> mapping:mappingList){
            plainText.append(mapping.getValue());
        }
        return plainText.toString();
    }


    /**
     * 生成params参数
     * @param requestData 业务参数
     * @param appSecret 应用秘钥
     * @return params参数
     */
    public static String generateParams(Map<String, String> requestData, String appSecret) {
        byte[] encValue = ChinaTelecomTeaUtil.encrypt(buildParams(requestData).getBytes(StandardCharsets.UTF_8), appSecret.getBytes(StandardCharsets.UTF_8));
        return ByteFormat.bytesToHexString(encValue);
    }

    public static String buildParams(Map<String, String> requestData)  {
        StringBuilder sb = new StringBuilder();
        for(Entry<String, String> entry : requestData.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        return sb.toString();
    }

    public static void main(String[] args) throws Exception {
        String params =
                "9FBCFC97786BBF8404E12E4BDFAA86A5EA250BF22DACBDC44E385324210249220954E63F8DEB39E561D63826F95CA940EB7DA451";
        byte[] result = ChinaTelecomTeaUtil.decrypt(ByteFormat.hexToBytes(params),
                "C16bkftbCzGuiUwNj10Q0NbZH4NKa7Vz".getBytes(StandardCharsets.UTF_8));
        System.out.println(new String(result));
    }
}
