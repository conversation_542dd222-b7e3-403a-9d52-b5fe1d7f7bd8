package com.yeelight.service.user.server.utils.chinatelecom;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yeelight.service.framework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @description: 返回结果工具类
 * <AUTHOR>
 */
public class ReturnUtil {
    public static final Integer SUCCESS_CODE = 0;
    public static final Integer FAIL_CODE = 99999;

    public static final String FAIL_MSG = "系统异常";

    public static final String SUCCESS_MSG = "系统异常";

    public static JSONObject success() {
        return constructResponseJson(SUCCESS_CODE, SUCCESS_MSG, null);
    }

    public static JSONObject success(Object data) {
        return constructResponseJson(SUCCESS_CODE, SUCCESS_MSG, data);
    }

    public static JSONObject fail() {
        return fail(FAIL_CODE, null, null);
    }

    public static JSONObject fail(String message, Object data) {
        return constructResponseJson(FAIL_CODE, message, data);
    }

    public static JSONObject fail(Integer errorCode, String message, Object data) {
        if(Objects.isNull(errorCode)) {
            errorCode = FAIL_CODE;
        }
        return constructResponseJson(errorCode, message, data);
    }

    public static JSONObject fail(String errorCode, String message, Object data) {
        if(StringUtils.isBlank(errorCode)) {
            errorCode = FAIL_CODE.toString();
        }
        return constructResponseJson(Integer.valueOf(errorCode), message, data);
    }

    public static JSONObject constructResponseJson(Integer code, String msg, Object data) {
        JSONObject jo = new JSONObject();
        // 流水号。建议格式为时间戳+序列号：YYYYMMDDHHMMSSxxxx，其中xxxx为序列号，从0001开始，排满9999后重新循环
        jo.put("sequenceNo", DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4));
        // 错误码，关于错误码的详细信息请参见“附录错误码描述”。error_code等于0代表操作成功，其他值为失败。
        jo.put("error_code", code);
        // 错误描述信息，用来帮助理解和解决发生的错误。
        jo.put("error_msg", msg);
        if(Objects.nonNull(data)) {
            // 密文，返回非通用参数json字符串加密，使用AES对称加密，加密模式：AES/ECB/PKCS5Padding，密钥长度：128。
            String jsonData = JSON.toJSONString(data);
            try {
                // 加密
                jo.put("encryptParam", EncryptUtil.encryptAesEcbPkcs5Padding(jsonData, Constants.SECRET_KEY));
            } catch (Exception e) {
                jo.put("encryptParam", "");
                jo.put("error_code", FAIL_CODE);
                jo.put("error_msg", e.getMessage());
            }
        }
        return jo;
    }
}