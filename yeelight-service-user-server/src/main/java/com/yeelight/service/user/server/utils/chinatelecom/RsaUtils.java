package com.yeelight.service.user.server.utils.chinatelecom;

import org.jetbrains.annotations.NotNull;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: 罗纳德·李维斯特（Ron [R]ivest）、阿迪·萨莫尔（Adi [S]hamir）和伦纳德·阿德曼（Leonard [A]dleman）
 * @description:
 * RSA公钥/私钥/签名工具包
 * 字符串格式的密钥在未在特殊说明情况下都为BASE64编码格式
 * 由于非对称加密速度极其缓慢，一般文件不使用它来加密而是使用对称加密，<br/>
 * 非对称加密算法可以用来对对称加密的密钥加密，这样保证密钥的安全也就保证了数据的安全
 * @date: Created in 11:20 2018/8/20
 */
public class RsaUtils {

    /**
     * 加密算法RSA
     */
    public static final String KEY_ALGORITHM = "RSA";

    /**
     * 签名算法
     */
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";

    /**
     * 获取公钥的key
     */
    private static final String PUBLIC_KEY = "RSAPublicKey";

    /**
     * 获取私钥的key
     */
    private static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * RSA最大加密明文大小
     */
    private static final int MAX_ENCRYPT_BLOCK = 117;

    /**
     * RSA最大解密密文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 128;

    /**
     * 生成密钥对(公钥和私钥)
     *
     * @return Map
     * @throws Exception 异常
     */
    public static Map<String, Object> genKeyPair() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(KEY_ALGORITHM);
        keyPairGen.initialize(1024);
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        Map<String, Object> keyMap = new HashMap<>(2);
        keyMap.put(PUBLIC_KEY, publicKey);
        keyMap.put(PRIVATE_KEY, privateKey);
        return keyMap;
    }

    /**
     * 用私钥对信息生成数字签名
     *
     * @param content       已加密数据
     * @param privateKey 私钥(BASE64编码)
     * @return String
     * @throws Exception 异常
     */
    public static String sign(String content, String privateKey) throws Exception {
        byte[] data = content.getBytes();
        byte[] keyBytes = Base64Utils.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PrivateKey privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateK);
        signature.update(data);
        return ByteFormat.bytesToHexString(signature.sign());
    }

    /**
     * 校验数字签名
     *
     * @param content      源数据
     * @param publicKey 公钥(BASE64编码)
     * @param sign      数字签名
     * @return boolean
     * @throws Exception 异常
     */
    public static boolean verify(String content, String publicKey, String sign)
            throws Exception {
        byte[] data = content.getBytes();
        byte[] keyBytes = Base64Utils.decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        PublicKey publicK = keyFactory.generatePublic(keySpec);
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicK);
        signature.update(data);
        return signature.verify(ByteFormat.hexToBytes(sign));
    }

    /**
     * 私钥解密
     *
     * @param encryptedStr 已加密数据
     * @param privateKey    私钥(BASE64编码)
     * @return String
     * @throws Exception 异常
     */
    public static String decryptByPrivateKeyForLongStr(String encryptedStr, String privateKey)
            throws Exception {
        byte[] encryptedData = ByteFormat.hexToBytes(encryptedStr);
        Cipher cipher = instanceCipherByPrivateKey(privateKey, Cipher.DECRYPT_MODE);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        out.close();
        return out.toString();
    }

    /**
     * 公钥解密
     *
     * @param encryptedData 已加密数据
     * @param publicKey     公钥(BASE64编码)
     * @return byte[]
     * @throws Exception 异常
     */
    public static byte[] decryptByPublicKey(byte[] encryptedData, String publicKey)
            throws Exception {
        Cipher cipher = instanceCopherByPublicKey(publicKey);
        int inputLen = encryptedData.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                cache = cipher.doFinal(encryptedData, offSet, MAX_DECRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_DECRYPT_BLOCK;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return decryptedData;
    }

    @NotNull
    private static Cipher instanceCopherByPublicKey(String publicKey) throws Exception {
        byte[] keyBytes = Base64Utils.decode(publicKey);
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key publicK = keyFactory.generatePublic(x509KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, publicK);
        return cipher;
    }

    /**
     * 公钥加密(数据比较长时，需要使用这个方法)
     *
     * @param content   源数据
     * @param publicKey 公钥(BASE64编码)
     * @return String
     * @throws Exception 异常
     */
    public static String encryptByPublicKeyForLongStr(String content, String publicKey)
            throws Exception {
        byte[] data = content.getBytes();
        Cipher cipher = instanceCopherByPublicKey(publicKey);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return ByteFormat.bytesToHexString(encryptedData);
    }

    /**
     * 私钥加密
     *
     * @param data       源数据
     * @param privateKey 私钥(BASE64编码)
     * @return byte[]
     * @throws Exception 异常
     */
    public static byte[] encryptByPrivateKey(byte[] data, String privateKey)
            throws Exception {
        Cipher cipher = instanceCipherByPrivateKey(privateKey, Cipher.ENCRYPT_MODE);
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段加密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
            } else {
                cache = cipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * MAX_ENCRYPT_BLOCK;
        }
        byte[] encryptedData = out.toByteArray();
        out.close();
        return encryptedData;
    }

    @NotNull
    private static Cipher instanceCipherByPrivateKey(String privateKey, int encryptMode) throws Exception {
        byte[] keyBytes = Base64Utils.decode(privateKey);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
        Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(encryptMode, privateK);
        return cipher;
    }

    /**
     * 获取私钥
     *
     * @param keyMap 密钥对
     * @return String
     */
    public static String getPrivateKey(Map<String, Object> keyMap) {
        Key key = (Key) keyMap.get(PRIVATE_KEY);
        return Base64Utils.encode(key.getEncoded());
    }

    /**
     * 获取公钥
     *
     * @param keyMap 密钥对
     * @return String
     */
    public static String getPublicKey(Map<String, Object> keyMap) {
        Key key = (Key) keyMap.get(PUBLIC_KEY);
        return Base64Utils.encode(key.getEncoded());
    }

    public static void main (String[] args) throws Exception {
        String publicKey = "";
        String privateKey = "";
        String msg = "{\"result\":0,\"msg\":\"操作成功\",\"email\":\"\",\"operator\":\"中国移动\"}";
        String encStr = encryptByPublicKeyForLongStr(msg, publicKey);
        System.out.println(encStr);

        encStr =
                "7DFE349C22FAC96619C7A80D9F07CC35D968D28F546FB39CAC71DAA71ABBF303C6AFDB5C56BB2F48F3D522EB76CADBAC3939FEAAA298C792510245EBF9137898605765C532AF7B4298A4A21EC96E9D292ED7662A26BFF9723C5BDD2F5976D20B0A09F5AA77DE1EE383E0D377F86A888463AB0A7CCB4A2C8599E83F62495EA94B13813D15FAA8DCCD0EFEC91076AEDCA8A05613D8639F11E9DAC0C85ABB473AB1FD27F3D70921ADD479B50537F8BC17048D497E77B775711DA60B2DC08CBE8ED3279429EAFFAC2107F742BD4D08D4CEB204D4725B7E11107DA177D27CCD6EA7BA817485B678DC786A00758828BF72230B0801DEE3A99E696B6A2E507FDC708AA44C58EDB909D6AA7FF2B1218C51C24047D96F25E4C03724C90F6EEEE8E49B14B25DCE431D0ECA7D115F65B2A135972D9AEB6BF0DF7EBEDF391B82F0C8F0355EC16090AF88333AA31B0210E3652015DF839A187AC0CFDA709119FD5C80D2710FC38BE265974F22E0E35AE375B32BA05029F4196C9BEF845312C04AD04359132BC8";
        String decStr = decryptByPrivateKeyForLongStr(encStr, privateKey);
        System.out.println(decStr);

    }

}

