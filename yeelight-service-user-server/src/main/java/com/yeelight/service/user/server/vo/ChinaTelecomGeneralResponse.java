package com.yeelight.service.user.server.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 中国电信用户信息实体类
 * <AUTHOR>
 */
@Data
public class ChinaTelecomGeneralResponse implements Serializable {
    /**
     * 流水号。建议格式为时间戳+序列号：YYYYMMDDHHMMSSxxxx，其中xxxx为序列号，从0001开始，排满9999后重新循环。
     * 非空
     */
    private String sequenceNo;
    /**
     * 密文，返回非通用参数json字符串加密，使用AES对称加密，加密模式：AES/ECB/PKCS5Padding，密钥长度：128。允许为空
     */
    private String encryptParam;
    /**
     * 错误码，关于错误码的详细信息请参见“附录错误码描述”。error_code等于0代表操作成功，其他值为失败。
     */
    @JSONField(name = "error_code")
    private Integer errorCode = 0;
    /**
     * 错误描述信息，用来帮助理解和解决发生的错误。
     */
    @JSONField(name = "error_msg")
    private String errorMsg = "";
}
