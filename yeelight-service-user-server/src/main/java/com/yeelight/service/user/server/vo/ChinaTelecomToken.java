/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.vo
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-07-25 17:20:17:20
 */
package com.yeelight.service.user.server.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Desc: 中国电信Token
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-07-25 17:20:17:20
 */
@Data
public class ChinaTelecomToken implements Serializable {
    /**
     * 操作结果返回码
     * 0 表示成功,其他返回表示出错
     */
    private Integer result;

    /**
     * 应答信息, 查询失败时返回失败原因
     */
    private String msg;

    /**
     * Token(仅合作方B能使用)
     */
    private String accessToken;

    /**
     * refreshToken
     */
    private String refreshToken;

    /**
     * refreshToken的有效期, 以秒为单位
     */
    private String expiresReTime;

    /**
     * accessToken的有效期, 以秒为单位
     */
    private String expiresTime;
}
