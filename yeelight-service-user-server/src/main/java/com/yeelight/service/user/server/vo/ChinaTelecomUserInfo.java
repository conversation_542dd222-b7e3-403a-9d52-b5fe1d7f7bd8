/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.vo
 * Description:
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2023-07-25 17:20:17:20
 */
package com.yeelight.service.user.server.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * Desc: 中国电信用户信息实体类
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2023-07-25 17:20:17:20
 */
@Data
public class ChinaTelecomUserInfo implements Serializable {
    /**
     * 手机号（如：189123456）,非空
     */
    private String mobile;

    /**
     * 用户昵称, 可为空
     */
    private String nickName;

    /**
     * 用户在该应用下统一标识，可以唯一标识一个用户,非空
     */
    private String openId;

    /**
     * 用户头像链接尺寸（大）160X160, 可为空
     */
    private String userIconUrl;

    /**
     * 用户头像链接尺寸（中）100*100, 可为空
     */
    private String userIconUrl2;

    /**
     * 用户头像链接尺寸（小）50X50, 可为空
     */
    private String userIconUrl3;

    /**
     * 用户绑定天翼账号的电子邮箱, 可为空
     */
    private String email;

    /**
     * 手机号码所属运营商:“中国电信”、“中国移动”、“中国联通”，可为空
     */
    private String operator;
}
