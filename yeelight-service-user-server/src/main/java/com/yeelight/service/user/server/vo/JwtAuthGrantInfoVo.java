/*
 * ProjectName: yeelight-service-user
 * PackageName: com.yeelight.service.user.server.vo
 * Description: 用户授权信息
 * CreateBy: lixiaodong
 * Email: <EMAIL>
 * Copyright: Yeelight
 * CreatedTime: 2024-04-25 17:28:17:28
 */
package com.yeelight.service.user.server.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * Desc: 用户授权信息
 *
 * <AUTHOR> [<EMAIL>]
 * @since 2024-04-25 17:28:17:28
 */
@Data
public class JwtAuthGrantInfoVo implements Serializable {
    /**
     * 客户端id
     */
    private String clientId;
    /**
     * 客户端名称
     */
    private String clientName;
    /**
     * 客户端警告内容
     */
    private String clientWarning;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userNickName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * state
     */
    private String state;
    /**
     * 授权范围
     */
    private Set<String> scopes;

    /**
     * 授权类型
     */
    private String responseType;

    /**
     * 挑战码
     */
    private String codeChallenge;

    /**
     * 挑战码方法
     */
    private String codeChallengeMethod;

    /**
     * 跳过授权确认
     */
    private String skipConfirm;

    /**
     * 强制授权确认
     */
    private String forceConfirm;

    /**
     * 重定向地址
     */
    private String redirectUri;
}
