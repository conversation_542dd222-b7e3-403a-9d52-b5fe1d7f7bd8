package com.yeelight.service.user.server.vo;

import com.yeelight.service.user.client.dto.OAuth2Token;
import com.yeelight.service.user.server.enums.ScanLoginStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * @program: yeelight-oauth-api
 * @description: 扫码登录状态
 * @author: lixiaodong
 * @create: 2022-02-11 09:40
 **/
@Data
@NoArgsConstructor
public class ScanLoginQrCodeStatusVo implements Serializable {
    private String qrCodeId;

    private String device;

    /**
     * 生成时间 ms
     */
    private Long createAt;

    /**
     * 有效期 ms
     */
    private Long expireIn;

    /**
     * 过期时间 ms
     */
    private Long expireAt;

    /**
     * 状态
     */
    private ScanLoginStatusEnum status;

    private Token token;

    /**
     * 来源，saas/pro 由调用方自定义，服务端透明存储
     */
    private String source;

    public ScanLoginQrCodeStatusVo(String device, String qrCodeId) {
        this.device = device;
        this.qrCodeId = qrCodeId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Token implements  Serializable {
        public Token(OAuth2Token auth2Token) {
            if (Objects.nonNull(auth2Token)) {
                this.accessToken = auth2Token.getAccessToken();
                this.tokenType = auth2Token.getTokenType();
                this.refreshToken = auth2Token.getRefreshToken();
                this.expiresIn = auth2Token.getExpiresIn();
                this.id = auth2Token.getId();
                this.region = auth2Token.getRegion();
                this.device = auth2Token.getDevice();
                this.clientId = auth2Token.getClientId();
                this.username = auth2Token.getUsername();
                this.scope = auth2Token.getScope();
            }
        }

        private String accessToken;
        private String tokenType;
        private String refreshToken;
        private Integer expiresIn;
        private Long id;
        private String region;
        private String device;
        private String clientId;
        private String username;

        private String scope;
    }
}
