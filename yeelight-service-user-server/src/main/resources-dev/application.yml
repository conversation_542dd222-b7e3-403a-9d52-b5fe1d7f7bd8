server:
  port: 18001
  servlet:
    session:
      timeout: 30d
      cookie:
        name: OAUTH_SESSION_ID
        max-age: 30d
        path: /
  env:
    suffix: -dev
    region: cn
logging:
  config: classpath:logback.xml
gateway:
  oauth:
    prefix-url: http://api-dev.yeedev.com/apis/account
    prefix-protocol: http
    prefix-host: api-dev.yeedev.com
    prefix-port: 80
spring:
  application:
    name: yeelight-service-user${server.env.suffix}
  mvc:
    view: # 视图相关
      suffix: .html
      prefix: /templates/
  messages:
    baseFolder: i18n/
    basename: yeelight-service-user-message
    encoding: UTF-8
    cacheMillis: 10000
  session:
    store-type: redis
  redis:
    host: ************
    port: 6379
    password: yeelink2019
    database: 1
    timeout: 5000
  cloud:
    sentinel:
      transport:
        port: 8719
        dashboard: ************:8888
    nacos:
      discovery:
        server-addr: ************:8848,************:8848,************:8848
        service: yeelight-service-user${server.env.suffix}
      config:
        server-addr: ************:8848,************:8848,************:8848
        group: DEV
datasource:
  user:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-config.yml
  oauth:
    driver-class-name: com.mysql.cj.jdbc.Driver
    jdbc-url: *********************************************************************************************************************************************************************
    username: root
    password: root123
    maxLifetime: 1000000
    maximumPoolSize: 20
    minIdle: 10
    poolName: oauth

security:
  oauth2:
    authorization:
      jwt:
        key-alias: yeelight-oauth
        key-password: Nupg6hgyiFNpuAZt7hZCs3AJl8D
        #keytool -genkeypair -alias yeelight-oauth -keyalg RSA -keypass Nupg6hgyiFNpuAZt7hZCs3AJl8D -keystore yeelight-oauth.jks -storepass Nupg6hgyiFNpuAZt7hZCs3AJl8D
        #keytool -list -rfc --keystore yeelight-oauth.jks | openssl x509 -inform pem -pubkey
        key-store: yeelight-oauth.jks
        key-store-password: Nupg6hgyiFNpuAZt7hZCs3AJl8D

mybatis: # mybatis 配置
  mapper-locations: classpath:mybatis/mapper/*.xml
  type-aliases-package: com.yeelight.service.user.server.domain
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mapper: # 通用 Mapper 配置
  identity: MYSQL
  mappers: com.yeelight.service.framework.util.MyMapper
  mapper-locations: "classpath*:mybatis/mapper/*.xml"
  not-empty: false

pagehelper: # 分页插件配置
  helperDialect: mysql
  params: count=countSql
  reasonable: true
  supportMethodsArguments: true

management:
  endpoints:
    web:
      exposure:
        include: "*"
  health:
    mail:
      enabled: false
    redis:
      enabled: false
    db:
      enabled: false
    sentinel:
      enabled: false
  metrics:
    tags:
      # 实际的spring.application.name，用于指标增加指标维度tag
      application: yeelight-service-user${server.env.suffix}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        dubbo.server.requests: true
      maximum-expected-value:
        # http接口正常情况最大响应时间，过大或过小会影响最终指标精确度
        http.server.requests: 5s
        # dubbo接口正常情况最大响应时间，过大或过小会影响最终指标精确度
        dubbo.server.requests: 5s

kafka:
  enable: true
  bootstrap.servers: ************:9092,************:9092
  topic:
    suffix:

dubbo:
  application:
    name: ${spring.application.name}
    environment: develop
  registry:
    address: ************:8848,************:8848,************:8848
    group: develop
    protocol: nacos
    check: false
  protocol:
    name: dubbo
    port: 28091
  provider:
    timeout: 5000
    retries: 0
    delay: -1
    group: develop
    version: 0.0.1
  consumer:
    group: develop
    check: false
    timeout: 2000
    retries: 0
    version: 0.0.1

# 前后端分离配置
frontend-backend-separated:
  switch: true
  frontend:
    domain: http://user-dev.yeedev.com
    # 前端页面路径
    path: /
    # 前端页面路径
    uris:
      # 登录页面路径
      index-uri: index
      # 登录页面路径
      login-uri: login
      # 注册页面路径
      register-uri: register
      # 授权确认页面路径
      grant-page-uri: consent
      # 找回密码页面路径
      reset-password-uri: findbackPWD
      # 异常页面
      error-uri: error
jwt:
  secret: dN+QbZlMJQA5IrqmwNsaEnDJTTefJAMMTF6mckYhw2ODBtr9BYmyBcInvjxRSrx5XJuUeWpDt/ypsGKfgcEtkQ==
  type: forever
  max-tokens: 20
  claims:
    issuer: Certification Center
    audience: JwtAuth
    subject: AuthUser
    expiration-time-days: 30

justauth:
  enabled: true
  cache:
    type: REDIS
    # 缓存前缀，目前只对redis缓存生效，默认 JUSTAUTH::STATE::
    prefix: 'JUSTAUTH::STATE::'
    # 超时时长，目前只对redis缓存生效，默认3分钟
    timeout: 1h
  http-config:
    timeout: 30000
    proxy:
      # 只配置默认三方类型代理即可，不需要配置带服务商前缀的三方类型代理，会自动匹配
      FACEBOOK:
        type: SOCKS
        hostname: 127.0.0.1
        port: 7891
  type:
    # 除了默认易来三方类型外，其他服务商的type名称需要将其在UserVendorEnum中对应的code加到前面，如SMARTLICHT_GOOGLE
    GOOGLE:
      client-id: ************-iliuvs8anshlaqgskprvloqulu586dp1.apps.googleusercontent.com
      client-secret: n6cNEWbyE9B5z80OwNWf7jdA
      redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/google
    SMARTLICHT_GOOGLE:
      client-id: ************-t76nqtcais188fl42k1kcskuhmjai52j.apps.googleusercontent.com
      client-secret: 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
      redirect-uri: https://api-dev.smartlicht-sys.com:18443/apis/account/third-party-auth/callback/google
    FACEBOOK:
      client-id: ****************
      client-secret: ********************************
      redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/facebook
    SMARTLICHT_FACEBOOK:
      client-id: ****************
      client-secret: ********************************
      redirect-uri: https://api-dev.smartlicht-sys.com:18443/apis/account/third-party-auth/callback/facebook
      scopes: email
    AMAZON:
      client-id: amzn1.application-oa2-client.3b42bfbc88f94869a72ff44a5492540d
      client-secret: 177bf4134c4587ca86c312f0f46aa930190ec3858031668b7785d9b5b178d2ea
      redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/amazon
    SMARTLICHT_AMAZON:
      client-id: amzn1.application-oa2-client.d3022e6ef53d43fda8672e9c7ee9d4d9
      client-secret: amzn1.oa2-cs.v1.f55766bf44924304d6a53bd1f9878eb08dd7f3818ebed76465b0c40e7606dc6f
      redirect-uri: https://api-dev.smartlicht-sys.com:18443/apis/account/third-party-auth/callback/amazon
    WECHAT_MP:
      client-id: wx503a9adadaa27925
      client-secret: ********************************
      redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/wechat_mp
    WECHAT_OPEN:
      client-id: wx237245ab867e75ba
      client-secret: ********************************
      redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/wechat_open
  extend:
    enum-class: com.yeelight.service.user.server.enums.AuthCustomSource
    config:
      APPLE:
        request-class: com.yeelight.service.user.server.request.auth.AuthAppleRequest
        client-id: com.yeelight.oauth
        client-secret: eyJraWQiOiJYOTdCV01XNENDIiwiYWxnIjoiRVMyNTYifQ.**********************************************************************************************************************************************************.kRBUaDCU0XMbO595hrTySFMC0bZ9hCrHhKe1GneBmiAX5CCM-8DBvMNRKmDKreMRdMlfyqJGR89FDuAvSry-gw
        redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/apple
        kid: 924D3VJXLL
        iss: S4JD8QVPVY
        auth-key: "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgrMsWgh7ZXo7OoUzx\n82SIB8snKlALOpLmI30AUUJAeXWgCgYIKoZIzj0DAQehRANCAAQK6Cx7/3Kku5pY\nJY5FeckS+heRApzsqYo/DXYOM5H2vlhZlSREZrIMsY/DYwTJ6MFN5AsIjTBHuje0\n1vN8iamT\n"
      SMARTLICHT_APPLE:
        request-class: com.yeelight.service.user.server.request.auth.AuthAppleRequest
        client-id: com.smartlicht-sys.applesignservices
        client-secret: eyJraWQiOiJYOTdCV01XNENDIiwiYWxnIjoiRVMyNTYifQ.**********************************************************************************************************************************************************.kRBUaDCU0XMbO595hrTySFMC0bZ9hCrHhKe1GneBmiAX5CCM-8DBvMNRKmDKreMRdMlfyqJGR89FDuAvSry-gw
        redirect-uri: https://api-dev.smartlicht-sys.com:18443/apis/account/third-party-auth/callback/apple
        kid: V42SDA6J56
        iss: CA3FDV4SKZ
        auth-key: "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgm86W5oeIDZLAaP0D\ncfTktmepSxQNgVWu19K4TBKrUyOgCgYIKoZIzj0DAQehRANCAATdCazvry3kbU86\nMlX2beUB7eL/7oHKiQjP90iAY2htoWT2w6hjSQOf9mWCjyBtugH2bGpkxTsQlkYA\nXdjqRYda\n"
      WECHAT_OPEN_APP:
        request-class: com.yeelight.service.user.server.request.auth.AuthWeChatOpenAppRequest
        client-id: wx55681e41f03ef2be
        client-secret: ********************************
        redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/wechat_open_app
      WECHAT_YEELIGHT_APP:
        request-class: com.yeelight.service.user.server.request.auth.AuthWeChatYeelightAppRequest
        client-id: wx8b5898ac7efeb161
        client-secret: ********************************
        redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/wechat_yeelight_app
      WECHAT_MINI_SHOP:
        request-class: com.yeelight.service.user.server.request.auth.AuthWechatMiniProgramRequest
        client-id: wxa2d2927a2db54258
        client-secret: ********************************
        ignore-check-redirect-uri: true
        ignore-check-state: true
      XIAOMI:
        request-class: com.yeelight.service.user.server.request.auth.AuthXiaoMiRequest
        client-id: 2882303761517308695
        client-secret: OrwZHJ/drEXakH1LsfwwqQ==
        redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/xiaomi
        ignore-check-state: true
      FEISHU_APP:
        request-class: com.yeelight.service.user.server.request.auth.AuthFeishuAppRequest
        client-id: cli_a164a54877fa500e
        client-secret: ********************************
        redirect-uri: https://test-frp.yeelight.com/third-party-auth/callback/feishu_app
      AMAZON_ALEXA:
        request-class: com.yeelight.service.user.server.request.auth.AuthAmazonAlexaRequest
        client-id: amzn1.application-oa2-client.45cdc8efd2074259a61a88fb94c4cfc6
        client-secret: 66b0ccd33e13517486ed2f9d350e7ba3fa138152d05fa97950dc68fce886f3ad
        redirect-uri: https://page.yeelight.com/alexa.html?redirect_url=yeelightpro://www.yeelight.com/zh_CN/pro?source=alexa
        yeelight-client-id: dev
        yeelight-redirect-uri: https://alexa.amazon.co.jp/api/skill/link/M2O27BYOX64OLH
        alexa-enablement-uri: /v1/users/~current/skills/amzn1.ask.skill.964892e7-44f2-4b91-b7cd-2f400499ac36/enablement
        alexa-lwa-redirect-uri: https://page.yeelight.com/alexa.html?redirect_url=yeelightpro://www.yeelight.com/zh_CN/pro?source=alexa
        alexa-redirect-uri: https://www.yeelight.com/zh_CN/pro?source=alexa
      SONOS:
        request-class: com.yeelight.service.user.server.request.auth.AuthSonosRequest
        client-id: 692c9dfc-2198-4c01-8437-60aee54aca37
        client-secret: 2f628cdc-7c22-46b0-abbc-1d6c304a30b6
        redirect-uri: https://fe-resource.yeelight.com/load-page/app-third-auth.html