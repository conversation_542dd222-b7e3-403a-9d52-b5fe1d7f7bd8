# https://shardingsphere.apache.org/document/current/cn/user-manual/shardingsphere-jdbc/yaml-config/rules/sharding/
mode:
  type: Standalone
  repository:
    type: JDBC
dataSources: # 数据源配置，可配置多个 <data-source-name>
  user: # 数据源名称
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource # 数据源完整类名
    driverClassName: com.mysql.cj.jdbc.Driver # 数据库驱动类名，以数据库连接池自身配置为准
    jdbcUrl: ********************************************************************************************************************************************************************
    username: root
    password: root123
    maxLifetime: 1000000
    maximumPoolSize: 20
    minIdle: 10
    poolName: user
rules:
  - !SHARDING
    tables: # 数据分片规则配置
      yeelight_users: # 逻辑表名称
        actualDataNodes: user.yeelight_users,user.smartlicht_users # 由数据源名 + 表名组成（参考 Inline 语法规则）
        tableStrategy: # 分表策略
          hint: # Hint 分片策略
            shardingAlgorithmName: userHintShardingAlgorithm # 分片算法名称
    shardingAlgorithms:
      userHintShardingAlgorithm:
        type: CLASS_BASED
        props:
          strategy: hint # 分片策略类型，支持 STANDARD、COMPLEX 或 HINT（不区分大小写）
          algorithmClassName: com.yeelight.service.user.server.config.UserHintShardingAlgorithm
    bindingTables:
      - yeelight_users # 绑定表规则，即逻辑表名
    defaultDatabaseStrategy: # 默认分库策略
      none: # 不分片
    defaultTableStrategy: # 默认表分片策略
      none: # 不分片
  - !SINGLE
    tables:
      - "*.*" # 加载全部单表
    defaultDataSource: user # 默认数据源，仅在执行 CREATE TABLE 创建单表时有效。缺失值为空，表示随机单播路由。
props:
  sql-show: true # 是否开启SQL显示