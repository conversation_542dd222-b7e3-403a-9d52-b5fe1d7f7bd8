<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level %logger{50}:%L -[%thread] - %msg%n</pattern>
        </encoder>
    </appender>
    <!--输出到文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logDir}/${app.key}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logDir}/${app.key}.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level %logger{50}:%L -[%thread] - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="error_log_file" class="ch.qos.logback.core.FileAppender">
        <file>${logDir}/${app.key}_error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印ERROR日志 -->
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level %logger{50}:%L -[%thread] - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="user_operation_log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logDir}/${app.key}.user-operation.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logDir}/${app.key}.user-operation.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>365</MaxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level %logger{50}:%L -[%thread] - %msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.yeelight.service.user.server.service.dubbo.impl.YeelightUserServiceImpl" level="info" additivity="false">
        <appender-ref ref="user_operation_log"/>
    </logger>
    <appender name="biz_log_file" class="ch.qos.logback.core.FileAppender">
        <file>${logDir}/platform_bizlog/${app.key}_biz.log</file>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>
    <logger name="com.yeelight.service.framework.log.PlatformLogger" level="INFO" additivity="false">
        <appender-ref ref="biz_log_file"/>
        <appender-ref ref="console"/>
    </logger>
    <logger name="com.alibaba.nacos.client.naming" level="OFF"/>
    <root level="info">
        <appender-ref ref="error_log_file"/>
        <appender-ref ref="file"/>
    </root>
</configuration>
