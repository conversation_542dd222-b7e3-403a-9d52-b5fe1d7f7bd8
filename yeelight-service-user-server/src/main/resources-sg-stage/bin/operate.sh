#!/bin/bash

APP_NAME="yeelight-service-user"
APP_PORT="18001"
ENVIRONMENT="test"
JARNAME=/lib/$APP_NAME.jar
logDir=/var/logs/$APP_NAME


if [ -z "$JAVA_HOME" ]; then
  export JAVA=`which java`
else
  export JAVA="$JAVA_HOME/bin/java"
fi

#取当前目录
BASE_PATH=`cd "$(dirname "$0")"; pwd`
echo "BASE_PATH is "$BASE_PATH
WORKDIR=`cd ..;pwd`


JAVA_OPTS='-server -Xmx1g -Xms1g -XX:SurvivorRatio=8 -XX:NewRatio=2 -XX:+DisableExplicitGC -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintCommandLineFlags -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:ParallelCMSThreads=4 -XX:+CMSClassUnloadingEnabled -XX:+UseCMSCompactAtFullCollection -XX:CMSFullGCsBeforeCompaction=1 -XX:CMSInitiatingOccupancyFraction=50  '


JAVA_CMD=""
JAVA_CMD=$JAVA_CMD" java  $JAVA_OPTS"
JAVA_CMD=$JAVA_CMD" -DlogDir=$logDir -Denvironment=$ENVIRONMENT -Dfile.encoding=utf8 -Dapp.key=$APP_NAME"
JAVA_CMD=$JAVA_CMD" -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$logDir/$APP_NAME.heaperr.log.`date "+%Y%m%d%H%M%S"`  -Xloggc:$logDir/$APP_NAME.gc.log.`date "+%Y%m%d%H%M%S"`"
JAVA_CMD=$JAVA_CMD" -jar $WORKDIR/$JARNAME "

exist(){
    if test $( pgrep -f "$JARNAME" | wc -l ) -eq 0
    then
        return 1
    else
        return 0
    fi
}

start(){
		# 注册被Prometheus监控服务
		host=$(hostname)
	  curl -i -X POST -d '{"port": "'$APP_PORT'", "job": "yeelight-service", "host": "'$host'"}' -H "Content-Type: application/json" http://prometheus-exporter-server-sg.yeelight.com:9091/create
	  echo "Service register to prometheus."

	  register

    if exist; then
            echo "Service is already running."
            exit 1
    else
        cd $BASE_PATH
        if [ ! -d "$logDir" ];then
        mkdir -p $logDir
        fi
        echo $JAVA_CMD
        su - yeelight_jenkins -c "$JAVA_CMD"
        sleep 1
        check
    fi
}

stop(){
    # 取消被Prometheus监控服务
    host=$(hostname)
    curl -i -X POST -d '{"port": "'$APP_PORT'", "job": "yeelight-service", "host": "'$host'"}' -H "Content-Type: application/json" http://prometheus-exporter-server-sg.yeelight.com:9091/delete
    echo "Service unregister from prometheus."


    unregister

    runningPID=`pgrep -f "$JARNAME"`
    if [ "$runningPID" ]; then
        echo "Service pid: $runningPID"
        count=0
        kwait=5
        echo "Service is stopping, please wait..."
        kill -15 $runningPID
        until [ `ps --pid $runningPID 2> /dev/null | grep -c $runningPID 2> /dev/null` -eq '0' ] || [ $count -gt $kwait ]
        do
            sleep 1
            let count=$count+1;
        done

        if [ $count -gt $kwait ]; then
            kill -9 $runningPID
        fi
        clear
        echo "Service is stopped."
    else
            echo "Service has not been started."
    fi
}

#获取本地IP
ip=$(ifconfig -a | grep inet | grep -v 127.0.0.1 | grep -v inet6 | awk '{print $2}' | tr -d "addr:")

register() {
  #验证服务节点是否已经在服务治理平台注册，同时更新Host节点信息
  host=$(hostname)
  SERVICE_CHECK_RESULT=$( curl --connect-timeout 10 -m 20  -i -X POST -d '{"host":"'$host'","ip":"'$ip'","port":"'$APP_PORT'","serviceId":"'$APP_NAME'","environment":"'$ENVIRONMENT'"}' -H "Content-Type: application/json" https://api-st.yeelight.com/apis/basic-platform/v1/service/metadata/w/register/1)

  if [ "$SERVICE_CHECK_RESULT" ]; then
    echo "Service register is success "
  else
    echo "Service register is fail "
    #注册失败 不允许服务启动
    exit 1
  fi
}

unregister() {
  #更新Host节点状态为下线
  host=$(hostname)
  SERVICE_CHECK_RESULT=$( curl --connect-timeout 10 -m 20  -i -X POST -d '{"host":"'$host'","ip":"'$ip'","port":"'$APP_PORT'","serviceId":"'$APP_NAME'","environment":"'$ENVIRONMENT'"}' -H "Content-Type: application/json" https://api-st.yeelight.com/apis/basic-platform/v1/service/metadata/w/register/2)
  if [ "$SERVICE_CHECK_RESULT" ]; then
    echo "Service unregister is success "
  else
    echo "Service unregister is fail "
  fi
}

check(){
   if exist; then
     echo "Service is alive."
     exit 0
   else
     echo "Service is dead."
     exit -1
   fi
}

restart(){
        stop
        start
}


case "$1" in

    start)
            start
    ;;
    stop)
            stop
    ;;
    restart)
            restart
    ;;
    check)
            check
    ;;
    *)
            echo "available operations: [start|stop|restart|check]"
            exit 1
    ;;
esac