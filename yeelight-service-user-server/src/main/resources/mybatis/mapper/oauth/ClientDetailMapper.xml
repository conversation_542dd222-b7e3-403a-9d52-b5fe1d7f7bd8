<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yeelight.service.user.server.mapper.oauth.ClientDetailMapper" >
  <resultMap id="BaseResultMap" type="com.yeelight.service.user.client.domain.ClientDetail" >
    <!--
      WARNING - @mbg.generated
    -->
    <result column="client_id" property="clientId" jdbcType="VARCHAR" />
    <result column="resource_ids" property="resourceIds" jdbcType="VARCHAR" />
    <result column="client_secret" property="clientSecret" jdbcType="VARCHAR" />
    <result column="scope" property="scope" jdbcType="VARCHAR" />
    <result column="authorized_grant_types" property="authorizedGrantTypes" jdbcType="VARCHAR" />
    <result column="web_server_redirect_uri" property="webServerRedirectUri" jdbcType="VARCHAR" />
    <result column="authorities" property="authorities" jdbcType="VARCHAR" />
    <result column="access_token_validity" property="accessTokenValidity" jdbcType="INTEGER" />
    <result column="refresh_token_validity" property="refreshTokenValidity" jdbcType="INTEGER" />
    <result column="additional_information" property="additionalInformation" jdbcType="VARCHAR" />
    <result column="autoapprove" property="autoApprove" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="fields">
    client_id,resource_ids,client_secret,scope,authorized_grant_types,web_server_redirect_uri,authorities,access_token_validity,refresh_token_validity,additional_information,autoapprove
  </sql>

  <!--根据clientId查询单个Client-->
  <select id="findOneByClientId" resultMap="BaseResultMap" resultType="com.yeelight.service.user.client.domain.ClientDetail">
    SELECT <include refid="fields"></include> FROM oauth_client_details WHERE client_id=#{clientId}
  </select>

  <insert id="createClient" parameterType="com.yeelight.service.user.client.domain.ClientDetail">
    insert into oauth_client_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientId != null">
        client_id,
      </if>
      <if test="resourceIds != null">
        resource_ids,
      </if>
      <if test="clientSecret != null">
        client_secret,
      </if>
      <if test="scope != null">
        scope,
      </if>
      <if test="authorizedGrantTypes != null">
        authorized_grant_types,
      </if>
      <if test="webServerRedirectUri != null">
        web_server_redirect_uri,
      </if>
      <if test="authorities != null">
        authorities,
      </if>
      <if test="accessTokenValidity != null">
        access_token_validity,
      </if>
      <if test="refreshTokenValidity != null">
        refresh_token_validity,
      </if>
      <if test="additionalInformation != null">
        additional_information,
      </if>
      <if test="autoApprove != null">
        autoapprove,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clientId != null">
        #{clientId,jdbcType=VARCHAR},
      </if>
      <if test="resourceIds != null">
        #{resourceIds,jdbcType=VARCHAR},
      </if>
      <if test="clientSecret != null">
        #{clientSecret,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=VARCHAR},
      </if>
      <if test="authorizedGrantTypes != null">
        #{authorizedGrantTypes,jdbcType=VARCHAR},
      </if>
      <if test="webServerRedirectUri != null">
        #{webServerRedirectUri,jdbcType=VARCHAR},
      </if>
      <if test="authorities != null">
        #{authorities,jdbcType=VARCHAR},
      </if>
      <if test="accessTokenValidity != null">
        #{accessTokenValidity,jdbcType=INTEGER},
      </if>
      <if test="refreshTokenValidity != null">
        #{refreshTokenValidity,jdbcType=INTEGER},
      </if>
      <if test="additionalInformation != null">
        #{additionalInformation,jdbcType=VARCHAR},
      </if>
      <if test="autoApprove != null">
        #{autoApprove,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateClient" parameterType="com.yeelight.service.user.client.domain.ClientDetail" >
    update oauth_client_details
    <set >
      <if test="resourceIds != null">
        resource_ids = #{resourceIds,jdbcType=VARCHAR},
      </if>
      <if test="clientSecret != null">
        client_secret = #{clientSecret,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        scope = #{scope,jdbcType=VARCHAR},
      </if>
      <if test="authorizedGrantTypes != null">
        authorized_grant_types = #{authorizedGrantTypes,jdbcType=VARCHAR},
      </if>
      <if test="webServerRedirectUri != null">
        web_server_redirect_uri = #{webServerRedirectUri,jdbcType=VARCHAR},
      </if>
      <if test="authorities != null">
        authorities = #{authorities,jdbcType=INTEGER},
      </if>
      <if test="accessTokenValidity != null">
        access_token_validity = #{accessTokenValidity,jdbcType=INTEGER},
      </if>
      <if test="refreshTokenValidity != null">
        refresh_token_validity = #{refreshTokenValidity,jdbcType=INTEGER},
      </if>
      <if test="additionalInformation != null">
        additional_information = #{additionalInformation,jdbcType=INTEGER},
      </if>
      <if test="autoApprove != null">
        autoapprove = #{autoApprove,jdbcType=INTEGER},
      </if>
    </set>
    where client_id = #{clientId,jdbcType=BIGINT}
  </update>

  <!--根据clientId删除Client-->
  <delete id="deleteByClientId">
    DELETE  FROM oauth_client_details WHERE client_id=#{clientId}
  </delete>
</mapper>