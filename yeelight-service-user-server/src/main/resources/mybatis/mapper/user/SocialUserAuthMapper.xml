<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeelight.service.user.server.mapper.user.SocialUserAuthMapper">
    <resultMap id="BaseResultMap" type="com.yeelight.service.user.server.domain.SocialUserAuth">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="social_user_id" jdbcType="BIGINT" property="socialUserId"/>
    </resultMap>
    <sql id="Base_Column_List">
      id, user_id, social_user_id
    </sql>
    <delete id="deleteSocialUserAuthOne">
        DELETE FROM social_user_auth WHERE social_user_id = #{socialUserId} AND user_id = #{yeelightId}
    </delete>
    <delete id="deleteSocialUserAuth">
        DELETE FROM social_user_auth WHERE social_user_id = #{socialUserId}
    </delete>
    <delete id="deleteSocialUserAuthByYeelightId">
        DELETE FROM social_user_auth WHERE user_id = #{yeelightId}
    </delete>

    <select id="selectSocialUserAuthOne" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM social_user_auth
        WHERE social_user_id = #{socialUserId}
        LIMIT 1
    </select>
    <select id="selectSocialUserAuthsWithYeelightId" resultType="com.yeelight.service.user.server.domain.SocialUserAuth" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM social_user_auth
        WHERE user_id = #{yeelightId}
    </select>
    <select id="selectSocialUserAuthsWithYeelightIdAndSource" resultType="com.yeelight.service.user.server.domain.SocialUserAuth">
        SELECT social_user_auth.*
        FROM social_user LEFT JOIN social_user_auth ON social_user.id = social_user_auth.social_user_id
        WHERE social_user_auth.user_id = #{yeelightId} AND social_user.source = #{source} LIMIT 1
    </select>
</mapper>
