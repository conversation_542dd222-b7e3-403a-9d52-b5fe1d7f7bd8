<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yeelight.service.user.server.mapper.user.SocialUserMapper">
    <resultMap id="BaseResultMap" type="com.yeelight.service.user.server.domain.SocialUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="access_token" jdbcType="VARCHAR" property="accessToken"/>
        <result column="expire_in" jdbcType="INTEGER" property="expireIn"/>
        <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="uid" jdbcType="VARCHAR" property="uid"/>
        <result column="access_code" jdbcType="VARCHAR" property="accessCode"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="scope" jdbcType="VARCHAR" property="scope"/>
        <result column="token_type" jdbcType="VARCHAR" property="tokenType"/>
        <result column="id_token" jdbcType="VARCHAR" property="idToken"/>
        <result column="mac_algorithm" jdbcType="VARCHAR" property="macAlgorithm"/>
        <result column="mac_key" jdbcType="VARCHAR" property="macKey"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="oauth_token" jdbcType="VARCHAR" property="oauthToken"/>
        <result column="oauth_token_secret" jdbcType="VARCHAR" property="oauthTokenSecret"/>
        <result column="session_key" jdbcType="VARCHAR" property="sessionKey"/>
        <result column="created_time" property="createdTime" jdbcType="INTEGER" />
        <result column="updated_time" property="updatedTime" jdbcType="INTEGER" />
        <result column="last_token_time" property="lastTokenTime" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List">
        id, uuid, `source`, access_token, expire_in, refresh_token, open_id, `uid`, access_code,
        union_id, `scope`, token_type, id_token, mac_algorithm, mac_key, code, oauth_token,
        oauth_token_secret, session_key, created_time, updated_time, last_token_time
      </sql>
    <select id="selectSocialUserOne" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM social_user
        WHERE uuid = #{uuid} AND source = #{source}
        LIMIT 1
    </select>
    <select id="selectSocialUsersByYeelightId" resultType="com.yeelight.service.user.server.domain.SocialUser" resultMap="BaseResultMap">
        SELECT social_user.*
        FROM social_user LEFT JOIN social_user_auth ON social_user.id = social_user_auth.social_user_id
        WHERE social_user_auth.user_id = #{yeelightId}
    </select>
    <select id="selectSocialUserOneByYeelightIdAndSource" resultType="com.yeelight.service.user.server.domain.SocialUser" resultMap="BaseResultMap">
        SELECT social_user.*
        FROM social_user LEFT JOIN social_user_auth ON social_user.id = social_user_auth.social_user_id
        WHERE social_user_auth.user_id = #{yeelightId} AND social_user.source = #{source} LIMIT 1
    </select>
    <select id="selectSocialUserOneByUnionId" resultType="com.yeelight.service.user.server.domain.SocialUser">
        SELECT
        <include refid="Base_Column_List"/>
        FROM social_user
        WHERE union_id = #{unionId}
        LIMIT 1
    </select>
</mapper>
