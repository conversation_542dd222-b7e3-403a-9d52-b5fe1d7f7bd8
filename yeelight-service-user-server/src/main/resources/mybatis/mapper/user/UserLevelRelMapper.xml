<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yeelight.service.user.server.mapper.user.UserLevelRelMapper" >
  <resultMap id="BaseResultMap" type="com.yeelight.service.user.client.domain.UserLevelRel" >
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="level_id" property="levelId" jdbcType="BIGINT" />
    <result column="yeelight_user_id" property="yeelightUserId" jdbcType="BIGINT" />
    <result column="level_type" property="levelType" jdbcType="VARCHAR" />
    <result column="level_name" property="levelName" jdbcType="VARCHAR" />
    <result column="growth" property="growth" jdbcType="INTEGER" />
  </resultMap>

  <sql id="fields">
    id,level_id,yeelight_user_id,level_type,level_name,growth
  </sql>

  <!--根据mid替换-->
  <update id="replaceLevelRel" parameterType="com.yeelight.service.user.client.domain.UserLevelRel" >
    <selectKey keyProperty="count" resultType="int" order="BEFORE">
      select count(*) from yeelight_user_level_rel where yeelight_user_id = #{yeelightUserId,jdbcType=BIGINT} AND level_type = #{levelType,jdbcType=VARCHAR}
    </selectKey>
    <if test="count > 0">
      update yeelight_user_level_rel
      <set >
        <if test="levelId != null">
          level_id = #{levelId,jdbcType=BIGINT},
        </if>
        <if test="levelName != null">
          level_name = #{levelName,jdbcType=VARCHAR},
        </if>
        <if test="growth != null">
          growth = #{growth,jdbcType=INTEGER},
        </if>
      </set>
      where yeelight_user_id = #{yeelightUserId,jdbcType=BIGINT} AND level_type = #{levelType,jdbcType=VARCHAR}
    </if>
    <if test="count==0">
      insert into yeelight_user_level_rel
      <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="levelId != null">
          level_id,
        </if>
        <if test="yeelightUserId != null">
          yeelight_user_id,
        </if>
        <if test="levelType != null">
          level_type,
        </if>
        <if test="levelName != null">
          level_name,
        </if>
        <if test="growth != null">
          growth,
        </if>
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="levelId != null">
          #{levelId,jdbcType=BIGINT},
        </if>
        <if test="yeelightUserId != null">
          #{yeelightUserId,jdbcType=BIGINT},
        </if>
        <if test="levelType != null">
          #{levelType,jdbcType=VARCHAR},
        </if>
        <if test="levelName != null">
          #{levelName,jdbcType=VARCHAR},
        </if>
        <if test="growth != null">
          #{growth,jdbcType=INTEGER},
        </if>
      </trim>
    </if>
  </update>
</mapper>