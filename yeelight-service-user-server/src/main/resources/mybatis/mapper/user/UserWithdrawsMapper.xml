<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yeelight.service.user.server.mapper.user.UserWithdrawsMapper" >
  <resultMap id="BaseResultMap" type="com.yeelight.service.user.client.domain.UserWithdraws" >
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="yeelight_user_id" property="yeelightUserId" jdbcType="BIGINT" />
    <result column="withdraw_type" property="withdrawType" jdbcType="VARCHAR" />
    <result column="withdraw_no" property="withdrawNo" jdbcType="VARCHAR" />
    <result column="apply_money" property="applyMoney" jdbcType="DECIMAL" />
    <result column="account_money" property="accountMoney" jdbcType="DECIMAL" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="apply_time" property="applyTime" jdbcType="INTEGER" />
    <result column="bank_user_name" property="bankUserName" jdbcType="VARCHAR" />
    <result column="bank_location" property="bankLocation" jdbcType="VARCHAR" />
    <result column="bank_name" property="bankName" jdbcType="VARCHAR" />
    <result column="bank_card" property="bankCard" jdbcType="VARCHAR" />
    <result column="audit_time" property="auditTime" jdbcType="INTEGER" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="audit_uid" property="auditUid" jdbcType="BIGINT" />
    <result column="audit_name" property="auditName" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="fields">
    id,yeelight_user_id,withdraw_type,withdraw_no,apply_money,account_money,status,apply_time,bank_user_name,bank_location,bank_name,bank_card,audit_time,remark,audit_uid,audit_name
  </sql>

  <select id="pageWithdrawsDetail" resultType="com.yeelight.service.user.client.dto.UserWithdrawDetailDto" parameterType="com.yeelight.service.user.client.query.UserWithdrawsDetailQuery">
    select a.id,a.yeelight_user_id as yeelightUserId,a.withdraw_type as withdrawType,a.withdraw_no as withdrawNo,
    a.apply_money as applyMoney,a.account_money as accountMoney,a.status,a.apply_time as applyTime,a.bank_user_name as bankUserName,
    a.bank_location as bankLocation,a.bank_name as bankName,a.bank_card as bankCard,a.audit_time as auditTime,a.remark,
    a.audit_uid as auditUid,a.audit_name as auditName,
    d.real_name as realName,b.phone_number as phoneNumber, d.province_id as provinceId,
    d.province_name as provinceName,d.city_id as cityId,
    d.city_name as cityName,d.region_id as regionId,
    d.region_name as regionName,d.address
    from yeelight_user_withdraws a
    left JOIN yeelight_users b on a.yeelight_user_id=b.id
    left JOIN yeelight_users_extend d on a.yeelight_user_id=d.yeelight_user_id
    <where>
      <if test="realName != null and realName != '' ">
        d.real_name like CONCAT('%',#{realName},'%')
      </if>
      <if test="phoneNumber != null and phoneNumber != '' ">
        and b.phone_number like CONCAT('%',#{phoneNumber},'%')
      </if>
      <if test="provinceId != null and provinceId != '' ">
        and d.province_id = #{provinceId}
      </if>
      <if test="cityId != null and cityId != '' ">
        and d.city_id = #{cityId}
      </if>
      <if test="withdrawType != null and withdrawType != '' ">
        and a.withdraw_type = #{withdrawType}
      </if>
      <if test="withdrawNo != null and withdrawNo != '' ">
        a.withdraw_no like CONCAT('%',#{withdrawNo},'%')
      </if>
      <if test="status != null">
        and a.status = #{status}
      </if>
      <if test="applyTimeStart != null">
        and a.apply_time <![CDATA[ >= ]]> #{applyTimeStart}
      </if>
      <if test="applyTimeEnd != null">
        and a.apply_time <![CDATA[ <= ]]> #{applyTimeEnd}
      </if>
      <if test="auditTimeStart != null">
        and a.audit_time <![CDATA[ >= ]]> #{auditTimeStart}
      </if>
      <if test="auditTimeEnd != null">
        and a.audit_time <![CDATA[ <= ]]> #{auditTimeEnd}
      </if>
      <if test="bankUserName != null and bankUserName != '' ">
        a.bankUser_name like CONCAT('%',#{bankUserName},'%')
      </if>
      <if test="bankLocation != null and bankLocation != '' ">
        a.bank_location like CONCAT('%',#{bankLocation},'%')
      </if>
      <if test="bankName != null and bankName != '' ">
        a.bank_name like CONCAT('%',#{bankName},'%')
      </if>
      <if test="bankCard != null and bankCard != '' ">
        a.bank_card like CONCAT('%',#{bankCard},'%')
      </if>
      <if test="auditUid != null">
        and a.audit_uid = #{auditUid}
      </if>
      <if test="auditName != null and auditName != '' ">
        and a.audit_name = #{auditName}
      </if>
      <if test="remark != null and remark != '' ">
        a.remark like CONCAT('%',#{remark},'%')
      </if>
      <if test="yeelightUserId != null">
        and a.yeelight_user_id = #{yeelightUserId}
      </if>
      <if test="id != null">
        and a.id = #{id}
      </if>
    </where>
    order by a.id desc
  </select>

</mapper>