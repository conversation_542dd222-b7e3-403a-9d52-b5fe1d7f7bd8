<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yeelight.service.user.server.mapper.user.YeelightUserMapper" >
  <resultMap id="BaseResultMap" type="com.yeelight.service.user.server.domain.YeelightUser" >
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" property="id" jdbcType="BIGINT" />
    <result column="username" property="username" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="salt" property="salt" jdbcType="VARCHAR" />
    <result column="phone_number" property="phoneNumber" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="VARCHAR" />
    <result column="avatar" property="avatar" jdbcType="VARCHAR" />
    <result column="region" property="region" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="fields">
    id,username,name,password,salt,phone_number,email,status,create_time,update_time,deleted,avatar,region
  </sql>
  <delete id="removeUserByUsername">
    DELETE FROM yeelight_users WHERE  username = #{username,jdbcType=VARCHAR}
  </delete>

  <!--获取指定属性记录数-->
  <select id="getCountByProperty" parameterType="java.util.Map" statementType="STATEMENT" resultType="java.lang.Integer">
    SELECT count(*) FROM yeelight_users
    WHERE ${property} = '${value}'
  </select>

  <select id="findUserById" resultMap="BaseResultMap" resultType="com.yeelight.service.user.server.domain.YeelightUser">
    SELECT <include refid="fields"></include> FROM yeelight_users
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <select id="findUserByIds" resultMap="BaseResultMap" resultType="com.yeelight.service.user.server.domain.YeelightUser">
    SELECT <include refid="fields"></include> FROM yeelight_users
    WHERE id in
    <foreach collection="ids" open="(" separator="," item="id" close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

  <select id="findUserByUsername" resultMap="BaseResultMap" resultType="com.yeelight.service.user.server.domain.YeelightUser">
    SELECT <include refid="fields"></include> FROM yeelight_users
    WHERE username = #{username,jdbcType=VARCHAR}
  </select>

  <select id="findUserByPhoneNumber" resultMap="BaseResultMap" resultType="com.yeelight.service.user.server.domain.YeelightUser">
    SELECT <include refid="fields"></include> FROM yeelight_users
    WHERE phone_number = #{phoneNumber,jdbcType=VARCHAR}
  </select>

  <select id="findUserByEmail" resultMap="BaseResultMap" resultType="com.yeelight.service.user.server.domain.YeelightUser">
    SELECT <include refid="fields"></include> FROM yeelight_users
    WHERE email = #{email,jdbcType=VARCHAR}
  </select>

  <select id="findUserByAccount" resultMap="BaseResultMap" resultType="com.yeelight.service.user.server.domain.YeelightUser">
    SELECT <include refid="fields"></include> FROM yeelight_users
    WHERE username = #{account,jdbcType=VARCHAR} OR phone_number = #{account,jdbcType=VARCHAR} OR email = #{account,jdbcType=VARCHAR} LIMIT 1
  </select>
  <select id="getValidIds" parameterType="java.util.Set" resultType="java.lang.Long">
    select id from  yeelight_users where id in
    <foreach collection="collection" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="findUserAndExtend" resultType="com.yeelight.service.user.client.domain.YeelightUserEntity" parameterType="com.yeelight.service.user.client.domain.YeelightUserExample">
    select a.id,b.real_name as realName,a.phone_number as phoneNumber, a.avatar,
    b.birthday,b.id_card as idCard,b.province_id as provinceId,
    b.province_name as provinceName,b.city_id as cityId,
    b.city_name as cityName,b.region_id as regionId,
    b.region_name as regionName,b.address,b.id_pic_obverse as idPicObverse,
    b.id_pic_reverse as idPicReverse,b.certification_data as certificationData
    from yeelight_users a
    left JOIN yeelight_users_extend b on a.id=b.yeelight_user_id
    left join yeelight_user_level_rel c on a.id=c.yeelight_user_id
    <where>
      <if test="realName != null and realName != '' ">
        b.real_name like CONCAT('%',#{realName},'%')
      </if>
      <if test="phoneNumber != null and phoneNumber != '' ">
        and a.phone_number like CONCAT('%',#{phoneNumber},'%')
      </if>
      <if test="birthday != null and birthday != '' ">
        and b.birthday = #{birthday}
      </if>
      <if test="birthday != null and birthday != '' ">
        and b.birthday = #{birthday}
      </if>
      <if test="idCard != null and idCard != '' ">
        and b.id_card = #{idCard}
      </if>
      <if test="provinceId != null and provinceId != '' ">
        and b.province_id = #{provinceId}
      </if>
      <if test="cityId != null and cityId != '' ">
        and b.city_id = #{cityId}
      </if>
      <if test="id != null">
        and a.id = #{id}
      </if>
      <if test="levelId != null">
        and c.level_id = #{levelId}
      </if>
    </where>
  </select>

</mapper>