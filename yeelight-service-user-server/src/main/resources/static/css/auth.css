[v-cloak] {
  display: none;
}
.checkAccount{
  text-decoration: none !important;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  color: #666;
}
html,
body {
  font-size: 12px !important;
}
.Container{
  padding-top: 0;
}
.inputBox-pc {
  display: flex;
  align-items: center;
  justify-content: center;
}
.container .top .bg-box {
  padding-bottom: 23px;
  width: 100%;
  text-align: center;
  position: relative;
}
.container .top .bg-box .img-box {
  width: 100%;
  position: relative;
  text-align: center;
}
.container .top .bg-box .img-box div {
  margin-top: 1.25rem;
  font-size: 1.67rem;
  font-family:SourceHanSansCN-Bold,SourceHanSansCN, PingFangSC-Medium, PingFang SC,"PingFangSC-Regular", "Segoe UI","Microsoft YaHei",FreeSans,Arimo,"Droid Sans","wenquanyi micro hei","Hiragino Sans GB","Hiragino Sans GB W3",Font<PERSON><PERSON><PERSON>,sans-serif;
  font-weight:bold;
  color:rgba(51,51,51,1);
  line-height:36px;
}
.container .roleList-box .listTitle {
  margin-bottom: 2rem;
  font-size: 1.33rem;
  font-weight: 400;
  color: #2D2A26;
}
.container .roleList-box .listItem {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  font-size: 1.16rem;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
  line-height: 1.9rem;
}
.container .roleList-box .listItem:first-child{
  margin-bottom: 20px;
}
.container .roleList-box .listItem:last-child{
  margin-bottom: 40px;
}


.container .button-box {
  margin-top: .5rem;
  text-align: center;
}
.dotBox{
  margin-right: 10px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.5);
}
.container button {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}
.container button.isAuth {
  margin-bottom: 1.67rem !important;
}

@media screen and (max-width: 600px) {
  html,body{
    font-size: 0;
  }
  .box-content{
    height: auto;
  }
  .inputBox-pc{
    padding-left: 0;
    padding-right: 0;
    width: 100%;
  }
  .form-box{
    width: 100%;
  }
  .roleList-box{
    padding-top: 47px;
  }
}