/* reset */
*{
  -webkit-tap-highlight-color: transparent;
}

html,
body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  font-family: PingFangSC-Regular, PingFangSC-Medium, PingFang SC;
  margin: 0;
  padding: 0;
  background-color: #f8f8f8;
}

.mx-10{
  margin-left: 10px;
  margin-right: 10px;
}

img{
  -webkit-user-drag: none;
}

.container .is-disabled {
  background: #bcbcbc;
  border-radius: 0.5rem;
}

.box-content {
  height: 100%;
}
.flex-left {
  box-sizing: border-box;
  width: 33.4%;
  min-width: 481px;
  height: 100%;
  background: url('../img/bg-left.png') no-repeat;
  background-size: 100% 100%;
  /*border-radius: 6px 0px 0px 6px;*/
  color: #fff;
  padding: 9.58rem 3.58rem 0;
}
.flex-left-cn {
  background: url('../img/bg_left_cn.png') no-repeat;
  background-size: cover;
}
.flex-left-cn-wecom {
  background: url('../img/bg_left_cn_wecom.jpg') no-repeat;
  background-size: cover;
}

.flex-left .line {
  width: 2.83rem;
  border-bottom: 1px solid #fff;
  margin: 2.5rem 0;
}
.flex-left .title {
  font-size: 1.67rem;
  font-weight: 400;
  line-height: 2.58rem;
  max-width: 25rem;
}

.container,
PCcontainer {
  height: 100%;
}

.PCcontainer button {
  box-sizing: border-box;
  padding: 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background: #1a1a1a;
  /*font-size: 1.625rem;*/
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  border-radius: 14px;
}
.PCcontainer.PCcontainerWecom button {
  background: #00A1E8;
}
.PCcontainer.PCcontainerWecom button:focus,
.PCcontainer.PCcontainerWecom button:hover {
  background: #186cea;
}

a:focus,
a:active {
  outline: none;
  text-decoration: none;
}

.d-flex {
  display: flex;
  position: relative;
  z-index: 10;
}
.d-justify-center {
  justify-content: center;
}
.d-justify-between {
  justify-content: space-between;
}
.d-align-center {
  align-items: center;
}
.d-align-end {
  align-items: flex-end;
}
.d-align-baseline {
  align-items: baseline;
}
.el-button{
  border: 0;
  transition: none;
}
.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  color: #fff;
  background: #bcbcbc;
}

.el-button:hover {
  color: #fff!important;
  background-color: #313131!important;
  border-color: #2D2A26!important;
}
.el-button:focus{
  color: #fff!important;
  background-color: #1a1a1a!important;
  border-color: #2D2A26!important;
}
.el-button:active {
  color: #fff!important;
  background-color: #151515!important;
  border-color: #2D2A26!important;
}

@media screen and (max-width: 600px) {
  .el-button:hover {
    color: #fff!important;
    background-color: #1a1a1a!important;
    border-color: #2D2A26!important;
  }
  .el-button:active {
    color: #fff!important;
    background-color: #313131!important;
    border-color: #2D2A26!important;
  }
}

.a-blue {
  color: #429bf9 !important;
  text-decoration-color: transparent;
}
.a-black {
  color: #222;
  font-weight: 400 !important;
  font-size: 12px;
  text-decoration: underline !important;
}
.a-black-important {
  font-weight: 400 !important;
  font-size: 13px;
}

.el-input__inner {
  border-color: #d2d2d7;
  color: #000000;
  border-width: 1px;
}
.el-input--suffix .el-input__inner{
  padding-right: 36px;
}

.PCcontainer {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rem 0;
}

.width-100{
  width: 100%!important;
}

.forget-pwd-tip {
  text-align: right;
  margin-bottom: 2.17rem;
}
.forget-pwd-tip .a-black-important, .forget-pwd-tip a {
  text-decoration: none !important;
  font-size: 12px;
  color: #666;
}
.back-login{
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  height: 24px;
  background: url("../img/back-login.png") no-repeat;
  background-size: 100%;
  cursor: pointer;
}
.back-login-mobile{
  top: 2rem;
  left: 2.5rem;
}


/*重写alert样式*/
/*.el-alert{*/
/*  position: absolute;*/
/*  top: 50%!important;*/
/*  left: 50%!important;*/
/*  transform: translate(-50%,-50%)!important;*/
/*  background: rgba(0,0,0,0.6)!important;*/
/*  z-index: 99999!important;*/
/*  color: #fff !important;*/
/*  display: flex;*/
/*  justify-content: center;*/
/*}*/
/*.el-alert__icon,.el-alert.is-light .el-alert__closebtn{*/
/*  display: none!important;*/
/*}*/

/*重写message样式*/
.el-message {
  top: 50%!important;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: auto;
  max-width: 300px;
  background: rgba(0,0,0,0.6);
  border-radius: 47px;
  border: 0;
  backdrop-filter: blur(10px);
}

.el-message__icon.el-icon-info,
.el-message__icon.el-icon-error,
.el-message__icon.el-icon-success,
.el-message__icon.el-icon-warning {
  display: none;
}
.el-message--info .el-message__content,
.el-message--success .el-message__content,
.el-message--warning .el-message__content,
.el-message--error .el-message__content {
  color: #ffffff;
  font-size: 13px;
  line-height: 20px;
}

@media screen and (max-height: 800px) {
  .PCcontainer {
    padding: 0;
  }
}
.PCBox {
  box-sizing: border-box;
  flex: 1;
  width: 100%;
  height: 100%;
}
.bgImg-box {
  display: none;
}
.yeeligt-box {
  display: none;
  margin: 23.5rem 0 0 25.5rem;
  width: 21.25rem;
  height: 3.125rem;
}
.yeeligt-box img {
  width: 100%;
  height: 100%;
}

.loginBtnPC.is-disabled {
  color: #fff;
  background: #bcbcbc;
}
.loginBtnPC.is-disabled:hover,
.loginBtnPC.is-disabled:focus,
.loginBtnPC.is-disabled:active {
  background: #bcbcbc !important;
}

.el-input__inner {
  /*border-radius: 0;*/
}
.el-icon-lock,
.el-icon-chat-line-square {
  margin-top: 1px;
}
.getcode {
  text-decoration: none;
}
.el-input__suffix {
  right: 1.2rem;
  line-height: 5rem!important;
  /*top: -0.7rem;*/
}
.suffix-icon {
  position: absolute;
  right: -0.3rem;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  width: 20px !important;
  height: 20px !important;
}

.el-input__icon{
  width: auto;
}
.el-icon-circle-close:before {
  content: "\e79d" !important;
  font-size: 20px;
  padding-left: 6px;
}

.container .el-form-item__error {
  display: flex;
  align-items: center;
}
.container .el-form-item__error::before {
  content: '';
  display: inline-block;
  background: url('../img/error_icon.png') no-repeat;
  background-size: 100% 100%;
  margin-right: 6px;
}



@media screen and (max-width: 600px) {
  html,
  body {
    background-color: #fff;
    font-family: PingFangSC-Regular, PingFangSC-Medium, PingFang SC;
  }
  .container .el-form-item__error::before {
    width: 1.2rem;
    height: 1.2rem;
  }
  .Container {
    height: 100%;
    padding-top: 7.2rem;
  }
  .container {
    flex-direction: column;
    background: url('https://fe-resource.yeelight.com/user/img/background-mobile.png') no-repeat;
    height: 100%;
    background-size: 100% 100%;
    color: #ffffff;
  }
  .container.container-cn {
    background: url('https://fe-resource.yeelight.com/user/img/background-mobile.png') no-repeat;
    background-size: 100%;
    padding-top: 6rem;
  }
  .flex-left {
    display: none;isOverseas
  }
  .PCBox {
    width: 100%;
  }
  .PCBox,
  .inputBox-pc {
    color: #ffffff;
    background-color: transparent;
  }
  .inputBox-pc {
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }
  .container button {
    width: 100%;
    color: rgba(255, 255, 255, 1);
    background: #1a1a1a;
    border-radius: 14px;
    font-weight: 400;
    font-size: 1.4rem;
    padding: 2rem 0;
    font-family: PingFangSC-Regular;
  }
  .container input {
    border: 0;
    background: #F3F3F3;
    border-radius: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
  }
  .container input::placeholder {
    color: rgba(216, 216, 216, 0.5);
  }

  .el-input-group__prepend {
    border: 0;
    border-top-left-radius: 14px;
    border-bottom-left-radius: 14px;
  }
  .el-input__inner {
    line-height: 6rem;
    height: 6rem;
    font-size: 13px;
  }
  .el-input__icon{
    line-height: 6rem;
  }
  .el-input__suffix {
    right: 16px;
  }
  .el-input__inner:hover,
  .el-input__inner:focus {
    border-color: transparent;
    opacity: 1;
  }

  .mb-top-logo {
    padding: 0 2rem;
    align-self: flex-start;
  }
  .mb-top-logo-cn {
    text-align: center;
    align-self: center;
  }
  .mb-top-logo-cn .logo {
    width: 60px;
    height: 60px;
  }
  .mb-top-logo .title {
    font-size: 14px;
    max-width: 19rem;
  }
  .mb-top-logo img {
    width: 15rem;
    height: 2.2rem;
    margin-bottom: 2rem;
  }
  /*.a-black {*/
  /*  font-size: 10px;*/
  /*}*/
  .a-black-important {
    font-size: 13px;
  }
  .getcode {
    text-decoration: none;
  }
  .suffix-icon {
    width: 20px !important;
    height: 20px !important;
    right: 0;
  }

  .a-black-mobile {
    color: #fff;
    opacity: 0.6;
    text-decoration: none;
    font-size: 1.2rem;
  }
  .el-form-item.is-error .el-input__inner {
    border: 0;
  }
  .el-form-item.is-error .el-input__inner:hover,
  .el-form-item.is-error .el-input__inner:focus {
    border: 0;
  }
  .el-form .tip-padding .el-input__inner {
    padding-top: 1.5rem;
    font-weight: 500;
  }
  .el-form .ios-hidePwd-input .el-input__inner {
    font-size: 12px !important;
  }
  .el-form .input-placeholder-tip {
    top: 14.5%;
    left: 15px;
    font-size: 10px;
    color: rgba(210, 210, 215, 0.5);
    transform: scale(0.95);
  }
  .el-input__inner::-webkit-input-placeholder {
    color: #B9B9B9!important;
  }
  .page-title{
    position: relative;
    font-size: 2.4rem;
    text-align: center;
    font-weight: 600;
    color: #333;
  }
  .PCcontainer .footer a,
  .PCcontainer .footer span {
    font-weight: 400;
    color: #000;
    font-size: 14px;
    text-decoration: none;
    font-family: PingFangSC-Regular, PingFang SC;
  }
}

@media screen and (min-width: 601px) {
  .Container, PCcontainer{
    width: 100%;
    height: 100%;
    background-image:  url('https://fe-resource.yeelight.com/user/img/background-pc.png');
    background-size: 100% 100%;
  }
  .container .el-form-item__error::before {
    width: 12px;
    height: 12px;
  }
  .el-form-item.is-error .el-input__inner {
    border: 0;
  }
  .el-form-item.is-error .el-input__inner:hover,
  .el-form-item.is-error .el-input__inner:focus {
    padding-left: 15px;
    border: 0;
  }
  .PCBox {
    display: block;
    border-radius: 6px;
    max-height: 804px;
  }
  .PCBox .logo {
    display: block;
    width: 16.25rem;
    height: 2.5rem;
  }
  .flex-left-cn .logo {
    width: 20rem;
  }
  .PCcontainer .footer {
    display: flex;
    position: static;
    margin-bottom: 0.875rem;
    width: 100%;
    text-align: left;
    font-size: 1rem;
  }
  .PCcontainer .footer div{
    width: 50%;
    text-align: center;
  }
  .PCcontainer .footer .left-content{
    cursor: pointer;
    position: relative;
  }
  .PCcontainer .footer .left-content:after{
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 12px;
    transform: translateY(-50%);
    background: rgba(17,25,51,0.16);
  }
  .PCcontainer .footer a,
  .PCcontainer .footer span {
    font-weight: 400;
    color: #000;
    font-size: 13px;
    text-decoration: none;
    font-family: PingFangSC-Regular, PingFang SC;
  }
  .PCcontainer .footer div:first-child {
    margin-bottom: 0.875rem;
  }

  .container input {
    border-radius: 0.5rem;
    height: 3.75rem;
  }
  .getCodePC {
    position: relative;
    cursor: pointer;
  }
  .getCodePC:after {
    content: '';
    position: absolute;
    top: 1.55rem;
    left: 0;
    width: 1px;
    /*height: 1.5rem;*/
    height: 0;
    background: #dcdfe6;
  }
  .PCcontainer .form-box-code .getcode {
    position: absolute;
    top: 0;
    right: 0;
    width: auto;
    vertical-align: bottom;
    background: transparent;
    padding: 0 20px;
    color: #333;
    font-size: 1.125rem;
    border: 0;
    height: 4.67rem;
    font-family: PingFangSC-Regular, PingFang SC;
    cursor: pointer;
    font-weight: 500;
  }

  .form-box{
    position: relative;
    padding: 36px 40px 30px;
    background: #fff;
    box-shadow: 0px 20px 50px 0px rgba(56,56,138,0.05);
    border-radius: 24px 24px 24px 24px;
    /*transition: all 0.4s ease-in-out;*/
  }
  .icon-box-pc{
    position: absolute;
    top: -92px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
  }

  .inputBox-pc{
    position: relative;
  }
  .getCodePC {
    text-decoration: none;
  }
  .getCodePC:focus {
    outline: none;
  }

  .flex-right {
    box-sizing: border-box;
    flex: 1;
  }
  .inputBox-pc {
    box-sizing: border-box;
    flex: 1;
  }
  .inputBox-pc form {
    width: 330px;
  }
  .inputBox-pc .feedback-alert {
    position: absolute;
    top: 2.5rem;
    width: 330px;
  }

  .inputBox-pc .title {
    font-size: 1.875rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    font-family: PingFangSC-Medium, PingFang SC;
    line-height: 2.625rem;
    margin-bottom: 2rem;

  }
  .inputBox-pc input {
    height: 4.67rem;
    font-size: 1.125rem;
    font-family: PingFangSC-Regular, PingFang SC;
    border: 0;
    background: #F3F3F3;
    border-radius: 14px 14px 14px 14px
  }
  .el-input-group__prepend {
    border: 0;
    border-top-left-radius: 14px;
    border-bottom-left-radius: 14px;
  }
  .inputBox-pc .imgCodeInput {
    width: 18rem;
    margin-right: 1.5625rem;
  }
  .inputBox-pc img {
    width: 11.5rem;
    height: 4.125rem;
    vertical-align: bottom;
  }
  .loginBtnPC {
    background: rgb(45, 42, 38);
    color: rgb(255, 255, 255);
  }

  .PCcontainer .loginBtn:focus {
    background: #313131;
    color: #fff;
    border-color: inherit;
  }
{
  height: 5rem;
  line-height: 0;
}
  /*.el-input--suffix .el-input__inner {*/
  /*  padding-right: 3rem;*/
  /*}*/

  .el-input__inner:hover,
  .el-input__inner:focus {
    color: #000;
    border-color: transparent;
    border-width: 0px;
  }
  .tip-padding .el-input__inner {
    padding-top: 1.5rem;
    font-weight: 500;
  }
  .input-placeholder-tip {
    top: 0.5rem;
    left: 1.1rem;
    font-size: 10px;
    color: #d8d8d8;
  }

  .page-title{
    position: relative;
    padding-bottom: 36px;
    font-size: 18px;
    text-align: center;
    font-weight: 600;
    color: #282931;
  }

  .third-party-pc{
    margin-top: 40px;
  }
}

@media screen and (min-width: 601px) {
  .mb-top-logo {
    display: none;
  }
}
@media screen and (max-width: 1024px) {
  .PCBox .flex-left {
    display: none;
  }
}
@media screen and (min-width: 1024px) {
  .bgImg-box {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .bgImg-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .yeeligt-box {
    display: block;
  }
  /*.inputBox-pc{*/
  /*  left: 52%;*/
  /*  transform: translateX(0);*/
  /*}*/
}

.input-placeholder-tip {
  user-select: none;
  position: absolute;
  top: 0.5rem;
  left: 1.1rem;
  font-size: 10px;
  transform: scale(0.9);
  line-height: 1;
}

.right-icon {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  cursor: pointer;
}

.fade-enter-active {
  animation: fadeInDown 0.3s linear;
}
.fade-leave-active {
  animation: fadeInDown 0.3s reverse;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}


input::-webkit-credentials-auto-fill-button,
input::-webkit-contacts-auto-fill-button {
  display: none !important;
  visibility: hidden;
  pointer-events: none;
  position: absolute;
  right: 0;
}

input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px #F3F3F3 inset !important;
}

.switch-account-box-container {
  display: flex;
}

.switch-account-box {
  flex: 1;
  font-weight: 400;
  color: #333333;
  display: inline-block;
  text-decoration: none;
  vertical-align: middle;
  font-size: 13px;
  margin-bottom: 2rem;
}
.switch-account-box .el-icon-sort {
  transform: rotate(90deg);
}

