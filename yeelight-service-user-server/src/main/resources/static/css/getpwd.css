[v-cloak] {
  display: none;
}

html,
body {
  background-color: #fff;
}
.Container{
  padding-top: 0;
}
@media screen and (max-width: 600px) {
  .top-box {
    display: none;
  }
  .container {
     padding-top: 7.2rem;
  }
  .dia-box-retpwd,
  .dia-box-code,
  .dia-box-setpwd {
    padding: 5.6rem 2rem 0;
    width: 100%;
    box-sizing: border-box;
  }
  .dia-box-setpwd {
    height: 27rem;
  }
  .dia-box-retpwd .title,
  .dia-box-code .title,
  .dia-box-setpwd .title {
    font-size: 2rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    line-height: 3.3rem;
    margin-bottom: 3.29rem;
  }
  .dia-box-retpwd .text,
  .dia-box-code .text,
  .dia-box-setpwd .text {
    font-size: 1.3rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 1.6rem;
  }
  .dia-box-retpwd .text a,
  .dia-box-code .text a,
  .dia-box-setpwd .text a {
    color: rgba(102, 102, 102, 1);
  }

  .dia-box-code .gainCode {
    font-size: 1.4rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 2rem;
    pointer-events: none;
  }
  .dia-box-code .gainCode.canOperate {
    pointer-events: auto;
    cursor: pointer;
  }
  .container .gainCode{
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 3rem 0 0 !important;
    height: 6rem;
    width: auto;
    font-size: 1.2rem;
    color: #333;
    vertical-align: bottom;
    background: transparent;
    border: none;
    text-decoration: underline;
  }
  .container .gainCode.gainCode-cn {
    text-decoration: none;
  }
  .gainCode::before {
    content: '';
    margin-right: 2.2rem;
    /*border-left: 1px solid #D2D2D7;*/
  }
  .container .gainCode:focus{
    outline:none;
  }
  #smsBox .el-input__suffix{
    left: -18rem;
  }
  .el-input--prefix .el-input__inner{
    padding-left: 0;
  }
  .el-input__prefix{
    display: none;
  }
  .PCcontainer .nextStepBtn {
    line-height: 1;
    height: inherit;
    margin-top: 3.8rem;
  }
  .el-button:focus, .el-button:hover {
    color: #2D2A26;
    background-color: #fff;
    border-color: #FFFFFF;
  }
  .footer span{
    color: #fff !important;
    opacity: 0.8;
  }
  .page-title{
    padding-bottom: 7.2rem;
  }
}


@media screen and (min-width: 601px)  {
  html,
  body {
    font-size: 12px !important;
    background-color: #F8F8F8;
  }
  .inputBox-pc {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .inputBox-pc .feedback-alert {
    /*top: 14.5rem;*/
  }
  .top-box {
    text-align: center;
    height: 18rem;
    padding-top: 6.375rem;
    background: rgba(45, 42, 38, 1);
  }
  .top-box img {
    width: 3.875rem;
    height: 3.875rem;
  }
  .top-box .text {
    margin-top: 1.1875rem;
    margin-bottom: 4.4375rem;
    font-size: 1.25rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 1.75rem;
    color: white;
  }
  .dia-box-setpwd {
    height: 27rem;
  }
  .dia-box-retpwd .title,
  .dia-box-code .title,
  .dia-box-setpwd .title {
    font-size: 1.875rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    line-height: 2.625rem;
    margin-bottom: 2rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .dia-box-retpwd .nextStepBtn,
  .dia-box-code .nextStepBtn,
  .dia-box-setpwd .nextStepBtn {
    width: 100%;
    margin-top: 30px;
  }
  .dia-box-retpwd .nextStepBtn.isOperate,
  .dia-box-code .nextStepBtn.isOperate,
  .dia-box-setpwd .nextStepBtn.isOperate {
    pointer-events: auto;
  }
  .dia-box-retpwd .text,
  .dia-box-code .text,
  .dia-box-setpwd .text {
    font-size: 1.1rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 1.6rem;
  }
  .dia-box-retpwd .text a,
  .dia-box-code .text a,
  .dia-box-setpwd .text a {
    color: rgba(102, 102, 102, 1);
  }

  .dia-box-code .gainCode {
    font-size: 1.4rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 2rem;
    pointer-events: none;
  }
  .dia-box-code .gainCode.canOperate {
    pointer-events: auto;
    cursor: pointer;
  }
  #smsBox .el-input__suffix{
    left: -16rem;
  }
}
