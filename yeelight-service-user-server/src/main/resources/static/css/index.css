[v-cloak] {
  display: none;
}
/*弹框样式*/
.register-content{
  margin-top: 34px;
}
.account-box{
  display: flex;
  margin-bottom: 16px;
  padding: 18px 19px;
  width: 100%;
  height: 52px;
  background: #F3F3F3;
  border-radius: 14px 14px 14px 14px;
  color: #333333;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  box-sizing: border-box;
}
.account-img{
  margin-top: -2px;
  padding-right: 8px;
  width: 20px;
  height: 20px;
}
.account-img img{
  width: 100%;
  height: 100%;
}
.desc-box{
  margin-bottom: 40px;
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
}
.register-btn{
  box-sizing: border-box;
  padding: 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background: #1a1a1a;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
  border-radius: 14px;
}
.register-btn:hover, .register-btn:focus{
  background: #1a1a1a;
  color: #fff;
}
.loginByCode-box .getcode {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 1.5rem 0 0 !important;
  height: 6rem;
  width: auto;
  font-size: 1.2rem;
  color: #1a1a1a;
  vertical-align: bottom;
  background: transparent;
  border: none;
  font-weight: 500;
  cursor: pointer;
}
.getcode::before {
  content: '';
  margin-right: 2rem;
  /*border-left: 1px solid #D2D2D7;*/
}

/* 手机端 */

@media screen and (max-width: 600px) {
  .Container {
   padding-top: 0;
  }
  .container {
    box-sizing: border-box;
    padding-top: 7.2rem;
  }
  .top-box {
    width: 100%;
    text-align: center;
    padding-top: 7rem;
  }
  .top-box img {
    width: 3.66rem !important;
    height: 3.66rem !important;
  }
  .top-box .text {
    font-size: 1.5rem;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 2.1rem;
    margin-top: 1rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .loginByPwd-box,
  .loginByCode-box {
    padding: 0 3rem 0;
    box-sizing: border-box;
  }
  .loginByPwd-box .title,
  .loginByCode-box .title,
  .form-box-pwd .title{
    padding: 0 1rem;
    font-family:SourceHanSansCN-Medium,SourceHanSansCN-Bold,SourceHanSansCN, PingFangSC-Medium, PingFang SC,"PingFangSC-Regular", "Segoe UI","Microsoft YaHei",FreeSans,Arimo,"Droid Sans","wenquanyi micro hei","Hiragino Sans GB","Hiragino Sans GB W3",FontAwesome,sans-serif;
    font-weight:500;
    line-height: 12px;
  }
  .loginByPwd-box .title-active,
  .loginByCode-box .title-active{
    font-size: 1.9rem;
    color: #333;
    border-bottom: 2px solid #333;
  }
  .loginByPwd-box .loginText,
  .loginByCode-box .loginText {
    font-size: 1.4rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 2rem;
    text-align: right;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .loginByCode-box .getcode:focus{
    outline: none;
  }
  .loginByCode-box .codeInput {
    margin: 0 !important;
  }
  .loginByCode-box .codeInput .el-input__inner {
    padding-right: 0 !important;
    width: 100%;
  }
  .loginByCode-box .codeInput .el-input__suffix{
    left: -18rem;
  }
  .footer{
    width: 100%;
  }
  .footer div{
    padding: .4rem 0 1rem;
  }
  .container .footer a {
    font-size: 1.3rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 2rem;
    text-decoration: none;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .login-code-btn {
    text-align: center;
    font-size: 1.3rem;
    margin-top: 8px;
  }

  .nextStep{
    margin-top: 20px;
  }

  .el-form{
    margin-top: 7rem;
  }
  .el-button:focus, .el-button:hover {
    color: #2D2A26;
    background-color: #fff;
    border-color: #2D2A26;
  }

  .toggle-button {
    justify-content: center;

  }
  .toggle-button .title,  .toggle-button a{
    font-size: 1.3rem;
    text-decoration: none;
    color: #333;
    cursor: pointer;
    line-height: initial;
  }
  .border-right {
    border-right: 1px solid #333;
  }
}

/*pc端*/
@media screen and (min-width: 601px)  {
  html,
  body {
    font-size: 12px !important;
  }
  .toogleText {
    text-align: right;
    cursor: pointer;
    font-size: 1.125rem;
    color: rgba(102, 102, 102, 1);
    line-height: 1.5625rem;
    font-family: PingFangSC-Regular, PingFang SC;
  }
  .PCcontainer {
    position: relative;
    display: flex !important;
  }

  .PCcontainer .top {
    width: 100%;
    height: 18rem;
    padding-top: 6.375rem;
    text-align: center;
    background: rgba(45, 42, 38, 1);
  }
  .PCcontainer .top img {
    width: 3.875rem;
    height: 3.875rem;
    margin-bottom: 1.1875rem;
  }
  .PCcontainer .top .text {
    font-size: 1.25rem;
    font-weight: 500;
    color: white;
    line-height: 1.75rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .inputBox-pc{
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .inputBox-pc .feedback-alert {
    /*top: 14.5rem;*/
  }
  .inputBox-pc .feedback-alert-cn {
    /*top: 5rem;*/
  }
  .inputBox-pc .title{
    box-sizing: border-box;
    margin-bottom: 4.67rem;
    padding: 0;
    width: 47.6%;
    height: 3.83rem;
    border-radius: 0.5rem;
    line-height: 3.83rem;
    border: 1px solid #D2D2D7;
    color: #D2D2D7;
    font-size: 1.25rem;
    text-align: center;
    cursor: pointer;
  }
  .inputBox-pc .title-active{
    color: #333;
    border: 1px solid #333;
  }

  .captchaInput-SMS .el-input__suffix{
    right: 10rem;
  }
  .PCcontainer .loginBtn {
    font-size: 14px;
    /*margin-top: 2.5rem;*/
  }

  /*.inputBox-pc-cn .getCodePC::after {*/
  /*  height: 2rem;*/
  /*  top: 1rem;*/
  /*  background: #AAA;*/
  /*}*/
}
.pwd-input-en .el-form-item__error {
  top: 100%;
}

.third-party-pc img {
  width: 72px;
  height: 44px;
  margin: 0 5px;
}
.third-party-pc{
  margin-top: 40px;
  width: 330px;
}
.third-party-pc > .d-justify-center,
.third-party-tel > .d-justify-center{
  flex-wrap: wrap;
}
.d-justify-center > a {
  margin-bottom: 10px;
}
.third-party-tel {
  /*position: absolute;*/
  /*bottom: 32px;*/
  /*left: 50%;*/
  /*transform: translateX(-50%);*/
  margin-top: 60px;
  padding-bottom: 20px;
}
.third-party-tel .desc {
  font-size: 12px;
  color: #91949B;
  margin-top: 2.9rem;
  text-align: center;
}
.third-party-tel img  {
  width: 7.2rem;
  height: 4.4rem;
  margin: 0 1.5rem;
}
/*高度小于667px的时候*/
@media screen and (max-height: 667px) {
  .third-party-tel{
    position: relative;
    bottom: auto;
    left: auto;
    margin-top: 32px;
    padding-bottom: 32px;
    transform: none;
  }
}