[v-cloak] {
  display: none;
}
html,
body {
  width: 100%;
  height: 100%;
  background: rgba(244, 244, 244, 1);
}

img {
  width: 100%;
  height: 100%;
}
.hidden {
  visibility: hidden;
}

@media screen and (max-width: 600px) {
  /*手机端*/
  .Container {
    padding-top: 0;
  }
  .PCcontainer {
    display: none;
  }
  .container {
    display: block;
    color: #222;
    background: none;
  }
  .section-mb {
    text-align: center;
    background: #fff;
    width: 100%;
    box-sizing: border-box;
  }
  .title-mb {
    position: relative;
    padding: 2.1rem 0;
    font-size: 1.7rem;
  }
  .left-mb {
    position: absolute;
    left: 2.2rem;
    width: 2.16rem;
  }

  .bgBanner-mb {
    position: relative;
    padding: 0 2.3rem 2rem;
    height: 20rem;
  }
  .titleBox-mb {
    position: absolute;
    top: 0;
    left: 2.3rem;
    width: 32.9rem;
    height: 20rem;
  }
  .bgBanner-mb-title {
    position: relative;
    z-index: 10;
    color: #fff;
  }
  .retitle-mb {
    margin-top: -0.8rem;
    width: 1.8rem;
    height: 1.8rem;
  }
  .logo-mb {
    width: 10.1rem;
    height: 1.523rem;
  }
  .bgBanner-mb-right {
    position: relative;
    width: 6rem;
    height: 6rem;
  }
  .getAvatar-mb-box {
    border-radius: 6rem;
    width: 6rem;
    height: 6rem;
    overflow: hidden;
  }
  .camera-m {
    position: absolute;
    z-index: 10;
    width: 2.1rem;
    height: 2.1rem;
    bottom: 0;
    right: 0;
  }
  .bgBanner-mb-left {
    flex-direction: column;
  }
  .bgBanner-mb-title {
    padding: 3rem 1.25rem 1.3rem;
    height: 100%;
    text-align: left;
    box-sizing: border-box;
  }
  .getName-mb {
    font-size: 2rem;
  }
  .getId-md {
    margin-top: 0.8rem;
    color: #c0c0c0;
    font-size: 1.3rem;
  }
  .logout-mb ,.detail-mb{
    margin-top: 0.8rem;
    padding: 2.25rem 0;
    color: #f86565;
    font-size: 1.5rem;
  }
  .detail-mb{
    color: #222;
  }
  .right-mb {
    width: 1.5rem;
    height: 1.5rem;
  }
  .descBox-mb {
    margin: 0 3rem;
    padding: 2.25rem 0;
    font-size: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  /*.descBox-mb-email{*/
  /*  border: 0;*/
  /*}*/
  .thirdPartyBox.descBox-mb .descBox-mb-title {
    text-align: left;
  }
  .thirdPartyBox.descBox-mb .thirdPartyBtn {
    padding: 0;
    color: #999;
    font-size: inherit;
    width: auto;
  }
  .thirdPartyBox.descBox-mb .thirdPartyBtn span{
    padding: 4px;
    display: block;
    font-size: 12px;
  }
  .getPhoneNumber-mb {
    margin-right: 11px;
    color: #999 !important;
    text-decoration: none !important;
  }
  .right-mb {
    margin-top: -2px;
  }

  .showMask-md {
    position: fixed;
    z-index: 99;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #fff;
  }
  .showMask-md-head {
    background-color: rgba(0, 0, 0, 0.7);
  }
  .md-title {
    text-align: center;
  }
  .contentBox-md {
    padding: 0 3rem;
    box-sizing: border-box;
  }
  .container .confimBtn {
    margin: 2.4rem 0;
    color: #fff;
    background: rgba(45, 42, 38, 1);
  }
  .container .confimBtn-sub-mb {
    margin: 0;
    padding: 1rem 3rem;
    background: transparent;
    border: 0;
    box-shadow: none;
    width: 50%;
    font-size: 1.8rem;
    color: #fff;
  }
  .container .confimBtn-sub-mb[disabled] {
    color: #bcbcbc;
  }
  .confimBtn:hover {
    background: rgba(45, 42, 38, 1);
  }
  .container .confimBtn[disabled] {
    color: #fff;
    background: #bcbcbc;
  }
  .container .gainCode[disabled] {
    color: #bcbcbc;
  }
  .container .confimBtn[disabled]:hover,
  .container .confimBtn[disabled]:active {
    color: #fff;
    background: #bcbcbc;
  }
  .changeTitleBox {
    margin-bottom: 5.5rem;
  }
  .container .gainCode {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    width: auto;
    height: 6rem;
    font-size: 1.2rem;
    padding-right: 1rem !important;
    color: #333;
    vertical-align: bottom;
    background: transparent;
    border: none;
  }
  .container .el-input__inner {
    margin-bottom: 22px;
  }
  .container .el-input__prefix,
  .el-input__suffix {
    height: auto;
  }
  .NumVerification-mb .el-input__suffix {
    left: -18rem;
  }
  .cut {
    margin: 30% auto 0;
    width: 37.5rem;
    height: 37.5rem;
  }
  .el-upload-dragger {
    margin: 40% auto 0;
    width: 37.5rem;
    height: 37.5rem;
  }
  .changeTitleBox-mb {
    margin-bottom: 0;
  }
  .reUpdate-mb {
    font-size: 1.4rem;
    padding: 1rem 3rem;
    text-align: right;
    text-decoration: underline;
  }
  .el-icon-upload-mb {
    margin-top: 10rem !important;
  }
  .el-upload {
    margin: 0 auto;
  }
  .btn-box-mb {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}

@media screen and (min-width: 601px) {
  /*电脑端*/
  html,
  body {
    font-size: 13px;
  }
  .container {
    display: none;
  }
  .PCcontainer {
    padding: 0;
    max-width: initial;
    width: 100%;
  }
  .PCBox {
    background: rgba(244, 244, 244, 1);
    max-height: initial;
    box-shadow: none;
    border-radius: 0;
  }
  .bgBanner {
    position: relative;
    visibility: hidden;
  }
  .topLogo {
    position: absolute;
    top: 3rem;
    left: 2.65rem;
    width: 13.56rem;
  }
  .bgContent {
    width: 100%;
    overflow: hidden;
    height: 540px;
  }
  .bgBanner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .account-top {
    padding-bottom: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .acountBox {
    margin: -23rem auto 0;
    padding: 2.625rem 3.75rem 6.25rem 3.75rem;
    position: relative;
    z-index: 10;
    width: 90%;
    box-sizing: border-box;
    background: #fff;
    border-radius: 8px;
  }
  .logoutContent {
    cursor: pointer;
  }
  .logoutBox,.detailImgBox {
    margin-right: 0.6875rem;
    width: 1.25rem;
  }
  .logoutBox img {
    height: auto;
    vertical-align: text-top;
  }
  .detailImgBox img {
    margin-top: -.5rem;
    width: 100%;
    height: auto;
  }
  .setAccount {
    font-size: 1.625rem;
  }
  .logoutText {
    font-size: 1.125rem;
    color: #f86565;
  }
  .detailBox{
    padding-right: 1.25rem;
    font-size: 1.125rem;
    cursor: pointer;
  }

  .infoBox {
  }
  .phoneBox {
    margin: 1rem 0;
  }
  .headImgBox-img {
    position: relative;
    margin: 3.125rem auto 1.25rem;
    width: 7.5rem;
    height: 7.5rem;
    border-radius: 7.5rem;
    overflow: hidden;
  }
  .maskCamera {
    position: absolute;
    top: 0;
    left: 0;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    cursor: pointer;
  }
  .maskCamera img {
    width: 45%;
    height: 45%;
  }
  .headImgBox-name {
    position: relative;
    text-align: center;
  }
  .userName {
    margin-bottom: 0.4375rem;
    font-size: 1.375rem;
  }
  .userName-fix {
    padding-left: 1rem;
    color: #429bf9;
    cursor: pointer;
    font-size: 1.125rem;
  }
  .phoneAndemail {
    font-size: 1.125rem;
    color: #999;
  }
  .boxTitle {
    width: 8rem;
  }
  .d-input {
    height: 42px;
    line-height: 42px;
    margin: 0 1.125rem;
    padding: 0 1.25rem;
    width: 100%;
    border: 1px solid rgba(210, 210, 210, 1);
  }
  .d-button {
    padding: 1rem 0;
    text-align: center;
    width: 8.25rem;
    border: 1px solid rgba(51, 51, 51, 1);
    cursor: pointer;
  }
  .d-button-active {
    border: 1px solid rgba(66, 155, 249, 1);
  }
  .d-button.d-button-active {
    color: #429bf9;
  }
  .idBox {
    margin-top: 7px;
    text-align: center;
    color: #999;
    font-size: 1rem;
  }

  .showMask {
    position: absolute;
    z-index: 99;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.4);
  }
  .changeBox {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 2.6875rem 2.5rem 3.75rem;
    width: 90%;
    box-sizing: border-box;
    background: #fff;
    border-radius: 8px;
  }
  .changeTitleBox {
    margin-bottom: 3.75rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 1.625rem;
    color: #333;
  }

  .closeBox {
    margin-top: -4px;
    width: 1.8rem;
    height: 1.8rem;
    cursor: pointer;
  }
  .titelP {
    width: 5rem;
    flex-shrink: 0;
    color: #999;
    font-size: 1.125rem;
  }
  .el-input__inner {
    border-radius: 0;
    height: 40px !important;
  }
  .selectItem {
    margin-bottom: 3.125rem;
  }
  .confimBtn {
    height: 3.625rem !important;
    line-height: 3.625rem !important;
    font-size: 1.25rem !important;
  }
  .confimBtn[disabled] {
    background: #bcbcbc;
    border: none;
  }
  .PCcontainer .verificationBtn {
    line-height: 40px;
    margin-left: 1.25rem;
    width: 8.2rem;
    height: 40px;
    flex-shrink: 0;
    font-size: 1.125rem;
    border: 1px solid rgba(51, 51, 51, 1);
    background: transparent;
    color: #333;
    border-radius: 0;
  }
  .PCcontainer .verificationBtn[disabled] {
    border-color: #dcdfe6;
    color: #dcdfe6;
  }
  .changeHeadBox {
    margin-bottom: 2rem;
  }
  .reUpdate {
    margin-bottom: 1rem;
    font-size: 1.125rem;
    color: #429bf9;
    cursor: pointer;
  }

  .el-upload-dragger {
    width: 18.125rem;
    height: 18.125rem;
  }
  .el-icon-upload {
    margin: 5rem 0 1rem !important;
  }
  .boxItemRight {
    flex-direction: column;
    width: 15rem;
    height: 18.125rem;
  }
  .previewBox {
    margin: 0 auto;
    width: 10rem;
    height: 10rem;
  }
  .previewBox-Content {
    margin: 0 auto;
    width: 10rem;
    height: 10rem;
    border-radius: 10rem;
    overflow: hidden;
  }

  .cut {
    width: 18.125rem;
    height: 18.125rem;
  }

  .thirdPartyBox {
    margin-top: 1rem;
  }

  .thirdPartyBox .thirdPartyBtn {
    width: 6.25rem;
    height: 3.28rem;
    background-color: #fff;
    line-height: inherit;
    font-size: inherit;
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-radius: 0;
    border-color: rgba(51, 51, 51, 1);
    color: #999;
  }
}
@media screen and (min-width: 590px) {
  .changeBox {
    width: 40rem;
    box-sizing: content-box;
  }
}
@media screen and (min-width: 1024px) {
  .acountBox {
    margin: -23rem auto 0;
    padding: 2.625rem 3.75rem 4.5rem 3.75rem;
    position: relative;
    z-index: 10;
    width: 56.625rem;
    box-sizing: content-box;
    background: #fff;
    border-radius: 8px;
  }
  .boxTitle {
    width: 5rem;
  }
  .userName-fix {
    left: 60%;
  }

  .bgBanner {
    position: relative;
    visibility: visible;
  }
}
