.popup-container{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
}

.mask-content{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.popup-content{
    position: absolute;
    top: 50%;
    left: 50%;
    padding: 24px 20px 20px;
    z-index: 1000;
    width: 330px;
    background: #fff;
    border-radius: 24px 24px 24px 24px;
    transform: translate(-50%,-50%);
    box-shadow:0px 20px 50px 0px rgba(56,56,138,0.05);
}

.popup-header{
    position: relative;
    width: 100%;
    text-align: center;
    font-size: 18px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #333333;
    line-height: 21px;
}

.close{
    position: absolute;
    top: -6px;
    right: 0px;
    width: 32px;
    height: 32px;
    border-radius: 16px;
    cursor: pointer;
    background: #F3F3F3;
}
.img-box{
    margin: 7px;
    width: 18px;
    height: 18px;
    background: url(../img/close.png) no-repeat center;
    background-size: 100% 100%;
    display: block;
}

.popup-enter-active , .popup-leave-active{
    transition: all 0.2s linear;
}
.popup-enter, .popup-leave-to {
    opacity: 0;
}

/*pc端动画效果取消*/
/*.popup-enter .popup-content,*/
/*.popup-leave-to .popup-content{*/
/*    transform: translate(-50%,-100%);*/
/*}*/

.popup-enter .popup-content,
.popup-enter-to .popup-content,
.popup-leave .popup-content,
.popup-leave-to .popup-content{
    transition: all 0.2s linear;
}


@media screen and (max-width: 600px) {
    .popup-content{
        position: fixed;
        top: auto;
        bottom: 0;
        left: 0;
        right: 0;
        transform: none;
        padding: 20px 20px 30px;
        width: 100%;
        box-sizing: border-box;
        border-radius: 24px 24px 0 0;
        color: #333;
    }
    .container button{
        line-height: normal;
    }

    .popup-enter .popup-content,
    .popup-leave-to .popup-content{
        transform: translate(0,100%);
    }
}