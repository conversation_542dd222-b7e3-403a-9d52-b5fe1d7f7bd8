[v-cloak] {
  display: none;
}

.text.footer{
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-input__suffix{
  line-height: 4.67rem;
}

.getcode{
  text-decoration: none!important;
}


.goLoginBox{
  padding-left: 8px;
  font-weight: 500;
    color: #333333;
  display: inline-block;
  text-decoration: none;
  vertical-align: middle;
}
.goLoginBox span{
  vertical-align: middle;
}
/*向右侧三角*/
.goLoginBox:after{
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 5px solid #333333;
    vertical-align: middle;
    margin-left: 5px;
}

/*弹框*/
.agreement-content{
  margin-top: 30px;
}
.agreement-box{
  display: flex;
  margin-bottom: 16px;
  justify-content: space-between;
}
.privacy-item{
  padding: 20px 0;
  width: 48%;
  background: #F3F3F3;
  border-radius: 14px 14px 14px 14px;
  text-align: center;
  cursor: pointer;
}
.icon-box{
  margin: 0 auto 8px;
  width: 24px;
  height: 24px;
  font-size: 13px;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 15px;
}
.icon-box img{
  width: 100%;
  height: 100%;
}
.message-box{
  margin-bottom: 10px;
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 24px;
}


@media screen and (max-width: 600px) {
  .Container{
    padding-top: 0;
  }
  .top-box {
    display: none;
  }
  .container {
    padding-top: 7.2rem;
  }
  .dia-box-register,
  .dia-box-code,
  .dia-box-setpwd {
    padding: 5.6rem 2rem 0;
    width: 100%;
    box-sizing: border-box;
  }
  .container-cn .dia-box-register {
    padding-top: 0;
  }
  .dia-box-setpwd {
    height: 27rem;
  }
  .dia-box-register .title,
  .dia-box-code .title,
  .dia-box-setpwd .title {
    font-size: 2rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    line-height: 3.3rem;
    margin-bottom: 3.29rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .dia-box-register .nextStepBtn,
  .dia-box-code .nextStepBtn,
  .dia-box-setpwd .nextStepBtn,
  .nextStepBtn.popupBtn{
    padding: 0;
  }

  .dia-box-register .text,
  .dia-box-code .text,
  .dia-box-setpwd .text {
    font-size: 1.3rem;
    font-weight: 400;
    color: rgba(255,255,255,0.6);
    line-height: 1.6rem;
  }
  .dia-box-register .text a,
  .dia-box-code .text a,
  .dia-box-setpwd .text a,
  .PCcontainer .footer span{
    font-size: 1.3rem;
  }
  .text.footer {
    display: flex;
  }
  .el-checkbox__label {
    color: rgba(255,255,255,0.6);
  }

  .dia-box-code .gainCode {
    font-size: 1.4rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 2rem;
    pointer-events: none;
  }
  .dia-box-code .gainCode.canOperate {
    pointer-events: auto;
    cursor: pointer;
  }

  .dia-box-register .el-form .el-form-item:last-child {
    margin-bottom: 1.2rem;
  }
  #registerFormCode{
    position: relative;
  }
  #registerFormCode .el-input__suffix{
    left: -18rem;
  }
  .container .gainCode{
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 3rem 0 0 !important;
    height: 6rem;
    width: auto;
    font-size: 1.2rem;
    color: #333;
    vertical-align: bottom;
    background: transparent;
    border: none;
    font-weight: 500;
  }
  .gainCode::before {
    content: '';
    margin-right: 3rem;
    /*border-left: 1px solid #D2D2D7;*/
  }
  .container .gainCode:focus{
    outline: none;
  }
  .nextStepBtn{
    margin-top: 20px;
  }
  .el-checkbox__label{
    font-size: 1.3rem;
    font-weight: normal;
  }
  .el-checkbox__input.is-checked+.el-checkbox__label{
    color: rgba(255,255,255,0.6);
  }

  .el-input--prefix .el-input__inner{
    padding-left: 15px;
  }
  .el-input__prefix{
    display: none;
  }
  .footer .a-black-important {
    opacity: 1;
  }
  .page-title{
    padding-bottom: 7rem;
  }
}

@media screen and (min-width: 601px)  {

  html,
  body {
    font-size: 12px !important;
  }
  .inputBox-pc{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .top-box {
    text-align: center;
    height: 18rem;
    padding-top: 6.375rem;
    background: rgba(45, 42, 38, 1);
  }
  .top-box img {
    width: 3.875rem;
    height: 3.875rem;
  }
  .top-box .text {
    margin-top: 1.1875rem;
    margin-bottom: 4.4375rem;
    font-size: 1.25rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: rgba(51, 51, 51, 1);
    line-height: 1.75rem;
    color: white;
  }
  .dia-box-register .title,
  .dia-box-code .title,
  .dia-box-setpwd .title {
    font-size: 1.875rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    line-height: 2.625rem;
    margin-bottom: 2rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .dia-box-register .text,
  .dia-box-code .text,
  .dia-box-setpwd .text {
    font-size: 1.1rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 1.6rem;
  }
  .text.footer {
    font-size: 1.125rem;
  }

  .dia-box-register .text a,
  .dia-box-code .text a,
  .dia-box-setpwd .text a {
    color: #333
  }

  .dia-box-code .gainCode {
    font-size: 1.4rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 2rem;
    pointer-events: none;
  }
  .dia-box-code .gainCode.canOperate {
    pointer-events: auto;
    cursor: pointer;
  }

  .dia-box-register .el-form .el-form-item:last-child {
    margin-bottom: 1.2rem;
  }
  .allowAgent{
    margin-bottom: 22px;
    color: #585858;
  }
  .container button.isOperate{
    padding: 0;
    margin-top: 1.15rem;
  }

  .el-button:active, .el-button:hover{
    color: #fff;
    background: #000;
  }
  .el-button:focus {
    background: #98989A;
    border-color: inherit;
    color: #fff;
  }
  #registerFormCode .el-input__suffix{
    left: -16rem;
  }
  .feedback-alert-cn {
    /*top: 10.5rem !important;*/
  }
  .nextStepBtn {
    margin-top: 2.5rem;
  }
}

.el-checkbox__input.is-checked .el-checkbox__inner{
  background-color: rgba(45, 42, 38, 1);
  border-color: rgba(45, 42, 38, 1);
}
.el-checkbox__input.is-focus .el-checkbox__inner {
  border-color: rgba(45, 42, 38, 1);
}
.el-checkbox__inner:hover {
  border-color: rgba(45, 42, 38, 1);
}