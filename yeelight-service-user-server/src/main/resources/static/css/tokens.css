[v-cloak] {
    display: none;
}
.el-button--primary{
    background:#222
}
html,
body {
    width: 100%;
    height: 100%;
    background: rgba(244, 244, 244, 1);
    font-size: 13px;
}
.PCBox{
    background: rgba(244, 244, 244, 1);
    max-height: initial;
}

img {
    width: 100%;
    height: 100%;
}
.hidden {
    visibility: hidden;
}
.PCcontainer{
    padding:0;
}
.bgBanner {
    position: relative;
}
.topLogo {
    position: absolute;
    top: 3rem;
    left: 2.65rem;
    width: 13.56rem;
}
.bgContent {
    width: 100%;
    overflow: hidden;
    height: 540px;
}
.acountBox {
    margin: -23rem auto 0;
    padding: 2.625rem 3.75rem 4.5rem 3.75rem;
    position: relative;
    z-index: 10;
    width: 56.625rem;
    box-sizing: content-box;
    background: #fff;
    border-radius: 8px;
}
.acountHead {
    padding-bottom: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.setAccount {
    font-size: 1.625rem;
}
.browserItem{
    padding: 15px 0 30px;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e5e5e5;
}
.infoBox{
    flex: 1;
    overflow: hidden;
}
.imgBox{
    flex-shrink: 0;
    margin-right: 15px;
    width: 45px;
    height: 45px;
}
.descBox{
    flex: 1;
    flex-wrap: wrap;
    width: 100%;
}
.logoutBox{
    margin-left: 15px;
}
.logoutBox button{
    color: inherit;
    background:inherit;
    border: 1px solid #e5e5e5;
    width: 80px;
    height: 30px;
    line-height: 20px;
}
.descHead{
    font-size: 15px;
}
.descItem{
    padding-top: 10px;
    font-size: 13px;
    line-height: 20px;
    color: #777;
}
.bottomMore{
    position: absolute;
    left: 50%;
    right: 50%;
    bottom: 0;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    width: 90%;
    height: 30px;
    cursor: pointer;
}
.moreBox{
    margin-top: 10px;
    width: 15px;
    height: 15px;
}
.currentDevice{
    padding: 4px 10px;
    display: inline-block;
    background: #ececec;
    color: #222;
    font-size: 12px;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
}

@media screen and (max-width: 1100px){
    /*.bgBanner {*/
    /*    position: relative;*/
    /*    visibility: hidden;*/
    /*}*/
    .acountBox {
        margin: -23rem auto 0;
        padding: 2.625rem 3.75rem 6.25rem 3.75rem;
        position: relative;
        z-index: 10;
        width: 90%;
        box-sizing: border-box;
        background: #fff;
        border-radius: 8px;
    }
}

@media screen and (max-width: 600px){
    .Container{
        padding-top: 0;
    }
    .PCcontainer{
        display: initial;
    }
    .PCBox, .inputBox-pc{
        color: inherit;
    }
    .bgBanner {
        position: relative;
        display: none;
    }
    .acountBox {
        position: relative;
        margin: auto;
        padding:  2rem 2.3rem;
        width: 100%;
        box-sizing: border-box;
    }

    .el-message-box{
        width: 300px;
    }
}
