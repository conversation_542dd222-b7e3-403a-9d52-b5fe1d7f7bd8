[v-cloak] {
  display: none;
}

.text.footer{
  justify-content: center;
}
.el-input__suffix{
  line-height: 4.67rem;
}


.goLoginBox{
  padding-left: 8px;
  font-weight: 500;
    color: #333333;
  display: inline-block;
}
/*向右侧三角*/
.goLoginBox:after{
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 5px solid #333333;
    vertical-align: middle;
    margin-top: -4px;
    margin-left: 5px;
}

@media screen and (max-width: 600px) {
  .Container{
    padding-top: 0;
  }
  .container {
    padding-top: 7.2rem;
  }
  .dia-box-signed {
    padding: 5.6rem 2rem 0;
    width: 100%;
    box-sizing: border-box;
  }
  .container-cn .dia-box-signed {
    padding-top: 0;
  }

  .dia-box-signed .title {
    font-size: 2rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    line-height: 3.3rem;
    margin-bottom: 3.29rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .dia-box-signed .text {
    font-size: 1.3rem;
    font-weight: 400;
    color: rgba(255,255,255,0.6);
    line-height: 1.6rem;
  }
  .dia-box-signed .text a,
  .PCcontainer .footer span{
    font-size: 1.3rem;
  }
  .text.footer {
    display: flex;
  }

  .dia-box-signed .el-form .el-form-item:last-child {
    margin-bottom: 1.2rem;
  }

  .nextStepBtn{
    margin-top: 20px;
  }

  .footer .a-black-important {
    opacity: 1;
  }
  .page-title{
    padding-bottom: 5rem;
  }
  .headImgBox{
    position: relative;
    text-align: center;
    padding-bottom: 1.25rem;
    margin-bottom: 5rem;
  }
  .headImgBox-img {
    position: relative;
    margin: 3.125rem auto 1.25rem;
    width: 9.5rem;
    height: 9.5rem;
    border-radius: 9.5rem;
    overflow: hidden;
  }
  .headImgBox-img > img{
    width: 9.5rem;
    height: 9.5rem;
    border-radius: 9.5rem;
    overflow: hidden;
  }
  .headImgBox-name {
    position: relative;
    text-align: center;
  }
  .userName > span {
    color: #333 !important;
    margin-bottom: 0.4375rem;
    font-size: 1.375rem;
  }
  .idBox {
    margin-top: 7px;
    text-align: center;
    color: #999;
    font-size: 1rem;
  }
}

@media screen and (min-width: 601px)  {

  html,
  body {
    font-size: 12px !important;
  }
  .inputBox-pc{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .dia-box-signed .title {
    font-size: 1.875rem;
    font-weight: 500;
    color: rgba(45, 42, 38, 1);
    line-height: 2.625rem;
    margin-bottom: 2rem;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  .dia-box-signed .text {
    font-size: 1.1rem;
    font-weight: 400;
    color: rgba(102, 102, 102, 1);
    line-height: 1.6rem;
  }
  .text.footer {
    font-size: 1.125rem;
  }

  .dia-box-signed .text a {
    color: #333
  }

  .dia-box-signed .el-form .el-form-item:last-child {
    margin-bottom: 1.2rem;
  }

  .el-button:active, .el-button:hover{
    color: #fff;
    background: #000;
  }
  .el-button:focus {
    background: #98989A;
    border-color: inherit;
    color: #fff;
  }
  .nextStepBtn {
    margin-top: 2.5rem;
  }
  .page-title {
    padding-bottom: 1.125rem;
  }
  .headImgBox{
    position: relative;
    text-align: center;
    padding-bottom: 1.25rem;
  }
  .headImgBox-img {
    position: relative;
    margin: 3.125rem auto 1.25rem;
    width: 7.5rem;
    height: 7.5rem;
    border-radius: 7.5rem;
    overflow: hidden;
  }
  .headImgBox-img > img{
    width: 7.5rem;
    height: 7.5rem;
    border-radius: 7.5rem;
    overflow: hidden;
  }
  .headImgBox-name {
    position: relative;
    text-align: center;
  }
  .idBox {
    margin-top: 7px;
    text-align: center;
    color: #999;
    font-size: 1rem;
  }
}