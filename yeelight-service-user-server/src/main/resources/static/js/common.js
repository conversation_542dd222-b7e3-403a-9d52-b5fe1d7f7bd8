var vendorName = document.currentScript.getAttribute("data-vendor-name");
// 添加meta
(function () {
  let lang = navigator.language.toLowerCase();
  if(lang === 'zh') lang = 'zh-cn';
  let meta = document.createElement('meta');
  meta.name = 'description';
  meta.content = lang === 'zh-cn' ? vendorName + '拥有完整的智能家居照明产品线，产品系列辐射全屋智能灯光定制、青空灯、家装照明、台上照明、氛围照明、商业照明产品等多个业务矩阵，'+vendorName+'是全球领先的智能照明品牌。' : vendorName + ' is the world-leading smart lighting brand, with in-depth exploration in smart interaction, industrial design and lighting experience. We continue to define the high standards in the lighting industry, and enjoy a portfolio of smart home lighting products, including home furnishing lights, table lights, ambiance lights and smart lighting control products. We have shipped over 11 million products worldwide, covering over 100 countries and regions in the globe. (A majority of '+vendorName+' products have been certified with CE, CB, UL, RCM and TISI to best satisfy the needs and requirements from the local market.) '+vendorName+' is committed to build a high-quality lighting environment and enable more people to enjoy the fun and convenience of smart lighting'
  document.getElementsByTagName('head')[0].appendChild(meta);
})()

// 禁止双击放大
document.documentElement.addEventListener('touchstart', function(event) {
  if (event.touches.length > 1) {
    event.preventDefault();
  }
}, { passive: false });

function checkPhoneFun(value) {
  return /^1(3|4|5|6|7|8|9)\d{9}$/.test(value)
};

function preCheck(param,sucFun) {
  let _this = param.this;
  $.ajax({
    url: '../public/captcha/pre_check',
    // url: 'http://api-dev.yeedev.com/apis/account/public/captcha/pre_check',
    type: 'POST',
    dataType: 'json',
    async: false,
    data: {
      captcha: param.captcha,
      captchaKey: param.captchaKey,
      number: param.number,
      captchaCachePrefix: 'yeelight:oauth'
    },
    success: function (result) {
      if (result.success) {
        let lastCheckKey = result.data.lastCheckKey;
        _this.lastCheckKey = lastCheckKey
        $('#lastCheckKey').val(lastCheckKey);
        (typeof sucFun == 'function') && sucFun();
      } else {
        showAlert(_this, 'error', result.msg)
        // _this.$message.error(result.msg);
      }
    },
    error: function () {
      showAlert(_this, 'error', '网络异常,请稍后重试')
      // _this.$message.error("网络异常,请稍后重试");
    }
  });
}

function dataURLtoFile(dataurl, filename) {//将base64转换为文件
  var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
  while(n--){
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, {type:mime});
}

function getCaptchaKey(phoneNumber) {
  let ticket = '';
  let param = {
    key:'',
    timestamp:''
  }

  let _this = this;
  $.ajax({
    url: '../public/mobile/captcha/key',
    type: 'post',
    dataType: 'json',
    async: false,
    success: function (result) {
      if (result.success) {
        let data = result.data;
        param.key = data.key;
        param.timestamp = data.timestamp;

        let clientTime = new Date().getTime();

        ticket = AES256.encrypt(`clientType=Web&phoneNumber=${phoneNumber}&requestId=${clientTime}&timestamp=${param.timestamp}`, param.key);
      } else {
        ticket = null;
        showAlert(_this, 'error', result.msg)
      }
    },
    error: function () {
      ticket = null;
      showAlert(_this, 'error', '网络异常,请稍后重试')
    },
  });
  return {
    ticket,
    param
  };
}

function showAlert(instant, type, title) {
  instant.$message.info({
    message: title,
    // duration: 0
  });

  let elMessage = document.querySelector('.el-message');
  elMessage.style.borderRadius = '47px';
  if(title && title.length > 20) {
    elMessage.style.borderRadius = '16px';
  }

  // if(instant.isMobile) {
  //   instant.$message.info({
  //     message: title,
  //     duration: 9999990
  //   });
  // } else {
  //   instant.alertInfo = Object.assign(instant.alertInfo, {
  //     show: true,
  //     type,
  //     title,
  //   })
  //   setTimeout(() => {
  //     instant.alertInfo.show = false;
  //     instant.alertInfo.title = '';
  //   }, 3000)
  // }
}
function handleFocus(evt) {
  let target = evt.target;
  if(target.nodeName == "SPAN"){
    target = evt.target.parentNode;
  }
  target.blur();
}
// 判断浏览器函数
function isMobile(){
    if(window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) {
      return true;
    }else if(document.documentElement.clientWidth <= 600){
      return true;
    } else {
      return  false
    }
}

function isIos() {
  let u = navigator.userAgent;
  let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
  if(isiOS) {
    return true
  };
}

function debounce(func, wait) {
  let timeout;
  return function () {
    let context = this;
    let args = arguments;
    if (timeout) clearTimeout(timeout);
    let callNow = !timeout;
    timeout = setTimeout(() => {
      timeout = null;
    }, wait)
    if (callNow) func.apply(context, args)
  }
}

function handleRegionAndLang() {
  // const regionUrl = location.host;
  let lang = navigator.language.toLowerCase();
  const langArr = ['en', 'zh-cn', 'zh-tw', 'ru', 'zh', 'zh-hk'];
  console.log(lang, 'lang');
  if(langArr.includes(lang)) {
    if(lang==='zh') lang = 'zh-cn';
    if(lang==='zh-hk') lang = 'zh-tw';
    return lang;
  } else {
    return 'en';
  }
}

function isCn() {
  let lang = handleRegionAndLang();
  return lang.includes('zh');
}

function isOverseas() {
  const host = location.host;
  // const host = location.href;
  return host.includes('sg') || host.includes('us') || host.includes('ru');
  // return host.includes('sg') || host.includes('us') || host.includes('ru');

}

function isYeelight() {
  const host = location.host;
  return host.includes('yeelight') || host.includes('yeedev');
}

function isDevelop() {
  const host = location.host;
  const noDevelop = getQueryString('noDevelop');
  return !noDevelop && (host.includes('local') || host.includes('dev') || host.includes('test'));
}


function getQueryString(name) {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  let r = location.search.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]); return null;
}

function getFormatTime(item) {
  if(!item) return '--';

  let date = new Date(item*1000);
  return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + date.getHours() + ':'  + date.getMinutes() + ':' + date.getSeconds()
}