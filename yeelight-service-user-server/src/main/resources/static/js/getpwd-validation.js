let checkPhone = (rule, value, callback) => {

    var captcha =$("input[name='captcha']").val();
    if (value && (value.length==11) && captcha) {
        addClass();
    } else {
        removeClass();
    }

    callback();
};
let checkCode = (rule, value, callback) => {
    let lang = navigator.language.toLowerCase();
    if(lang === 'zh') lang = 'zh-cn';
    let val = '';
    if(lang === 'zh-cn') {
        val = $("input[name='phoneNumber']").val();
    } else {
        val = $("#emailInput").val();
    }
    if (value && (value.length==6) && val) {
        addClass();
    } else {
        removeClass();
    }
    callback();
};
let checkPwd = (rule, value, callback) => {
    var newPassword =$("input[name='newPassword']").val();
    if (value && (value.length>=6) && (newPassword.length>=6)) {
        addClassPassword()
    }else {
        removeClassPassword()
    }
    callback();
};
let checkConfirmPwd = (rule, value, callback) => {
    var password =$("input[name='password']").val();
    if (value && (value.length>=6) && (password.length>=6)) {
        addClassPassword()
    }else {
        removeClassPassword()
    }
    callback();
};

function addClass() {
    $(".dia-box-retpwd .nextStepBtn").attr("disabled", false);
    $(".dia-box-retpwd .nextStepBtn").addClass("isOperate");
    $(".dia-box-retpwd .nextStepBtn").removeClass("is-disabled");
}
function removeClass() {
    if ($(".dia-box-retpwd .nextStepBtn").hasClass("isOperate")) {
        $(".dia-box-retpwd .nextStepBtn").attr("disabled", true);
        $(".dia-box-retpwd .nextStepBtn").removeClass("isOperate");
        $(".dia-box-retpwd .nextStepBtn").addClass("is-disabled");
    }
}

function addClassPassword() {
    $(".dia-box-code .nextStepBtn").attr("disabled", false);
    $(".dia-box-code .nextStepBtn").addClass("isOperate");
    $(".dia-box-code .nextStepBtn").removeClass("is-disabled");
}

function removeClassPassword() {
    if ($(".dia-box-code .nextStepBtn").hasClass("isOperate")) {
        $(".dia-box-code .nextStepBtn").attr("disabled", true);
        $(".dia-box-code .nextStepBtn").removeClass("isOperate");
        $(".dia-box-code .nextStepBtn").addClass("is-disabled");
    }
}