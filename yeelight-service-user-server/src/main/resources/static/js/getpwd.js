(function (doc, win) {
  var docEl = doc.documentElement;
  var resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';
  var recalc = function () {
    var clientWidth = docEl.clientWidth;
    if (!clientWidth) {
      return false;
    } else {
      docEl.style.fontSize = 10 * (clientWidth / 375) + 'px';
      // if (clientWidth <= 414) {
      //   docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
      // } else {
      //   docEl.style.fontSize = 3.125 * (clientWidth / 375) + "px";
      // }
    }
  };
  if (!doc.addEventListener) return;
  win.addEventListener(resizeEvt, recalc, false);
  doc.addEventListener('DOMContentLoaded', recalc, false);
})(document, window);

new Vue({
  el: '.container',
  data() {
    if (errorMsg != null) {
      showAlert(this, 'error', errorMsg);
    }
    let lang = handleRegionAndLang();
    this.langMsg = i18n[lang].findPwd;

    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.langMsg.pwdEmpty));
      } else if (this.containSpecial(value) || this.checkChinese(value)) {
        callback(new Error(this.langMsg.pwdFormatCheck));
      } else {
        if (this.retpwdForm.confirmPwd !== '') {
          this.$refs.retpwdForm.validateField('confirmPwd');
        }
        callback();
      }
    };
    var validateConfirmPwd = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.langMsg.confirmPwdDiff));
      } else if (value !== this.retpwdForm.pwd) {
        callback(new Error(this.langMsg.confirmPwdDiff));
      } else {
        callback();
      }
    };
    return {
      isIos: isIos(),
      isMobile: isMobile(),
      lang: lang,
      retpwdVis: true,
      codeVis: false,
      setPwdVis: false,
      msg: '发送验证码',
      count: 60,
      lastCheckKey: '',
      retpwdForm: {
        tel: '',
        email: '',
        code: '',
        pwd: '',
        confirmPwd: '',
      },
      retpwdFormRules: {
        tel: [
          { required: true, message: '手机号码不能为空', trigger: 'blur' },
          {
            pattern: /^1(3|4|5|6|7|8|9)\d{9}$/,
            message: lang === 'zh-cn' ? '手机号格式不对' : 'Incorrect format of mobile phone',
            trigger: 'blur',
          },
        ],
        email: [
          { required: true, message: this.langMsg.emailEmpty, trigger: 'blur' },
          { type: 'email', message: this.langMsg.emailErrorMsg, trigger: ['blur'] },
        ],
        code: [
          { required: true, message: this.langMsg.codeEmpty, trigger: 'blur' },
          // { validator: checkCode, trigger: "change" }
        ],
        pwd: [
          { min: 6, message: this.langMsg.pwdError, trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' },
        ],
        confirmPwd: [{ validator: validateConfirmPwd, trigger: 'blur' }],
      },
      alertInfo: {
        show: false,
        type: 'error',
        title: '',
        top: 0,
      },
      langMsg: {
        findPwd: {},
      },
      pwdFlag: false,
      pwdRepeteFlag: false,
    };
  },
  computed: {
    isOverseas() {
      return isOverseas();
    },
    isYeelight() {
      return isYeelight();
    },
    isDevelop() {
      return isDevelop()
    },
    pwdType() {
      return this.pwdFlag ? 'text' : 'password';
    },
    pwdRepeteType() {
      return this.pwdRepeteFlag ? 'text' : 'password';
    },
  },
  mounted() {
    this.langMsg = i18n[this.lang].findPwd;
    this.msg = this.langMsg.sendMsg;
    document.title = this.langMsg.pageTitle;
    let formBox = document.querySelector('.form-box');
    this.alertInfo.top = formBox && formBox.offsetTop - 190;
    this.getPhoneCode = debounce(this.getPhoneCode, 1000);
    this.getEmailCode = debounce(this.getEmailCode, 1000);
  },
  methods: {
    nextStep(evt) {
      handleFocus(evt);
      this.$refs['retpwdForm'].validate(valid => {
        if (valid) {
          var params = {
            this: this,
            captcha: $("input[name='captcha']").val(),
            captchaKey: $('#smsCaptchaKey').val(),
            number:
              this.lang === 'zh-cn' && !this.isOverseas
                ? $("input[name='phoneNumber']").val()
                : $("input[name='email']").val(),
          };
          preCheck(params, () => {
            // 发送验证码
            this.codeVis = true;
            this.retpwdVis = false;
            this.langMsg.title = this.langMsg.subTitle;
            // this.langMsg.subTitle = this.lang === 'zh-cn' ? this.langMsg.subTitle : '';
          });
        }
      });
    },

    // 修改密码
    updatePwd(evt) {
      handleFocus(evt);
      let _this = this;
      // if(this.retpwdForm.pwd != this.retpwdForm.confirmPwd){
      //   return  _this.$message.error("两次输入密码不同，请确认输入同一密码");
      // }
      // if(this.containSpecial(this.retpwdForm.pwd)||this.checkChinese(this.retpwdForm.pwd)){
      //   return _this.$message.error("密码中不能包含特殊符号与中文");
      // }
      this.$refs['retpwdForm'].validate(valid => {
        if (valid) {
          let data = {
            captcha: 'CHECK_LAST_RESULT',
            captchaKey: this.lastCheckKey || $('#lastCheckKey').val(),
            password: this.retpwdForm.pwd,
          };
          let url = '';
          // '../public/reset'
          if (this.lang === 'zh-cn' && !this.isOverseas) {
            data.phoneNumber = this.retpwdForm.tel;
            url = '../public/reset';
            // url = 'http://api-dev.yeedev.com/apis/account/public/reset';
          } else {
            data.email = this.retpwdForm.email;
            url = '../public/resetByEmail';
            // url = 'http://api-dev.yeedev.com/apis/account/public/resetByEmail';
          }
          $.ajax({
            url,
            type: 'post',
            contentType: 'application/json;charset=utf-8',
            dataType: 'json',
            async: false,
            data: JSON.stringify(data),
            success: function (result) {
              if (result.success) {
                let data = result.data;
                showAlert(_this, 'success', _this.lang === 'zh-cn' ? '修改成功' : 'Success');
                setTimeout(function () {
                  location.href = '../oauth/login';
                }, 500);
              } else {
                showAlert(_this, 'error', result.msg);
              }
            },
            error: function () {
              showAlert(
                _this,
                'error',
                _this.lang === 'zh-cn' ? '网络异常,请稍后重试' : 'Network error, please try again later'
              );
            },
          });
        }
      });
    },
    handleSendCode() {
      if (this.lang === 'zh-cn' && !this.isOverseas) {
        this.getPhoneCode();
      } else {
        this.getEmailCode();
      }
    },
    getEmailCode() {
      let that = this;
      let email = this.retpwdForm.email;
      let reg = /^[A-Za-z0-9-._]+@[A-Za-z0-9-]+(.[A-Za-z0-9]+)*(.[A-Za-z]{2,6})$/;
      // let valid = ''
      // this.$refs.retpwdForm.validateField('email', res => {
      //   console.log(res)
      //   valid = res;
      // })
      // if(valid) return;
      if (email === '') {
        showAlert(this, 'error', this.langMsg.emailEmpty);
        return false;
      } else if (!reg.test(email)) {
        showAlert(this, 'error', this.langMsg.emailErrorMsg);
        return false;
      }
      let timer = setInterval(() => {
        if (this.count == 0) {
          this.msg = this.langMsg.sendMsg;
          this.count = 60;
          if (!$('.dia-box-retpwd .gainCode').hasClass('canOperate')) {
            $('.dia-box-retpwd .gainCode').addClass('canOperate');
          }
          $('.dia-box-retpwd .gainCode').attr('disabled', false);
          $('.dia-box-retpwd .gainCode').css({
             color: 'rgba(26,26,26, 1)',
            'border-color': 'rgba(45, 42, 38, 1)',
          });
          clearInterval(timer);
        } else {
          this.count--;
          this.msg = `${this.langMsg.reSendMsg}(${this.count})`;
          $('.dia-box-retpwd .gainCode').attr('disabled', true);
          $('.dia-box-retpwd .gainCode').css({
             color: 'rgba(26,26,26,.5)',
            'border-color': '#DCDFE6',
          });
        }
      }, 1000);

      $.ajax({
        url: '../public/email/captcha',
        // url: 'http://api-dev.yeedev.com/apis/account/public/email/captcha',
        type: 'get',
        dataType: 'json',
        data: { email: email, exist: false },
        success: function (result) {
          if (result.success) {
            let data = result.data;
            $('#smsCaptchaKey').val(data.captchaKey);
          } else {
            showAlert(that, 'error', result.msg);
            clearInterval(timer);
          }
        },
        error: function () {
          showAlert(that, 'error', 'Network error, please try again later');
          clearInterval(timer);
        },
      });
    },
    getPhoneCode() {
      let _this = this;
      let phoneNumber = this.retpwdForm.tel;
      let valid = '';
      this.$refs.retpwdForm.validateField('tel', res => {
        valid = res;
      });
      if (valid) return;
      clearInterval(timer);
      var timer = setInterval(function () {
        if (_this.count == 0) {
          _this.msg = '获取验证码';
          _this.count = 60;
          if (!$('.dia-box-retpwd .gainCode').hasClass('canOperate')) {
            $('.dia-box-retpwd .gainCode').addClass('canOperate');
          }
          $('.dia-box-retpwd .gainCode').attr('disabled', false);
          $('.dia-box-retpwd .gainCode').css({
            color: 'rgba(26,26,26, 1)',
            'border-color': 'rgba(45, 42, 38, 1)',
          });
          clearInterval(timer);
        } else {
          _this.count--;
          _this.msg = '重新发送(' + _this.count + 's)';
          $('.dia-box-retpwd .gainCode').attr('disabled', true);
          $('.dia-box-retpwd .gainCode').css({
            color: 'rgba(26,26,26,.5)',
            'border-color': '#DCDFE6',
          });
        }
      }, 1000);

      var res = false;

      let resParam = getCaptchaKey(phoneNumber);
      if(!resParam.ticket){
        clearInterval(timer);
        return;
      }


      $.ajax({
        url: '../public/mobile/captcha',
        type: 'post',
        contentType: 'application/json;charset=utf-8',
        dataType: 'json',
        async: false,
        data: JSON.stringify({ phoneNumber: phoneNumber, exist: false, ticket: resParam.ticket, timestamp: resParam.param.timestamp }),
        success: function (result) {
          if (result.success) {
            let data = result.data;
            $('#smsCaptchaKey').val(data.captchaKey);
            res = true;
          } else {
            showAlert(_this, 'error', result.msg);
            clearInterval(timer);
          }
        },
        error: function () {
          showAlert(_this, 'error', '网络异常,请稍后重试');
          clearInterval(timer);
        },
      });
      return res;
    },
    containSpecial(str) {
      var containSpecial = RegExp(
        /[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\,)(\/)(\<)(\>)(\?)(\)]+/
      );
      return containSpecial.test(str);
    },
    checkChinese(val) {
      var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
      return reg.test(val);
    },
    goToLogin() {
      location.href = '../oauth/login'
    }
  },
});
