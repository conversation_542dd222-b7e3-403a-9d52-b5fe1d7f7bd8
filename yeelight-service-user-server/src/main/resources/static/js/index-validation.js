let checkPhone = (rule, value, callback) => {
  if(value){
    var password =$("input[name='password']").val() || $("input[name='captcha']").val();
    if( password.length){
      addClass();
    }
  }else {
    removeClass();
  }
  callback();
};
let checkImgCode = (rule, value, callback) => {
  if(value){
    var username =$("input[name='username']").val();
    if( username.length){
      addClass();
    }
  }else {
    $(".container .loginByPwd-box .nextStep").removeClass("isOperate");
  }
  callback();
};
let checkPwd = (rule, value, callback) => {
  if(value){
    var username =$("input[name='username']").val();
    if( username.length){
     addClass();
    }
  }else {
    removeClass();
  }
  callback();
};

let checkPhoneByCode = (rule, value, callback) => {
  if (value) {
    var captcha =$("input[name='captcha']").val();
    captcha.length && addClassSMS();
  } else {
    removeClassSMS();
  }
  callback();
};
let checkCode = (rule, value, callback) => {
  if (value && value.length==6) {
    var phoneNumber =$("input[name='phoneNumber']").val();
    phoneNumber.length && addClassSMS();
  }  else {
    removeClassSMS();
  }
  callback();
};

// PC端
let checkPhonePCPassword = (rule, value, callback) => {
  var password =$("input[id='FormByPwdPCPwd']").val() || $("input[id='FormByPwdPCImgCode']").val();
  if (value && password) {
    addClassSMSPCPassword();
  }else {
    removeClassSMSPCPassword();
  }
  callback();
};

let checkImgCodePC = (rule, value, callback) => {
  var FormByPwdPCTel =$("input[id='FormByPwdPCTel']").val() ;
  if (value && FormByPwdPCTel) {
    addClassSMSPCPassword();
  }else {
    removeClassSMSPCPassword();
  }
  callback();
};
let checkPwdPC = (rule, value, callback) => {
  let val = ''
  // let lang = navigator.language;
  let lang = 'en'
  if(lang === 'zh-cn') {
    val = $("input[id='FormByPwdPCTel']").val();
  } else {
    val = $("input[id='FormByPwdPCEmail']").val();
  }
  if (value && val) {
    addClassSMSPCPassword();
  }else {
    removeClassSMSPCPassword();
  }
  callback();
};

let checkPhonePC = (rule, value, callback) => {
  var captcha =$("input[id='captchaInputPC']").val();
  if (value && (value.length==11) && captcha) {
    addClassSMSPC();
  }else {
    removeClassSMSPC();
  }
  callback();
};
let checkCodePC = (rule, value, callback) => {
  var phoneInputPC =$("input[id='phoneInputPC']").val();
  if (value && (value.length==6) && phoneInputPC) {
    addClassSMSPC();
  }else {
    removeClassSMSPC();
  }
  callback();
};

function addClass() {
  $(".container .loginByPwd-box .nextStep").attr("disabled", false);
  if (!$(".container .loginByPwd-box .nextStep").hasClass("isOperate")) {
    $(".container .loginByPwd-box .nextStep").addClass("isOperate");
  }
}

function removeClass() {
  $(".container .loginByPwd-box .nextStep").removeClass("isOperate");
  $(".container .loginByPwd-box .nextStep").attr("disabled", true);
}

function addClassSMS() {
  $(".container .loginByCode-box .nextStep").attr("disabled", false);
  if (!$(".container .loginByCode-box .nextStep").hasClass("isOperate")) {
    $(".container .loginByCode-box .nextStep").addClass("isOperate");
  }
}

function removeClassSMS() {
  $(".container .loginByCode-box .nextStep").attr("disabled", true);
  $(".container .loginByCode-box .nextStep").removeClass("isOperate");

}

function addClassSMSPCPassword() {
  $(".loginBtnPCSMSPassword").attr("disabled", false);
  $(".loginBtnPCSMSPassword").removeClass("is-disabled");
}

function removeClassSMSPCPassword() {
  $(".loginBtnPCSMSPassword").attr("disabled", true);
  $(".loginBtnPCSMSPassword").addClass("is-disabled", true);
}

function addClassSMSPC() {
  $(".loginBtnPCSMS").attr("disabled", false);
  $(".loginBtnPCSMS").removeClass("is-disabled");
}

function removeClassSMSPC() {
  $(".loginBtnPCSMS").attr("disabled", true);
  $(".loginBtnPCSMS").addClass("is-disabled", true);
}