new Vue({
    el: '.Container',
    data() {
        let lang = handleRegionAndLang();
        const langMsg = i18n[lang].login;

        return {
            lang,
            langMsg,
            isMobile: isMobile(),
            isIos: isIos(),
            msg: langMsg.sendMsg,
            readOnlyFlag: true,
            count: 60,
            btnDisabled: true,
            showNextByPwd: true,
            showNextByCode: true,
            showBoxByPwd: {
                tel: true,
                imgCode: false,
                pwd: true,
            },
            showBoxByCode: {
                tel: true,
                code: false,
            },
            showbox: false,
            FormByPwd: {
                tel: '',
                pwd: '',
                imgCode: '',
            },
            FormByCode: {
                email: '',
                tel: '',
                code: '',
            },
            FormByEmailCode: {
                email: '',
                code: '',
            },
            FormByEmailCodeRules: {
                email: [
                    { required: true, message: langMsg.emailEmpty, trigger: 'blur' },
                    { type: 'email', message: langMsg.emailErrorMsg, trigger: ['blur'] },
                ],
                code: [{ required: true, message: langMsg.codeEmpty, trigger: 'blur' }],
            },
            FormByPwdRules: {
                tel: [
                    { required: true, message: langMsg.accountEmpty, trigger: 'blur' },
                    { pattern: /^1\d{10}$|(\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)$/, message: langMsg.phoneAndEmail, trigger: "blur"},
                ],
                pwd: [
                    { required: true, message: langMsg.pwdEmpty, trigger: 'blur' },
                    { min: 6, message: langMsg.pwdError, trigger: 'blur' },
                ],
                imgCode: [{ required: imgCodeRequired, validator: checkImgCode, trigger: 'change' }],
            },
            FormByCodeRules: {
                tel: [
                    { required: true, message: langMsg.mobileEmpty, trigger: 'blur' },
                    { pattern: /^1\d{10}$/, message: langMsg.mobileError, trigger: 'blur' },
                ],
                code: [
                    { required: true, message: langMsg.codeEmpty, trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: langMsg.codeError, trigger: 'blur' },
                ],
            },

            // PC端
            pwdPCVisiable: false,
            FormByPwdPC: {
                tel: '',
                pwd: '',
                email: '',
                imgCode: '',
            },
            // FormByCodePC: {
            //   email: '',
            //   tel: '',
            //   code: '',
            // },
            FormByPwdRulesPC: {
                tel: [
                    { required: true, message: langMsg.accountEmpty, trigger: 'blur' },
                    { pattern: /^1(3|4|5|6|7|8|9)\d{9}$|(\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)$/, message: langMsg.phoneAndEmail, trigger: "blur"},
                ],
                email: [
                    { required: true, message: langMsg.emailEmpty, trigger: 'blur' },
                    { type: 'email', message: langMsg.emailErrorMsg, trigger: 'blur' },
                ],
                imgCode: [{ required: imgCodeRequired, validator: checkImgCodePC, trigger: 'change' }],
                pwd: [
                    { required: true, message: langMsg.pwdEmpty, trigger: 'blur' },
                    // { validator: checkPwdPC, trigger: ['blur', 'change'] }
                ],
            },
            FormByCodeRulesPC: {
                email: [
                    { required: true, message: langMsg.emailEmpty, trigger: 'blur' },
                    { type: 'email', message: langMsg.emailErrorMsg, trigger: 'blur' },
                ],
                tel: [
                    { required: true, message: langMsg.mobileEmpty, trigger: 'blur' },
                    { pattern: /^1\d{10}$/, message: langMsg.mobileError, trigger: "blur"},
                ],
                code: [
                    { required: true, message: langMsg.codeEmpty, trigger: 'blur' },
                    { pattern: /^\d{6}$/, message: langMsg.codeError, trigger: 'blur' },
                ],
            },
            alertInfo: {
                show: false,
                type: 'error',
                title: '',
                top: 0,
            },
            pwdFlag: false,
            emailLogin: true,

            showPopupFlag: false,
            registerAccount:'',

            isPhone: true,
        };
    },
    computed: {
        referrer() {
            let referUrl = document.referrer.split('://')[1];
            referUrl = referUrl ? referUrl.substring(0, referUrl.length ) : '';
            if (!referUrl) return 'default';
            if (referUrl.match(/^portal(.)*\.(yeelight|yeedev|smartlicht-sys)\.com/)) {
                return 'portal';
            } else if (referUrl.match(/\.(yeelight|yeedev|smartlicht-sys)\.com/)) {
                return 'default';
            } else {
                return 'thirdParty';
            }
        },
        pwdType() {
            return this.pwdFlag ? 'text' : 'password';
        },
        isOverseas() {
            return isOverseas();
            // const host = location.host;
            // console.log(location.href, 111)
            // return host.includes('sg') || host.includes('us') || host.includes('ru');
        },
        isYeelight() {
            return isYeelight();
        },
        isDevelop() {
            return isDevelop()
        },
    },
    mounted() {
        document.title = this.langMsg.pageTitle;
        this.msg = this.langMsg.sendMsg;
        let formBox = document.querySelector('.form-box');
        this.alertInfo.top = formBox && formBox.offsetTop - 115;
        if (errorMsg != null) {
            if(errorMsg.includes('未注册')){
                this.showPopupFlag = true;
                this.registerAccount = sessionUsername;
            }else{
                showAlert(this, 'error', errorMsg);
            }

        }
        this.getCodePC = debounce(this.getCodePC, 1000);
        this.getCode = debounce(this.getCode, 1000);

        window.addEventListener('resize', () => {
            if (document.documentElement.clientWidth <= 600) {
                this.isMobile = true;
            } else {
                this.isMobile = false;
            }
        });

        let referUrl = document.referrer.split('://')[1];
        referUrl = referUrl || '';
        if (referUrl.match(/intelligent-recommender/)) {
            this.showbox = false;
        }
        // 第三方登录，默认账号密码登陆
        if (this.referrer === 'thirdParty') {
            this.showbox = true;
            this.pwdPCVisiable = true;
        }
        const type = getQueryString('type');
        if(type === 'pwd') {
            this.showbox = true;
            this.pwdPCVisiable = true;
            const acount = getQueryString('account');
            if(acount === 'demo'){
                this.FormByPwd.tel = '<EMAIL>';
                this.FormByPwd.pwd = '********';
                this.FormByPwdPC.tel = '<EMAIL>';
                this.FormByPwdPC.pwd = '********';
            }
        }
    },
    methods: {
        handleCloseAlert() {
            this.alertInfo.show = false;
        },
        switchAccountType() {
            this.isPhone = !this.isPhone;
            this.$refs.codeForm.resetFields();
        },
        nextStepByCode() {
            if (this.FormByCode.tel) {
                this.showBoxByCode.tel = false;
                this.showBoxByCode.code = true;
                this.showNextByCode = false;
            }
            $('.container .loginByCode-box .nextStep').attr('disabled', true);
            $('.container .loginByCode-box .nextStep').removeClass('isOperate');
            $('.container .loginByCode-box .nextStep').addClass('notOperate');
        },
        changeCaptcha() {
            let captchaImg = document.getElementById('captchaImg');
            captchaImg.src = '../public/captcha.jpg?t=' + new Date().getTime();
        },
        changeCaptchaPC() {
            let captchaImg = document.getElementById('captchaImgPC');
            captchaImg.src = '../public/captcha.jpg?t=' + new Date().getTime();
        },
        getEmailCode() {
            let that = this;
            let email = this.FormByCode.email;
            let reg = /^[A-Za-z0-9-._]+@[A-Za-z0-9-]+(.[A-Za-z0-9]+)*(.[A-Za-z]{2,6})$/;
            if (email === '') {
                showAlert(this, 'error', this.langMsg.emailEmpty);
                return false;
            } else if (!reg.test(email)) {
                showAlert(this, 'error', this.langMsg.emailErrorMsg);
                return false;
            }
            let timer = setInterval(() => {
                if (this.count == 0) {
                    this.msg = this.langMsg.sendMsg;
                    this.count = 60;
                    $('.gainCode').attr('disabled', false);
                    $('.gainCode').css({
                        color: 'rgba(26,26,26, 1)',
                        'border-color': 'rgba(45, 42, 38, 1)',
                    });
                    clearInterval(timer);
                } else {
                    this.count--;
                    this.msg = `${this.langMsg.reSendMsg}(${this.count})`;
                    $('.gainCode').attr('disabled', true);
                    $('.gainCode').css({
                        color: 'rgba(26,26,26,.5)',
                        'border-color': '#DCDFE6',
                    });
                }
            }, 1000);

            $.ajax({
                url: '../public/email/captcha',
                type: 'get',
                dataType: 'json',
                data: { email: email, exist: false },
                success: function (result) {
                    if (result.success) {
                        let data = result.data;
                        $('#smsCaptchaKey').val(data.captchaKey);
                    } else {
                        clearInterval(timer);
                        if(result.msg.includes('未注册')){
                            that.showPopupFlag = true;
                            that.registerAccount = email;
                            return;
                        }
                        showAlert(that, 'error', result.msg);

                    }
                },
                error: function () {
                    showAlert(that, 'error', 'Network error, please try again later');
                    clearInterval(timer);
                },
            });
        },
        getCode() {
            if (!this.isPhone) {
                this.getEmailCode();
                return;
            }
            let _this = this;
            let phoneNumber = this.FormByCode.tel;

            let valid = '';
            this.$refs.codeForm.validateField('tel', res => {
                valid = res;
            });
            if (valid) return;

            clearInterval(timer);

            var timer = setInterval(function () {
                if (_this.count == 0) {
                    _this.msg = _this.langMsg.sendMsg;
                    _this.count = 60;
                    $('.gainCode').attr('disabled', false);
                    $('.gainCode').css({
                        color: 'rgba(26,26,26, 1)',
                        'border-color': 'rgba(45, 42, 38, 1)',
                    });
                    clearInterval(timer);
                } else {
                    _this.count--;
                    _this.msg = '重新发送(' + _this.count + 's)';
                    $('.gainCode').attr('disabled', true);
                    $('.gainCode').css({
                        color: 'rgba(26,26,26,.5)',
                        'border-color': '#DCDFE6',
                    });
                }
            }, 1000);

            let resParam = getCaptchaKey(phoneNumber);
            if(!resParam.ticket){
                clearInterval(timer);
                return;
            }

            $.ajax({
                url: '../public/mobile/captcha',
                type: 'post',
                contentType: 'application/json;charset=utf-8',
                dataType: 'json',
                async: true,
                data: JSON.stringify({ phoneNumber: phoneNumber, exist: false, ticket: resParam.ticket, timestamp: resParam.param.timestamp }),
                success: function (result) {
                    if (result.success) {
                        let data = result.data;
                        $('#smsCaptchaKey').val(data.captchaKey);
                    } else {
                        clearInterval(timer);
                        if(result.msg.includes('未注册')){
                            _this.showPopupFlag = true;
                            _this.registerAccount = phoneNumber;
                            return
                        }
                        showAlert(_this, 'error', result.msg);
                    }
                },
                error: function () {
                    showAlert(_this, 'error', '网络异常,请稍后重试');
                    clearInterval(timer);
                },
            });

        },

        loginByEmailCode(evt) {
            handleFocus(evt);
            this.$refs['emailForm'].validate(valid => {
                if (valid) {
                    console.log($('#emailForm'));
                    $('#emailForm').submit();
                } else {
                    return false;
                }
            });
        },
        loginByPwd(evt) {
            handleFocus(evt);
            this.$refs['pwdForm'].validate(valid => {
                if (valid) {
                    // 登录
                    $('#pwdForm').submit();
                } else {
                    return false;
                }
            });
        },
        loginByCode(evt) {
            handleFocus(evt);
            let _this = this;
            this.$refs['codeForm'].validate(valid => {
                if (!valid) return false
                // 登录
                $('#codeForm').submit();
            });
        },
        toggleLogin(event) {
            if (event == 1 && !this.showbox) {
                // this.FormByPwd.tel = "";
                // this.FormByPwd.imgCode = "";
                // this.FormByPwd.pwd = "";
                this.$refs.pwdForm.resetFields();
                this.showbox = true;
                this.showNextByCode = true;
            }
            if (event == 2 && this.showbox) {
                // this.FormByCode.tel = "";
                // this.FormByCode.code = "";
                this.$refs.codeForm.resetFields();
                this.showbox = false;
                this.showNextByPwd = true;
            }
        },
        // PC端
        loginByPwdPC(evt) {
            handleFocus(evt);
            this.$refs['pwdFormPC'].validate(valid => {
                if (valid) {
                    // 登录
                    $('#pwdFormPC').submit();
                } else {
                    return false;
                }
            });
        },
        getCodePC() {
            if (!this.isPhone) {
                this.getEmailCode();
                return;
            }
            let _this = this;
            // let phoneNumber = document.getElementById('phoneInputPC').value;
            let phoneNumber = this.FormByCode.tel;
            let valid = '';
            this.$refs.codeFormPC.validateField('tel', res => {
                valid = res;
            });
            if (valid) return;

            clearInterval(timer);
            var timer = setInterval(function () {
                if (_this.count == 0) {
                    _this.msg = _this.langMsg.sendMsg;
                    _this.count = 60;
                    $('.form-box-code .getcode').attr('disabled', false);
                    $('.form-box-code .getcode').css({
                        color: 'rgba(26,26,26, 1)',
                        'border-color': 'rgba(45, 42, 38, 1)',
                    });
                    clearInterval(timer);
                } else {
                    _this.count--;
                    _this.msg = '重新发送(' + _this.count + 's)';
                    $('.form-box-code .getcode').attr('disabled', true);
                    $('.form-box-code .getcode').css({
                        color: 'rgba(26,26,26,.5)',
                        'border-color': '#DCDFE6',
                    });
                }
            }, 1000);

            let resParam = getCaptchaKey(phoneNumber);
            if(!resParam.ticket){
                clearInterval(timer);
                return;
            }

            $.ajax({
                url: '../public/mobile/captcha',
                type: 'post',
                contentType: 'application/json;charset=utf-8',
                dataType: 'json',
                async: true,
                data: JSON.stringify({ phoneNumber: phoneNumber, exist: false, ticket: resParam.ticket, timestamp: resParam.param.timestamp }),
                success: function (result) {
                    if (result.success) {
                        let data = result.data;
                        $('#smsCaptchaKeyPC').val(data.captchaKey);
                    } else {
                        clearInterval(timer);
                        if(result.msg.includes('未注册')){
                            _this.showPopupFlag = true;
                            _this.registerAccount = phoneNumber;
                            return
                        }
                        showAlert(_this, 'error', result.msg);
                    }
                },
                error: function () {
                    showAlert(_this, 'error', '网络异常,请稍后重试');
                    clearInterval(timer);
                },
            });
        },
        toggleLoginPC(type) {
            if (type == 1 && !this.pwdPCVisiable) {
                this.pwdPCVisiable = !this.pwdPCVisiable;
                this.$refs.codeForm.resetFields();
            }

            if (type == 2 && this.pwdPCVisiable) {
                this.pwdPCVisiable = !this.pwdPCVisiable;
                this.$refs.pwdFormPC.resetFields();
            }
        },
        closePopup() {
            this.showPopupFlag = false;
        },
        doPopupFun() {
            this.showPopupFlag = false;
            location.href = `../oauth/register?account=${this.registerAccount}`;
        }
    },
});
function byte2Hex(b) {
    if (b < 0x10) return '0' + b.toString(16);
    else return b.toString(16);
}
function bin2hex(str) {
    var result = '';
    for (i = 0; i < str.length; i++) {
        var c = str.charCodeAt(i);
        result += byte2Hex((c >> 8) & 0xff); // 高字节
        result += byte2Hex(c & 0xff); // 低字节
    }
    return result;
}
function getUUid() {
    var canvas = document.createElement('canvas');
    var ctx = canvas.getContext('2d');
    var txt = window.location.origin;
    ctx.textBaseline = 'top';
    ctx.font = "14px 'Arial'";
    ctx.textBaseline = 'yeelight';
    ctx.fillStyle = '#f60';
    ctx.fillRect(125, 1, 62, 20);
    ctx.fillStyle = '#069';
    ctx.fillText(txt, 2, 15);
    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
    ctx.fillText(txt, 4, 17);
    var b64 = canvas.toDataURL().replace('data:image/png;base64,', '');
    var bin = atob(b64);
    var crc = bin2hex(bin.slice(-16, -12));
    return crc;
}
function getDomain() {
    let arr = window.location.hostname.split('.');
    arr.shift();
    return `.${arr.join('.')}`;
}
function setCookie(name, value) {
    var exp = new Date();
    exp.setTime(exp.getTime() + 90 * 24 * 60 * 60 * 1000);
    document.cookie = name + '=' + escape(value) + ';expires=' + exp.toGMTString() + ';domain=' + getDomain() + ';path=/';
}
setCookie('brower_uuid', getUUid());
