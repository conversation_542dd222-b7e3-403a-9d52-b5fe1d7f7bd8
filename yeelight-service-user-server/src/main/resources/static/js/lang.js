var vendorName = document.currentScript.getAttribute("data-vendor-name");
const i18n = {
  'zh-cn': {
    login: {
      pageTitle: vendorName + ' 登录',
      forget: '忘记密码？',
      tip: '还没有账号？',
      login: '登录',
      title: '欢迎回来',
      sendMsg: '发送验证码',
      reSendMsg: '重新发送',
      pwdEmpty: '请输入密码',
      email: '邮箱',
      password: '密码',
      emailEmpty: '邮箱不能为空',
      emailErrorMsg: '请输入正确的邮箱地址',
      pwdEmpty: '密码不能为空',
      noAccountTip: '还没有账号？',
      noAccount: '注册',
      pwdLogin: '密码登录',
      code: '验证码',
      codeEmpty: '验证码不能为空',
      placeholder: '手机号/邮箱',
      accountEmpty: '手机号/邮箱不能为空',
      passwordPlaceholder: '密码',
      phone: '手机号',
      mobileEmpty: '手机号码不能为空',
      mobileError: '请输入正确的手机号码',
      codeError: '您输入的验证码不正确',
      pwdError: '登录密码最少6位',
      otherLoginWay: '其他登录方式',
      emailLogin: '邮箱验证码登录',
      passwordLogin: '密码登录',

      pageTitle: vendorName + '账号登录',
      register: '注册账号',
      mobileLogin: '验证码登录',
      popupRegister: '账号不存在',
      popupDesc:'此账号暂未注册，点击“去注册”立即注册新账号',
      popupRegisterBtn: '去注册',
      sendMsg: '发送验证码',

      phoneAndEmail:'手机号或邮箱格式错误',
      useEmail: '使用邮箱登录',
      usePhone: '使用手机号登录',
    },
    findPwd: {
      pageTitle: '找回密码',
      title: '忘记密码?',
      subTitle: '修改密码',
      tip: '已有账号？',
      login: '直接登录',
      email: '邮箱',
      capture: '验证码',
      password: '密码',
      phoneEmpty: '手机号码不能为空',
      phoneErrorMsg: '请输入正确的手机号码',
      emailEmpty: '请输入您的邮箱',
      emailErrorMsg: '请输入正确的邮箱地址',
      pwdEmpty: '密码不能为空',
      captchaPlaceholder: '短信验证码',
      sendMsg: '发送验证码',
      reSendMsg: '重新发送',
      next: '下一步',
      tipNewPwdPlaceholder: '请输入新密码',
      newPwdPlaceholder: '新密码',
      confirmPwdPlaceholder: '再次输入密码',
      confirmPwdBtn: '确认',
      confirmPwdDiff: '两次输入密码不一致',
      pwdFormatCheck: '密码中不能包含特殊符号与中文',
      pwdError: '密码最少6位',
      codeEmpty: '验证码不能为空',
      resetPassword: '重置密码',
      useEmail: '使用邮箱找回',
      usePhone: '使用手机号找回',
    },
    register: {
      pageTitle: vendorName + ' 账号注册',
      title: '注册'+vendorName+'账号',
      tip: '已有账号？',
      login: '登录',
      checkboxLabel: '已同意' + vendorName,
      sendMsg: '发送验证码',
      reSendMsg: '重新发送',
      and: '和',
      next: '注册',
      mobileNext: '下一步',
      email: '邮箱',
      password: '密码',
      emailEmpty: '请输入您的邮箱',
      emailErrorMsg: '请输入正确的邮箱地址',
      phoneEmpty: '请输入您的手机号',
      pwdEmpty: '密码不能为空',
      username: '用户名',
      userEmpty: '用户名不能为空',
      code: '验证码',
      codeEmpty: '验证码不能为空',
      popupTitle:'用户协议&隐私政策',
      popupDesc:'请您务必审慎、仔细阅读《用户协议》和《隐私政策》相关条款。当您点击“同意并注册”即表示您已充分阅读、理解并接受 《用户协议》及《隐私政策》。',
      popupBtn:'同意并注册',
      agreement: '用户协议',
      privacy: '隐私政策',
      pwdError: '密码最少6位',
      pwdFormatCheck: '密码中不能包含特殊符号与中文',
      phone: '手机号',
      useEmail: '使用邮箱注册',
      usePhone: '使用手机号注册',
    },
    grant: {
      pageTitle: vendorName + ' 账号授权',
      title: '授权'+vendorName+'账号',
      confirm: '确认授权',
      login: '切换账号',
      tipTitle: '授权后该应用获得以下权限：',
      firstTip: '访问您的个人信息',
      secondTip: '访问您的头像/用户名等信息',
      thirdTip: '此后权限条件从后端接口获取',
    },
    personal: {
      pageTitle: vendorName + ' 个人中心',
      title: '账号设置',
      phoneNumber: '手机号',
      nickName: '昵称',
      email: '邮箱',
      captcha: '验证码',
      bound: '已绑定',
      unBound: '未绑定',
      bind: '绑定',
      unBind: '解绑',
      change: '更换',
      logout: '退出登录',
      cancel: '取消',
      submit: '确认',
      updateNickName: '修改昵称',
      bindNickName: '绑定昵称',
      requireNickNameTip: '请输入新昵称',
      updatePhoneNumber: '修改手机',
      bindPhoneNumber: '绑定手机',
      requirePhoneNumberTip: '请输入新手机号',
      updateEmail: '修改邮箱',
      bindEmail: '绑定邮箱',
      requireEmailTip: '请输入新邮箱',
      requireCaptchaTip: '请输入验证码',
      uploadAvatar: '上传头像',
      uploadAvatarAgain: '重新上传',
      clickUploadAvatar: '点击上传',
      uploadAvatarTip: '单张图片最大支持2MB',
      tokensManage: '授权管理',
      sessionsManage: '会话管理',
    },
    userSignedIn: {
      pageTitle: vendorName + ' 用户已登录',
      title: '已登录用户',
      quickLogin: '快速登录',
      loginOther: '登录其他账号',
      login: '登录'
    },
    tokens:{
      pageTitle: ' 授权管理',
      title: ' 授权管理',
      logoutAll: '全部退出',
      logout: '退出',
      lastAccessedTime: '最后访问时间',
      creationTime: '创建时间',
      expirationTime: '过期时间',
      browserName:'浏览器类型',
      browserVersion:'浏览器版本',
      platform:'平台类型',
      osName:'系统类型',
      osVersion:'系统版本',
      locale:'地区语言',
      confirmText:'确认',
      cancelText:'取消',
      logoutSuccess:'操作成功',
      currentDevice:'本机',

      GrantType:{
        authorization_code: '授权码模式',
        password: '密码模式',
        client_credentials: '客户端模式',
        implicit: '简化模式',
        refresh_token: '刷新token',
      },
      OAuth2LoginType:{
        EmailAndCode: '邮箱+验证码',
        PhoneAndCode: '手机号+验证码',
        UsernameAndCode: '用户名+验证码',
        IdAndCode: 'ID+验证码',
        AccountAndCode: '账号+验证码',
        PhoneAndWechatInfo: '手机号+微信信息',
        SocialInfo: '三方登录',
        PhoneOneStep: '手机号一键登录',
        ExternalId: '外部系统一键登录',
        password: '账号密码登录',
      },
      LoginType:{
        UsernamePassword: '用户名密码登录',
        EmailCode: '邮箱验证码登录',
        GoogleCode: 'Google令牌登录',
        Sms: '手机验证码登录',
        JustAuth: '三方登录',
        OAuth2: 'OAuth登录',
        RememberMe: '记住我登录',
        PreAuthenticated: '预身份验证登录',
        Anonymous: '匿名登录',
        Other: '其他登录',
      }
    },
    sessions:{
      pageTitle: ' 会话管理',
      title: ' 会话管理',
      logoutAll: '全部退出',
      logout: '退出',
      lastAccessedTime: '最后访问时间',
      creationTime: '创建时间',
      expirationTime: '过期时间',
      browserName:'浏览器类型',
      browserVersion:'浏览器版本',
      platform:'平台类型',
      osName:'系统类型',
      osVersion:'系统版本',
      locale:'地区语言',
      confirmText:'确认',
      cancelText:'取消',
      logoutSuccess:'操作成功',
      currentDevice:'本机',
    }
  },
  'zh-tw': {
    findPwd: {
      pageTitle: '找回密碼',
      title: '忘記密碼?',
      subTitle: '修改密碼',
      tip: '已有賬號？',
      login: '直接登入',
      email: '信箱',
      capture: '驗證碼',
      password: '密碼',
      emailEmpty: '請輸入您的信箱',
      emailErrorMsg: '請輸入正確的信箱地址',
      pwdEmpty: '密碼不能為空',
      captchaPlaceholder: '短信驗證碼',
      sendMsg: '發送驗證碼',
      reSendMsg: '重新發送',
      next: '下一步',
      tipNewPwdPlaceholder: '請輸入新密碼',
      newPwdPlaceholder: '新密碼',
      confirmPwdPlaceholder: '再次輸入密碼',
      confirmPwdBtn: '確認',
      confirmPwdDiff: '兩次輸入密碼不一致',
      pwdFormatCheck: '密碼中不能包含特殊符號與中文',
      pwdError: '登入密碼最少6位',
      resetPassword: '重設密碼',
    },
    login: {
      pageTitle: vendorName+ ' 登入',
      forget: '忘記密碼？',
      tip: '還沒有賬號？',
      register: '立即註冊',
      login: '登入',
      title: '歡迎回來',
      sendMsg: '發送驗證碼',
      reSendMsg: '重新發送',
      email: '信箱',
      password: '密碼',
      emailEmpty: '信箱不能為空',
      emailErrorMsg: '請輸入正確的信箱地址',
      pwdEmpty: '密碼不能為空',
      noAccountTip: '還沒有賬號？',
      noAccount: '註冊',
      emailLogin: '信箱驗證碼登入',
      passwordLogin: '密碼登入',
      code: '驗證碼',
      codeEmpty: '請輸入信箱驗證碼',
      codeError: '您輸入的驗證碼錯誤',


      pageTitle:'易來賬號登錄',
      register: '註冊帳號',
      mobileLogin: '驗證碼登錄',
      popupRegister: '帳號不存在',
      popupDesc:'此帳號暫未註冊，點擊“去註冊”立即註冊新帳號',
      popupRegisterBtn: '去註冊',
    },
    register: {
      pageTitle: vendorName + ' 賬號註冊',
      title: '註冊'+vendorName+'賬號',
      tip: '已有賬號？',
      login: '直接登入',
      checkboxLabel: '已同意' + vendorName,
      sendMsg: '發送驗證碼',
      reSendMsg: '重新發送',
      agreement: '用戶協議',
      privacy: '隱私政策',
      and: '和',
      next: '註冊',
      mobileNext: '下一步',
      email: '信箱',
      password: '密碼',
      emailEmpty: '請輸入您的信箱',
      emailErrorMsg: '請輸入正確的信箱地址',
      pwdEmpty: '密碼不能為空',
      username: '用戶名',
      userEmpty: '用戶名不能為空',
      code: '驗證碼',
      codeEmpty: '驗證碼不能為空',

      popupTitle:'用戶協議&隱私政策',
      popupDesc:'請您務必審慎、仔細閱讀《用戶協議》和《隱私政策》相關條款。當您點擊“同意並註冊”即表示您已充分閱讀、理解並接受《用戶協議》及《隱私政策》',
      popupBtn:'同意並註冊',
      agreement: '用戶協議',
      pwdError: '登入密碼最少6位',
      phone: '手機號',
    },
    grant: {
      pageTitle: vendorName + ' 賬號授權',
      title: '授權'+vendorName+'賬號',
      confirm: '確認授權',
      login: '切換賬號',
      tipTitle: '授權後該應用獲得以下權限：',
      firstTip: '訪問您的個人信息',
      secondTip: '訪問您的頭像/用戶名等信息',
      thirdTip: '此後權限條件從後端接口獲取',
    },
    personal: {
      pageTitle: vendorName + ' 個人中心',
      title: '賬號設置',
      phoneNumber: '手機號',
      nickName: '昵稱',
      email: '郵箱',
      captcha: '驗證碼',
      bound: '已綁定',
      unBound: '未綁定',
      bind: '綁定',
      unBind: '解綁',
      change: '更換',
      logout: '退出登錄',
      cancel: '取消',
      submit: '確認',
      updateNickName: '修改昵稱',
      bindNickName: '綁定昵稱',
      requireNickNameTip: '請輸入新昵稱',
      updatePhoneNumber: '修改手機',
      bindPhoneNumber: '綁定手機',
      requirePhoneNumberTip: '請輸入新手機號',
      updateEmail: '修改郵箱',
      bindEmail: '綁定郵箱',
      requireEmailTip: '請輸入新郵箱',
      requireCaptchaTip: '請輸入驗證碼',
      uploadAvatar: '上傳頭像',
      uploadAvatarAgain: '重新上傳',
      clickUploadAvatar: '點擊上傳',
      uploadAvatarTip: '單張圖片最大支持2MB',
      tokensManage: '授權管理',
      sessionsManage: '會話管理',
    },
    userSignedIn: {
      pageTitle: vendorName + ' 用戶已登錄',
      title: '已登錄用戶',
      quickLogin: '快速登錄',
      loginOther: '登錄其他帳號',
      login: '登錄'
    },
    tokens:{
        "pageTitle": "授權管理",
        "title": "授權管理",
        "logoutAll": "全部登出",
        "logout": "登出",
        "lastAccessedTime": "最後訪問時間",
        "creationTime": "創建時間",
        "expirationTime": "過期時間",
        "browserName": "瀏覽器類型",
        "browserVersion": "瀏覽器版本",
        "platform": "平台類型",
        "osName": "系統類型",
        "osVersion": "系統版本",
        "locale": "地區語言",
        "confirmText": "確認",
        "cancelText": "取消",
        "logoutSuccess": "操作成功",
        "currentDevice": "本機",

      GrantType:{
        authorization_code: '授權碼模式',
        password: '密碼模式',
        client_credentials: '客戶端模式',
        implicit: '簡化模式',
        refresh_token: '刷新token',
      },
      OAuth2LoginType:{
        EmailAndCode: '郵箱+驗證碼',
        PhoneAndCode: '手機號碼+驗證碼',
        UsernameAndCode: '用戶名+驗證碼',
        IdAndCode: 'ID+驗證碼',
        AccountAndCode: '帳號+驗證碼',
        PhoneAndWechatInfo: '手機號碼+微信信息',
        SocialInfo: '三方登錄',
        PhoneOneStep: '手機號碼一鍵登錄',
        ExternalId: '外部系統一鍵登錄',
        password: '帳號密碼登錄',
      },
      LoginType:{
        UsernamePassword: '用戶名密碼登錄',
        EmailCode: '郵箱驗證碼登錄',
        GoogleCode: 'Google令牌登錄',
        Sms: '手機驗證碼登錄',
        JustAuth: '三方登錄',
        OAuth2: 'OAuth登錄',
        RememberMe: '記住我登錄',
        PreAuthenticated: '預身份驗證登錄',
        Anonymous: '匿名登錄',
        Other: '其他登錄',
      },
    },
    sessions:{
      "pageTitle": "會話管理",
      "title": "會話管理",
      "logoutAll": "全部登出",
      "logout": "登出",
      "lastAccessedTime": "最後訪問時間",
      "creationTime": "創建時間",
      "expirationTime": "過期時間",
      "browserName": "瀏覽器類型",
      "browserVersion": "瀏覽器版本",
      "platform": "平台類型",
      "osName": "系統類型",
      "osVersion": "系統版本",
      "locale": "地區語言",
      "confirmText": "確認",
      "cancelText": "取消",
      "logoutSuccess": "操作成功",
      "currentDevice": "本機"
    }
  },
  en: {
    register: {
      pageTitle: vendorName + ' Account Register',
      title: 'Building up Your Smart Home Starts from ' + vendorName,
      subTitle: '',
      tip: 'Already have an account?',
      login: 'Login directly.',
      checkboxLabel: 'I agree to the',
      sendMsg: 'Send verification code',
      reSendMsg: 'Resend code',
      agreement: 'Terms and Conditions.',
      privacy: 'Privacy Policy',
      next: 'Create Account',
      mobileNext: 'Next',
      and: 'and the',
      email: 'E-mail',
      password: 'Password',
      emailEmpty: 'E-mail can not be blank',
      emailErrorMsg: 'Email is invalid.',
      phoneEmpty: 'Phone number cannot be blank',
      phoneErrorMsg: 'Phone number is invalid.',
      pwdEmpty: 'Password can not be blank',
      username: 'User name',
      userEmpty: 'User name is required',
      code: 'Verification code',
      codeEmpty: 'Please enter the verification code',
      pwdError: 'Password at least 6 characters',
      pwdFormatCheck: 'Password cannot contain special symbols',


      popupTitle:'User Agreement & Privacy Policy',
      popupDesc:'Please be sure to carefully read the relevant terms of the "Service Agreement" and "Privacy Policy". By clicking "Agree and Register", you indicate that you have fully read, understood, and accepted the "Service Agreement" and "Privacy Policy"',
      popupBtn:'Agree and Register',
      agreement: 'User Agreement',
      phone: 'PhoneNumber',
      useEmail: 'Register using email',
      usePhone: 'Register using your phone number',
    },
    login: {
      pageTitle: vendorName + ' Login',
      forget: 'Forgot password?',
      tip: 'No Account？',
      register: 'Register Now',
      login: 'Login',
      title: 'Building up Your Smart Home Starts from ' + vendorName,
      code: 'Verification code',
      codeEmpty: 'Please enter the verification code',
      sendMsg: 'Send verification code',
      reSendMsg: 'Resend code',
      noAccountTip: "Don't have an account yet?",
      noAccount: 'Sign up here.',
      email: 'E-mail',
      password: 'Password',
      emailEmpty: 'E-mail can not be blank',
      emailErrorMsg: 'Email is invalid.',
      pwdEmpty: 'Password can not be blank',
      pwdLogin: 'Password Login',
      mobileLogin: 'Message Login',
      emailLogin: 'Login with e-mail verification code',
      passwordLogin: 'Login with password',
      placeholder: 'Phone/E-mail',
      accountEmpty: 'Phone/E-mail can not be blank',
      passwordPlaceholder: 'Password',
      phone: 'PhoneNumber',
      mobileEmpty: 'PhoneNumber can not be blank',
      mobileError: 'PhoneNumber is invalid',
      codeError: 'Verification code you entered is incorrect',
      pwdError: 'The password consists of at least 6 characters',
      otherLoginWay: 'Other Login Way',

      pageTitle:'Login with '+vendorName+' account',
      register: 'Register',
      mobileLogin: 'Verification code login',
      popupRegister: 'Account does not exist',
      popupDesc:'This account is not registered yet. Click "Register Now" to create a new account',
      popupRegisterBtn: 'Register Now',
      useEmail: 'Log in using email',
      usePhone: 'Log in using your phone number',
    },
    findPwd: {
      pageTitle: vendorName + ' Forget Password',
      title: 'Building up Your Smart Home Starts from ' + vendorName,
      subTitle: 'Create a new password.',
      tip: 'Remember your password? ',
      login: 'Login',
      email: 'E-mail',
      capture: 'capture',
      sendMsg: 'Send verification code',
      reSendMsg: 'Resend code',
      next: 'Next',
      phoneEmpty: 'Phone number cannot be blank',
      phoneErrorMsg: 'Phone number is invalid.',
      emailEmpty: 'E-mail can not be blank',
      emailErrorMsg: 'Email is invalid.',
      captchaPlaceholder: 'Verification code',
      tipNewPwdPlaceholder: 'New password',
      newPwdPlaceholder: 'New password',
      confirmPwdPlaceholder: 'Confirm password',
      confirmPwdBtn: 'Change password',
      pwdEmpty: 'Password can not be blank',
      confirmPwdDiff: 'Entered passwords differ',
      pwdFormatCheck: 'Password cannot contain special symbols',
      codeEmpty: 'Please enter the verification code',
      resetPassword: 'Reset password',
      useEmail: 'Retrieve using email',
      usePhone: 'Retrieve using phone number',
    },
    grant: {
      pageTitle: vendorName + ' Authorization',
      title: 'Authorize the '+vendorName+' account',
      confirm: 'Confirm',
      login: 'Change Account',
      tipTitle: 'After authorization, the website obtains the following permissions:',
      firstTip: 'Access to your personal information',
      secondTip: 'Access your profile picture/user name, etc',
      thirdTip: 'The permission condition is then obtained from the backend interface',
    },
    personal: {
      pageTitle: vendorName + ' Personal Center',
      title: 'AccountSettings',
      phoneNumber: 'PhoneNumber',
      nickName: 'Nickname',
      email: 'Email',
      captcha: 'Captcha',
      bound: 'Bound',
      unBound: 'Unbound',
      bind: 'Bind',
      unBind: 'Untie',
      change: 'Change',
      logout: 'SignOut',
      cancel: 'Cancel',
      submit: 'Submit',
      updateNickName: 'Modify the nickname',
      bindNickName: 'Bind A Nickname',
      requireNickNameTip: 'Please enter a new nickname',
      updatePhoneNumber: 'Modify the phone',
      bindPhoneNumber: 'Bind your phone',
      requirePhoneNumberTip: 'Please enter a new phone number',
      updateEmail: 'Modify the email',
      bindEmail: 'Bind an email address',
      requireEmailTip: 'Please enter a new email address',
      requireCaptchaTip: 'Please enter the verification code',
      uploadAvatar: 'Upload an avatar',
      uploadAvatarAgain: 'Re-upload',
      clickUploadAvatar: 'Click to Upload',
      uploadAvatarTip: 'A maximum of 2 MB is supported for a single image',
      tokensManage: 'Authorization Management',
      sessionsManage: 'Session Management',
    },
    userSignedIn: {
      pageTitle: 'Logged in as ' + vendorName,
      title: 'Logged-in User',
      quickLogin: 'Quick Login',
      loginOther: 'Login with Another Account',
      login: 'Login'
    },
    tokens:{
      pageTitle: 'Authorization Management',
      title: 'Authorization Management',
      logoutAll: 'Logout All',
      logout: 'Logout',
      lastAccessedTime: 'Last Accessed Time',
      creationTime: 'Creation Time',
      expirationTime: 'Expiration Time',
      browserName: 'Browser Type',
      browserVersion: 'Browser Version',
      platform: 'Platform Type',
      osName: 'Operating System Type',
      osVersion: 'Operating System Version',
      locale: 'Locale Language',
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      logoutSuccess: 'Operation Successful',
      currentDevice: 'Local Device',

      GrantType: {
        authorization_code: 'Authorization Code Mode',
        password: 'Password Mode',
        client_credentials: 'Client Credentials Mode',
        implicit: 'Implicit Mode',
        refresh_token: 'Refresh Token',
      },
      OAuth2LoginType: {
        EmailAndCode: 'Email + Verification Code',
        PhoneAndCode: 'Phone Number + Verification Code',
        UsernameAndCode: 'Username + Verification Code',
        IdAndCode: 'ID + Verification Code',
        AccountAndCode: 'Account + Verification Code',
        PhoneAndWechatInfo: 'Phone Number + WeChat Information',
        SocialInfo: 'Social Login',
        PhoneOneStep: 'Phone Number One-Step Login',
        ExternalId: 'External System One-Step Login',
        password: 'Account Password Login',
      },
      LoginType: {
        UsernamePassword: 'Username Password Login',
        EmailCode: 'Email Verification Code Login',
        GoogleCode: 'Google Token Login',
        Sms: 'Phone Verification Code Login',
        JustAuth: 'Social Login',
        OAuth2: 'OAuth Login',
        RememberMe: 'Remember Me Login',
        PreAuthenticated: 'Pre-authenticated Login',
        Anonymous: 'Anonymous Login',
        Other: 'Other Login',
      }
    },
    sessions:{
      "pageTitle": "Session Management",
      "title": "Session Management",
      "logoutAll": "Logout All",
      "logout": "Logout",
      "lastAccessedTime": "Last Accessed Time",
      "creationTime": "Creation Time",
      "expirationTime": "Expiration Time",
      "browserName": "Browser Type",
      "browserVersion": "Browser Version",
      "platform": "Platform Type",
      "osName": "Operating System Type",
      "osVersion": "Operating System Version",
      "locale": "Locale",
      "confirmText": "Confirm",
      "cancelText": "Cancel",
      "logoutSuccess": "Operation Successful",
      "currentDevice": "Local Device"
    }
  },
  ru: {
    login: {
      pageTitle: vendorName + ' Авторизоваться',
      forget: 'Забыли пароль？',
      tip: 'Номера счетов пока нет？',
      login: 'Авторизоваться',
      title: 'С возвращением',
      sendMsg: 'Отправить проверочный код',
      reSendMsg: 'Пересылка',
      pwdEmpty: 'Введите пароль',
      email: 'Электронная почта',
      password: 'паролем',
      emailEmpty: 'Почта ящик не может быть пустым',
      emailErrorMsg: 'Пожалуйста введите правильный адрес',
      pwdEmpty: 'Пароль не может быть пустым',
      noAccountTip: 'Номера счетов пока нет？',
      noAccount: 'Зарегистрировать',
      pwdLogin: 'Вход с паролем',
      code: 'Код подтверждения',
      codeEmpty: 'Почта ящик не может быть пустым',
      placeholder: 'Телефон / Электронная почта',
      accountEmpty:  'Телефон / Электронная почта не может быть пустым',
      passwordPlaceholder: 'Пароль',
      phone: 'Телефон',
      mobileEmpty: 'Поле телефон не может быть пустым',
      mobileError: 'Введите правильный номер телефона',
      codeError: 'Введенный вами  код подтверждения  неверен',
      pwdError:  'Пароль должен содержать не менее 6 символов',
      otherLoginWay: 'Другие способы входа',
      emailLogin: 'Войдите в систему с помощью кода подтверждения электронной почты',
      passwordLogin: 'Пароль доступа',

      pageTitle:'Войти с аккаунтом ' + vendorName,
      register: 'Регистрация',
      mobileLogin: 'Логин сообщений',
      popupRegister: 'Аккаунт не существует',
      popupDesc:'Этот аккаунт еще не зарегистрирован. Щелкните "Зарегистрироваться сейчас", чтобы создать новую учетную запись',
      popupRegisterBtn: 'Зарегистрироваться сейчас',
      sendMsg: 'Отправить код подтверждения',

      phoneAndEmail: 'Неверный формат телефона или электронной почты',
      useEmail: 'Войти с помощью электронной почты',
      usePhone: 'Войти с помощью телефона',
    },
    findPwd: {
      pageTitle: 'Найди пароль',
      title: 'Забыли пароль?',
      subTitle: 'модификац пароль',
      tip: 'У вас уже есть учетная запись？',
      login: 'Войдите в систему напрямую.',
      email: 'Электронная почта',
      capture: 'Код подтверждения',
      password: 'паролем',
      phoneEmpty: 'Поле телефон не может быть пустым',
      phoneErrorMsg: 'Введите правильный номер телефона',
      emailEmpty: 'Пожалуйста, введите ваш почтовый ящик',
      emailErrorMsg: 'Пожалуйста, введите правильный адрес',
      pwdEmpty: 'Пароль не может быть пустым',
      captchaPlaceholder: 'Код подтверждения СМС',
      sendMsg: 'Отправить проверочный код',
      reSendMsg: 'Пересылка',
      next: 'Следующий шаг',
      tipNewPwdPlaceholder: 'Введите новый пароль',
      newPwdPlaceholder: 'Пароль состоит как минимум из 6 цыфр, состоящих из цифр, букв или обычных символов.',
      confirmPwdPlaceholder: 'Введите еще раз',
      confirmPwdBtn: 'подтверд',
      confirmPwdDiff: 'Два разных входных пароля',
      pwdFormatCheck: 'В пароле не должно быть специальных символов с китайцами',
      pwdError: 'Не менее 6 цифр пароля для входа в систему',
      codeEmpty: 'Пожалуйста, введите код подтверждения электронной почты',
      resetPassword: 'Сбросить пароль',
      useEmail: 'Восстановить пароль с помощью электронной почты',
      usePhone: 'Восстановить пароль с помощью телефона',
    },
    register: {
      pageTitle: vendorName + ' Регистрация счета',
      title: 'зарегистрирова '+vendorName+' аккаунт',
      tip: 'Номер счета？',
      login: 'Авторизоваться',
      checkboxLabel: 'Договорились ' + vendorName,
      sendMsg: 'Отправить проверочный код',
      reSendMsg: 'Пересылка',
      and: 'и',
      next: 'Зарегистрировать',
      mobileNext: 'Следующий шаг',
      email: 'Электронная почта',
      password: 'паролем',
      emailEmpty: 'Пожалуйста, введите ваш почтовый ящик',
      emailErrorMsg: 'Пожалуйста, введите правильный адрес',
      phoneEmpty: 'Введите свой номер телефона',
      pwdEmpty: 'Пароль не может быть пустым',
      username: 'Имя пользователя',
      userEmpty: 'Имя пользователя не может быть пустым',
      code: 'Код подтверждения',
      codeEmpty: 'Капча не может быть пустой',
      popupTitle:'Пользовательское соглашение и политика конфиденциальности',
      popupDesc:'Пожалуйста, обязательно внимательно прочтите соответствующие условия "Соглашения об услугах" и "Политики конфиденциальности". Нажимая кнопку "Согласен и зарегистрироваться", вы подтверждаете, что полностью ознакомились, поняли и приняли "Соглашение об услугах" и "Политику конфиденциальности"',
      popupBtn:'Согласиться и зарегистрироваться',
      agreement: 'Протокол пользователя',
      privacy: 'Политика конфиденциальности',
      pwdError: 'Пароль должен содержать не менее 6 символов',
      pwdError: 'Не менее 6 цифр пароля для входа в систему',
      phone: 'Номер телефона',
      useEmail: 'Зарегистрироваться с помощью электронной почты',
      usePhone: 'Зарегистрироваться с помощью телефона',
    },
    grant: {
      pageTitle: vendorName + ' Номер счёта разрешен',
      title: 'разрешен '+vendorName+' аккаунт',
      confirm: 'Подтверждаю',
      login: 'Смени номер счета',
      tipTitle: 'После получения разрешения приложение получает следующий доступ：',
      firstTip: 'Доступ к вашей личной информации',
      secondTip: 'Доступ к информации, например, к вашей аватарке/имени пользователя',
      thirdTip: 'После этого условия доступа были получены из задней части интерфейса',
    },
    personal: {
      pageTitle: vendorName + ' Личный кабинет',
      title: 'Настройки аккаунта',
      phoneNumber: 'Номер телефона',
      nickName: 'Псевдоним',
      email: 'Электронная почта',
      captcha: 'Капча',
      bound: 'Привязан',
      unBound: 'Не привязан',
      bind: 'Привязать',
      unBind: 'Отвязать',
      change: 'Изменить',
      logout: 'Выйти',
      cancel: 'Отмена',
      submit: 'Подтвердить',
      updateNickName: 'Изменить псевдоним',
      bindNickName: 'Привязать псевдоним',
      requireNickNameTip: 'Введите новый псевдоним',
      updatePhoneNumber: 'Изменить номер телефона',
      bindPhoneNumber: 'Привязать номер телефона',
      requirePhoneNumberTip: 'Введите новый номер телефона',
      updateEmail: 'Изменить электронную почту',
      bindEmail: 'Привязать электронную почту',
      requireEmailTip: 'Введите новую электронную почту',
      requireCaptchaTip: 'Введите капчу',
      uploadAvatar: 'Загрузить аватар',
      uploadAvatarAgain: 'Загрузить заново',
      clickUploadAvatar: 'Нажмите, чтобы загрузить',
      uploadAvatarTip: 'Максимальный размер одного изображения - 2 МБ',
      tokensManage: 'Управление авторизацией',
      sessionsManage: 'Управление сеансом',
    },
    userSignedIn: {
      pageTitle: 'Авторизован как ' + vendorName,
      title: 'Авторизованный пользователь',
      quickLogin: 'Быстрый вход',
      loginOther: 'Войти с другой учетной записью',
      login: 'Войти'
    },
    tokens:{
      pageTitle: "Управление авторизацией",
      title: "Управление авторизацией",
      logoutAll: "Выйти из всех",
      logout: "Выйти",
      lastAccessedTime: "Последнее посещение",
      creationTime: "Время создания",
      expirationTime: "Время истечения",
      browserName: "Тип браузера",
      browserVersion: "Версия браузера",
      platform: "Тип платформы",
      osName: "Тип операционной системы",
      osVersion: "Версия операционной системы",
      locale: "Локаль",
      confirmText: "Подтвердить",
      cancelText: "Отмена",
      logoutSuccess: "Операция успешна",
      currentDevice: "Локальное устройство",

      GrantType:{
        authorization_code: 'режим авторизационного кода',
        password: 'режим пароля',
        client_credentials: 'режим клиентских учетных данных',
        implicit: 'упрощенный режим',
        refresh_token: 'обновить токен',
      },
      OAuth2LoginType:{
        EmailAndCode: 'Эл. почта + Код подтверждения',
        PhoneAndCode: 'Номер телефона + Код подтверждения',
        UsernameAndCode: 'Имя пользователя + Код подтверждения',
        IdAndCode: 'ID + Код подтверждения',
        AccountAndCode: 'Аккаунт + Код подтверждения',
        PhoneAndWechatInfo: 'Номер телефона + Информация WeChat',
        SocialInfo: 'Вход через социальные сети',
        PhoneOneStep: 'Вход по номеру телефона в один клик',
        ExternalId: 'Вход через внешнюю систему в один клик',
        password: 'Вход по аккаунту и паролю',
      },
      LoginType:{
        UsernamePassword: 'Вход по имени пользователя и паролю',
        EmailCode: 'Вход по эл. почте и коду подтверждения',
        GoogleCode: 'Вход с использованием кода Google',
        Sms: 'Вход по коду из СМС',
        JustAuth: 'Вход через социальные сети',
        OAuth2: 'Вход через OAuth',
        RememberMe: 'Вход с функцией "Запомнить меня"',
        PreAuthenticated: 'Вход с предварительной аутентификацией',
        Anonymous: 'Анонимный вход',
        Other: 'Другие способы входа',
      }
    },
    sessions:{
      pageTitle: "Управление сеансом",
      title: "Управление сеансом",
      logoutAll: "Выйти из всех",
      logout: "Выйти",
      lastAccessedTime: "Последнее посещение",
      creationTime: "Время создания",
      expirationTime: "Время истечения",
      browserName: "Тип браузера",
      browserVersion: "Версия браузера",
      platform: "Тип платформы",
      osName: "Тип операционной системы",
      osVersion: "Версия операционной системы",
      locale: "Локаль",
      confirmText: "Подтвердить",
      cancelText: "Отмена",
      logoutSuccess: "Операция успешна",
      currentDevice: "Локальное устройство"
    }
  },
};
