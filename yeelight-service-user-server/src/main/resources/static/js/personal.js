Vue.use(window['vue-cropper']);
new Vue({
  el: '.Container',
  data() {
    let lang = handleRegionAndLang();
    const langMsg = i18n[lang].personal;
    if (errorMsg != null) {
      this.$message.error(errorMsg);
    }
    return {
      lang,
      langMsg,
      // 身份相关数据
      userData: {
        userAvatar: '',
        userName: '未设置',
        phone: '18000000000',
        email: '未绑定',
      },

      // 输入input数据
      inputData: {
        userNameChange: '',
        userPhoneChange: '',
        // 电话验证码
        NumVerification: '',
        userEmailChange: '',
        // 邮箱验证码
        emailVerification: '',
      },
      // 预览图片
      previews: {},

      // 短信验证码
      msg: {
        getCodeMsgPhone: '发送验证码',
        countPhone: 60,
        captchaKeyPhone: '',
        disiblePhone: false,
        getCodeMsgEmail: '发送验证码',
        countEmail: 60,
        captchaKeyEmail: '',
        disibleEmail: false,
      },

      // 滑动显示修改框
      showOutBox: {
        showCamera: false,
        showNameFix: false,
        showNumFix: false,
        showEmailFix: false,
      },

      // 显示弹出框
      showMaskBox: {
        showHeadImgChangeBox: false,
        showTitleChangeBox: false,
        showPhoneChangeBox: false,
        showEmailChangeBox: false,
      },

      // 头像裁剪
      option: {
        img: '',
        size: 0.8,
        full: false,
        outputType: 'png',
        canMove: false,
        fixedBox: false,
        original: false,
        canMoveBox: true,
        autoCrop: true,
        // 只有自动截图开启 宽度高度才生效
        autoCropWidth: 8,
        centerBox: false,
        high: true,
        max: 99999,
        fixedNumber: [1, 1],
      },

      // 三方信息
      thirdPartySource: [
        { source: 'APPLE', sourceName: 'Apple ID', sourceEnName: 'Apple ID', isShow: true },
        { source: 'FEISHU_APP', sourceName: '飞书', sourceEnName: 'Lark' , isShow: isDevelop() || isYeelight() },
        { source: 'WECHAT_OPEN', sourceName: '微信', sourceEnName: 'Wechat' , isShow: isDevelop() || isYeelight() },
        { source: 'WECHAT_MP', sourceName: '微信', sourceEnName: 'Wechat Mini Program' , isShow: isDevelop() || isYeelight() },
        { source: 'XIAOMI', sourceName: '小米', sourceEnName: 'Xiaomi' , isShow: isDevelop() || isYeelight() },
        { source: 'FACEBOOK', sourceName: 'Facebook', sourceEnName: 'Facebook' , isShow: isDevelop() || isOverseas() },
        { source: 'GOOGLE', sourceName: 'Google', sourceEnName: 'Google' , isShow: isDevelop() || (isOverseas() && !isYeelight()) },
        { source: 'AMAZON', sourceName: 'Amazon', sourceEnName: 'Amazon' , isShow: isDevelop() || (isOverseas() && !isYeelight()) },
      ],
      thirdPartyInfo: [],
    };
  },
  computed: {
    showNumConfirmBtn: function () {
      var tel = this.inputData.userPhoneChange && this.inputData.userPhoneChange.length == 11;
      var verfication = this.inputData.NumVerification && this.inputData.NumVerification.length == 6;
      if (tel && verfication) {
        return false;
      }
      return true;
    },
    showEmailConfirmBtn: function () {
      var email = this.inputData.userEmailChange;
      var verfication = this.inputData.emailVerification && this.inputData.emailVerification.length == 6;
      if (email && verfication) {
        return false;
      }
      return true;
    },
    isOverseas() {
      return isOverseas();
    },
    isYeelight() {
      return isYeelight();
    },
    isCn() {
      return isCn();
    },
  },
  mounted() {
    this.resetInputData();
    this.initThirdPartyInfo();
    this.loadThirdPartyInfo();
    document.title = this.langMsg.pageTitle;
  },
  methods: {
    initThirdPartyInfo() {
      this.thirdPartyInfo.splice(0);
      this.thirdPartySource.forEach(x => {
        this.thirdPartyInfo.push(x);
      });
    },
    loadThirdPartyInfo() {
      let that = this;
      $.ajax({
        url: `../oauth/socialUsers`,
        type: 'get',
        success: function (result) {
          if (result.success) {
            let data = result.data;
            that.formatThirdPartyInfo(data);
          }
        },
      });
    },
    formatThirdPartyInfo(data) {
      const arr = JSON.parse(JSON.stringify(this.thirdPartyInfo));
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        for (let j = 0; j < data.length; j++) {
          console.log(data[j].source, item.source);
          if (data[j].source === item.source || data[j].source.includes(item.source)) {
            arr[i] = {
              ...item,
              ...data[j],
            };
          }
        }
      }
      this.thirdPartyInfo = [...arr];
    },
    bindThirdParty(item) {
      let source = item.source.toLowerCase();
      const url = `../third-party-auth/render/${source}`;
      location.href = url;
    },
    unBindThirdParty(item) {
      let that = this;
      $.ajax({
        url: `../oauth/unBindSocialUser/${item.source}`,
        type: 'DELETE',
        success: function (result) {
          console.log(result, 111);
          if (result.success) {
            that.$message.success('解绑成功');
            that.initThirdPartyInfo();
            that.loadThirdPartyInfo();
          }
        },
      });
    },
    // 上传按钮   限制图片大小
    changeUpload(file, fileList) {
      const isJPG = file.raw.type === 'image/jpeg' || file.raw.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        return this.$message.error('上传头像图片只能是 jpg/png 文件!');
      }
      if (!isLt2M) {
        return this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      this.fileinfo = file;
      this.showMaskBox.showHeadImgChangeBox = true;
      // 上传成功后将图片地址赋值给裁剪框显示图片
      this.$nextTick(() => {
        this.file = file;
        this.option.img = URL.createObjectURL(file.raw);
      });
    },
    // 实时预览函数
    realTime(data) {
      this.previews = data;
    },
    /**
     *
     * @param type:1 手机   2：邮箱
     * @returns {boolean}
     */
    getCode(type) {
      var _this = this;
      var name = '';
      if (type == 1) {
        name = 'Phone';
      } else {
        name = 'Email';
      }

      var userNameChange = 'user' + name + 'Change';
      var countName = 'count' + name;
      var getCodeMsgName = 'getCodeMsg' + name;
      var disibleName = 'disible' + name;

      if (name == 'Phone') {
        if (!this.inputData[userNameChange]) {
          this.$message.error('请输入手机号');
          return false;
        } else if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(this.inputData[userNameChange])) {
          this.$message.error('手机号格式不正确');
          return false;
        }
      } else {
        if (!this.inputData[userNameChange]) {
          this.$message.error('请输入邮箱号');
          return false;
        } else if (
          !/^[A-Za-z0-9-._]+@[A-Za-z0-9-]+(.[A-Za-z0-9]+)*(.[A-Za-z]{2,6})$/.test(this.inputData[userNameChange])
        ) {
          this.$message.error('邮箱格式不正确');
          return false;
        }
      }

      clearInterval(this.timer);
      this.timer = setInterval(function () {
        if (_this.msg[countName] == 0) {
          _this.msg[getCodeMsgName] = '发送验证码';
          _this.msg[countName] = 60;
          _this.msg[disibleName] = false;
          clearInterval(_this.timer);
        } else {
          _this.msg[countName]--;
          _this.msg[getCodeMsgName] = '重新发送(' + _this.msg[countName] + 's)';
          _this.msg[disibleName] = true;
        }
      }, 1000);

      var url = '';
      var captchaKeyName = 'captchaKey' + name;
      var data = {};

      var res = false;
      var reqType = 'get';
      if (name == 'Phone') {

        url = '../public/mobile/captcha';
        reqType = 'post';
        data.phoneNumber = this.inputData[userNameChange];
        data.exist = true;

        let resParam = getCaptchaKey(data.phoneNumber);
        if(!resParam.ticket){
          clearInterval(_this.timer);
          return res
        }

        data = JSON.stringify({ phoneNumber: data.phoneNumber, exist: data.exist, ticket: resParam.ticket, timestamp: resParam.param.timestamp });

      } else {
        url = '../public/email/captcha';
        data.email = this.inputData[userNameChange];
        data.exist = true;
      }

      $.ajax({
        url: url,
        type: reqType,
        contentType:'application/json;charset=utf-8',
        dataType: 'json',
        async: false,
        data: data,
        success: function (result) {
          if (result.success) {
            let data = result.data;
            _this.msg[captchaKeyName] = data.captchaKey;
            res = true;
          } else {
            _this.$message.error(result.msg);
            clearInterval(_this.timer);
          }
        },
        error: function () {
          _this.$message.error('网络异常,请稍后重试');
          clearInterval(_this.timer);
        },
      });
      return res;
    },
    /**
     *
     * @param type:1 修改昵称  2:修改手机号  3:修改email  4：修改头像
     *        url: 新头像url
     */
    uploadData(type, url) {
      var _this = this;
      var data = {
        id: this.$refs.getId.textContent,
      };
      switch (type) {
        case 1:
          if (!this.inputData.userNameChange) return this.$message.error('请输入昵称');
          data.name = this.inputData.userNameChange;
          break;
        case 2:
          if (!this.inputData.userPhoneChange) return this.$message.error('请输入手机号');
          data.phoneNumber = this.inputData.userPhoneChange;
          data.captcha = this.inputData.NumVerification;
          data.captchaKey = this.msg.captchaKeyPhone;
          break;
        case 3:
          if (!this.inputData.userEmailChange) return this.$message.error('请输入邮箱');
          data.email = this.inputData.userEmailChange;
          data.captcha = this.inputData.emailVerification;
          data.captchaKey = this.msg.captchaKeyEmail;
          break;
        case 4:
          if (!url) return this.$message.error('请重新上传头像');
          data.avatar = url;
          break;
      }

      $.ajax({
        url: '../public/user',
        type: 'put',
        contentType: 'application/json;charset=utf-8',
        dataType: 'json',
        async: false,
        data: JSON.stringify(data),
        success: function (result) {
          if (result.success) {
            let data = result.data;
            _this.$message({
              message: '修改成功',
              type: 'success',
            });
            _this.modifyCallback(type);
          } else {
            _this.$message.error(result.msg);
          }
        },
        error: function () {
          _this.$message.error('网络异常,请稍后重试');
        },
      });
    },
    upDataAvatar() {
      this.$refs.cropper.getCropData(imgData => {
        // do something
        console.log(imgData);
        // debugger
        imgData = dataURLtoFile(imgData, this.file.name);
        var _this = this;

        // var file =this.file.raw;
        var data = new FormData();
        data.append('file', imgData);

        $.ajax({
          url: '../public/avatar/upload',
          type: 'post',
          dataType: 'json',
          async: false,
          data: data,
          // 不校验数据
          processData: false,
          contentType: false,

          success: function (result) {
            if (result.success) {
              let data = result.data;
              _this.uploadData(4, data);
            } else {
              _this.$message.error(result.msg);
            }
          },
          error: function () {
            _this.$message.error('网络异常,请稍后重试');
          },
        });
      });
    },
    resetInputData() {
      this.inputData.userNameChange = this.$refs.getName.textContent;
      this.inputData.userPhoneChange = this.$refs.getPhoneNumber.textContent;
      this.inputData.userEmailChange = this.$refs.getEmail.textContent;
    },
    modifyCallback(type, data) {
      // 同步更新数据
      /**
       * @param type:1 修改昵称  2:修改手机号  3:修改email  4：修改头像
       */
      setTimeout(function () {
        location.reload();
      }, 500);
      this.hideAlltap();
    },
    hideAlltap() {
      // 关闭弹出框
      for (var key in this.showMaskBox) {
        if (this.showMaskBox[key]) {
          this.showMaskBox[key] = false;
        }
      }
    },
    // 移动端 显示头像框
    showTitleChangeBox() {
      var uploadmb = document.querySelector('.el-upload--text');
      uploadmb.click();
    },
  },
});
