Vue.component('popup',{
    template : `  
  <div class="popup-container">
    <div class="mask-content" @click="closePopup">
    </div>
    <div class="popup-content">
        <div class="popup-header">
            {{titleDesc}}
            <span class="close" @click="closePopup" v-if="!isMobile">
                <span class="img-box"></span>
            </span>
        </div>
       
         <slot></slot>
    </div>
  </div>`,
    props: {
        titleDesc: {
            type: String,
            default: '服务&隐私协议'
        }
    },
    data() {
        return {
            isMobile: isMobile(),
        }
    },
    mounted() {
    },
    methods: {
        closePopup() {
            this.$emit('close-popup')
        }
    }
})