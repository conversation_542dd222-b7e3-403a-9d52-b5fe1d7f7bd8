let checkPhone = (rule, value, callback) => {
    var captcha =$("input[name='captcha']").val();
    if (value && (value.length==11) && captcha) {
        addClass();
    } else {
        removeClass();
    }
    callback();
};
let checkCode = (rule, value, callback) => {
    var phoneNumber =$("input[name='phoneNumber']").val();
    if (value && (value.length==6) && phoneNumber) {
        addClass();
    } else {
        removeClass();
    }
    callback();
};
let checkPwd = (rule, value, callback) => {
    var password2 =$("input[name='password2']").val();
    if (value && (value.length>=6) && (password2.length>=6)) {
        addClassPassword()
    }else {
        removeClassPassword()
    }
    callback();
};
let checkConfirmPwd = (rule, value, callback) => {
    var password =$("input[name='password']").val();
    if (value && (value.length>=6) && (password.length>=6)) {
        addClassPassword()
    }else {
        removeClassPassword()
    }
    callback();
};

function addClass() {
    $(".dia-box-register .nextStepBtn").attr("disabled", false);
    $(".dia-box-register .nextStepBtn").addClass("isOperate");
    $(".dia-box-register .nextStepBtn").removeClass("is-disabled");
}
function removeClass() {
    if ($(".dia-box-register .nextStepBtn").hasClass("isOperate")) {
        $(".dia-box-register .nextStepBtn").attr("disabled", true);
        $(".dia-box-register .nextStepBtn").removeClass("isOperate");
        $(".dia-box-register .nextStepBtn").addClass("is-disabled");
    }
}

function addClassPassword() {
    $(".dia-box-setpwd .nextStepBtn").attr("disabled", false);
    $(".dia-box-setpwd .nextStepBtn").addClass("isOperate");
    $(".dia-box-setpwd .nextStepBtn").removeClass("is-disabled");
}

function removeClassPassword() {
    if ($(".dia-box-setpwd .nextStepBtn").hasClass("isOperate")) {
        $(".dia-box-setpwd .nextStepBtn").attr("disabled", true);
        $(".dia-box-setpwd .nextStepBtn").removeClass("isOperate");
        $(".dia-box-setpwd .nextStepBtn").addClass("is-disabled");
    }
}