(function(doc, win) {
    var docEl = doc.documentElement;
    var resizeEvt =
        "orientationchange" in window ? "orientationchange" : "resize";
    var recalc = function() {
        var clientWidth = docEl.clientWidth;
        if (!clientWidth) {
            return false;
        } else {
            docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
        }
    };
    if (!doc.addEventListener) return;
    win.addEventListener(resizeEvt, recalc, false);
    doc.addEventListener("DOMContentLoaded", recalc, false);
})(document, window);

new Vue({
    el: ".container",
    data() {
        if (errorMsg != null) {
            showAlert(this, 'error', errorMsg);
        }
        let lang = handleRegionAndLang();

        this.langMsg = i18n[lang].register;

        return {
            isIos: isIos(),
            isMobile: isMobile(),
            lang: lang,
            btnDisabled: true,
            registerVis: true,
            codeVis: false,
            setPwdVis: false,
            lastCheckKey: '',
            msg: "发送验证码",
            count: 60,
            allowRules:false,
            registerForm: {
                tel: "",
                email: "",
                code: "",
                captchaKey: "",
                pwd: "",
                lastCheckKey: ''
            },
            // registerFormForEn: {
            //   email: '',
            //   pwd: "",
            //   code: '',
            //   captchaKey: '',
            //   lastCheckKey: ''
            // },
            // registerFormForEnRules: {
            //   email: [
            //     { required: true, message: this.langMsg.emailEmpty, trigger: 'blur' },
            //     { type: 'email', message: this.langMsg.emailErrorMsg, trigger: 'blur' }
            //   ],
            //   tel: [
            //     { required: true, message:this.langMsg.phoneEmpty, trigger: 'blur' },
            //     { pattern: /^1\d{10}$/, message: this.langMsg.phoneErrorMsg, trigger: 'blur' }
            //   ],
            //   code: [
            //     { required: true, message: this.langMsg.codeEmpty, trigger: 'blur' },
            //   ],
            //   pwd: [
            //     { required: true, message: this.langMsg.pwdEmpty, trigger: 'blur' },
            //     { min: 6, message: this.langMsg.pwdError, trigger: "blur" },
            //   ]
            // },
            alertInfo: {
                show: false,
                type: 'error',
                title: '',
                top: 0
            },
            langMsg: {},
            pwdFlag: false,
            showPopupFlag:false,
            popupCallback: null,

            registerAccount:'',
            isPhone: true,
        };
    },
    computed: {
        pwdType() {
            return this.pwdFlag ? "text" : "password";
        },
        isOverseas() {
            return isOverseas();
        },
        isYeelight() {
            return isYeelight();
        },
        isDevelop() {
            return isDevelop()
        },
        registerFormRules() {

            return {
                email: [
                    {required: true, message: this.langMsg.emailEmpty, trigger: 'blur'},
                    {type: 'email', message: this.langMsg.emailErrorMsg, trigger: 'blur'}
                ],
                tel: [
                    {required: true, message: this.langMsg.phoneEmpty, trigger: 'blur'},
                    {pattern: /^1\d{10}$/, message: this.langMsg.phoneErrorMsg, trigger: 'blur'}
                ],
                code: [
                    {required: true, message: this.langMsg.codeEmpty, trigger: 'blur'},
                ],
                pwd: [
                    {required: true, message: this.langMsg.pwdEmpty, trigger: 'blur'},
                    {min: 6, message: this.langMsg.pwdError, trigger: "blur"},
                    {validator: this.validatePass, trigger: "blur"}
                ],
                confirmPwd: [{validator: checkConfirmPwd, trigger: "change"}]
            }
        },
    },
    mounted() {
        this.langMsg = i18n[this.lang].register;
        this.msg = this.langMsg.sendMsg;
        document.title = this.langMsg.pageTitle;
        let formBox = document.querySelector('.form-box');
        this.alertInfo.top = formBox && (formBox.offsetTop - 90);
        this.getEmailCode = debounce(this.getEmailCode, 1000);
        this.getCode = debounce(this.getCode, 1000);

        //获取url account参数
        this.registerAccount = getQueryString('account');
        if(this.registerAccount){
            if(/^1\d{10}$/.test(this.registerAccount)) {
                this.isPhone = true;
                this.registerForm.tel = this.registerAccount;
            }else {
                this.isPhone = false;
                this.registerForm.email = this.registerAccount;
            }
        }
    },
    methods: {
        validatePass  (rule, value, callback)  {
            if (value === '') {
                callback(new Error(this.langMsg.pwdError));
            } else if(this.checkChinese(value)) {
                callback(new Error(this.langMsg.pwdFormatCheck));
            } else {
                callback();
            }
        },
        handleCloseAlert() {
            this.alertInfo.show = false;
        },
        switchAccountType() {
            this.isPhone = !this.isPhone;
            this.$refs.registerForm.resetFields();
        },
        nextStep(evt) {
            // handleFocus(evt);
            var params = {
                this: this,
                captcha: $("input[name='captcha']").val(),
                captchaKey: $('#smsCaptchaKey').val(),
                number:$("input[name='phoneNumber']").val(),
            }
            preCheck(params,()=>{
                // 发送验证码
                this.registerVis = false;
                this.setPwdVis = true;
            });
        },
        // nextStepEn(evt) {
        //   // handleFocus(evt);
        //   this.$refs.registerFormForEn.validate( valid => {
        //     if(valid) {
        //       var params = {
        //         this: this,
        //         captcha: this.registerFormForEn.code,
        //         captchaKey: this.registerFormForEn.captchaKey,
        //         number: this.registerFormForEn.email,
        //       }
        //       preCheck(params,()=>{
        //         // 发送验证码
        //         this.registerVis = false;
        //         this.setPwdVis = true;
        //       });
        //     }
        //   })
        // },
        // 海外注册
        doRegister(evt) {
            // handleFocus(evt);
            let _this = this;
            this.$refs.registerForm.validate( valid => {
                if(valid) {
                    let params = {
                        email: this.registerForm.email,
                        captcha: this.registerForm.code,
                        captchaKey: this.registerForm.captchaKey,
                        password: this.registerForm.pwd,
                        password2: this.registerForm.pwd,
                    }
                    if(this.setPwdVis) {
                        params.captcha = 'CHECK_LAST_RESULT';
                        params.captchaKey = this.lastCheckKey;
                    }
                    $.ajax({
                        url: '../user/register',
                        // url: 'http://api-dev.yeedev.com/apis/account/user/register',
                        type: 'post',
                        contentType: "application/x-www-form-urlencoded;charset=utf-8",
                        async: false,
                        data: params,
                        success: function (result) {
                            if (result.success) {
                                let data = result.data;
                                showAlert(_this, 'success', 'Registered Successfully');
                                setTimeout(function () {
                                    location.href = '../oauth/login'
                                },500)
                            } else {
                                showAlert(_this, 'error', result.msg);
                            }
                            _this.showPopupFlag = false;
                        },
                        error: function () {
                            showAlert(_this, 'error', 'Network error, please try again later');
                            _this.showPopupFlag = false;
                        }
                    });
                }
            })

        },
        // 注册
        register(evt) {
            // handleFocus(evt);
            let _this = this;
            this.$refs.registerForm.validate( valid => {
                if(valid) {
                    var params = {
                        phoneNumber: this.registerForm.tel,
                        captcha: this.registerForm.code,
                        captchaKey: this.registerForm.captchaKey,
                        password: this.registerForm.pwd,
                        password2: this.registerForm.pwd,
                    };

                    $.ajax({
                        url: '../user/register',
                        // url: 'http://api-dev.yeedev.com/apis/account/user/register',
                        type: 'post',
                        contentType: "application/x-www-form-urlencoded;charset=utf-8",
                        dataType: 'json',
                        async: false,
                        data: params,
                        success: function (result) {
                            if (result.success) {
                                let data = result.data;
                                showAlert(_this, 'success', '注册成功');
                                setTimeout(function () {
                                    location.href = '../oauth/login'
                                }, 500)
                            } else {
                                showAlert(_this, 'error', result.msg);
                            }
                            _this.showPopupFlag = false;
                        },
                        error: function () {
                            showAlert(_this, 'error', '网络异常,请稍后重试');
                            _this.showPopupFlag = false;
                        }
                    })
                }
            })
        },
        getCode() {
            if(this.isPhone) return this.getPhoneCode();
            return this.getEmailCode();
        },
        getEmailCode() {
            let that = this;
            let email = this.registerForm.email;
            let reg = /^[A-Za-z0-9-._]+@[A-Za-z0-9-]+(.[A-Za-z0-9]+)*(.[A-Za-z]{2,6})$/;
            let valid = ''
            this.$refs.registerForm.validateField('email', res => {
                console.log(res)
                valid = res;
            })
            if(valid) return;
            let timer = setInterval(() => {
                if (this.count == 0) {
                    this.msg = this.langMsg.sendMsg;
                    this.count = 60;
                    if (!$(".getEmailCode").hasClass("canOperate")) {
                        $(".getEmailCode").addClass("canOperate");
                    }
                    $(".getEmailCode").attr("disabled", false);
                    $(".getEmailCode").css({
                        color: '#333',
                        'border-color': 'rgba(45, 42, 38, 1)',
                    });
                    clearInterval(timer);
                } else {
                    this.count--;
                    this.msg = `${this.langMsg.reSendMsg}(${this.count})`;
                    $(".getEmailCode").attr("disabled", true);
                    $(".getEmailCode").css({
                        color: "#DCDFE6",
                        "border-color": "#DCDFE6"
                    });

                }
            }, 1000);
            $.ajax({
                url: '../public/email/captcha',
                // url: 'http://api-dev.yeedev.com/apis/account/public/email/captcha',
                type: 'get',
                dataType: 'json',
                async: false,
                data: {email: email, exist: true},
                success: function (result) {
                    console.log(result)
                    if (result.success) {
                        let data = result.data;
                        that.registerForm.captchaKey = data.captchaKey;
                    } else {
                        showAlert(that, 'error', result.msg);
                        clearInterval(timer);
                    }
                },
                error: function () {
                    showAlert(that, 'error', 'Network error, please try again later');
                    clearInterval(timer);
                }
            });
        },
        getPhoneCode() {
            let _this = this;
            let phoneNumber = this.registerForm.tel;
            let valid = ''
            this.$refs.registerForm.validateField('tel', res => {
                valid = res
            })
            if(valid) return ;
            clearInterval(timer);
            var timer = setInterval(function() {
                if (_this.count == 0) {
                    _this.msg = "发送验证码";
                    _this.count = 60;
                    if (!$(".gainCode").hasClass("canOperate")) {
                        $(".gainCode").addClass("canOperate");
                    }
                    $(".gainCode").attr("disabled", false);
                    $(".gainCode").css({
                        color: '#333',
                        "border-color": "rgba(45, 42, 38, 1)"
                    });
                    clearInterval(timer);
                } else {
                    _this.count--;
                    _this.msg = "重新发送(" + _this.count + "s)";
                    $(".gainCode").attr("disabled", true);
                    $(".gainCode").css({
                        color: "#DCDFE6",
                        "border-color": "#DCDFE6"
                    });
                }
            }, 1000);

            let resParam = getCaptchaKey(phoneNumber);
            if(!resParam.ticket){
                clearInterval(timer);
                return;
            }

            $.ajax({
                url: '../public/mobile/captcha',
                type: 'post',
                contentType: 'application/json;charset=utf-8',
                dataType: 'json',
                async: false,
                data: JSON.stringify({ phoneNumber: phoneNumber, exist: true, ticket: resParam.ticket, timestamp: resParam.param.timestamp }),
                success: function (result) {
                    if (result.success) {
                        let data = result.data;
                        _this.registerForm.captchaKey = data.captchaKey;
                        $('#smsCaptchaKey').val(data.captchaKey);
                        res =  true;
                    } else {
                        showAlert(_this, 'error', result.msg);
                        clearInterval(timer);
                    }
                },
                error: function () {
                    showAlert(_this, 'error', '网络异常,请稍后重试');
                    clearInterval(timer);
                }
            });
        },
        containSpecial(str) {
            var containSpecial = RegExp(
                /[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\,)(\/)(\<)(\>)(\?)(\)]+/
            );
            return containSpecial.test(str);
        },
        checkChinese(val) {
            var reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");
            return reg.test(val);
        },
        showPopup(refName,callbackFun){
            let popRefName = this.$refs[refName];
            popRefName.validate( valid => {
                if(valid) {
                    this.showPopupFlag = true;
                    this.popupCallback = this.isPhone? this.register : this.doRegister;
                }
            });
        },
        closePopup() {
            this.showPopupFlag = false;
        },
        doPopupFun() {
            typeof this.popupCallback === 'function' && this.popupCallback();
        }
    }
});

