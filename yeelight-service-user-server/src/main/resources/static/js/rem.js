(function(doc, win) {
  var docEl = doc.documentElement;
  var resizeEvt =
      "orientationchange" in window ? "orientationchange" : "resize";
  var recalc = function() {
    var clientWidth = docEl.clientWidth;
    if (!clientWidth) {
      return false;
    } else {
      docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
      // if (clientWidth <= 414) {
      //   docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
      // } else {
      //   docEl.style.fontSize = 3.125 * (clientWidth / 375) + "px";
      // }
    }
  };
  if (!doc.addEventListener) return;
  win.addEventListener(resizeEvt, recalc, false);
  doc.addEventListener("DOMContentLoaded", recalc, false);
})(document, window);