new Vue({
    el: '.Container',
    data() {
        let lang = handleRegionAndLang();
        const langMsg = i18n[lang].tokens;
        if (errorMsg != null) {
            this.$message.error(errorMsg);
        }
        return {
            lang,
            langMsg,
            browserList: [
            ]
        };
    },
    mounted() {
        document.title = this.langMsg.pageTitle;
        this.getAuthorizedList();
    },
    methods: {
        getAuthorizedList() {
            let that = this;
            $.ajax({
                url:'../oauth/authorizedTokens',
                type:'get',
                data:{
                    userId:yeelightUserID,
                },
                success:function (result) {
                    if (result.success) {
                        let data = result.data;
                        console.log('data: ,',data);
                        data.forEach(item=>item.showDetail = false)
                        that.browserList = data;
                    }
                }
            })
        },
        getImgSrc(item) {
            if(!broserEnum[item.browserName]) return broserEnum.Default;
            return broserEnum[item.browserName]
        },
        logout(typeId,desc) {
            if(!typeId) return;
            this.$alert(this.langMsg.confirmText+desc+'?', desc, {
                confirmButtonText: this.langMsg.confirmText,
                cancelButtonText: this.langMsg.cancelText,
                callback: action => {
                    // this.$message({
                    //     type: 'info',
                    //     message: `action: ${ action }`
                    // });
                    if(action === 'confirm') {
                        let url = '../oauth/removeToken';
                        let data = {
                            token: typeId
                        }
                        if(typeId === 'all') {
                            url = '../oauth/removeTokens';
                            data = {
                            }
                        }
                        let that = this;
                        $.ajax({
                            url:url,
                            type:'get',
                            data:data,
                            success:function (result) {
                                if (result.success) {
                                    that.$message({
                                        type: 'success',
                                        message: that.langMsg.logoutSuccess
                                    })
                                    that.getAuthorizedList();
                                }
                            }
                        })
                    }
                }
            });
        },
        getEnumDesc(key,enumObj) {
            if(!key || !enumObj[key]) return '';
            return enumObj[key];
        }
    },
});
