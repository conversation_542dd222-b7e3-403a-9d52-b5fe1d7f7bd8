(function(doc, win) {
  var docEl = doc.documentElement;
  var resizeEvt =
      "orientationchange" in window ? "orientationchange" : "resize";
  var recalc = function() {
    var clientWidth = docEl.clientWidth;
    if (!clientWidth) {
      return false;
    } else {
      docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
    }
  };
  if (!doc.addEventListener) return;
  win.addEventListener(resizeEvt, recalc, false);
  doc.addEventListener("DOMContentLoaded", recalc, false);
})(document, window);

new Vue({
  el: ".container",
  data() {
    if (errorMsg != null) {
      showAlert(this, 'error', errorMsg);
    }
    let lang = handleRegionAndLang();

    this.langMsg = i18n[lang].userSignedIn;

    return {
      isIos: isIos(),
      isMobile: isMobile(),
      lang: lang,
      langMsg: {}
    };
  },
  computed: {
    isOverseas() {
      return isOverseas();
    },
    isYeelight() {
      return isYeelight();
    },
    isDevelop() {
      return isDevelop()
    },
  },
  mounted() {
    this.langMsg = i18n[this.lang].userSignedIn;
    document.title = this.langMsg.pageTitle;
  },
  methods: {
    quickLogin() {
      window.location.href = '../oauth/existingUser';
    }
  }
});

