<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title th:text="${VENDOR_NAME}"></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
    <link rel="stylesheet" th:href="@{/apis/account/static/css/common.css}" />
    <link rel="stylesheet" th:href="@{/apis/account/static/css/error.css}" />
    <script>
      (function(doc, win) {
        var docEl = doc.documentElement;
        var resizeEvt =
                "orientationchange" in window ? "orientationchange" : "resize";
        var recalc = function() {
          var clientWidth = docEl.clientWidth;
          console.log(clientWidth);
          if (!clientWidth) {
            return false;
          } else {
            if (clientWidth <= 414) {
              docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
            } else {
              docEl.style.fontSize = 3.125 * (clientWidth / 375) + "px";
            }
          }
        };
        if (!doc.addEventListener) return;
        win.addEventListener(resizeEvt, recalc, false);
        doc.addEventListener("DOMContentLoaded", recalc, false);
      })(document, window);
    </script>
  </head>
  <body>
    <div class="container">
      <img
        th:src="@{/apis/account/static/img/error.png}"
        alt=""
        style="width: 10rem;height: 10rem;display: block;margin: 0 auto;margin-top: 15rem;margin-bottom: 5.4rem;"
      />
      <div style="text-align: center;width: 80%;margin-left:10%;margin-bottom: 5.4rem;font-size: 1.2rem; color: red;" th:text="${errorCode}"></div>
      <div class="button-box">
        <button type="button" class="btn" onclick="window.history.go(-1)">返回</button>
      </div>
    </div>
    <div class="error-detail" style="display: none" th:text="${errorSummary}"></div>
  </body>
</html>
