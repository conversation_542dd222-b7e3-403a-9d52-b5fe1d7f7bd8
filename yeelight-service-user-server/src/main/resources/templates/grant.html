<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" style="height: 100%">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
    <link rel="stylesheet" th:href="@{../static/css/element-ui.css}" />
    <link rel="stylesheet" th:href="@{../static/css/common.css}" />
    <link rel="stylesheet" th:href="@{../static/css/auth.css}" />
    <title th:text="${VENDOR_NAME}"></title>
    <script>
        (function(doc, win) {
            var docEl = doc.documentElement;
            var resizeEvt =
                "orientationchange" in window ? "orientationchange" : "resize";
            var recalc = function() {
                var clientWidth = docEl.clientWidth;
                if (!clientWidth) {
                    return false;
                } else {
                    docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
                    // if (clientWidth <= 414) {
                    //     docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
                    // }
                }
            };
            if (!doc.addEventListener) return;
            win.addEventListener(resizeEvt, recalc, false);
            doc.addEventListener("DOMContentLoaded", recalc, false);
        })(document, window);
    </script>
</head>
<body style="height: 100%; margin: 0" ontouchstart>
    <div class="Container">
        <div class="container PCcontainer" :class="{'container-cn': lang==='zh-cn'}" v-cloak>
            <div class="PCBox" v-cloak>
                <div class="d-flex box-content">

                    <div class="inputBox-pc">
                        <div class="form-box form-box-code">
                            <div class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                            <div>
                                <section class="mb-top-logo-cn mb-top-logo">
                                    <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>
                                </section>
                                <div class="top">
                                    <div class="bg-box">
                                        <div class="img-box">
                                            <div class="page-title">{{langMsg.title}}</div>
                                            <p th:text="${warning}" style="font-size: 1rem;color:gray;"></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="roleList-box">
                                    <div class="listTitle">{{langMsg.tipTitle}}</div>
                                    <div class="listItem">
                                        <span class="dotBox"></span>
                                        <span>{{langMsg.firstTip}}</span>
                                    </div>
                                    <div class="listItem">
                                        <span class="dotBox"></span>
                                        <span>{{langMsg.secondTip}}</span>
                                    </div>
                                    <!--                                <div class="listItem">-->
                                    <!--                                  <span class="dotBox"></span>-->
                                    <!--                                  <span>此后权限条件从后端接口获取</span>-->
                                    <!--                                </div>-->
                                </div>
                                <div class="button-box">
                                    <form name='confirmationForm' th:action="@{../oauth/authorize}" th:method='post'>
                                        <input name='user_oauth_approval' value='true' type='hidden'/>
                                        <input th:name="${s}" value="true" type="hidden" th:each="s : ${scopeList}"/>
                                        <div>
                                            <el-button
                                                    id="submit_once"
                                                    class="isAuth"
                                                    name='authorize'
                                                    onclick="dosubmit()"
                                            >{{langMsg.confirm}}</el-button>
                                        </div>
                                    </form>
                                    <div style="text-align: center;">
                                        <a class="a-black-important checkAccount" onclick="switchAccount(this)" th:href="@{../oauth/logout(followUp=${requestUrl})}">{{langMsg.login}}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>


<script th:src="@{../static/js/vue.js}"></script>
<script th:src="@{../static/js/element-ui.js}"></script>
<script th:src="@{../static/js/jquery.min.js}"></script>
<script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script>
    function switchAccount(e) {
        window.location.href = $(e).attr('href');
    }
    function dosubmit() {
      var btnSubmit = document.getElementById("submit_once");
      btnSubmit.disabled = "true";
      document.confirmationForm.submit();
    }
    new Vue({
        el: ".container",
        data() {
            let lang = handleRegionAndLang();
            this.langMsg = i18n[lang].grant;
            return {
                isMobile: isMobile(),
                lang: lang,
                langMsg: {}
            }
        },
        computed: {
            isOverseas() {
                return isOverseas()
            },
            isYeelight() {
                return isYeelight()
            }
        },
        mounted() {
            this.langMsg = i18n[this.lang].grant;
            document.title = this.langMsg.pageTitle;
        }
    })
</script>
</html>
