<!DOCTYPE html>
<html style="height: 100%" lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="referrer" content="no-referrer-when-downgrade"/>
    <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}"/>
    <link rel="stylesheet" th:href="@{../static/css/element-ui.css}"/>
    <link rel="stylesheet" th:href="@{../static/css/common.css}"/>
    <link rel="stylesheet" th:href="@{../static/css/index.css}"/>
    <link rel="stylesheet" th:href="@{../static/css/popup.css}" />
    <title th:text="${VENDOR_NAME}"></title>
    <script th:src="@{../static/js/rem.js}"></script>
</head>
<body style="height: 100%; margin: 0" ontouchstart>
<div class="Container" v-cloak>
    <transition name="popup">
        <popup v-if="showPopupFlag"  @close-popup="closePopup" :title-desc="langMsg.popupRegister">
            <div class="register-content">
                <div class="account-box">
                    <div class="account-img">
                        <img class="logo" th:src="@{../static/img/login-icon.png}"/>
                    </div>
                    <div>
                        {{registerAccount}}
                    </div>
                </div>
                <div class="desc-box">{{langMsg.popupDesc}}</div>

                <div>
                    <el-button class="register-btn" @click="doPopupFun">{{langMsg.popupRegisterBtn}}</el-button>
                </div>
            </div>
        </popup>
    </transition>
    <!-- 手机端 -->
    <div class="container" :class="{'container-cn': lang==='zh-cn'}" v-if="isMobile">
<!--        大陆服务器-->
        <template v-if="!isOverseas">
<!--        <template v-if="false">-->
            <section class="mb-top-logo-cn mb-top-logo">
                <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>
            </section>
            <div class="page-title">
                {{langMsg.pageTitle}}
            </div>
            <!-- 密码登录 -->
            <div class="loginByPwd-box" v-show="showbox">
                <el-form
                        :model="FormByPwd"
                        id="pwdForm"
                        :rules="FormByPwdRules"
                        ref="pwdForm"
                        th:method="post"
                        th:action="@{../oauth/do_login}"
                >
                    <el-form-item prop="tel">
                        <el-input
                                v-model="FormByPwd.tel"
                                name="username"
                                th:value="${username}"
                                clearable
                                :placeholder="langMsg.placeholder"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="imgCode" th:if="${enableCaptcha}" v-show="showBoxByPwd.imgCode">
                        <el-input
                                v-model="FormByPwd.imgCode"
                                :placeholder="langMsg.code"
                                clearable
                                name="captcha"
                                style="width: 12rem; margin-right: 0.5rem"
                        ></el-input>
                        <img
                                th:if="${enableCaptcha}"
                                @click="changeCaptcha"
                                id="captchaImg"
                                th:src="@{../public/captcha.jpg}"
                                alt=""
                                style="width: 12.5rem; vertical-align: bottom; height: 4.5rem"
                        />
                    </el-form-item>
                    <el-form-item prop="pwd">
                        <el-input
                                v-model="FormByPwd.pwd"
                                :class="[{'ios-hidePwd-input': isIos && !pwdFlag && FormByPwd.pwd }]"
                                :placeholder="langMsg.password"
                                :type="pwdType"
                                name="password"
                        >
                            <img
                                    v-if="pwdFlag && FormByPwd.pwd"
                                    slot="suffix"
                                    class="suffix-icon"
                                    th:src="@{../static/img/eye_view.png}"
                                    @click="pwdFlag=!pwdFlag"
                            />
                            <img
                                    v-if="!pwdFlag && FormByPwd.pwd"
                                    slot="suffix"
                                    class="suffix-icon"
                                    th:src="@{../static/img/eye_hide.png}"
                                    @click="pwdFlag=!pwdFlag"
                            />
                        </el-input>
                    </el-form-item>
                    <div class="forget-pwd-tip">
                        <a th:href="@{../oauth/getpass}" style="text-decoration: none"
                        ><span>{{langMsg.forget}}</span></a
                        >
                    </div>

                    <el-form-item style="margin-top: 2rem">
                        <el-button class="nextStep" style="margin-left: 0" @click="loginByPwd"> {{langMsg.login}}
                        </el-button>
                    </el-form-item>
                </el-form>
                <div class="d-flex toggle-button">
                    <div class="title border-right" @click="toggleLogin(2)">{{langMsg.mobileLogin}}</div>
                    <a class="title" th:href="@{../oauth/register}">{{langMsg.register}}</a>
                </div>
            </div>

            <!-- 验证码登录 -->
            <div class="loginByCode-box" v-show="!showbox">
                <el-form
                        :model="FormByCode"
                        id="codeForm"
                        :rules="FormByCodeRules"
                        ref="codeForm"
                        th:action="@{../authentication/phone_number}"
                        th:method="post"
                >
                    <el-form-item prop="tel">
                        <el-input
                                name="phoneNumber"
                                id="phoneInput"
                                th:value="${username}"
                                v-model="FormByCode.tel"
                                type="tel"
                                maxlength="11"
                                clearable
                                :placeholder="langMsg.phone"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="code">
                        <el-input
                                name="captcha"
                                v-model="FormByCode.code"
                                :placeholder="langMsg.code"
                                class="codeInput"
                                maxlength="6"
                        ></el-input>
                        <input type="hidden" id="smsCaptchaKey" name="captchaKey"/>
                        <button type="button" class="getcode" @click="getCode">{{msg}}</button>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="nextStep" style="margin-left: 0" @click="loginByCode"> {{langMsg.login}}
                        </el-button>
                    </el-form-item>
                </el-form>
                <div class="d-flex toggle-button">
                    <div class="title border-right" @click="toggleLogin(1)">{{langMsg.pwdLogin}}</div>
                    <a class="title" th:href="@{../oauth/register}">{{langMsg.register}}</a>
                </div>
            </div>
            <!--三方登录 render/xiaomi-->
            <div class="third-party-tel" v-if="referrer!='thirdParty'">
<!--            <div class="third-party-tel">-->
                <div class="d-flex d-justify-center">
                    <a v-if="isDevelop || (!isOverseas  && isYeelight)" th:href="@{../third-party-auth/render/wechat_mp}"
                    ><img th:src="@{../static/img/wx_login.png}"/>
                    </a>
                    <a v-if="referrer==='portal' && (isDevelop  || (!isOverseas  && isYeelight))" th:href="@{../third-party-auth/render/feishu_app}"
                    ><img th:src="@{../static/img/feishu_login.png}"/>
                    </a>
                    <a th:href="@{../third-party-auth/render/apple}"
                    ><img th:src="@{../static/img/apple_login.png}"/>
                    </a>
                    <a v-if="isDevelop || isOverseas" th:href="@{../third-party-auth/render/facebook}"
                    ><img th:src="@{../static/img/facebook_login.png}"/>
                    </a>
                    <a v-if="isDevelop || (isOverseas && !isYeelight)" th:href="@{../third-party-auth/render/google}"
                    ><img th:src="@{../static/img/google_login.png}"/>
                    </a>
                    <a v-if="isDevelop || (isOverseas  && !isYeelight)" th:href="@{../third-party-auth/render/amazon}"
                    ><img th:src="@{../static/img/amazon_login.png}"/>
                    </a>
                </div>
            </div>
        </template>
        <template v-else>
            <section class="mb-top-logo-cn mb-top-logo">
                <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>
            </section>
            <div class="page-title">
                {{langMsg.pageTitle}}
            </div>
            <!-- 密码登录 -->
            <div class="form-box-pwd inputBox-pc" v-show="!emailLogin">
                <el-form
                        :model="FormByPwdPC"
                        id="pwdFormPC"
                        :rules="FormByPwdRulesPC"
                        ref="pwdFormPC"
                        th:action="@{../oauth/do_login}"
                        th:method="post"
                >
                    <el-form-item prop="email">
                        <el-input
                                v-model="FormByPwdPC.email"
                                :placeholder="langMsg.email"
                                name="username"
                                clearable
                                th:value="${username}"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="pwd" class="pwd-input-en">
                        <el-input
                                v-model="FormByPwdPC.pwd"
                                :type="pwdType"
                                :class="[{'ios-hidePwd-input': isIos && !pwdFlag && FormByPwdPC.pwd }]"
                                :placeholder="langMsg.password"
                                name="password"
                        >
                            <img
                                    v-if="pwdFlag && FormByPwdPC.pwd"
                                    slot="suffix"
                                    class="suffix-icon"
                                    th:src="@{../static/img/eye_view.png}"
                                    @click="pwdFlag=!pwdFlag"
                            />
                            <img
                                    v-if="!pwdFlag && FormByPwdPC.pwd"
                                    slot="suffix"
                                    class="suffix-icon"
                                    th:src="@{../static/img/eye_hide.png}"
                                    @click="pwdFlag=!pwdFlag"
                            />
                        </el-input>
                    </el-form-item>

                    <div class="forget-pwd-tip">
                        <a th:href="@{../oauth/getpass}"><span>{{langMsg.forget}}</span></a>
                    </div>

                    <el-form-item>
                        <el-button class="loginBtn loginBtnPC loginBtnPCSMSPassword"
                                   style="margin-top: 20px;"
                                   @click="loginByPwdPC"
                        >{{langMsg.login}}
                        </el-button>
                    </el-form-item>

                    <div class="d-flex toggle-button">
                        <div class="title border-right" @click="emailLogin = true;">{{langMsg.mobileLogin}}</div>
                        <a class="title" th:href="@{../oauth/register}">{{langMsg.register}}</a>
                    </div>

                </el-form>
            </div>

            <!-- 验证码登录 -->
            <div class="loginByCode-box dia-box-retpwd" v-show="emailLogin">
                <el-form
                        :model="FormByEmailCode"
                        id="emailForm"
                        :rules="FormByEmailCodeRules"
                        ref="emailForm"
                        th:action="@{../authentication/email}"
                        th:method="post"
                >
                    <el-form-item prop="email">
                        <el-input
                                v-model="FormByEmailCode.email"
                                :placeholder="langMsg.email"
                                name="email"
                                th:value="${email}"
                        ></el-input>
                    </el-form-item>
                    <el-form-item prop="code" class="pwd-input-en">
                        <el-input
                                name="captcha"
                                v-model="FormByEmailCode.code"
                                :placeholder="langMsg.code"
                                class="codeInput"
                                maxlength="6"
                        ></el-input>
                        <input type="hidden" id="smsCaptchaKey" name="captchaKey"/>
                        <button type="button" class="getcode gainCode" @click="getCode('email')">{{msg}}
                        </button>
                    </el-form-item>

<!--                    <div class="forget-pwd-tip">-->
<!--                        <a th:href="@{../oauth/getpass}"><span>{{langMsg.forget}}</span></a>-->
<!--                    </div>-->

                    <el-form-item>
                        <el-button class="loginBtn loginBtnPC loginBtnPCSMSPassword"
                                   style="margin-top: 20px;"
                                   @click="loginByEmailCode"
                        >{{langMsg.login}}
                        </el-button>
                    </el-form-item>
                </el-form>

                <div class="d-flex toggle-button">
                    <div class="title border-right" @click="emailLogin = false;">{{langMsg.passwordLogin}}</div>
                    <a class="title" th:href="@{../oauth/register}">{{langMsg.register}}</a>
                </div>
            </div>

            <!--三方登录 render/xiaomi-->
            <div class="third-party-tel" v-if="referrer!='thirdParty'">
                <!--            <div class="third-party-tel">-->
                <div class="d-flex d-justify-center">
                    <a v-if="isDevelop || (!isOverseas  && isYeelight)" th:href="@{../third-party-auth/render/wechat_mp}"
                    ><img th:src="@{../static/img/wx_login.png}"/>
                    </a>
                    <a v-if="referrer==='portal' && (isDevelop || (!isOverseas  && isYeelight))" th:href="@{../third-party-auth/render/feishu_app}"
                    ><img th:src="@{../static/img/feishu_login.png}"/>
                    </a>
                    <a th:href="@{../third-party-auth/render/apple}"
                    ><img th:src="@{../static/img/apple_login.png}"/>
                    </a>
                    <a v-if="isDevelop || isOverseas" th:href="@{../third-party-auth/render/facebook}"
                    ><img th:src="@{../static/img/facebook_login.png}"/>
                    </a>
                    <a v-if="isDevelop || (isOverseas && !isYeelight)" th:href="@{../third-party-auth/render/google}"
                    ><img th:src="@{../static/img/google_login.png}"/>
                    </a>
                    <a v-if="isDevelop || (isOverseas  && !isYeelight)" th:href="@{../third-party-auth/render/amazon}"
                    ><img th:src="@{../static/img/amazon_login.png}"/>
                    </a>
                </div>
            </div>
        </template>
    </div>

    <!-- PC端 -->
    <div class="container PCcontainer" :class="{'container-cn': lang==='zh-cn'}" v-else>
        <div class="PCBox" v-cloak>
            <div class="d-flex box-content">
                <!--大陆用户-->
                <template v-if="!isOverseas">
<!--                <template v-if="false">-->
                    <div class="inputBox-pc inputBox-pc-cn">
                        <transition name="fade">
                            <el-alert
                                    show-icon
                                    v-if="alertInfo.show"
                                    class="feedback-alert feedback-alert-cn"
                                    :title="alertInfo.title"
                                    :type="alertInfo.type"
                                    :style="{top: alertInfo.top + 'px'}"
                                    @close="alertInfo.show = false"
                            >
                            </el-alert>
                        </transition>

                        <!-- 密码登录 -->
                        <div class="form-box-pwd form-box" v-show="pwdPCVisiable"
                             :class="{ 'slide-left': !pwdPCVisiable }">
                            <div class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                            <div class="page-title">
                                {{langMsg.pageTitle}}
                            </div>
                            <el-form
                                    :model="FormByPwdPC"
                                    id="pwdFormPC"
                                    :rules="FormByPwdRulesPC"
                                    ref="pwdFormPC"
                                    @keyup.enter.native="loginByPwdPC"
                                    th:action="@{../oauth/do_login}"
                                    th:method="post"
                                    autocomplete="off"
                            >
                                <el-form-item prop="tel">
                                    <el-input
                                            v-model="FormByPwdPC.tel"
                                            id="FormByPwdPCTel"
                                            :placeholder="langMsg.placeholder"
                                            name="username"
                                            clearable
                                            th:value="${username}"
                                    >
                                    </el-input>
                                </el-form-item>
                                <el-form-item prop="pwd">
                                    <el-input
                                            v-model="FormByPwdPC.pwd"
                                            :type="pwdType"
                                            id="FormByPwdPCPwd"
                                            :placeholder="langMsg.passwordPlaceholder"
                                            name="password"
                                    >
                                        <img
                                                v-if="pwdFlag && FormByPwdPC.pwd"
                                                slot="suffix"
                                                class="suffix-icon"
                                                th:src="@{../static/img/eye_view.png}"
                                                @click="pwdFlag=!pwdFlag"
                                        />
                                        <img
                                                v-if="!pwdFlag && FormByPwdPC.pwd"
                                                slot="suffix"
                                                class="suffix-icon"
                                                th:src="@{../static/img/eye_hide.png}"
                                                @click="pwdFlag=!pwdFlag"
                                        />
                                    </el-input>
                                </el-form-item>
                                <el-form-item th:if="${enableCaptcha}" prop="imgCode">
                                    <el-input
                                            th:if="${enableCaptcha}"
                                            v-model="FormByPwdPC.imgCode"
                                            id="FormByPwdPCImgCode"
                                            name="captcha"
                                            placeholder="短信验证码"
                                            prefix-icon="el-icon-chat-line-square"
                                            clearable
                                            class="imgCodeInput"
                                    ></el-input>
                                    <img
                                            th:if="${enableCaptcha}"
                                            th:src="@{../public/captcha.jpg}"
                                            id="captchaImgPC"
                                            @click="changeCaptchaPC"
                                            alt=""
                                    />
                                </el-form-item>
                                <div class="forget-pwd-tip">
                                    <a th:href="@{../oauth/getpass}"><span>{{langMsg.forget}}</span></a>
                                </div>
                                <!--                                <a th:href="@{../third-party-auth/render/xiaomi}">Xiaomi Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/wechat_mp}">Wechat Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/wechat_open}">Wechat Qrcode Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/feishu_app}">Feishu Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/google}">Google Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/amazon}">Amazon Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/amazon_alexa}">Amazon Alexa Login</a>-->
                                <!--                                <a th:href="@{../third-party-auth/render/apple}">Apple Login</a>-->
                                <el-form-item>
                                    <el-button class="loginBtn loginBtnPC loginBtnPCSMSPassword" @click="loginByPwdPC"
                                    >{{langMsg.login}}
                                    </el-button>
                                </el-form-item>
                            </el-form>


                            <div class="footer">
                                <div class="a-black-important left-content" @click="toggleLoginPC(2)">
                                    {{langMsg.mobileLogin}}
                                </div>
                                <div>
                                    <a th:href="@{../oauth/register}"
                                    ><span class="a-black-important">{{langMsg.register}}</span></a
                                    >
                                </div>
                            </div>
                            <!--三方登录-->
                            <div class="third-party-pc" v-if="referrer!='thirdParty'">
<!--                            <div class="third-party-pc" v-if="lang==='zh-cn'">-->
                                <div class="d-flex d-justify-center">
                                    <a v-if="isDevelop || (!isOverseas  && isYeelight)" th:href="@{../third-party-auth/render/wechat_open}"
                                    ><img th:src="@{../static/img/wx_login.png}"/>
                                    </a>
                                    <a v-if="referrer==='portal' && (isDevelop || (!isOverseas  && isYeelight))" th:href="@{../third-party-auth/render/feishu_app}"
                                    ><img th:src="@{../static/img/feishu_login.png}"/>
                                    </a>
                                    <a th:href="@{../third-party-auth/render/apple}"
                                    ><img th:src="@{../static/img/apple_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || isOverseas" th:href="@{../third-party-auth/render/facebook}"
                                    ><img th:src="@{../static/img/facebook_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas && !isYeelight)" th:href="@{../third-party-auth/render/google}"
                                    ><img th:src="@{../static/img/google_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas  && !isYeelight)" th:href="@{../third-party-auth/render/amazon}"
                                    ><img th:src="@{../static/img/amazon_login.png}"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <!-- 验证码登录 -->
                        <div class="form-box-code form-box" v-show="!pwdPCVisiable"
                             :class="{ 'slide-right': pwdPCVisiable }">
                            <div class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                            <div class="page-title">
                                {{langMsg.pageTitle}}
                            </div>

                            <el-form
                                    :model="FormByCodePC"
                                    id="codeFormPC"
                                    :rules="FormByCodeRulesPC"
                                    ref="codeFormPC"
                                    @keyup.enter.native="loginByCodePC"
                                    th:action="@{../authentication/phone_number}"
                                    th:method="post"
                            >
                                <el-form-item
                                        prop="tel"
                                        :rules="[
                        { required: true, message: `${langMsg.mobileEmpty}`, trigger: 'blur' },
                        { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: `${langMsg.mobileError}`, trigger: ['blur', 'change']},
                      ]"
                                >
                                    <el-input
                                            v-model="FormByCodePC.tel"
                                            id="phoneInputPC"
                                            clearable
                                            maxlength="11"
                                            :placeholder="langMsg.phone"
                                            name="phoneNumber"
                                            th:value="${username}"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item prop="code" class="captchaInput-SMS">
                                    <el-input
                                            name="captcha"
                                            v-model="FormByCodePC.code"
                                            id="captchaInputPC"
                                            maxlength="6"
                                            clearable
                                            :placeholder="langMsg.code"
                                    ></el-input>
                                    <input type="hidden" id="smsCaptchaKeyPC" name="captchaKey"/>
                                    <button type="button" class="getcode" @click="getCodePC" style="padding-left: 0">{{msg}}</button>
                                </el-form-item>
                                <div class="forget-pwd-tip">
                                    <a th:href="@{../oauth/getpass}"><span>{{langMsg.forget}}</span></a>
                                </div>
                                <el-form-item>
                                    <el-button class="loginBtn loginBtnPC loginBtnPCSMS" @click="loginByCodePC">
                                        {{langMsg.login}}
                                    </el-button>
                                </el-form-item>
                            </el-form>
                            <div class="footer">
                                <div class="a-black-important left-content" @click="toggleLoginPC(1)">
                                    {{langMsg.pwdLogin}}
                                </div>
                                <div>
                                    <a th:href="@{../oauth/register}"
                                    ><span class="a-black-important">{{langMsg.register}}</span></a
                                    >
                                </div>
                            </div>
                            <!--三方登录-->
                            <div class="third-party-pc" v-if="referrer!='thirdParty'">
                                <div class="d-flex d-justify-center">
                                    <a v-if="isDevelop || (!isOverseas  && isYeelight)" th:href="@{../third-party-auth/render/wechat_open}"
                                    ><img th:src="@{../static/img/wx_login.png}"/>
                                    </a>
                                    <a v-if="referrer==='portal' && (isDevelop || (!isOverseas  && isYeelight))" th:href="@{../third-party-auth/render/feishu_app}"
                                    ><img th:src="@{../static/img/feishu_login.png}"/>
                                    </a>
                                    <a th:href="@{../third-party-auth/render/apple}"
                                    ><img th:src="@{../static/img/apple_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || isOverseas" th:href="@{../third-party-auth/render/facebook}"
                                    ><img th:src="@{../static/img/facebook_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas && !isYeelight)" th:href="@{../third-party-auth/render/google}"
                                    ><img th:src="@{../static/img/google_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas  && !isYeelight)" th:href="@{../third-party-auth/render/amazon}"
                                    ><img th:src="@{../static/img/amazon_login.png}"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <transition name="fade">
                        <el-alert
                                show-icon
                                v-if="alertInfo.show"
                                class="feedback-alert"
                                :title="alertInfo.title"
                                :type="alertInfo.type"
                                :style="{top: alertInfo.top + 'px'}"
                                @close="alertInfo.show = false"
                        >
                        </el-alert>
                    </transition>
                    <!-- 国外邮箱密码登录 -->
                    <div class="form-box-pwd inputBox-pc" v-show="pwdPCVisiable">
                        <div  class="form-box-code form-box">
                            <div class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                            <div class="page-title">
                                {{langMsg.pageTitle}}
                            </div>

                            <el-form
                                    :model="FormByPwdPC"
                                    id="pwdFormPC"
                                    :rules="FormByPwdRulesPC"
                                    ref="pwdFormPC"
                                    th:action="@{../oauth/do_login}"
                                    th:method="post"
                            >
                                <el-form-item prop="email">
                                    <el-input
                                            v-model="FormByPwdPC.email"
                                            id="FormByPwdPCEmail"
                                            :placeholder="langMsg.email"
                                            name="username"
                                            th:value="${username}"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item prop="pwd" class="pwd-input-en">
                                    <el-input
                                            v-model="FormByPwdPC.pwd"
                                            :type="pwdType"
                                            id="FormByPwdPCPwd"
                                            :placeholder="langMsg.password"
                                            name="password"
                                    >
                                        <img
                                                v-if="pwdFlag && FormByPwdPC.pwd"
                                                slot="suffix"
                                                class="suffix-icon"
                                                th:src="@{../static/img/eye_view.png}"
                                                @click="pwdFlag=!pwdFlag"
                                        />
                                        <img
                                                v-if="!pwdFlag && FormByPwdPC.pwd"
                                                slot="suffix"
                                                class="suffix-icon"
                                                th:src="@{../static/img/eye_hide.png}"
                                                @click="pwdFlag=!pwdFlag"
                                        />
                                    </el-input>
                                </el-form-item>

                                <div class="forget-pwd-tip">
                                    <a th:href="@{../oauth/getpass}"><span>{{langMsg.forget}}</span></a>
                                </div>

                                <el-form-item>
                                    <el-button class="loginBtn loginBtnPC loginBtnPCSMSPassword" @click="loginByPwdPC"
                                    >{{langMsg.login}}
                                    </el-button
                                    >
                                </el-form-item>

                                <div class="footer">
                                    <div class="a-black-important left-content" @click="toggleLoginPC(2)">
                                        {{langMsg.mobileLogin}}
                                    </div>
                                    <div>
                                        <a th:href="@{../oauth/register}"
                                        ><span class="a-black-important">{{langMsg.register}}</span></a
                                        >
                                    </div>
                                </div>
                            </el-form>
                            <!--三方登录-->
                            <div class="third-party-pc" v-if="referrer!='thirdParty'">
                                <!--                            <div class="third-party-pc" v-if="lang==='zh-cn'">-->
                                <div class="d-flex d-justify-center">
                                    <a v-if="isDevelop || (!isOverseas  && isYeelight)" th:href="@{../third-party-auth/render/wechat_mp}"
                                    ><img th:src="@{../static/img/wx_login.png}"/>
                                    </a>
                                    <a v-if="referrer==='portal' && ( isDevelop || (!isOverseas  && isYeelight))" th:href="@{../third-party-auth/render/feishu_app}"
                                    ><img th:src="@{../static/img/feishu_login.png}"/>
                                    </a>
                                    <a th:href="@{../third-party-auth/render/apple}"
                                    ><img th:src="@{../static/img/apple_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || isOverseas" th:href="@{../third-party-auth/render/facebook}"
                                    ><img th:src="@{../static/img/facebook_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas && !isYeelight)" th:href="@{../third-party-auth/render/google}"
                                    ><img th:src="@{../static/img/google_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas  && !isYeelight)" th:href="@{../third-party-auth/render/amazon}"
                                    ><img th:src="@{../static/img/amazon_login.png}"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 国外邮箱验证码登录 -->
                    <div class="form-box-pwd dia-box-retpwd inputBox-pc" v-show="!pwdPCVisiable">
                        <div  class="form-box-code form-box">
                            <div class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                            <div class="page-title">
                                {{langMsg.pageTitle}}
                            </div>

                            <el-form
                                    :model="FormByEmailCode"
                                    id="emailForm"
                                    :rules="FormByEmailCodeRules"
                                    ref="emailForm"
                                    @keyup.enter.native="loginByCodePC"
                                    th:action="@{../authentication/email}"
                                    th:method="post"
                            >
                                <el-form-item prop="email">
                                    <el-input
                                            v-model="FormByEmailCode.email"
                                            :placeholder="langMsg.email"
                                            name="email"
                                            th:value="${email}"
                                    ></el-input>
                                </el-form-item>
                                <el-form-item prop="code" class="captchaInput-SMS">
                                    <el-input
                                            name="captcha"
                                            v-model="FormByEmailCode.code"
                                            :placeholder="langMsg.code"
                                            class="codeInput"
                                            maxlength="6"
                                    ></el-input>
                                    <input type="hidden" id="smsCaptchaKey" name="captchaKey"/>
                                    <button type="button" class="getcode gainCode" @click="getCode('email')" style="padding-left: 0">{{msg}}
                                    </button>
                                </el-form-item>
                                <div class="forget-pwd-tip">
                                    <a th:href="@{../oauth/getpass}"><span>{{langMsg.forget}}</span></a>
                                </div>
                                <el-form-item>
                                    <el-button class="loginBtn loginBtnPC loginBtnPCSMS" @click="loginByEmailCode">
                                        {{langMsg.login}}
                                    </el-button>
                                </el-form-item>
                            </el-form>
                            <div class="footer">
                                <div class="a-black-important left-content" @click="toggleLoginPC(1)">
                                    {{langMsg.pwdLogin}}
                                </div>
                                <div>
                                    <a th:href="@{../oauth/register}"
                                    ><span class="a-black-important">{{langMsg.register}}</span></a
                                    >
                                </div>
                            </div>

                            <!--三方登录-->
                            <div class="third-party-pc" v-if="referrer!='thirdParty'">
                                <!--                            <div class="third-party-pc" v-if="lang==='zh-cn'">-->
                                <div class="d-flex d-justify-center">
                                    <a v-if="isDevelop || (!isOverseas  && isYeelight)" th:href="@{../third-party-auth/render/wechat_mp}"
                                    ><img th:src="@{../static/img/wx_login.png}"/>
                                    </a>
                                    <a v-if="referrer==='portal' && (isDevelop || (!isOverseas && isYeelight))" th:href="@{../third-party-auth/render/feishu_app}"
                                    ><img th:src="@{../static/img/feishu_login.png}"/>
                                    </a>
                                    <a th:href="@{../third-party-auth/render/apple}"
                                    ><img th:src="@{../static/img/apple_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || isOverseas" th:href="@{../third-party-auth/render/facebook}"
                                    ><img th:src="@{../static/img/facebook_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas && !isYeelight)" th:href="@{../third-party-auth/render/google}"
                                    ><img th:src="@{../static/img/google_login.png}"/>
                                    </a>
                                    <a v-if="isDevelop || (isOverseas  && !isYeelight)" th:href="@{../third-party-auth/render/amazon}"
                                    ><img th:src="@{../static/img/amazon_login.png}"/>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>
</body>

<script th:inline="javascript">
    /*<![CDATA[*/
    let imgCodeRequired = [[${enableCaptcha}]];
    let errorMsg = [[${errorMsg}]];
    let type = [[${type}]];
    let sessionUsername = [[${sessionUsername}]];
    /*]]>*/
</script>
<script th:src="@{../static/js/aes256.min.js}"></script>
<script th:src="@{../static/js/vue.js}"></script>
<script th:src="@{../static/js/element-ui.js}"></script>
<script th:src="@{../static/js/jquery.min.js}"></script>
<script th:src="@{../static/js/index-validation.js}"></script>
<script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/popup.js}"></script>
<script th:src="@{../static/js/index.js}"></script>
</html>
