<!DOCTYPE html>
<html style="height: 100%" lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
  />
  <meta name="referrer" content="no-referrer-when-downgrade" />
  <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}" />
  <link rel="stylesheet" th:href="@{../static/css/element-ui.css}" />
  <link rel="stylesheet" th:href="@{../static/css/common.css}" />
  <link rel="stylesheet" th:href="@{../static/css/index.css}" />
  <title th:text="${VENDOR_NAME}"></title>
  <script th:src="@{../static/js/rem.js}"></script>
</head>
<body style="height: 100%; margin: 0" ontouchstart>
<div class="Container" v-cloak>
  <!-- PC端 -->
  <div class="container PCcontainer PCcontainerWecom" :class="{'container-cn': lang==='zh-cn'}">
    <div class="PCBox" v-cloak>
      <div class="d-flex box-content">
        <div class="flex-left flex-left-cn-wecom"></div>
        <div class="inputBox-pc inputBox-pc-cn">
            <transition name="fade">
              <el-alert
                      show-icon
                      v-if="alertInfo.show"
                      class="feedback-alert feedback-alert-cn"
                      :title="alertInfo.title"
                      :type="alertInfo.type"
                      :style="{top: alertInfo.top + 'px'}"
                      @close="alertInfo.show = false"
              >
              </el-alert>
            </transition>
            <!-- 密码登录 -->
            <div class="form-box-pwd form-box" v-show="pwdPCVisiable">
              <el-form
                      :model="FormByPwdPC"
                      id="pwdFormPC"
                      :rules="FormByPwdRulesPC"
                      ref="pwdFormPC"
                      @keyup.enter.native="loginByPwdPC"
                      th:action="@{../oauth/do_login}"
                      th:method="post"
                      autocomplete="off"
              >
                <el-form-item prop="tel">
                  <el-input
                          v-model="FormByPwdPC.tel"
                          id="FormByPwdPCTel"
                          :class="{'tip-padding':FormByPwdPC.tel}"
                          :placeholder="langMsg.placeholder"
                          name="username"
                          clearable
                          th:value="${username}"
                  >
                  </el-input>
                  <span class="input-placeholder-tip" v-if="FormByPwdPC.tel">{{langMsg.placeholder}}</span>
                </el-form-item>
                <el-form-item prop="pwd">
                  <el-input
                          v-model="FormByPwdPC.pwd"
                          :type="pwdType"
                          id="FormByPwdPCPwd"
                          :class="{'tip-padding':FormByPwdPC.pwd}"
                          :placeholder="langMsg.passwordPlaceholder"
                          name="password"
                  >
                    <img
                            v-if="pwdFlag"
                            slot="suffix"
                            class="suffix-icon"
                            th:src="@{../static/img/eye_view.png}"
                            @click="pwdFlag=!pwdFlag"
                    />
                    <img
                            v-else
                            slot="suffix"
                            class="suffix-icon"
                            th:src="@{../static/img/eye_hide.png}"
                            @click="pwdFlag=!pwdFlag"
                    />
                  </el-input>
                  <span class="input-placeholder-tip" v-if="FormByPwdPC.pwd">{{langMsg.passwordPlaceholder}}</span>
                </el-form-item>
                <el-form-item th:if="${enableCaptcha}" prop="imgCode">
                  <el-input
                          th:if="${enableCaptcha}"
                          v-model="FormByPwdPC.imgCode"
                          id="FormByPwdPCImgCode"
                          name="captcha"
                          placeholder="短信验证码"
                          prefix-icon="el-icon-chat-line-square"
                          clearable
                          class="imgCodeInput"
                  ></el-input>
                  <img
                          th:if="${enableCaptcha}"
                          th:src="@{../public/captcha.jpg}"
                          id="captchaImgPC"
                          @click="changeCaptchaPC"
                          alt=""
                  />
                </el-form-item>
                <!--                                <a th:href="@{../third-party-auth/render/xiaomi}">Xiaomi Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/wechat_mp}">Wechat Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/wechat_open}">Wechat Qrcode Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/feishu_app}">Feishu Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/google}">Google Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/amazon}">Amazon Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/amazon_alexa}">Amazon Alexa Login</a>-->
                <!--                                <a th:href="@{../third-party-auth/render/apple}">Apple Login</a>-->
                <el-form-item>
                  <el-button class="loginBtn loginBtnPC loginBtnPCSMSPassword" @click="loginByPwdPC"
                  >{{langMsg.login}}
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
      </div>
    </div>
  </div>
</div>
</body>

<script th:inline="javascript">
  /*<![CDATA[*/
  let imgCodeRequired = [[${enableCaptcha}]];
  let errorMsg = [[${errorMsg}]];
  /*]]>*/
</script>
<script th:src="@{../static/js/vue.js}"></script>
<script th:src="@{../static/js/element-ui.js}"></script>
<script th:src="@{../static/js/jquery.min.js}"></script>
<script th:src="@{../static/js/index-validation.js}"></script>
<script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/lang.js}" th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/index.js}"></script>
</html>
