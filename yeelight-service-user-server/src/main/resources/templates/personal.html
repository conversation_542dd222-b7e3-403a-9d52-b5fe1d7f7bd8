<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
      <meta name="format-detection" content="telephone=no" />
      <title>{{langMsg.pageTitle}}</title>
      <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
      <link rel="stylesheet" th:href="@{../static/css/element-ui.css}"/>
      <link rel="stylesheet" th:href="@{../static/css/reset.css}"/>
      <link rel="stylesheet" th:href="@{../static/css/common.css}"/>
      <link rel="stylesheet" th:href="@{../static/css/personal.css}"/>
<!--    <script th:src="@{../static/js/rem.js}"></script>-->
  </head>
  <body>
    <div class="Container" v-cloak>
    <!--手机端-->
      <div class="container">
        <section>
            <div class="section-mb">
                <div class="title-mb">
                    <div @click="history.go(-1)" class="left-mb">
                        <img th:src="@{../static/img/left-mb.png}" alt="">
                    </div>
                    <p>{{langMsg.title}}</p>
                </div>
            </div>
            <div class="section-mb bgBanner-mb">
                <div class="titleBox-mb">
                    <img th:src="@{../static/img/titleBox-mb.png}" alt="">
                </div>
                <div class="d-flex d-justify-between bgBanner-mb-title">
                    <div class="d-flex d-justify-between bgBanner-mb-left">
                        <div>
                            <div>
                                <span class="getName-mb" th:text="${yeelightUser.getName()}"></span>
                                <img class="retitle-mb"
                                     @click="showMaskBox.showTitleChangeBox = true"
                                     th:src="@{../static/img/retitle-mb.png}" alt="">
                            </div>
                            <p class="getId-md">ID:<span th:text="${yeelightUser.getId()}"></span></p>
                        </div>

                        <div>
                            <img class="logo-mb" th:src="@{${RESOURCES_STATIC_IMG_MOBILE_LOGO}}" alt="">
                        </div>
                    </div>
                    <div class="bgBanner-mb-right">
                        <div class="getAvatar-mb-box"
                             @click="showTitleChangeBox">
                            <img class="getAvatar-mb" th:src="${yeelightUser.getAvatar()}" alt="" />
                        </div>
                        <img class="camera-m" th:src="@{../static/img/camera-mb.png}" alt="" />
                    </div>
                </div>
            </div>

            <div class="section-mb">
                <div class="d-flex d-justify-between descBox-mb">
                    <p class="descBox-mb-title">{{langMsg.phoneNumber}}</p>
                    <div
                            @click="showMaskBox.showPhoneChangeBox=true"
                            class="d-flex">
                        <div class="getPhoneNumber-mb" th:text="${yeelightUser.getPhoneNumber()}"></div>
                        <img class="right-mb" th:src="@{../static/img/right-mb.png}" alt="" />
                    </div>
                </div>
                <div class="d-flex d-justify-between descBox-mb descBox-mb-email">
                    <p class="descBox-mb-title">{{langMsg.email}}</p>
                    <div
                            @click="showMaskBox.showEmailChangeBox = true"
                            class="d-flex">
                        <p class="getPhoneNumber-mb" th:text="${yeelightUser.getEmail()}"></p>
                        <img class="right-mb" th:src="@{../static/img/right-mb.png}" alt="" />
                    </div>
                </div>
                <template v-for="item in thirdPartyInfo">
                    <div class="thirdPartyBox descBox-mb d-flex d-align-center d-justify-between" v-if="item.source!=='WECHAT_OPEN' && item.isShow">
                        <p v-if= "isCn" class="descBox-mb-title" style="flex: 1;">
                            {{ item.sourceName}}
                        </p>
                        <p v-else class="descBox-mb-title" style="flex: 1;">
                            {{ item.sourceEnName}}
                        </p>
                        <div>
                            <span style="margin-right: 1rem;">
                                      {{ item.id ? langMsg.bound : langMsg.unBound }}
                                  </span>
                            <el-button class="thirdPartyBtn" v-if="!item.id" type="text" @click="bindThirdParty(item)">{{langMsg.bind}}</el-button>
                            <el-button class="thirdPartyBtn" v-else type="text"  @click="unBindThirdParty(item)">{{langMsg.unBind}}</el-button>
                        </div>
                    </div>
                </template>
            </div>

            <div class="section-mb" th:onclick="window.location.href='../oauth/tokens'">
                <p class="detail-mb">{{langMsg.tokensManage}}</p>
            </div>
            <div class="section-mb" th:onclick="window.location.href='../oauth/sessions'">
                <p class="detail-mb">{{langMsg.sessionsManage}}</p>
            </div>
            <div class="section-mb" th:onclick="window.location.href='../oauth/logout'">
                <p class="logout-mb">{{langMsg.logout}}</p>
            </div>
        </section>



          <!-- mb 修改头像-->
          <section
                  v-show="showMaskBox.showHeadImgChangeBox"
                  class="showMask-md showMask-md-head changeTitle">
              <div class="changeBox">
                  <div class="contentBox">
                      <div class="boxItem">
                          <div class="orgImgBox">
                              <el-upload class="d-flex upload-demo" action="" drag
                                         v-show="false"
                                         :auto-upload="false" :show-file-list="false" :on-change='changeUpload'>
                              </el-upload>
                              <div v-show="option.img"
                                   title="图片剪裁">
                                  <div class="cropper-content">
                                      <div class="cut">
                                          <vue-cropper ref="cropper" :img="option.img" :output-size="option.size" :output-type="option.outputType" :info="true" :full="option.full" fixed="true" :fixed-number="option.fixedNumber"
                                                       :can-move="option.canMove" :can-move-box="option.canMoveBox" :fixed-box="option.fixedBox" :original="option.original"
                                                       :auto-crop="option.autoCrop" :auto-crop-width="option.autoCropWidth" :auto-crop-height="option.autoCropHeight" :center-box="option.centerBox"
                                                       @real-time="realTime" :high="option.high"
                                                       mode="cover" :max-img-size="option.max"></vue-cropper>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="boxItem boxItemRight d-flex d-justify-between">
                          <div v-show="false" class="previewBox-Content">
                              <div class="previewBox" :style="previews.div">
                                  <img :src="previews.url" :style="previews.img" alt="">
                              </div>
                          </div>
                      </div>
                      <div class="d-flex d-align-center btn-box-mb">
                          <p class="confimBtn-sub-mb reUpdate-md" @click="showMaskBox.showHeadImgChangeBox=false">{{langMsg.cancel}}</p>
                          <button class="confimBtn-sub-mb"
                                  @click="upDataAvatar"
                                  :disabled="!option.img">{{langMsg.submit}}</button>
                      </div>
                  </div>
              </div>
          </section>


          <!-- 修改昵称-->
          <section
                  v-show="showMaskBox.showTitleChangeBox"
                  class="showMask-md changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox">
                      <div class="section-mb">
                          <div class="title-mb">
                              <div class="left-mb" @click="hideAlltap">
                                  <img th:src="@{../static/img/left-mb.png}" alt="">
                              </div>
                              <p>{{langMsg.updateNickName}}</p>
                          </div>
                      </div>
                  </div>

                  <div class="contentBox contentBox-md">
                      <div class="d-flex d-align-center selectItem">
                          <el-input
                                  :placeholder="langMsg.requireNickNameTip"
                                  v-model="inputData.userNameChange"
                                  clearable>
                          </el-input>
                      </div>
                      <button class="el-button nextStepBtn confimBtn"
                              @click="uploadData(1)"
                              :disabled="!inputData.userNameChange">{{langMsg.submit}}</button>
                  </div>
              </div>
          </section>

          <!-- mb 修改手机号-->
          <section
                  v-show="showMaskBox.showPhoneChangeBox"
                  class="showMask-md changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox">
                      <div class="section-mb">
                          <div class="title-mb">
                              <div class="left-mb" @click="hideAlltap">
                                  <img th:src="@{../static/img/left-mb.png}" alt="">
                              </div>
                              <p>{{langMsg.updatePhoneNumber}}</p>
                          </div>
                      </div>
                  </div>

                  <div class="contentBox contentBox-md">
                      <div class="d-flex d-align-center selectItem">
                          <el-input
                                  :placeholder="langMsg.requirePhoneNumberTip"
                                  v-model="inputData.userPhoneChange"
                                  maxlength="11"
                                  clearable>
                          </el-input>
                      </div>
                      <div class="d-flex d-align-center selectItem">
                          <el-input
                                  :placeholder="langMsg.requireCaptchaTip"
                                  v-model="inputData.NumVerification"
                                  class="NumVerification-mb"
                                  maxlength="6"
                                  clearable>
                          </el-input>
                          <button @click="getCode(1)"
                                  :disabled="msg.disiblePhone"
                                  class="gainCode getCodePC getcode verificationBtn">{{msg.getCodeMsgPhone}}</button>
                      </div>
                      <button class="el-button nextStepBtn confimBtn"
                              @click="uploadData(2)"
                              :disabled="showNumConfirmBtn">{{langMsg.submit}}</button>
                  </div>
              </div>
          </section>

          <!-- mb 修改邮箱-->
          <section
                  v-show="showMaskBox.showEmailChangeBox"
                  class="showMask-md changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox">
                      <div class="section-mb">
                          <div class="title-mb">
                              <div class="left-mb" @click="hideAlltap">
                                  <img th:src="@{../static/img/left-mb.png}" alt="">
                              </div>
                              <p>{{langMsg.updateEmail}}</p>
                          </div>
                      </div>
                  </div>

                  <div class="contentBox contentBox-md">
                      <div class="d-flex d-align-center selectItem">
                          <el-input
                                  :placeholder="langMsg.requireEmailTip"
                                  v-model="inputData.userEmailChange"
                                  maxlength="50"
                                  clearable>
                          </el-input>
                      </div>
                      <div class="d-flex d-align-center selectItem">
                          <el-input
                                  :placeholder="langMsg.requireCaptchaTip"
                                  class="NumVerification-mb"
                                  v-model="inputData.emailVerification"
                                  maxlength="6"
                                  clearable>
                          </el-input>
                          <button @click="getCode(2)"
                                  :disabled="msg.disibleEmail"
                                  class="gainCode getCodePC getcode verificationBtn">{{msg.getCodeMsgEmail}}</button>
                      </div>
                      <button class="el-button nextStepBtn confimBtn"
                              @click="uploadData(3)"
                              :disabled="showEmailConfirmBtn">{{langMsg.submit}}</button>
                  </div>
              </div>
          </section>

      </div>
      <!-- PC端 -->
      <div class="PCcontainer PCBox">
          <section class="bgBanner">
              <div class="topLogo">
                  <img th:src="@{${RESOURCES_STATIC_IMG_PERSON_LOGO}}" alt="">
              </div>
              <div class="bgContent">
                  <img th:src="@{../static/img/bg-person.png}" alt="">
              </div>
          </section>

          <section class="acountBox">
              <div  class="acountHead">
                  <div class="d-flex d-justify-between account-top">
                      <p class="setAccount">{{langMsg.title}}</p>
                      <div class="d-flex">
                          <div class="d-flex detailBox" th:onclick="window.location.href='../oauth/tokens'">
                              <div class="detailImgBox"><img th:src="@{../static/img/jumpTo.png}" alt=""></div>
                              <span>{{langMsg.tokensManage}}</span>
                          </div>
                          <div class="d-flex detailBox" th:onclick="window.location.href='../oauth/sessions'">
                              <div class="detailImgBox"><img th:src="@{../static/img/jumpTo.png}" alt=""></div>
                              <span>{{langMsg.sessionsManage}}</span>
                          </div>
                          <div class="d-flex logoutContent" th:onclick="window.location.href='../oauth/logout'">
                              <div class="logoutBox"><img th:src="@{../static/img/logout-person.png}" alt=""></div>
                              <span class="logoutText">{{langMsg.logout}}</span>
                          </div>
                      </div>
                  </div>
              </div>

              <div class="infoBox">
                  <div class="headImgBox">
                      <div class="infoBoxItem headImgBox-img" @mouseenter="showOutBox.showCamera=true" @mouseleave="showOutBox.showCamera=false"
                      @click="showMaskBox.showHeadImgChangeBox = true">
                          <img th:src="${yeelightUser.getAvatar()}" alt="" />
                          <p v-show="showOutBox.showCamera" class="d-flex maskCamera"><img th:src="@{../static/img/canmera-person.png}" alt=""></p>
                      </div>
                      <div class="headImgBox-name"
                           @mouseenter="showOutBox.showNameFix=true"
                           @mouseleave="showOutBox.showNameFix=false">
                          <p class="userData.userName">
                              <span style="margin-left: 2.5rem">{{langMsg.nickName}}：</span>
                              <span ref="getName"
                                    th:text="${yeelightUser.getName()}"></span>
                              <span
                                      @click="showMaskBox.showTitleChangeBox=true"
                                      class="userName-fix"
                                      :class = "{'hidden':!showOutBox.showNameFix}">{{langMsg.change}}</span>
                          </p>
                          <div class="idBox">
                              <span>ID：</span><span class="account"
                                                    ref="getId"
                                                    th:text="${yeelightUser.getId()}"></span>
                          </div>
                      </div>

                      <div class="infoBoxItem phoneAndemail">
                          <div class="phoneBox d-flex d-justify-between d-align-center"
                               @mouseenter="showOutBox.showNumFix=true"
                               @mouseleave="showOutBox.showNumFix=false">
                              <p class="boxTitle">{{langMsg.phoneNumber}}</p>
                              <div class="d-input"
                                   :class="{'d-button-active':showOutBox.showNumFix}"
                                   ref="getPhoneNumber"
                              th:text="${yeelightUser.getPhoneNumber()}">
                              </div>
                              <div class="d-button"
                                   @click="showMaskBox.showPhoneChangeBox=true"
                                   :class="{'d-button-active':showOutBox.showNumFix}">
                                  <span v-if="userData.phone">{{langMsg.change}}</span>
                                  <span v-else>{{langMsg.bind}}</span>
                              </div>
                          </div>
                          <div class="emailBox d-flex d-justify-between d-align-center"
                               @mouseenter="showOutBox.showEmailFix=true"
                               @mouseleave="showOutBox.showEmailFix=false">
                              <p class="boxTitle">{{langMsg.email}}</p>
                              <div class="d-input"
                                   ref="getEmail"
                                   :class="{'d-button-active':showOutBox.showEmailFix}"
                              th:text="${yeelightUser.getEmail()}">
                              </div>
                              <div class="d-button"
                                   @click="showMaskBox.showEmailChangeBox=true"
                                   :class="{'d-button-active':showOutBox.showEmailFix}">
                                  <span v-if="userData.email">{{langMsg.change}}</span>
                                  <span v-else>{{langMsg.bind}}</span>
                              </div>
                          </div>
<!--                          三方账号信息-->
                          <template v-for="item in thirdPartyInfo">
                              <div class="thirdPartyBox d-flex d-align-center d-justify-between" v-if="item.source!=='WECHAT_MP' && item.isShow" >
                                  <p v-if= "isCn" class="boxTitle" style="flex: 1;">
                                      {{ item.sourceName}}
                                  <span style="margin-left: 2rem;">
                                      {{ item.id ? langMsg.bound : langMsg.unBound }}
                                  </span>
                                  </p>
                                  <p v-else class="boxTitle" style="flex: 1;">
                                      {{ item.sourceEnName}}
                                      <span style="margin-left: 2rem;">
                                      {{ item.id ? langMsg.bound : langMsg.unBound }}
                                  </span>
                                  </p>
                                  <el-button class="thirdPartyBtn" v-if="!item.id" plain @click="bindThirdParty(item)">{{langMsg.bind}}</el-button>
                                  <el-button class="thirdPartyBtn" v-else plain @click="unBindThirdParty(item)">{{langMsg.unBind}}</el-button>
                              </div>
                          </template>
                      </div>
                  </div>
              </div>
          </section>
          <!-- 修改头像-->
          <section
                  v-show="showMaskBox.showHeadImgChangeBox"
                  class="showMask changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox changeHeadBox">
                      <p >{{langMsg.uploadAvatar}}</p>
                      <p class="closeBox" @click="showMaskBox.showHeadImgChangeBox = false">
                          <img th:src="@{../static/img/closeBtn.png}" alt="">
                      </p>
                  </div>

                  <div class="d-flex d-justify-between d-align-end contentBox">
                      <div class="boxItem">
                          <p class="reUpdate" @click="option.img=''">{{langMsg.uploadAvatarAgain}}</p>
                          <div class="orgImgBox">
                              <el-upload class="upload-demo" action="" drag
                                         v-show="!option.img"
                                         :auto-upload="false" :show-file-list="false" :on-change='changeUpload'>
                                  <div>
                                      <i class="el-icon-upload"></i>
                                      <div class="el-upload__text">{{langMsg.clickUploadAvatar}}</div>
                                      <div class="el-upload__tip">{{langMsg.uploadAvatarTip}}</div>
                                  </div>
                              </el-upload>
                              <div v-show="option.img"
                                   title="图片剪裁">
                                  <div class="cropper-content">
                                      <div class="cut">
                                          <vue-cropper ref="cropper" :img="option.img" :output-size="option.size" :output-type="option.outputType" :info="true" :full="option.full" fixed="true" :fixed-number="option.fixedNumber"
                                                       :can-move="option.canMove" :can-move-box="option.canMoveBox" :fixed-box="option.fixedBox" :original="option.original"
                                                       :auto-crop="option.autoCrop" :auto-crop-width="option.autoCropWidth" :auto-crop-height="option.autoCropHeight" :center-box="option.centerBox"
                                                       @real-time="realTime" :high="option.high"
                                                       mode="cover" :max-img-size="option.max"></vue-cropper>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                      <div class="boxItem boxItemRight d-flex d-justify-between">
                          <div class="previewBox-Content">
                              <div class="previewBox" :style="previews.div">
                                  <img :src="previews.url" :style="previews.img" alt="">
                              </div>
                          </div>
                          <button class="confimBtn"
                                  @click="upDataAvatar"
                                  :disabled="!option.img">{{langMsg.submit}}</button>
                      </div>
                  </div>
              </div>
          </section>
          <!-- 修改昵称-->
          <section
                  v-show="showMaskBox.showTitleChangeBox"
                  class="showMask changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox">
                      <p
                              th:if="${yeelightUser.getName()}">{{langMsg.updateNickName}}</p>
                      <p th:if="!${yeelightUser.getName()}">{{langMsg.bindNickName}}</p>
                      <p class="closeBox" @click="showMaskBox.showTitleChangeBox = false">
                          <img th:src="@{../static/img/closeBtn.png}" alt="">
                      </p>
                  </div>

                  <div class="contentBox">
                      <div class="d-flex d-align-center selectItem">
                          <p class="titelP">{{langMsg.nickName}}</p>
                          <el-input
                                  :placeholder="langMsg.requireNickNameTip"
                                  v-model="inputData.userNameChange"
                                  clearable>
                          </el-input>
                      </div>
                      <button class="confimBtn"
                              @click="uploadData(1)"
                              :disabled="!inputData.userNameChange">{{langMsg.submit}}</button>
                  </div>
              </div>
          </section>

          <!-- 修改手机号-->
          <section
                  v-show="showMaskBox.showPhoneChangeBox"
                  class="showMask changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox">
                      <p v-if="userData.phone">{{langMsg.updatePhoneNumber}}</p>
                      <p v-else>{{langMsg.bindPhoneNumber}}</p>
                      <p class="closeBox" @click="showMaskBox.showPhoneChangeBox = false">
                          <img th:src="@{../static/img/closeBtn.png}" alt="">
                      </p>
                  </div>

                  <div class="contentBox">
                      <div class="d-flex d-align-center selectItem">
                          <p class="titelP">{{langMsg.phoneNumber}}</p>
                          <el-input
                                  :placeholder="langMsg.requirePhoneNumberTip"
                                  v-model="inputData.userPhoneChange"
                                  maxlength="11"
                                  clearable>
                          </el-input>
                      </div>
                      <div class="d-flex d-align-center selectItem">
                          <p class="titelP">{{langMsg.captcha}}</p>
                          <el-input
                                  :placeholder="langMsg.requireCaptchaTip"
                                  v-model="inputData.NumVerification"
                                  maxlength="6"
                                  clearable>
                          </el-input>
                          <button @click="getCode(1)"
                                  :disabled="msg.disiblePhone"
                                  class="verificationBtn">{{msg.getCodeMsgPhone}}</button>
                      </div>
                      <button class="confimBtn"
                              @click="uploadData(2)"
                              :disabled="showNumConfirmBtn">{{langMsg.submit}}</button>
                  </div>
              </div>
          </section>

          <!-- 修改邮箱-->
          <section
                  v-show="showMaskBox.showEmailChangeBox"
                  class="showMask changeTitle">
              <div class="changeBox">
                  <div class="d-flex d-justify-between d-align-center changeTitleBox">
                      <p v-if="userData.phone">{{langMsg.updateEmail}}</p>
                      <p v-else>{{langMsg.bindEmail}}</p>
                      <p class="closeBox" @click="showMaskBox.showEmailChangeBox = false">
                          <img th:src="@{../static/img/closeBtn.png}" alt="">
                      </p>
                  </div>

                  <div class="contentBox">
                      <div class="d-flex d-align-center selectItem">
                          <p class="titelP">{{langMsg.email}}</p>
                          <el-input
                                  :placeholder="langMsg.requireEmailTip"
                                  v-model="inputData.userEmailChange"
                                  maxlength="50"
                                  clearable>
                          </el-input>
                      </div>
                      <div class="d-flex d-align-center selectItem">
                          <p class="titelP">{{langMsg.captcha}}</p>
                          <el-input
                                  :placeholder="langMsg.requireCaptchaTip"
                                  v-model="inputData.emailVerification"
                                  maxlength="6"
                                  clearable>
                          </el-input>
                          <button @click="getCode(2)"
                                  :disabled="msg.disibleEmail"
                                  class="verificationBtn">{{msg.getCodeMsgEmail}}</button>
                      </div>
                      <button class="confimBtn"
                              @click="uploadData(3)"
                              :disabled="showEmailConfirmBtn">{{langMsg.submit}}</button>
                  </div>
              </div>
          </section>
      </div>
    </div>


<!--      <div class="avatar-box">-->
<!--        <img th:src="${yeelightUser.getAvatar()}" alt="" />-->
<!--        <div class="username" th:text="${username}">欢迎</div>-->
<!--        <div class="account" th:text="${yeelightUser.getId()}"></div>-->
<!--      </div>-->
<!--      <div class="button-box">-->
<!--        <button type="button" class="btn" th:onclick="window.location.href='../oauth/logout'">退出登录</button>-->
<!--      </div>-->
<!--    </div>-->
  </body>
  <script th:inline="javascript">
      /*<![CDATA[*/
      let imgCodeRequired = [[${enableCaptcha}]];
      let errorMsg = [[${errorMsg}]];
      /*]]>*/
  </script>
  <script>
      (function(doc, win) {
          var docEl = doc.documentElement;
          var resizeEvt =
            "orientationchange" in window ? "orientationchange" : "resize";
          var recalc = function() {
              var clientWidth = docEl.clientWidth;
              if (!clientWidth) {
                  return false;
              } else {
                  if (clientWidth <= 600) {
                      docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
                  }
              }
          };
          if (!doc.addEventListener) return;
          win.addEventListener(resizeEvt, recalc, false);
          doc.addEventListener("DOMContentLoaded", recalc, false);
      })(document, window);
  </script>
  <script th:src="@{../static/js/aes256.min.js}"></script>
  <script th:src="@{../static/js/vue.js}"></script>
  <script th:src="@{../static/js/element-ui.js}"></script>
  <script th:src="@{../static/js/jquery.min.js}"></script>
  <script th:src="@{../static/js/vue-cropper.min.js}"></script>
  <script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
  <script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
  <script th:src="@{../static/js/personal.js}"   th:attr="data-vendor-name=${VENDOR_NAME}"></script>
</html>
