<!DOCTYPE html>
<html style="height: 100%" lang="en" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
    <link rel="stylesheet" th:href="@{../static/css/element-ui.css}" />
    <link rel="stylesheet" th:href="@{../static/css/common.css}" />
    <link rel="stylesheet" th:href="@{../static/css/register.css}" />
    <link rel="stylesheet" th:href="@{../static/css/popup.css}" />
    <title th:text="${VENDOR_NAME}"></title>
  </head>
  <body style="height: 100%; margin: 0" ontouchstart>
    <div class="Container">
      <div class="container PCcontainer" :class="{'container-cn': lang==='zh-cn'}" v-cloak>
        <transition name="popup">
          <popup v-if="showPopupFlag" @close-popup="closePopup" :title-desc="langMsg.popupTitle">
            <div class="agreement-content">
              <div class="agreement-box mx-10">
                <div class="privacy-item" @click="window.open('../public/license','_blank')">
                  <div class="icon-box">
                    <img class="logo" th:src="@{../static/img/agreemet-icon.png}"/>
                  </div>
                  <div class="agreement-title">{{langMsg.agreement}}</div>
                </div>

                <div class="privacy-item" @click="window.open('../public/privacy','_blank')">
                  <div class="icon-box">
                    <img class="logo" th:src="@{../static/img/privacy-icon.png}"/>
                  </div>
                  <div class="agreement-title">{{langMsg.privacy}}</div>
                </div>
              </div>

              <div class="message-box mx-10">
                {{langMsg.popupDesc}}
              </div>

              <div>
                <el-button class="nextStepBtn popupBtn" @click="doPopupFun">{{langMsg.popupBtn}}</el-button>
              </div>
            </div>
          </popup>
        </transition>


        <div class="PCBox">
          <div class="d-flex box-content">
            <!-- 注册 -->
<!--            <template v-if="isPhone">-->
<!--            <template v-if="false">-->
              <div class="dia-box-register form-box-code inputBox-pc inputBox-pc-cn">
                <transition name="fade">

                  <el-alert
                          show-icon
                          v-if="alertInfo.show"
                          class="feedback-alert feedback-alert-cn"
                          :title="alertInfo.title"
                          :type="alertInfo.type"
                          :style="{top: alertInfo.top + 'px'}"
                          @close="handleCloseAlert">
                  </el-alert>
                </transition>
                <el-form class="form-box" :model="registerForm" :rules="registerFormRules" :validate-on-rule-change="false" ref="registerForm">
                  <div v-if="!isMobile" class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                  <section v-else class="mb-top-logo-cn mb-top-logo">
                    <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>
                  </section>
                  <div class="page-title">
                    {{langMsg.pageTitle}}
                  </div>

                  <el-form-item v-if="isPhone" prop="tel">
                    <el-input
                            class="telInput"
                            clearable
                            type="tel"
                            maxlength="11"
                            v-model="registerForm.tel" id="phoneInput" name="phoneNumber" :placeholder="langMsg.phone">
                      <template slot="prepend">+86</template>
                    </el-input>
                  </el-form-item>

                  <el-form-item v-else prop="email">
                    <el-input
                            clearable
                            v-model="registerForm.email"
                            id="phoneInput"
                            name="emailNumber"
                            :placeholder="langMsg.email"></el-input>
                  </el-form-item>

                  <el-form-item prop="pwd">
                    <el-input
                            v-model="registerForm.pwd"
                            :class="[{'ios-hidePwd-input': isIos && !pwdFlag && registerForm.pwd }]"
                            :type="pwdType"
                            minlength = "6"
                            maxlength="32"
                            name="password"
                            :placeholder="langMsg.pwdError">
                      <template v-if="registerForm.pwd">
                        <img v-if="pwdFlag" slot="suffix" class="suffix-icon" th:src="@{../static/img/eye_view.png}" @click="pwdFlag=!pwdFlag" />
                        <img v-else slot="suffix" class="suffix-icon" th:src="@{../static/img/eye_hide.png}" @click="pwdFlag=!pwdFlag" />
                      </template>
                    </el-input>
                  </el-form-item>
                  <el-form-item prop="code"
                                id="registerFormCode">
                    <el-input v-model="registerForm.code"
                              maxlength="6"
                              name="captcha" :placeholder="langMsg.code"></el-input>
                    <input type="hidden" id="smsCaptchaKey" name="captchaKey">
                    <input type="hidden" id="lastCheckKey" name="lastCheckKey">
                    <button type="button" class="gainCode getCodePC getcode getEmailCode"
                            @click="getCode">{{msg}}</button>
                  </el-form-item>
                  <div class="switch-account-box-container">
                    <a href="javascript:void(0)" class="switch-account-box" @click="switchAccountType()">
                      <span v-if="isPhone">{{langMsg.useEmail}}</span>
                      <span v-else>{{langMsg.usePhone}}</span>
                      <el-icon name="sort">el-icon-sort</el-icon>
                    </a>
                  </div>
                  <el-form-item>
                    <el-button class="loginBtnPC nextStepBtn" @click="showPopup('registerForm')">{{langMsg.next}}</el-button>
                  </el-form-item>
                  <div class="footer text">
                    <span style="color: rgba(102, 102, 102, 1)">{{langMsg.tip}}</span>
                    <a class="goLoginBox" th:href="@{../oauth/login}">
                      <span>{{langMsg.login}}</span>
                    </a>
                  </div>
                </el-form>

              </div>
<!--            </template>-->
<!--            <template v-else>-->
<!--              <div class="dia-box-register form-box-code inputBox-pc" v-if="registerVis">-->
<!--                <transition name="fade">-->
<!--                  <el-alert-->
<!--                          show-icon-->
<!--                          v-if="alertInfo.show"-->
<!--                          class="feedback-alert"-->
<!--                          :title="alertInfo.title"-->
<!--                          :type="alertInfo.type"-->
<!--                          :style="{top: alertInfo.top + 'px'}"-->
<!--                          @close="alertInfo.show = false">-->
<!--                  </el-alert>-->
<!--                </transition>-->
<!--                <el-form class="form-box" :model="registerFormForEn" ref="registerFormForEn" :rules="registerFormForEnRules">-->
<!--                  <div v-if="!isMobile" class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>-->
<!--                  <section v-else class="mb-top-logo-cn mb-top-logo">-->
<!--                    <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>-->
<!--                  </section>-->
<!--                  <div class="page-title">-->
<!--                    {{langMsg.pageTitle}}-->
<!--                  </div>-->
<!--                  <el-form-item prop="email">-->
<!--                    <el-input-->
<!--                            clearable-->
<!--                            v-model="registerFormForEn.email"-->
<!--                            id="phoneInput"-->
<!--                            name="emailNumber"-->
<!--                            :placeholder="langMsg.email"></el-input>-->
<!--                  </el-form-item>-->
<!--                  <el-form-item prop="code" v-if="isMobile">-->
<!--                    <el-input-->
<!--                            v-model="registerFormForEn.code"-->
<!--                            maxlength="6"-->
<!--                            name="captcha"-->
<!--                            :placeholder="langMsg.code">-->
<!--                    </el-input>-->
<!--                    <input type="hidden" id="smsCaptchaKey" name="captchaKey">-->
<!--                    <input type="hidden" id="lastCheckKey" name="lastCheckKey">-->
<!--                    <button type="button" class="gainCode getCodePC getcode getEmailCode" @click="getEmailCode">{{msg}}</button>-->
<!--                  </el-form-item>-->

<!--                  <template v-if="!isMobile">-->
<!--                    <el-form-item prop="pwd" class="pwd-input-en">-->
<!--                      <el-input-->
<!--                              v-model="registerFormForEn.pwd"-->
<!--                              :class="[{'ios-hidePwd-input': isIos && !pwdFlag && registerFormForEn.pwd}]"-->
<!--                              :type="pwdType"-->
<!--                              :placeholder="langMsg.pwdError"-->
<!--                              name="password">-->
<!--                        <img v-if="pwdFlag" slot="suffix" class="suffix-icon" th:src="@{../static/img/eye_view.png}" @click="pwdFlag=!pwdFlag" />-->
<!--                        <img v-else slot="suffix" class="suffix-icon" th:src="@{../static/img/eye_hide.png}" @click="pwdFlag=!pwdFlag" />-->
<!--                      </el-input>-->
<!--                    </el-form-item>-->
<!--                    <el-form-item prop="code">-->
<!--                      <el-input-->
<!--                              v-model="registerFormForEn.code"-->
<!--                              maxlength="6"-->
<!--                              name="captcha"-->
<!--                              :placeholder="langMsg.code">-->
<!--                      </el-input>-->
<!--                      <input type="hidden" id="smsCaptchaKey" name="captchaKey">-->
<!--                      <input type="hidden" id="lastCheckKey" name="lastCheckKey">-->
<!--                      <button type="button" class="gainCode getCodePC getcode getEmailCode" @click="getEmailCode">{{msg}}</button>-->
<!--                    </el-form-item>-->

<!--                  </template>-->

<!--                  <el-form-item>-->
<!--                    <el-button class="loginBtn nextStepBtn" v-if="isMobile" @click="nextStepEn">{{langMsg.mobileNext}}</el-button>-->
<!--                    <el-button v-else class="loginBtnPC nextStepBtn" @click="showPopup('registerFormForEn',doRegister)">{{langMsg.next}}</el-button>-->
<!--                  </el-form-item>-->
<!--                  <div class="text footer">-->
<!--                    <template v-if="isMobile">-->
<!--                      <a class="forget-pwd-tip" th:href="@{../oauth/login}">-->
<!--                        <span>{{langMsg.tip}}</span>-->
<!--                        <span class="goLoginBox">{{langMsg.login}}</span>-->
<!--                      </a>-->
<!--                    </template>-->
<!--                    <template v-else>-->
<!--                      <span style="color: #000;">{{langMsg.tip}}</span>-->
<!--                      <a class="goLoginBox" th:href="@{../oauth/login}">-->
<!--                        <span>{{langMsg.login}}</span>-->
<!--                      </a>-->
<!--                    </template>-->
<!--                  </div>-->
<!--                </el-form>-->
<!--              </div>-->
<!--              <div class="form-box-code inputBox-pc dia-box-setpwd" v-if="setPwdVis">-->
<!--                <el-form  :model="registerFormForEn"-->
<!--                          class="form-box"-->
<!--                          ref="registerFormForEn" :rules="registerFormForEnRules">-->
<!--                  <div v-if="!isMobile" class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>-->
<!--                  <section v-else class="mb-top-logo-cn mb-top-logo">-->
<!--                    <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>-->
<!--                  </section>-->
<!--                  <div class="page-title">-->
<!--                    {{langMsg.pageTitle}}-->
<!--                  </div>-->
<!--                  <el-form-item prop="pwd" class="pwd-input-en">-->
<!--                    <el-input-->
<!--                            v-model="registerFormForEn.pwd"-->
<!--                            :class="[{'ios-hidePwd-input': isIos && !pwdFlag && registerFormForEn.pwd }]"-->
<!--                            :type="pwdType"-->
<!--                            :placeholder="langMsg.pwdError"-->
<!--                            name="password">-->
<!--                      <img v-if="pwdFlag" slot="suffix" class="suffix-icon" th:src="@{../static/img/eye_view.png}" @click="pwdFlag=!pwdFlag" />-->
<!--                      <img v-else slot="suffix" class="suffix-icon" th:src="@{../static/img/eye_hide.png}" @click="pwdFlag=!pwdFlag" />-->
<!--                    </el-input>-->
<!--                  </el-form-item>-->

<!--                  <el-form-item>-->
<!--                    <el-button class="loginBtnPC nextStepBtn isOperate" @click="showPopup('registerFormForEn',doRegister)">{{langMsg.next}}</el-button>-->
<!--                  </el-form-item>-->
<!--                  <div class="text footer">-->
<!--                    <span style="color: #000;">{{langMsg.tip}}</span>-->
<!--                    <a class="goLoginBox" th:href="@{../oauth/login}">-->
<!--                      <span>{{langMsg.login}}</span>-->
<!--                    </a>-->
<!--                  </div>-->
<!--                </el-form>-->
<!--              </div>-->
<!--            </template>-->
          </div>
        </div>
      </div>
    </div>
  </body>
  <script th:inline="javascript">
    /*<![CDATA[*/
    let errorMsg = [[${errorMsg}]];
    /*]]>*/
  </script>
  <script th:src="@{../static/js/aes256.min.js}"></script>
  <script th:src="@{../static/js/vue.js}"></script>
  <script th:src="@{../static/js/element-ui.js}"></script>
  <script th:src="@{../static/js/jquery.min.js}"></script>
  <script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
  <script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
  <script th:src="@{../static/js/register-validation.js}"></script>
  <script th:src="@{../static/js/popup.js}"></script>
  <script th:src="@{../static/js/register_smartlicht.js}"></script>
</html>
