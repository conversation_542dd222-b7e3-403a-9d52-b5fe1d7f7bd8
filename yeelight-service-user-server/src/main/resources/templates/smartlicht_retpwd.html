<!DOCTYPE html>
<html style="height: 100%" lang="en" xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html">
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
  <link rel="stylesheet" th:href="@{../static/css/element-ui.css}" />
  <link rel="stylesheet" th:href="@{../static/css/common.css}" />
  <link rel="stylesheet" th:href="@{../static/css/getpwd.css}" />
  <title th:text="${VENDOR_NAME}"></title>
</head>
<body style="height: 100%; margin: 0" ontouchstart>
<div class="Container">
  <div class="container PCcontainer" :class="{'container-cn': lang==='zh-cn'}" v-cloak>
    <div v-if="isMobile" class="back-login back-login-mobile" @click="goToLogin"></div>
    <div class="PCBox">
      <div class="d-flex box-content">
        <!-- 找回密码 -->
        <div v-if="retpwdVis" class="form-box-code inputBox-pc dia-box-retpwd inputBox-pc" :class="{'inputBox-pc-cn': lang==='zh-cn'}" >
          <transition name="fade">
            <el-alert
                    show-icon
                    v-if="alertInfo.show"
                    class="feedback-alert"
                    :title="alertInfo.title"
                    :type="alertInfo.type"
                    :style="{top: alertInfo.top + 'px'}"
                    @close="alertInfo.show = false">
            </el-alert>
          </transition>
          <el-form class="form-box" :model="retpwdForm" :rules="retpwdFormRules" ref="retpwdForm">
            <div v-if="!isMobile" class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
            <div class="page-title">
              {{langMsg.pageTitle}}
              <div v-if="!isMobile" class="back-login" @click="goToLogin"></div>
            </div>
            <el-form-item prop="tel" v-if="isPhone">
              <el-input
                      v-model="retpwdForm.tel"
                      class="telInput"
                      name="phoneNumber"
                      id="phoneInput"
                      type="tel"
                      maxlength="11"
                      placeholder="手机号">
                <template slot="prepend">+86</template>
              </el-input>
            </el-form-item>
            <el-form-item prop="email" v-else>
              <el-input class="emailInput" name="email" id="emailInput"
                        v-model="retpwdForm.email" :placeholder="langMsg.email"></el-input>
            </el-form-item>
            <el-form-item prop="code">
              <el-input v-model="retpwdForm.code"
                        maxlength="6"
                        name="captcha" :placeholder="langMsg.captchaPlaceholder"></el-input>
              <input type="hidden" id="smsCaptchaKey" name="captchaKey">
              <input type="hidden" id="lastCheckKey" name="lastCheckKey">
              <button type="button" class="getcode getCodePC gainCode"
                      :class="{'gainCode-cn':lang==='zh-cn'}"
                      @click="handleSendCode">{{msg}}</button>
            </el-form-item>
            <div class="switch-account-box-container">
              <a href="javascript:void(0)" class="switch-account-box" @click="switchAccountType()">
                <span v-if="isPhone">{{langMsg.useEmail}}</span>
                <span v-else>{{langMsg.usePhone}}</span>
                <el-icon name="sort">el-icon-sort</el-icon>
              </a>
            </div>
            <el-form-item>
              <el-button class="nextStepBtn" @click="nextStep">{{langMsg.next}}</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="form-box-code inputBox-pc dia-box-code" v-if="codeVis">
          <!-- 设置密码 -->
          <el-form class="form-box" :model="retpwdForm" id="retpwdForm" :rules="retpwdFormRules" ref="retpwdForm">
            <div v-if="!isMobile" class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
            <div class="page-title">
              {{langMsg.resetPassword}}
              <div v-if="!isMobile" class="back-login" @click="goToLogin"></div>
            </div>
            <el-form-item prop="pwd">
              <el-input
                      v-model="retpwdForm.pwd"
                      :class="[{'ios-hidePwd-input': isIos && retpwdForm.pwd }]"
                      minlength = "6"
                      maxlength="32"
                      :type="pwdType" name="password"
                      :placeholder="langMsg.newPwdPlaceholder">

                <img
                        v-if="retpwdForm.pwd && pwdFlag"
                        slot="suffix"
                        class="suffix-icon"
                        th:src="@{../static/img/eye_view.png}"
                        @click="pwdFlag=!pwdFlag"
                />
                <img
                        v-if="retpwdForm.pwd && !pwdFlag"
                        slot="suffix"
                        class="suffix-icon"
                        th:src="@{../static/img/eye_hide.png}"
                        @click="pwdFlag=!pwdFlag"
                />
              </el-input>
            </el-form-item>
            <el-form-item prop="confirmPwd">
              <el-input
                      v-model="retpwdForm.confirmPwd"
                      :class="[{'ios-hidePwd-input': isIos && retpwdForm.confirmPwd }]"
                      minlength = "6"
                      maxlength="32"
                      :type="pwdRepeteType" name="newPassword"
                      :placeholder="langMsg.confirmPwdPlaceholder">

                <img
                        v-if="retpwdForm.confirmPwd && pwdRepeteFlag"
                        slot="suffix"
                        class="suffix-icon"
                        th:src="@{../static/img/eye_view.png}"
                        @click="pwdRepeteFlag=!pwdRepeteFlag"
                />
                <img
                        v-if="retpwdForm.confirmPwd && !pwdRepeteFlag"
                        slot="suffix"
                        class="suffix-icon"
                        th:src="@{../static/img/eye_hide.png}"
                        @click="pwdRepeteFlag=!pwdRepeteFlag"
                />
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button class="nextStepBtn" @click="updatePwd">{{langMsg.confirmPwdBtn}}</el-button>
            </el-form-item>
            <!--              <div class="text" v-if="!isMobile">-->
            <!--                <span>{{langMsg.findPwd.tip}}</span>-->
            <!--                <a class="a-black-important" th:href="@{../oauth/login}">{{langMsg.findPwd.login}}</a>-->
            <!--              </div>-->
          </el-form>
        </div>
      </div>
    </div>
  </div>
</div>
</body>

<script th:inline="javascript">
  /*<![CDATA[*/
  let errorMsg = [[${errorMsg}]];
  /*]]>*/
</script>
<script th:src="@{../static/js/aes256.min.js}"></script>
<script th:src="@{../static/js/vue.js}"></script>
<script th:src="@{../static/js/element-ui.js}"></script>
<script th:src="@{../static/js/jquery.min.js}"></script>
<script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/getpwd-validation.js}"></script>
<script th:src="@{../static/js/getpwd_smartlicht.js}"></script>

</html>
