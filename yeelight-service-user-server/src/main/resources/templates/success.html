<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
    <title th:text="${VENDOR_NAME}"></title>
    <style>
        .button-box {
            width: 100%;
            text-align: center;
        }
        .btn {
            margin: 0;
            padding: 0;
            border: 0;
            outline: none;
            width: 28rem;
            height: 4.5rem;
            background: rgba(45, 42, 38, 1);
            font-size: 1.7rem;
            font-weight: 400;
            color: rgba(255, 255, 255, 1);
            line-height: 2.4rem;
        }
    </style>
</head>
<body>
<div class="container">
    <img
            th:src="@{${RESOURCES_STATIC_IMG_BLACK_LOGO}}"
            alt=""
            style="width: 10rem;height: 10rem;display: block;margin: 0 auto;margin-top: 15rem;margin-bottom: 5.4rem;"
    />
    <div style="text-align: center;width: 80%;margin-left:10%;margin-bottom: 5.4rem;font-size: 1.2rem; color: green;" th:text="${msg}"></div>
    <div class="button-box">
        <button type="button" class="btn" th:href="${url}" onclick="redirectToUrl(this)">确定</button>
    </div>
</div>

<script th:src="@{/apis/account/static/js/jquery.min.js}"></script>
<script>
    function redirectToUrl(e) {
        console.log($(e).attr('href'))
        window.location.href = $(e).attr('href');
    }

    (function(doc, win) {
        var docEl = doc.documentElement;
        var resizeEvt =
            "orientationchange" in window ? "orientationchange" : "resize";
        var recalc = function() {
            var clientWidth = docEl.clientWidth;
            console.log(clientWidth);
            if (!clientWidth) {
                return false;
            } else {
                if (clientWidth <= 414) {
                    docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
                } else {
                    docEl.style.fontSize = 3.125 * (clientWidth / 375) + "px";
                }
            }
        };
        if (!doc.addEventListener) return;
        win.addEventListener(resizeEvt, recalc, false);
        doc.addEventListener("DOMContentLoaded", recalc, false);
    })(document, window);
</script>
</body>
</html>
