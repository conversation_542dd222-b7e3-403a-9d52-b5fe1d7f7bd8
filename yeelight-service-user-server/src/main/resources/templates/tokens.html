<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="format-detection" content="telephone=no" />
  <title>{{langMsg.pageTitle}}</title>
  <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
  <link rel="stylesheet" th:href="@{../static/css/element-ui.css}"/>
  <link rel="stylesheet" th:href="@{../static/css/reset.css}"/>
  <link rel="stylesheet" th:href="@{../static/css/common.css}"/>
  <link rel="stylesheet" th:href="@{../static/css/tokens.css}"/>
  <!--    <script th:src="@{../static/js/rem.js}"></script>-->
</head>
<body>
<div class="Container" v-cloak>
  <div class="PCcontainer PCBox">
    <section class="bgBanner">
      <div class="topLogo">
        <img th:src="@{${RESOURCES_STATIC_IMG_PERSON_LOGO}}" alt="">
      </div>
      <div class="bgContent">
        <img th:src="@{../static/img/bg-person.png}" alt="">
      </div>
    </section>

    <section class="acountBox">
      <div  class="acountHead d-flex d-justify-between d-align-center">
        <div class="account-top">
          <p class="setAccount">{{langMsg.title}}</p>
        </div>
        <div class="logoutBox">
          <el-button @click="logout('all',langMsg.logoutAll)">{{langMsg.logoutAll}}</el-button>
        </div>
      </div>

      <div class="listBox">
        <div v-for="item in browserList" class="browserItem">
          <div class="infoBox d-flex">
            <div class="imgBox">
              <img :src="getImgSrc(item)" alt="">
            </div>
            <div class="descBox">
              <div class="descHead">
                {{getEnumDesc(item.loginType,langMsg.LoginType)}}
              </div>
              <div class="descItem">
                <span class="descWord">{{getEnumDesc(item.oauth2GrantType,langMsg.GrantType)}}</span>
                <span v-show="item.oauth2LoginType" class="descWord">{{getEnumDesc(item.oauth2LoginType,langMsg.OAuth2LoginType)}}</span>
              </div>
              <div class="descItem">
                {{langMsg.expirationTime}}: <span class="descWord">{{getFormatTime(item.expirationTime)}}</span>
              </div>
              <div v-show="item.showDetail" class="moreDesc">
                <div v-show="item.browserName" class="descItem">
                  {{langMsg.browserName}}: <span class="descWord">{{item.browserName}}</span>
                </div>
                <div v-show="item.browserVersion" class="descItem">
                  {{langMsg.browserVersion}}: <span class="descWord">{{item.browserVersion}}</span>
                </div>
                <div v-if="item.platform||item.osName||item.osVersion" class="descItem">
                  <span>{{item.platform}}</span>
                  <span>{{item.osName}}</span>
                  <span>{{item.osVersion}}</span>
                </div>
                <div v-show="item.creationTime" class="descItem">
                  {{langMsg.creationTime}}: <span class="descWord">{{getFormatTime(item.creationTime)}}</span>
                </div>
                <div v-show="item.browserName" class="descItem">
                  {{langMsg.browserName}}: <span class="descWord">{{item.browserName||'--'}}</span>
                </div>
                <div v-show="item.browserVersion" class="descItem">
                  {{langMsg.browserVersion}}: <span class="descWord">{{item.browserVersion||'--'}}</span>
                </div>
                <div v-show="item.platform" class="descItem">
                  {{langMsg.platform}}: <span class="descWord">{{item.platform||'--'}}</span>
                </div>
                <div v-show="item.osName" class="descItem">
                  {{langMsg.osName}}: <span class="descWord">{{item.osName||'--'}}</span>
                </div>
                <div v-show="item.osVersion" class="descItem">
                  {{langMsg.osVersion}}: <span class="descWord">{{item.osVersion||'--'}}</span>
                </div>
                <div v-show="item.locale" class="descItem">
                  {{langMsg.locale}}: <span class="descWord">{{item.locale||'--'}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="logoutBox">
            <el-button @click="logout(item.accessToken,langMsg.logout)">
              {{langMsg.logout}}
            </el-button>
          </div>

          <div class="bottomMore" @click="item.showDetail=!item.showDetail">
            <div class="moreBox">
              <img v-if="!item.showDetail" th:src="@{../static/img/browser/downArrow.png}" alt="">
              <img v-else th:src="@{../static/img/browser/upArrow.png}" alt="">
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>

</body>
<script th:inline="javascript">
  /*<![CDATA[*/
  let imgCodeRequired = [[${enableCaptcha}]];
  let errorMsg = [[${errorMsg}]];
  let yeelightUserID = [[${yeelightUser.getId()}]];


  const broserEnum = {
    wxwork: [[@{../static/img/browser/enterprise-weixin.png}]],
  MicroMessenger: [[@{../static/img/browser/weixin.png}]],
  miniProgram: [[@{../static/img/browser/mini-program.png}]],
  MQQBrowser: [[@{../static/img/browser/qq.png}]],
  "DingTalk-win": [[@{../static/img/browser/dingtalk.png}]],
  DingTalk: [[@{../static/img/browser/dingtalk.png}]],
  Alipay: [[@{../static/img/browser/alipay.png}]],
  Taobao: [[@{../static/img/browser/taobao.png}]],
  UCBrowser: [[@{../static/img/browser/uc.png}]],
  MSEdge: [[@{../static/img/browser/ie.png}]],
  Chrome: [[@{../static/img/browser/chrome.png}]],
  Firefox: [[@{../static/img/browser/firefox.png}]],
  IEMobile: [[@{../static/img/browser/ie.png}]],
  Safari: [[@{../static/img/browser/safari.png}]],
  Opera: [[@{../static/img/browser/opera.png}]],
  Default: [[@{../static/img/browser/others.png}]],
  }
  /*]]>*/
</script>
<script>
  (function(doc, win) {
    var docEl = doc.documentElement;
    var resizeEvt =
            "orientationchange" in window ? "orientationchange" : "resize";
    var recalc = function() {
      var clientWidth = docEl.clientWidth;
      if (!clientWidth) {
        return false;
      } else {
        if (clientWidth <= 600) {
          docEl.style.fontSize = 10 * (clientWidth / 375) + "px";
        }
      }
    };
    if (!doc.addEventListener) return;
    win.addEventListener(resizeEvt, recalc, false);
    doc.addEventListener("DOMContentLoaded", recalc, false);
  })(document, window);
</script>
<script th:src="@{../static/js/aes256.min.js}"></script>
<script th:src="@{../static/js/vue.js}"></script>
<script th:src="@{../static/js/element-ui.js}"></script>
<script th:src="@{../static/js/jquery.min.js}"></script>
<script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<!--<script th:src="@{../static/js/enum.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>-->
<script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
<script th:src="@{../static/js/tokens.js}"   th:attr="data-vendor-name=${VENDOR_NAME}"></script>
</html>
