<!DOCTYPE html>
<html style="height: 100%" lang="en" xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="shortcut icon" th:href="@{${RESOURCES_STATIC_IMG_FAVICON}}">
    <link rel="stylesheet" th:href="@{../static/css/element-ui.css}" />
    <link rel="stylesheet" th:href="@{../static/css/common.css}" />
    <link rel="stylesheet" th:href="@{../static/css/user-signed-in.css}" />
    <link rel="stylesheet" th:href="@{../static/css/popup.css}" />
    <title th:text="${VENDOR_NAME}"></title>
  </head>
  <body style="height: 100%; margin: 0" ontouchstart>
    <div class="Container">
      <div class="container PCcontainer" :class="{'container-cn': lang==='zh-cn'}" v-cloak>
        <div class="PCBox">
          <div class="d-flex box-content">
            <!-- 已登录 -->
            <div class="dia-box-signed form-box-code inputBox-pc inputBox-pc-cn">
              <el-form class="form-box">
                <div v-if="!isMobile" class="icon-box-pc" th:style="${RESOURCES_STATIC_IMG_LOGO_STYLE}"></div>
                <section v-else class="mb-top-logo-cn mb-top-logo">
                  <img class="logo" th:src="@{${RESOURCES_STATIC_IMG_LOGO}}"/>
                </section>
                <div class="page-title">
                  {{langMsg.title}}
                </div>
                <div class="headImgBox">
                  <div class="infoBoxItem headImgBox-img">
                    <img th:src="${yeelightUser.getAvatar()}" alt="" />
                  </div>
                  <div class="headImgBox-name">
                    <p class="userName">
                      <span ref="getName"
                            th:text="${yeelightUser.getName()}"></span>
                    </p>
                    <div class="idBox">
                      <span>ID：</span><span class="account" ref="getId" th:text="${yeelightUser.getId()}"></span>
                    </div>
                  </div>
                </div>

                <el-form-item>
                  <el-button class="loginBtnPC" @click="quickLogin()">{{langMsg.quickLogin}}</el-button>
                </el-form-item>
                <div class="footer text">
                  <span style="color: rgba(102, 102, 102, 1)">{{langMsg.loginOther}}</span>
                  <a class="goLoginBox" th:href="@{../oauth/login?forceLogin=true}">{{langMsg.login}}</a>
                </div>
              </el-form>

            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script th:inline="javascript">
    /*<![CDATA[*/
    let errorMsg = [[${errorMsg}]];
    /*]]>*/
  </script>
  <script th:src="@{../static/js/aes256.min.js}"></script>
  <script th:src="@{../static/js/vue.js}"></script>
  <script th:src="@{../static/js/element-ui.js}"></script>
  <script th:src="@{../static/js/jquery.min.js}"></script>
  <script th:src="@{../static/js/lang.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
  <script th:src="@{../static/js/common.js}"  th:attr="data-vendor-name=${VENDOR_NAME}"></script>
  <script th:src="@{../static/js/user-signed-in.js}"></script>
</html>
