package com.yeelight.service.user.server;

import com.alibaba.fastjson.JSON;
import com.yeelight.service.framework.util.PageResultSet;
import com.yeelight.service.framework.util.Result;
import com.yeelight.service.user.client.domain.UserEarnings;
import com.yeelight.service.user.client.domain.UserLevel;
import com.yeelight.service.user.client.dto.CheckedToken;
import com.yeelight.service.user.client.dto.UserWithdrawDetailDto;
import com.yeelight.service.user.client.enums.UserEarningFrom;
import com.yeelight.service.user.client.enums.UserEarningTypes;
import com.yeelight.service.user.client.enums.UserLevelTypes;
import com.yeelight.service.user.client.query.UserWithdrawsDetailQuery;
import com.yeelight.service.user.client.request.AddUserGrowthRequest;
import com.yeelight.service.user.client.service.TokenService;
import com.yeelight.service.user.server.service.dubbo.impl.*;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@Disabled
public class YeelightServiceUserServerApplicationTests {
    @Resource
    private YeelightUserReadServiceImpl yeelightUserReadService;

    @Resource
    private YeelightUserWriteServiceImpl yeelightUserWriteService;

    @Resource
    private TokenService tokenService;

    @Resource
    private UserLevelServiceImpl userLevelService;

    @Resource
    private UserEarningsServiceImpl userEarningsService;

    @Resource
    private UserWithdrawsServiceImpl userWithdrawsService;

    @Test
    public void contextLoads() {
    }

    @Test
    @Disabled
    public void testValidIds() {
        Set<Long> ids = new HashSet<>();
        ids.add(100026L);
        ids.add(100027L);
        ids.add(100028L);
        ids.add(100029L);
        ids.add(100030L);
        Set<Long> notValidIds = yeelightUserReadService.notValidIds(ids);
        System.out.println(notValidIds);
    }

    @Test
    @Disabled
    public void testCheckToken() {
        Result<CheckedToken> token =  tokenService.checkToken("********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        System.out.println(token);
    }

    @Test
    @Disabled
    public void testAddGrowth() {
        AddUserGrowthRequest addUserGrowthRequest = new AddUserGrowthRequest();
        addUserGrowthRequest.setIncreasedGrowth(-66);
        addUserGrowthRequest.setOperateUid(100028L);
        addUserGrowthRequest.setUserLevelType(UserLevelTypes.DESIGNER);
        addUserGrowthRequest.setYeelightUserId(100029L);
        userLevelService.addGrowth(addUserGrowthRequest);
    }

    @Test
    @Disabled
    public void testUpdateUserEarning() {
        UserEarnings userEarnings = new UserEarnings();
        userEarnings.setYeelightUserId(100028L);
        userEarnings.setEarningFrom(UserEarningFrom.ORDER.getCode());
        userEarnings.setIsCredit(1);
        userEarnings.setFromId(1999999L);
        userEarnings.setEarningType(UserEarningTypes.DESIGNER_COMMISSION.getCode());
        userEarnings.setEarningMoney(new BigDecimal("199.99"));
        userEarningsService.updateUserEarning(userEarnings, UserLevelTypes.DESIGNER);
    }

    @Test
    @Disabled
    public void testUserLevelParse() {
        UserLevel userLevel = userLevelService.selectByPrimaryKey(1L);
        System.out.println(userLevel);
        UserLevel.CommissionRule commissionRule = JSON.parseObject(userLevel.getCommissionRules(), UserLevel.CommissionRule.class);
        System.out.println(commissionRule);

        UserLevel.GrowthRule growthRule = JSON.parseObject(userLevel.getGrowthRules(), UserLevel.GrowthRule.class);
        System.out.println(growthRule);
    }

    @Test
    @Disabled
    public void testUserWithdraws() {
        UserWithdrawsDetailQuery query = UserWithdrawsDetailQuery.builder().yeelightUserId(122433L).build();
        PageResultSet<UserWithdrawDetailDto> pageResultSet = userWithdrawsService.pageWithdrawsDetailList(query);
        System.out.println(pageResultSet);
    }

    @Test
    public void testRemoveUser() {
        yeelightUserWriteService.removeUserByUserName("wechatrrdawb");
    }
}
