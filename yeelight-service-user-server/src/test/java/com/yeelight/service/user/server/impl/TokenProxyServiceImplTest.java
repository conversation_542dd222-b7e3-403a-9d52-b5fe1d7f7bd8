package com.yeelight.service.user.server.impl;

import com.google.common.collect.Maps;
import com.yeelight.service.user.client.domain.ProxyInfo;
import com.yeelight.service.user.client.service.TokenProxyService;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import org.springframework.boot.test.context.SpringBootTest;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * ${DESCRIPTION}
 *
 * <AUTHOR>
 * @date 2020-04-14 13:32
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@Disabled
class TokenProxyServiceImplTest {
    private long workerId = 122399L;
//    private long workerId = 1L;
    private long userId = 122374L;
    private long subJobId = 77L;

    //    @Reference(injvm = false,version = "0.0.1",group = "develop")
    @Resource
    private TokenProxyService service;

    public TokenProxyServiceImplTest() {
    }

    @Test
    void getAuthentication() {
    }

    @Test
    void bindProxy() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("sub_job_id", 78);
        ProxyInfo proxyInfo = ProxyInfo.builder()
                .token("******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
                .applyApplicationList(Lists.newArrayList("yeelight-iot-api", "saas-portal-admin-api"))
                .params(map)
                .build();
        service.bindProxy(122373, proxyInfo);
        map = Maps.newHashMap();
        map.put("sub_job_id", 77);
        proxyInfo = ProxyInfo.builder()
                .token("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiIxNzg1MzcxMTcwNCIsInNjb3BlIjpbInJlYWQiLCJ3cml0ZSJdLCJpZCI6MTIyMzc0LCJleHAiOjE1OTQ2OTUwMzEsInJlZ2lvbiI6IkNOIiwiZGV2aWNlIjoiOGE5MGJhYTEtY2ExNi00YzVhLWI1NjAtM2YwZDQ2ZDgzY2RlIiwiYXV0aG9yaXRpZXMiOlsiUk9MRV9VU0VSIl0sImp0aSI6IjA4NmMxNjM1LTIxYmYtNDRiMC1hNWVjLWRkMGNiODZkOTE0NCIsImNsaWVudF9pZCI6ImRldiIsInVzZXJuYW1lIjoiMTc4NTM3MTE3MDQifQ.BP-Z0jZJKMWC3t3HDUIrbJGJh9RV9Z03km8m7hTHSjxPOGchM1fPXF3-9630FN0EY5mSGSzgP8du3PKY2XROq4l3hd8b-ZZKJan3IIWA-ek4GRgNPYNYfBfKs-oXPIYTrcxL6d4qFMT1s2crNO70FWnG7MAtHNOwVyBsnaE-TFgOyqt-gZDfNxt5fxkfs_aitAyD0LIVlfz7a60_kbolA3OaH6AR8A94VPYkm7VSdDfqkhN_cxHKBz3Vzw_-E8SkJP_dvxg8_SEMeqPuEVjlRj0YbVCRddVK2hmxXGoainjVs8OlIJ_n0xD5qV8TBUu7-THjnUeycNcQWppYXWWk4Q")
                .applyApplicationList(Lists.newArrayList("yeelight-iot-api", "saas-portal-admin-api"))
                .params(map)
                .build();
        service.bindProxy(122399, proxyInfo);
    }

    @Test
    void getProxyInfo() {
        System.out.println("proxyInfo-122373:" + service.getProxyInfo(122373));
        System.out.println("proxyInfo-122399:" + service.getProxyInfo(122399));
    }

    @Test
    void unbindProxy() {
    }
}