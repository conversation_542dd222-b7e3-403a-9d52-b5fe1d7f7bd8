#!/bin/bash

# Yeelight User Service 优化启动脚本
# 解决JDK 21兼容性问题和Nacos线程资源耗尽问题

echo "🚀 启动 Yeelight User Service (优化版本)"
echo "📋 JVM优化参数："
echo "   - 堆内存: 1GB"
echo "   - 线程栈大小: 512KB"
echo "   - GC优化: ZGC"
echo "   - Nacos线程池优化"
echo ""

# 设置优化的JVM参数（专注于JDK 21兼容性修复）
export MAVEN_OPTS="-server -Xmx1g -Xms1g \
--add-opens java.base/java.lang=ALL-UNNAMED \
--add-opens java.base/java.util=ALL-UNNAMED \
--add-opens java.base/java.lang.reflect=ALL-UNNAMED \
--add-opens java.base/java.util.concurrent=ALL-UNNAMED \
--add-opens java.base/java.net=ALL-UNNAMED \
--add-opens java.base/java.io=ALL-UNNAMED \
--add-opens java.base/sun.nio.ch=ALL-UNNAMED \
--add-opens java.base/sun.security.util=ALL-UNNAMED \
--add-opens java.base/sun.security.x509=ALL-UNNAMED \
--add-opens java.base/java.security=ALL-UNNAMED \
--add-opens java.base/javax.crypto=ALL-UNNAMED \
-XX:+UseZGC \
-XX:MaxHeapSize=1g \
-XX:InitialHeapSize=1g \
-XX:ConcGCThreads=2 \
-XX:ParallelGCThreads=2 \
-XX:+ZProactive \
-XX:SoftMaxHeapSize=768m \
-XX:-ZUncommit \
-XX:+ZGenerational \
-Dfile.encoding=utf8 \
-Duser.timezone=Asia/Shanghai \
-XX:+HeapDumpOnOutOfMemoryError \
-Dspring.aop.proxy-target-class=false \
-Dspring.main.allow-bean-definition-overriding=true \
-Dcglib.debugLocation=/tmp/cglib-debug \
-Dnacos.client.grpc.pool.core.size=4 \
-Dnacos.client.grpc.pool.max.size=8 \
-Dnacos.client.grpc.pool.keep.alive.time=600 \
-Dnacos.client.config.retry.time=5000 \
-Dnacos.client.config.longPollTimeout=30000 \
-Dnacos.client.worker.thread.num=4 \
-Dnacos.client.config.listener.thread.num=4"

echo "✅ JVM参数设置完成"
echo "🔧 开始Maven编译..."

# 进入项目目录
cd yeelight-service-user-server

# 编译项目
./mvnw clean compile -DskipTests

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    echo "🚀 启动应用..."

    # 启动Spring Boot应用
    ./mvnw spring-boot:run -Dspring-boot.run.profiles=local
else
    echo "❌ 编译失败，请检查代码"
    exit 1
fi
